import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { SYSTEM_APPS, getIconForCategory } from '@/lib/app-definitions'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    // Buscar apps instaladas do usuário
    const installedApps = await prisma.$queryRaw`
      SELECT "appId" FROM installed_apps
      WHERE "userId" = ${session.user.id} AND "isActive" = true
    ` as any[]

    const installedAppIds = installedApps.map((app: any) => app.appId)

    // Buscar contadores de ativações para cada app
    const activationCounts = await prisma.$queryRaw`
      SELECT "appId", COUNT(*) as activations
      FROM installed_apps
      WHERE "isActive" = true
      GROUP BY "appId"
    ` as any[]

    const activationsMap = activationCounts.reduce((acc: any, item: any) => {
      acc[item.appId] = Number(item.activations)
      return acc
    }, {})

    // Buscar plano atual do usuário
    const userSubscription = await prisma.$queryRaw`
      SELECT
        s.*,
        sp."availableApps" as plan_available_apps
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${session.user.id}
      AND s.status = 'ACTIVE'
      ORDER BY s."createdAt" DESC
      LIMIT 1
    ` as any[]

    const userAvailableApps = userSubscription.length > 0
      ? userSubscription[0].plan_available_apps || []
      : []

    // Buscar configurações personalizadas das apps (do admin)
    let appConfigs: Record<string, any> = {}
    try {
      // Buscar configurações de qualquer admin (não específico do lojista)
      const configs = await prisma.appConfig.findMany()

      configs.forEach(config => {
        appConfigs[config.appId] = config.settings
      })
    } catch (error) {
      console.log('No custom app configs found')
    }

    // Usar apps do sistema com configurações personalizadas
    const availableApps = SYSTEM_APPS.map((app) => {
      const isIncludedInPlan = userAvailableApps.includes(app.appId)
      const isInstalled = installedAppIds.includes(app.appId)
      const customConfig = appConfigs[app.appId] || {}

      return {
        id: app.appId,
        name: customConfig.name || app.name,
        description: customConfig.description || app.description,
        icon: app.icon,
        category: app.category,
        price: customConfig.monthlyPrice ?? app.monthlyPrice,
        rating: 4.5, // Default rating
        activations: activationsMap[app.appId] || 0,
        features: customConfig.features || app.features,
        screenshots: app.screenshots,
        requiresPlan: isIncludedInPlan ? [] : app.requiredPlans,
        developer: app.developer,
        version: app.version,
        isPaid: (customConfig.monthlyPrice ?? app.monthlyPrice) > 0,
        hasTrialPeriod: app.hasTrialPeriod,
        trialDays: app.trialDays,
        isPopular: customConfig.isPopular ?? app.isPopular,
        isInstalled: isInstalled,
        canInstall: isIncludedInPlan || app.hasTrialPeriod || !((customConfig.monthlyPrice ?? app.monthlyPrice) > 0),
        isActive: customConfig.isActive ?? app.isActive
      }
    }).filter(app => app.isActive) // Filtrar apenas apps ativas

    console.log('availableApps final:', availableApps.length)
    console.log('Sample app:', availableApps[0])

    return NextResponse.json({
      apps: availableApps,
      categories: [
        { id: 'all', name: 'Todas', count: availableApps.length },
        { id: 'finance', name: 'Financeiro', count: 1 },
        { id: 'marketing', name: 'Marketing', count: 1 },
        { id: 'productivity', name: 'Produtividade', count: 1 },
        { id: 'communication', name: 'Comunicação', count: 1 },
        { id: 'analytics', name: 'Analytics', count: 1 },
        { id: 'crm', name: 'CRM', count: 1 }
      ]
    })

  } catch (error) {
    console.error('Erro ao buscar apps disponíveis:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
