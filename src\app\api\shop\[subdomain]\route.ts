import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: { subdomain: string} }
) {
  try {
    const subdomain = params.subdomain

    if (!subdomain) {
      return NextResponse.json(
        { message: "Subdomínio é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomínio personalizado
    const shop = await prisma.user.findFirst({
      where: {
        role: REPAIR_SHOP,
        profile: {
          customSubdomain: subdomain}
      },
      include: {
        profile: {
          select: {
            phone: true,
            address: true,
            city: true,
            postalCode: true,
            averageRepairTime: true,
            serviceRadius: true,
            workingBrands: true,
            workingCategories: true,
            workingProblems: true,
            logo: true,
            companyName: true,
            description: true,
            shopConfig: {
              select: {
                logoUrl: true}
            }
          }
        },
        subscription: {
          include: {
            plan: {
              select: {
                individualRepairs: true,
                miniStore: true}
            }
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: "Loja não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se a loja tem acesso à funcionalidade de mini-loja
    if (!shop.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: "Mini-loja não disponível para esta loja" },
        { status: 403 }
      )
    }

    // Calcular rating médio (se houver sistema de avaliações)
    const reviews = await prisma.review.findMany({
      where: {
        repairShopId: shop.id
      
}
    })

    const rating = reviews.length > 0 
      ? reviews.reduce((sum, 'review') => sum + review.rating, 0) / reviews.length
      : null

    // Formatar dados para o frontend
    const shopData = {
      id: shop.id,
      name: shop.name,
      companyName: shop.profile?.companyName || shop.name,
      description: shop.profile?.description,
      logo: shop.profile?.shopConfig?.logoUrl || shop.profile?.logo,
      profile: shop.profile,
      rating,
      reviewCount: reviews.length,
      hasIndividualRepairs: shop.subscription?.plan?.individualRepairs || false
}

    return NextResponse.json({
      success: true,
      shop: shopData})

  } catch (error) {
    console.error('Erro ao buscar loja:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
