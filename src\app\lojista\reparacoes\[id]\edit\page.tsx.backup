'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, Save } from 'lucide-react'

interface Repair {
  id: string
  status: string
  description: string
  estimatedPrice?: number
  finalPrice?: number
  customerName: string
  customerPhone: string
  customerNif?: string
  deliveryMethod: string
  pickupAddress?: string
  deliveryAddress?: string
}

const STATUS_OPTIONS = [
  { value: 'CONFIRMED', label: 'Confirmado' },
  { value: 'RECEIVED', label: 'Recebido' },
  { value: 'DIAGNOSIS', label: 'Diagnóstico' },
  { value: 'WAITING_PARTS', label: 'Aguarda Peças' },
  { value: 'IN_REPAIR', label: 'Em Reparação' },
  { value: 'TESTING', label: 'Teste' },
  { value: 'COMPLETED', label: 'Concluí<PERSON>' },
  { value: 'DELIVERED', label: 'Entregue' },
  { value: 'CANCELLED', label: 'Cancelado' }
]

export default function LojistaRepairEditPage({ params }: { params: { id: string } }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [repair, setRepair] = useState<Repair | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    status: '',
    finalPrice: '',
    trackingCode: '',
    notes: ''
  })

  useEffect(() => {
    if (status === 'loading') return

    if (!session?.user || session.user.role !== 'REPAIR_SHOP') {
      router.push('/auth/signin')
      return
    }

    fetchRepairDetails()
  }, [session, status, router, params.id])

  const fetchRepairDetails = async () => {
    try {
      const response = await fetch(`/api/repairs/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data)
        setFormData({
          status: data.status,
          finalPrice: data.finalPrice ? Number(data.finalPrice).toString() : '',
          trackingCode: data.trackingCode || '',
          notes: ''
        })
      } else {
        console.error('Reparação não encontrada')
      }
    } catch (error) {
      console.error('Erro ao carregar reparação:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!repair || isSaving) return

    setIsSaving(true)
    try {
      const response = await fetch(`/api/lojista/repairs/${repair.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: formData.status,
          finalPrice: formData.finalPrice ? parseFloat(formData.finalPrice) : null,
          notes: formData.notes.trim() || undefined
        })
      })

      if (response.ok) {
        router.push(`/lojista/reparacoes/${repair.id}`)
      } else {
        console.error('Erro ao atualizar reparação')
      }
    } catch (error) {
      console.error('Erro ao atualizar reparação:', error)
    } finally {
      setIsSaving(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!repair) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Reparação não encontrada</h1>
          <Link href="/lojista/reparacoes" className="text-black hover:underline">
            Voltar às reparações
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href={`/lojista/reparacoes/${repair.id}`}
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-700" />
              </Link>
              <h1 className="text-2xl font-bold text-black">Editar Reparação #{repair.id.slice(-8)}</h1>
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Guardando...' : 'Guardar'}
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="space-y-6">
            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estado da Reparação
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              >
                {STATUS_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Preço Final */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preço Final (€)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.finalPrice}
                onChange={(e) => setFormData({ ...formData, finalPrice: e.target.value })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                placeholder="0.00"
              />
            </div>

            {/* Tracking Code (se for envio por correio) */}
            {repair?.deliveryMethod === 'MAIL_SEND' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Código de Tracking dos CTT
                </label>
                <input
                  type="text"
                  value={formData.trackingCode}
                  onChange={(e) => setFormData({ ...formData, trackingCode: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                  placeholder="Ex: RR123456789PT"
                />
              </div>
            )}

            {/* Notas */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notas da Atualização
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                placeholder="Adicione notas sobre esta atualização..."
              />
            </div>

            {/* Informações do Cliente (apenas leitura) */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Informações do Cliente</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Nome</div>
                  <div className="font-medium text-gray-900">{repair.customerName}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Telefone</div>
                  <div className="font-medium text-gray-900">{repair.customerPhone}</div>
                </div>
                {repair.customerNif && (
                  <div>
                    <div className="text-sm text-gray-600">NIF</div>
                    <div className="font-medium text-gray-900">{repair.customerNif}</div>
                  </div>
                )}
                <div>
                  <div className="text-sm text-gray-600">Método de Entrega</div>
                  <div className="font-medium text-gray-900">
                    {repair.deliveryMethod === 'STORE_PICKUP' && 'Recolha na Loja'}
                    {repair.deliveryMethod === 'COURIER_PICKUP' && 'Recolha por Estafeta'}
                    {repair.deliveryMethod === 'MAIL_SEND' && 'Envio por Correio'}
                  </div>
                </div>
              </div>
            </div>

            {/* Descrição do Problema (apenas leitura) */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Descrição do Problema</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-900">{repair.description}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
