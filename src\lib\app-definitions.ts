// Apps hardcoded do sistema - estas são as apps que existem na plataforma
export const SYSTEM_APPS = [
  {
    appId: moloni,
    name: <PERSON><PERSON><PERSON>,
    description: Integração completa com Moloni para emissão automática de faturas e gestão fiscal.,
    category: finance,
    isPaid: false,
    monthlyPrice: 0,
    hasTrialPeriod: false,
    trialDays: 0,
    features: [
      'Emissão automática de faturas',
      'Sincronização de produtos',
      'Relatórios fiscais',
      'Gestão de clientes',
      'Backup automático'
    ],
    requiredPlans: ['REVY BASIC', 'REVY PRO'],
    isActive: true,
    isPopular: false,
    icon: FileText,
    developer: Revify,
    version: '2.1.0',
    screenshots: []
  
},
  {
    appId: 'newsletter-pro',
    name: 'Newsletter Pro',
    description: 'Sistema avançado de email marketing com templates profissionais e automação.',
    category: marketing,
    isPaid: true,
    monthlyPrice: 14.99,
    hasTrialPeriod: true,
    trialDays: 30,
    features: [
      'Templates profissionais',
      'Automação de campanhas',
      'Segmentação avançada',
      'Analytics detalhados',
      'A/B Testing'
    ],
    requiredPlans: ['REVY PRO'],
    isActive: true,
    isPopular: true,
    icon: Mail,
    developer: Revify,
    version: '1.5.0',
    screenshots: []
  },
  {
    appId: 'crm-advanced',
    name: 'CRM Avançado',
    description: 'Sistema completo de gestão de relacionamento com clientes, com automação e analytics.',
    category: productivity,
    isPaid: true,
    monthlyPrice: 9.99,
    hasTrialPeriod: true,
    trialDays: 30,
    features: [
      'Gestão de leads',
      'Automação de follow-up',
      'Analytics avançados',
      'Integração WhatsApp',
      'Relatórios personalizados'
    ],
    requiredPlans: ['REVY PRO'],
    isActive: true,
    isPopular: false,
    icon: Users,
    developer: Revify,
    version: '1.2.0',
    screenshots: []
  },
  {
    appId: 'inventory-manager',
    name: 'Gestor de Stock',
    description: 'Controlo avançado de inventário com alertas automáticos e previsões de procura.',
    category: productivity,
    isPaid: true,
    monthlyPrice: 7.99,
    hasTrialPeriod: true,
    trialDays: 30,
    features: [
      'Controlo de stock em tempo real',
      'Alertas de stock baixo',
      'Previsões de procura',
      'Códigos de barras',
      'Relatórios de movimento'
    ],
    requiredPlans: ['REVY PRO'],
    isActive: true,
    isPopular: false,
    icon: Package,
    developer: Revify,
    version: '2.0.3',
    screenshots: []
  },
  {
    appId: 'whatsapp-business',
    name: 'WhatsApp Business',
    description: 'Integração com WhatsApp Business para comunicação automatizada com clientes.',
    category: communication,
    isPaid: true,
    monthlyPrice: 12.99,
    hasTrialPeriod: true,
    trialDays: 30,
    features: [
      'Mensagens automáticas',
      'Templates aprovados',
      'Chatbot inteligente',
      'Integração com CRM',
      'Analytics de conversas'
    ],
    requiredPlans: ['REVY PRO'],
    isActive: true,
    isPopular: true,
    icon: MessageCircle,
    developer: Revify,
    version: '1.8.0',
    screenshots: []
  },
  {
    appId: 'analytics-pro',
    name: 'Analytics Pro',
    description: 'Relatórios avançados e dashboards personalizáveis para análise de negócio.',
    category: analytics,
    isPaid: true,
    monthlyPrice: 5.99,
    hasTrialPeriod: true,
    trialDays: 30,
    features: [
      'Dashboards personalizáveis',
      'Relatórios automáticos',
      'Métricas de performance',
      'Exportação de dados',
      'Alertas inteligentes'
    ],
    requiredPlans: ['REVY PRO'],
    isActive: true,
    isPopular: false,
    icon: BarChart3,
    developer: Revify,
    version: '1.0.5',
    screenshots: []
  }
]

export function getAppById(appId: string) {
  return SYSTEM_APPS.find(app => app.appId === 'appId')
}

export function getActiveApps() {
  return SYSTEM_APPS.filter(app => app.isActive)
}

export function getAppsByCategory(category: string) {
  return SYSTEM_APPS.filter(app => app.category === category && app.isActive)
}

export function getIconForCategory(category: string): string {
  const iconMap: Record<string, string> = {
    'finance': 'FileText',
    'marketing': 'Mail',
    'productivity': 'Package',
    'integration': 'Link',
    'communication': 'MessageCircle',
    'analytics': 'BarChart3',
    'crm': 'Users'
  }
  return iconMap[category] || 'Package'
}
