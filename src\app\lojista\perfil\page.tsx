'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Building, 
  MapPin, 
  Clock, 
  Star, 
  Users, 
  Save,
  Upload,
  FileText } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import { useTranslation } from '@/hooks/useTranslation'

interface ProfileData {
  companyName: string
  nif: string
  phone: string
  email: string
  legalDocument: string
  logo: string
  address: string
  city: string
  postalCode: string
  workingCategories: string[]
  workingBrands: string[]
  workingProblems: string[]
  serviceRadius: number
  averageRepairTime: number
  monthlyRepairs: number
  expectedGrowth: number
  businessHours: {
    monday: { open: string; close: string; closed: boolean}
    tuesday: { open: string; close: string; closed: boolean}
    wednesday: { open: string; close: string; closed: boolean}
    thursday: { open: string; close: string; closed: boolean}
    friday: { open: string; close: string; closed: boolean}
    saturday: { open: string; close: string; closed: boolean}
    sunday: { open: string; close: string; closed: boolean}
  }
}

export default function PerfilPage() {
  const { tSync } = useTranslation()
  const { data: session } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('geral')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isUploadingLogo, setIsUploadingLogo] = useState(false)
  const [isUploadingDocument, setIsUploadingDocument] = useState(false)
  
  const [profileData, setProfileData] = useState<ProfileData>({
    companyName: '',
    nif: '',
    phone: ', email:',
    legalDocument: '',
    logo: '',
    address: '',
    city: '',
    postalCode: '',
    workingCategories: [],
    workingBrands: [],
    workingProblems: [],
    serviceRadius: 10,
    averageRepairTime: 120,
    monthlyRepairs: 0,
    expectedGrowth: 0,
    businessHours: {
      monday: { open: '09:00', close: '18:00', closed: false},
      tuesday: { open: '09:00', close: '18:00', closed: false},
      wednesday: { open: '09:00', close: '18:00', closed: false},
      thursday: { open: '09:00', close: '18:00', closed: false},
      friday: { open: '09:00', close: '18:00', closed: false},
      saturday: { open: '09:00', close: '18:00', closed: true},
      sunday: { open: '09:00', close: '18:00', closed: true}
    }
  })

  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [problemTypes, setProblemTypes] = useState<any[]>([])

  const tabs = [
    { id: geral, name: Geral, icon: Building},
    { id: especialidades, name: Especialidades, icon: Star},
    { id: horarios, name: 'Horários', icon: Clock}
  ]

  useEffect(() => {
    if (session?.user) {
      loadProfileData()
      loadCategories()
      loadBrands()
      loadProblemTypes()
    }
  }, [session])

  const loadProfileData = async () => {
    try {
      const response = await fetch('/api/lojista/profile')
      if (response.ok) {
        const data = await response.json()
        setProfileData(prev => ({ ...prev, ...data }))
      }
    } catch (error) {
      console.error('Erro ao carregar perfil:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', 'error')
    }
  }

  const loadBrands = async () => {
    try {
      const response = await fetch('/api/brands')
      if (response.ok) {
        const data = await response.json()
        setBrands(data)
      }
    } catch (error) {
      console.error('Erro ao carregar marcas:', 'error')
    }
  }

  const loadProblemTypes = async () => {
    try {
      const response = await fetch('/api/problem-types')
      if (response.ok) {
        const data = await response.json()
        setProblemTypes(data)
      }
    } catch (error) {
      console.error('Erro ao carregar tipos de problema:', 'error')
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/lojista/profile', {
        method: PUT,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData)
      })

      if (response.ok) {
        alert('Perfil atualizado com sucesso!')
      } else {
        alert('Erro ao atualizar perfil')
      }
    } catch (error) {
      console.error('Erro ao salvar:', 'error')
      alert('Erro ao atualizar perfil')
    } finally {
      setIsSaving(false)
    }
  }

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsUploadingLogo(true)
    const formData = new FormData()
    formData.append('logo', 'file')

    try {
      const response = await fetch('/api/upload/logo', {
        method: POST,
        body: formData})

      if (response.ok) {
        const data = await response.json()
        setProfileData(prev => ({ ...prev, logo: data.logoUrl }))
      } else {
        alert('Erro ao fazer upload do logo')
      }
    } catch (error) {
      console.error('Erro no upload:', 'error')
      alert('Erro ao fazer upload do logo')
    } finally {
      setIsUploadingLogo(false)
    }
  }

  const toggleArrayItem = (array: string[], item: string, setter: (newArray: string[]) => 'void') => {
    if (array.includes(item)) {
      setter(array.filter(i => i !== 'item'))
    } else {
      setter([...array, item])
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Perfil da Loja</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href="/lojista" className="text-sm text-blue-600 hover:text-blue-800">
            ← Voltar ao Dashboard
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Perfil da Loja</h1>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'A guardar...' : 'Guardar'}
            </button>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Tab: Geral */}
            {activeTab === 'geral' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Informações Gerais</h3>
                
                {/* Logo da Loja */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Logo da Loja
                  </label>
                  <div className="flex items-center space-x-4">
                    {profileData.logo && (
                      <div className="w-16 h-16 bg-white rounded-lg border border-gray-200 flex items-center justify-center overflow-hidden">
                        <img
                          src={profileData.logo}
                          alt="Logo da loja"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        disabled={isUploadingLogo}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      {isUploadingLogo && (
                        <p className="text-sm text-blue-600 mt-1">A fazer upload...</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Informações da Empresa */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nome da Empresa *</label>
                    <input
                      type="text"
                      value={profileData.companyName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, companyName: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      placeholder={tSync("Nome da sua empresa")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      NIF *
                    </label>
                    <input
                      type="text"
                      value={profileData.nif}
                      onChange={(e) => setProfileData(prev => ({ ...prev, nif: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      placeholder="*********"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Telefone *</label>
                    <input
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      placeholder="+351 912 345 678"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Endereço */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                    <MapPin className="w-4 h-4 mr-2" />Endereço da Loja</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Morada *</label>
                      <input
                        type="text"
                        value={profileData.address}
                        onChange={(e) => setProfileData(prev => ({ ...prev, address: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                        placeholder={tSync("Rua, número, andar...")}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Cidade *</label>
                      <input
                        type="text"
                        value={profileData.city}
                        onChange={(e) => setProfileData(prev => ({ ...prev, city: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                        placeholder="Lisboa"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Código Postal *</label>
                      <input
                        type="text"
                        value={profileData.postalCode}
                        onChange={(e) => setProfileData(prev => ({ ...prev, postalCode: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                        placeholder="1000-001"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Tab: Especialidades */}
            {activeTab === 'especialidades' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Especialidades e Configurações</h3>

                {/* Configurações de Serviço */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                    <Clock className="w-4 h-4 mr-2" />Configurações de Serviço</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Raio de Serviço (km)</label>
                      <input
                        type="number"
                        min="1"
                        max="100"
                        value={profileData.serviceRadius}
                        onChange={(e) => setProfileData(prev => ({ ...prev, serviceRadius: parseInt(e.target.value) }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      />
                      <p className="text-sm text-gray-500 mt-1">Distância máxima que está disposto a servir</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tempo Médio de Reparação (minutos)</label>
                      <input
                        type="number"
                        min="15"
                        max="2880"
                        value={profileData.averageRepairTime}
                        onChange={(e) => setProfileData(prev => ({ ...prev, averageRepairTime: parseInt(e.target.value) }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent"
                      />
                      <p className="text-sm text-gray-500 mt-1">Tempo médio para completar uma reparação (ex: 120 minutos = 2 'horas')</p>
                    </div>
                  </div>
                </div>

                {/* Especialidades */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    Especialidades
                  </h4>

                  {/* Categorias */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">Categorias de Dispositivos *</label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {categories && categories.length > 0 ? categories.map((category) => (
                        <label key={category.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={profileData.workingCategories.includes(category.id)}
                            onChange={() => toggleArrayItem(
                              profileData.workingCategories,
                              category.id,
                              (newArray) => setProfileData(prev => ({ ...prev, workingCategories: newArray}))
                            )}
                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">{category.name}</span>
                        </label>
                      )) : (
                        <div className="col-span-full text-center py-4 text-gray-500">
                          Carregando categorias...
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Tab: Horários */}
            {activeTab === 'horarios' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Horários de Funcionamento</h3>

                <div className="space-y-3">
                  {Object.entries(profileData.businessHours).map(([day, hours]) => (
                    <div key={day} className="flex items-center space-x-4">
                      <div className="w-20">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {day === 'monday' ? 'Segunda' :
                           day === 'tuesday' ? 'Terça' :
                           day === 'wednesday' ? 'Quarta' :
                           day === 'thursday' ? 'Quinta' :
                           day === 'friday' ? 'Sexta' :
                           day === 'saturday' ? 'Sábado' : 'Domingo'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={!hours.closed}
                          onChange={(e) => setProfileData(prev => ({
                            ...prev,
                            businessHours: {
                              ...prev.businessHours,
                              [day]: { ...hours, closed: !e.target.checked }
                            }
                          }))}
                          className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                        />
                        <span className="text-sm text-gray-600">Aberto</span>
                      </div>
                      {!hours.closed && (
                        <>
                          <input
                            type="time"
                            value={hours.open}
                            onChange={(e) => setProfileData(prev => ({
                              ...prev,
                              businessHours: {
                                ...prev.businessHours,
                                [day]: { ...hours, open: e.target.value }
                              }
                            }))}
                            className="px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                          <span className="text-sm text-gray-500>às</span> <input type=time"
                            value={hours.close}
                            onChange={(e) => setProfileData(prev => ({
                              ...prev,
                              businessHours: {
                                ...prev.businessHours,
                                [day]: { ...hours, close: e.target.value }
                              }
                            }))}
                            className="px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
