const { exec } = require('child_process')
const { spawn } = require('child_process')

function checkServerRunning() {
  return new Promise((resolve) => {
    exec('netstat -an | findstr :3001', (error, stdout) => {
      resolve(stdout.includes(':3001'))
    })
  })
}

function openBrowser(url) {
  const start = process.platform === 'darwin' ? 'open' : 
                process.platform === 'win32' ? 'start' : 'xdg-open'
  exec(`${start} ${url}`)
}

async function startAndOpen() {
  console.log('🚀 Verificando servidor Revify...')
  
  const isRunning = await checkServerRunning()
  
  if (!isRunning) {
    console.log('⚠️ Servidor não está a correr. A iniciar...')
    console.log('💡 Execute: npm run dev')
    console.log('📍 Aguarde o servidor iniciar em http://localhost:3001')
    return
  }
  
  console.log('✅ Servidor está a correr!')
  console.log('\n🌐 A abrir aplicação no browser...')
  
  // Abrir páginas principais
  setTimeout(() => {
    console.log('📱 A abrir página principal...')
    openBrowser('http://localhost:3001')
  }, 1000)
  
  setTimeout(() => {
    console.log('🔐 A abrir página de login...')
    openBrowser('http://localhost:3001/auth/signin')
  }, 2000)
  
  setTimeout(() => {
    console.log('🏪 A abrir loja de teste...')
    openBrowser('http://localhost:3001/shop/techfix-porto')
  }, 3000)
  
  console.log('\n📋 CREDENCIAIS DE ACESSO:')
  console.log('\n👤 SUPERADMIN:')
  console.log('   📧 Email: <EMAIL>')
  console.log('   🔑 Password: Teste123123_')
  
  console.log('\n🏪 LOJISTA DE TESTE:')
  console.log('   📧 Email: <EMAIL>')
  console.log('   🔑 Password: teste123')
  
  console.log('\n✅ Aplicação pronta para uso!')
}

startAndOpen()
