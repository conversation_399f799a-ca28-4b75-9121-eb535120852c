'use client'

import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface RegistrationCardProps {
  title: string
  description: string
  features: string[]
  role: 'customer' | 'repair_shop' | 'courier'
  bgColor: string
  hoverColor: string
  icon: React.ReactNode
}

export default function RegistrationCard({
  title,
  description,
  features,
  role,
  bgColor,
  hoverColor,
  icon
}: RegistrationCardProps) {
  const { data: session } = useSession()

  const getDashboardUrl = () => {
    if (!session) return `/auth/signup?role=${role}`
    
    switch (session.user.role) {
      case 'ADMIN':
        return '/admin'
      case 'REPAIR_SHOP':
        return '/lojista'
      case 'COURIER':
        return '/estafeta'
      case 'CUSTOMER':
        return '/cliente'
      default:
        return '/cliente'
    }
  }

  const getButtonText = () => {
    if (!session) {
      switch (role) {
        case 'customer':
          return 'Registar como Cliente'
        case 'repair_shop':
          return 'Registar como Lojista'
        case 'courier':
          return 'Registar como Estafeta'
        default:
          return 'Registar'
      }
    }
    return 'Ir para Dashboard'
  }

  return (
    <div className="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-shadow border border-gray-200">
      <div className="mb-6">
        {icon}
      </div>
      <h3 className="text-xl font-bold text-gray-900 mb-4">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      <ul className="text-left space-y-2 mb-8">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center text-gray-700">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            {feature}
          </li>
        ))}
      </ul>
      <Link
        href={getDashboardUrl()}
        className={`block w-full ${bgColor} text-white py-3 px-6 rounded-xl ${hoverColor} transition-colors font-semibold`}
      >
        {getButtonText()}
      </Link>
    </div>
  )
}
