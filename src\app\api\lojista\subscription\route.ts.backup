import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar subscrição do usuário (incluindo pendentes)
    const subscription = await prisma.$queryRaw`
      SELECT
        s.*,
        sp.name as plan_name,
        sp.features as plan_features,
        sp."monthlyPrice" as plan_monthly_price,
        sp."yearlyPrice" as plan_yearly_price
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${session.user.id}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    // Buscar pagamentos da subscrição
    let payments = []
    if (subscription.length > 0) {
      const paymentsData = await prisma.$queryRaw`
        SELECT * FROM subscription_payments
        WHERE "subscriptionId" = ${subscription[0].id}
        ORDER BY "createdAt" DESC
        LIMIT 10
      `
      payments = paymentsData.map((payment: any) => ({
        id: payment.id,
        amount: Number(payment.amount),
        currency: payment.currency,
        status: payment.status,
        createdAt: payment.createdAt,
        periodStart: payment.periodStart,
        periodEnd: payment.periodEnd,
        multibancoEntity: payment.multibancoEntity,
        multibancoReference: payment.multibancoReference,
        paidAt: payment.paidAt
      }))
    }

    const formattedSubscription = subscription.length > 0 ? {
      id: subscription[0].id,
      plan: {
        id: subscription[0].planId,
        name: subscription[0].plan_name,
        features: subscription[0].plan_features || [],
        monthlyPrice: Number(subscription[0].plan_monthly_price),
        yearlyPrice: Number(subscription[0].plan_yearly_price)
      },
      status: subscription[0].status,
      currentPeriodStart: subscription[0].currentPeriodStart,
      currentPeriodEnd: subscription[0].currentPeriodEnd,
      billingCycle: subscription[0].billingCycle,
      cancelAtPeriodEnd: subscription[0].cancelAtPeriodEnd,
      canceledAt: subscription[0].canceledAt,
      payments
    } : null

    return NextResponse.json({
      subscription: formattedSubscription
    })

  } catch (error) {
    console.error('Erro ao buscar subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
