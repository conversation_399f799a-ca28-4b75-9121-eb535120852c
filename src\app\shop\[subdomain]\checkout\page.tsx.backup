'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import ShopLayout from '@/components/shop/ShopLayout'
import { useToast, showSuccessToast, showErrorToast } from '@/components/ui/Toast'
import { CreditCard, Truck, MapPin, User, Mail, Phone, Package, CheckCircle } from 'lucide-react'

interface Product {
  id: string
  name: string
  price: number
  images: string[]
  stock: number
}

interface CartItem {
  product: Product
  quantity: number
}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  logoUrl?: string
}

function CheckoutContent() {
  const { addToast } = useToast()
  const params = useParams()
  const router = useRouter()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    deliveryMethod: 'delivery',
    paymentMethod: 'card'
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [paymentResult, setPaymentResult] = useState<{
    type: 'stripe' | 'multibanco' | null
    data: any
  }>({ type: null, data: null })
  const [shopConfig, setShopConfig] = useState<any>(null)

  const subdomain = params.subdomain as string

  useEffect(() => {
    if (subdomain) {
      loadCart()
      fetchShopConfig()
    }
  }, [subdomain])

  const fetchShopConfig = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShopConfig(data)
      }
    } catch (error) {
      console.error('Erro ao buscar configurações da loja:', error)
    }
  }

  const loadCart = async () => {
    try {
      const savedCart = localStorage.getItem(`cart_${subdomain}`)
      if (!savedCart || savedCart === '{}') {
        router.push(`/shop/${subdomain}/carrinho`)
        return
      }

      const cart = JSON.parse(savedCart)
      const productIds = Object.keys(cart)
      
      if (productIds.length > 0) {
        const response = await fetch(`/api/shop/${subdomain}/produtos`)
        if (response.ok) {
          const data = await response.json()
          const products = data.products || []
          
          const items: CartItem[] = productIds.map(productId => {
            const product = products.find((p: Product) => p.id === productId)
            return {
              product,
              quantity: cart[productId]
            }
          }).filter(item => item.product)

          setCartItems(items)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar carrinho:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getSubtotal = () => {
    return cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
  }

  const getShipping = () => {
    const subtotal = getSubtotal()
    const freeShippingThreshold = shopConfig?.freeShippingThreshold || 50
    const defaultShippingRate = shopConfig?.defaultShippingRate || 5.99
    return subtotal >= freeShippingThreshold ? 0 : defaultShippingRate
  }

  const getTotalPrice = () => {
    return getSubtotal() + getShipping()
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.email) newErrors.email = 'Email é obrigatório'
    if (!formData.name) newErrors.name = 'Nome é obrigatório'
    if (!formData.phone) newErrors.phone = 'Telefone é obrigatório'
    
    if (formData.deliveryMethod === 'delivery') {
      if (!formData.address) newErrors.address = 'Morada é obrigatória para entrega'
      if (!formData.city) newErrors.city = 'Cidade é obrigatória para entrega'
      if (!formData.postalCode) newErrors.postalCode = 'Código postal é obrigatório para entrega'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      showErrorToast(addToast, 'Erro no formulário', 'Por favor, preencha todos os campos obrigatórios')
      return
    }

    if (cartItems.length === 0) {
      showErrorToast(addToast, 'Carrinho vazio', 'Adicione produtos ao carrinho antes de finalizar')
      return
    }

    setIsProcessing(true)
    setErrors({})

    try {
      // Create order
      const orderResponse = await fetch(`/api/shop/${subdomain}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          items: cartItems.map(item => ({
            productId: item.product.id,
            quantity: item.quantity,
            price: item.product.price
          })),
          customer: {
            email: formData.email,
            name: formData.name,
            phone: formData.phone,
            address: formData.deliveryMethod === 'delivery' ? formData.address : null,
            city: formData.deliveryMethod === 'delivery' ? formData.city : null,
            postalCode: formData.deliveryMethod === 'delivery' ? formData.postalCode : null
          },
          deliveryMethod: formData.deliveryMethod,
          paymentMethod: formData.paymentMethod,
          total: getTotalPrice(),
          shopSubdomain: subdomain
        })
      })

      const orderResult = await orderResponse.json()

      if (!orderResponse.ok) {
        throw new Error(orderResult.error || 'Erro ao processar encomenda')
      }

      // Handle payment based on method
      if (formData.paymentMethod === 'multibanco') {
        // For now, redirect to success page with order info
        router.push(`/shop/${subdomain}/checkout/sucesso?order=${orderResult.orderNumber}&payment=multibanco`)
        return
      }

      if (formData.paymentMethod === 'card') {
        // For now, redirect to success page with order info
        router.push(`/shop/${subdomain}/checkout/sucesso?order=${orderResult.orderNumber}&payment=card`)
        return
      }

      // Clear cart
      localStorage.removeItem(`cart_${subdomain}`)

    } catch (error) {
      console.error('Erro ao processar checkout:', error)
      showErrorToast(addToast, 'Erro no checkout', error instanceof Error ? error.message : 'Erro ao processar pedido')
      setErrors({
        general: error instanceof Error ? error.message : 'Erro ao processar pedido. Tente novamente.'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Carrinho Vazio</h2>
          <p className="text-gray-600 mb-6">Adicione produtos ao carrinho para continuar</p>
          <Link 
            href={`/shop/${subdomain}`}
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          >
            Continuar Compras
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Finalizar Compra</h1>
          </div>

          <form onSubmit={handleSubmit} className="p-6">
            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{errors.general}</p>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Form */}
              <div className="space-y-6">
                {/* Customer Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <User className="w-5 h-5 mr-2" />
                    Informações do Cliente
                  </h3>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                          errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nome Completo *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                          errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="João Silva"
                      />
                      {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Telefone *
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                          errors.phone ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="+351 912 345 678"
                      />
                      {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone}</p>}
                    </div>
                  </div>
                </div>

                {/* Delivery Method */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Truck className="w-5 h-5 mr-2" />
                    Método de Entrega
                  </h3>

                  <div className="space-y-3">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="deliveryMethod"
                        value="delivery"
                        checked={formData.deliveryMethod === 'delivery'}
                        onChange={handleInputChange}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">Entrega ao Domicílio</div>
                        <div className="text-sm text-gray-500">Entrega no endereço indicado</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="deliveryMethod"
                        value="pickup"
                        checked={formData.deliveryMethod === 'pickup'}
                        onChange={handleInputChange}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">Levantamento na Loja</div>
                        <div className="text-sm text-gray-500">Levantar na loja física</div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Delivery Address */}
                {formData.deliveryMethod === 'delivery' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <MapPin className="w-5 h-5 mr-2" />
                      Endereço de Entrega
                    </h3>

                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Morada *
                        </label>
                        <input
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                            errors.address ? 'border-red-300' : 'border-gray-300'
                          }`}
                          placeholder="Rua das Flores, 123"
                        />
                        {errors.address && <p className="text-red-600 text-sm mt-1">{errors.address}</p>}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Cidade *
                          </label>
                          <input
                            type="text"
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                              errors.city ? 'border-red-300' : 'border-gray-300'
                            }`}
                            placeholder="Lisboa"
                          />
                          {errors.city && <p className="text-red-600 text-sm mt-1">{errors.city}</p>}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Código Postal *
                          </label>
                          <input
                            type="text"
                            name="postalCode"
                            value={formData.postalCode}
                            onChange={handleInputChange}
                            className={`block w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 ${
                              errors.postalCode ? 'border-red-300' : 'border-gray-300'
                            }`}
                            placeholder="1000-001"
                          />
                          {errors.postalCode && <p className="text-red-600 text-sm mt-1">{errors.postalCode}</p>}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Payment Method */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <CreditCard className="w-5 h-5 mr-2" />
                    Método de Pagamento
                  </h3>

                  <div className="space-y-3">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="card"
                        checked={formData.paymentMethod === 'card'}
                        onChange={handleInputChange}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">Cartão de Crédito/Débito</div>
                        <div className="text-sm text-gray-500">Visa, Mastercard, American Express</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="multibanco"
                        checked={formData.paymentMethod === 'multibanco'}
                        onChange={handleInputChange}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">Multibanco</div>
                        <div className="text-sm text-gray-500">Referência para pagamento</div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Right Column - Order Summary */}
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo da Encomenda</h3>

                  <div className="space-y-4">
                    {cartItems.map((item) => (
                      <div key={item.product.id} className="flex items-center space-x-3">
                        {item.product.images && item.product.images.length > 0 && (
                          <Image
                            src={item.product.images[0]}
                            alt={item.product.name}
                            width={60}
                            height={60}
                            className="rounded-lg object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.product.name}</h4>
                          <p className="text-sm text-gray-500">Quantidade: {item.quantity}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">€{(item.product.price * item.quantity).toFixed(2)}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="border-t border-gray-200 mt-4 pt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal:</span>
                      <span>€{getSubtotal().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Envio:</span>
                      <span>{getShipping() === 0 ? 'Grátis' : `€${getShipping().toFixed(2)}`}</span>
                    </div>
                    <div className="flex justify-between text-lg font-semibold border-t border-gray-200 pt-2">
                      <span>Total:</span>
                      <span>€{getTotalPrice().toFixed(2)}</span>
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isProcessing}
                    className="w-full mt-6 bg-gradient-to-r from-gray-900 to-black text-white py-3 px-4 rounded-lg hover:from-black hover:to-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                  >
                    {isProcessing ? 'A processar...' : 'Finalizar Compra'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const subdomain = params.subdomain as string

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}`)
      if (response.ok) {
        const data = await response.json()
        setShop(data.shop)
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <Link
            href="/"
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          >
            Voltar ao início
          </Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      logoUrl={shop.logoUrl}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <CheckoutContent />
    </ShopLayout>
  )
}
