import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { updateCustomPage, 
  deleteCustomPage, 
  UpdateCustomPageData } from '@/lib/customPages'

// PUT - Atualizar página customizada
export async function PUT(
  request: NextRequest,
  { params
}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { slug, title, content, metaDescription, isActive, showInHeader, showInFooter, headerOrder, footerOrder } = body

    // Validar slug se fornecido
    if (slug) {
      const slugRegex = /^[a-z0-9-]+$/
      if (!slugRegex.test(slug)) {
        return NextResponse.json(
          { message: "Slug deve conter apenas letras minúsculas, números e hífens" },
          { status: 400 }
        )
      }
    }

    const updateData: UpdateCustomPageData = {
      id: params.id,
      slug,
      title,
      content,
      metaDescription,
      isActive,
      showInHeader,
      showInFooter,
      headerOrder, footerOrder }

    const updatedPage = await updateCustomPage(session.user.id, updateData)

    return NextResponse.json({
      message: "Página atualizada com sucesso",
      page: updatedPage
})

  } catch (error) {
    console.error("Erro ao atualizar página customizada:", 'error')
    
    if (error instanceof Error) {
      if (error.message === "Página não encontrada") {
        return NextResponse.json(
          { message: error.message },
          { status: 404 }
        )
      }
      if (error.message === "Já existe uma página com este slug") {
        return NextResponse.json(
          { message: error.message },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// DELETE - Deletar página customizada
export async function DELETE(
  request: NextRequest,
  { params
}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    await deleteCustomPage(session.user.id, params.id)

    return NextResponse.json({
      message: "Página deletada com sucesso"
    })

  } catch (error) {
    console.error("Erro ao deletar página customizada:", 'error')
    
    if (error instanceof Error && error.message === "Página não encontrada") {
      return NextResponse.json(
        { message: error.message },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
