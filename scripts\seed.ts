import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 A semear base de dados...')

  // Criar superadmin (não pode ser apagado)
  const superAdminPassword = await bcrypt.hash('Teste123123_', 12)
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      // Sempre garantir que mantém as permissões corretas
      role: 'ADMIN',
      isVerified: true,
      isSuperAdmin: true
    },
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: superAdminPassword,
      role: 'ADMIN',
      isVerified: true,
      isSuperAdmin: true,
      profile: {
        create: {
          phone: '+351 912 000 000',
          customerName: '<PERSON>'
        }
      }
    }
  })

  // Criar admin de teste (pode ser apagado)
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin Teste',
      password: adminPassword,
      role: 'ADMIN',
      isVerified: true,
      profile: {
        create: {
          phone: '+*********** 678',
        }
      }
    }
  })

  // Criar utilizador lojista
  const lojistaPassword = await bcrypt.hash('lojista123', 12)
  const lojista = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Lojista Teste',
      password: lojistaPassword,
      role: 'REPAIR_SHOP',
      isVerified: true,
      isFeatured: true,
      profile: {
        create: {
          phone: '+*********** 679',
          companyName: 'TechRepair Lda',
          companyNif: '*********',
          description: 'Especialistas em reparação de dispositivos móveis.',
          street: 'Rua da Tecnologia, 123',
          city: 'Lisboa',
          postalCode: '1000-001',
          country: 'Portugal',
          averageRepairTime: 60,
          serviceRadius: 25,
          workingCategories: ['Smartphone', 'Tablet'],
          workingBrands: ['Apple', 'Samsung'],
          businessHours: {
            monday: { open: '09:00', close: '18:00' },
            tuesday: { open: '09:00', close: '18:00' },
            wednesday: { open: '09:00', close: '18:00' },
            thursday: { open: '09:00', close: '18:00' },
            friday: { open: '09:00', close: '18:00' },
            saturday: { open: '09:00', close: '13:00' },
            sunday: { closed: true }
          }
        }
      }
    }
  })

  // Criar utilizador cliente
  const clientePassword = await bcrypt.hash('cliente123', 12)
  const cliente = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Cliente Teste',
      password: clientePassword,
      role: 'CUSTOMER',
      profile: {
        create: {
          phone: '+*********** 680',
        }
      }
    }
  })

  // Criar utilizador estafeta
  const estafetaPassword = await bcrypt.hash('estafeta123', 12)
  const estafeta = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Estafeta Teste',
      password: estafetaPassword,
      role: 'COURIER',
      profile: {
        create: {
          phone: '+*********** 681',
        }
      }
    }
  })

  // Criar categorias
  const smartphone = await prisma.category.upsert({
    where: { name: 'Smartphone' },
    update: {},
    create: {
      name: 'Smartphone',
      description: 'Telemóveis e smartphones',
      isActive: true
    }
  })

  const tablet = await prisma.category.upsert({
    where: { name: 'Tablet' },
    update: {},
    create: {
      name: 'Tablet',
      description: 'Tablets e iPads',
      isActive: true
    }
  })

  const laptop = await prisma.category.upsert({
    where: { name: 'Laptop' },
    update: {},
    create: {
      name: 'Laptop',
      description: 'Portáteis e laptops',
      isActive: true
    }
  })

  // Criar algumas marcas
  const apple = await prisma.brand.upsert({
    where: { name: 'Apple' },
    update: {},
    create: {
      name: 'Apple',
      logo: '/brands/apple.png',
      isActive: true
    }
  })

  const samsung = await prisma.brand.upsert({
    where: { name: 'Samsung' },
    update: {},
    create: {
      name: 'Samsung',
      logo: '/brands/samsung.png',
      isActive: true
    }
  })

  const xiaomi = await prisma.brand.upsert({
    where: { name: 'Xiaomi' },
    update: {},
    create: {
      name: 'Xiaomi',
      logo: '/brands/xiaomi.png',
      isActive: true
    }
  })

  // Criar alguns modelos
  await prisma.deviceModel.upsert({
    where: { brandId_name: { brandId: apple.id, name: 'iPhone 15 Pro' } },
    update: {},
    create: {
      brandId: apple.id,
      categoryId: smartphone.id,
      name: 'iPhone 15 Pro',
      releaseYear: 2023,
      specifications: {
        screen: '6.1"',
        storage: ['128GB', '256GB', '512GB', '1TB'],
        colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium']
      }
    }
  })

  await prisma.deviceModel.upsert({
    where: { brandId_name: { brandId: samsung.id, name: 'Galaxy S24 Ultra' } },
    update: {},
    create: {
      brandId: samsung.id,
      categoryId: smartphone.id,
      name: 'Galaxy S24 Ultra',
      releaseYear: 2024,
      specifications: {
        screen: '6.8"',
        storage: ['256GB', '512GB', '1TB'],
        colors: ['Titanium Black', 'Titanium Gray', 'Titanium Violet', 'Titanium Yellow']
      }
    }
  })

  await prisma.deviceModel.upsert({
    where: { brandId_name: { brandId: apple.id, name: 'iPad Pro' } },
    update: {},
    create: {
      brandId: apple.id,
      categoryId: tablet.id,
      name: 'iPad Pro',
      releaseYear: 2024,
      specifications: {
        screen: '12.9"',
        storage: ['128GB', '256GB', '512GB', '1TB', '2TB'],
        colors: ['Space Gray', 'Silver']
      }
    }
  })

  // Criar tipos de problemas
  const problemTypes = [
    { name: 'Ecrã', description: 'Problemas com o ecrã (rachado, não funciona, manchas)', icon: '📱' },
    { name: 'Bateria', description: 'Problemas de bateria (não carrega, descarrega rápido)', icon: '🔋' },
    { name: 'Câmara', description: 'Problemas com a câmara (não funciona, imagem desfocada)', icon: '📷' },
    { name: 'Áudio', description: 'Problemas de som (sem som, microfone não funciona)', icon: '🔊' },
    { name: 'Botões', description: 'Problemas com botões (não respondem, presos)', icon: '🔘' },
    { name: 'Conectividade', description: 'Problemas de rede (WiFi, Bluetooth, dados móveis)', icon: '📶' },
    { name: 'Software', description: 'Problemas de software (lento, aplicações crasham)', icon: '💻' },
    { name: 'Água', description: 'Danos por água ou líquidos', icon: '💧' },
    { name: 'Outro', description: 'Outros problemas não listados', icon: '🔧' }
  ]

  for (const problemType of problemTypes) {
    await prisma.problemType.upsert({
      where: { name: problemType.name },
      update: {},
      create: problemType
    })
  }

  // Criar preços base para combinações categoria + tipo de problema
  const categories = await prisma.category.findMany()
  const problemTypesFromDB = await prisma.problemType.findMany()

  const basePricesData = [
    // Smartphone
    { categoryName: 'Smartphone', problemName: 'Ecrã', basePrice: 89.99, estimatedTime: 60 },
    { categoryName: 'Smartphone', problemName: 'Bateria', basePrice: 49.99, estimatedTime: 45 },
    { categoryName: 'Smartphone', problemName: 'Câmara', basePrice: 69.99, estimatedTime: 90 },
    { categoryName: 'Smartphone', problemName: 'Áudio', basePrice: 39.99, estimatedTime: 30 },
    { categoryName: 'Smartphone', problemName: 'Botões', basePrice: 29.99, estimatedTime: 30 },
    { categoryName: 'Smartphone', problemName: 'Conectividade', basePrice: 59.99, estimatedTime: 120 },
    { categoryName: 'Smartphone', problemName: 'Software', basePrice: 19.99, estimatedTime: 60 },
    { categoryName: 'Smartphone', problemName: 'Água', basePrice: 99.99, estimatedTime: 180 },

    // Tablet
    { categoryName: 'Tablet', problemName: 'Ecrã', basePrice: 129.99, estimatedTime: 90 },
    { categoryName: 'Tablet', problemName: 'Bateria', basePrice: 79.99, estimatedTime: 60 },
    { categoryName: 'Tablet', problemName: 'Câmara', basePrice: 89.99, estimatedTime: 90 },
    { categoryName: 'Tablet', problemName: 'Áudio', basePrice: 49.99, estimatedTime: 45 },
    { categoryName: 'Tablet', problemName: 'Botões', basePrice: 39.99, estimatedTime: 30 },
    { categoryName: 'Tablet', problemName: 'Conectividade', basePrice: 69.99, estimatedTime: 120 },
    { categoryName: 'Tablet', problemName: 'Software', basePrice: 29.99, estimatedTime: 60 },

    // Laptop
    { categoryName: 'Laptop', problemName: 'Ecrã', basePrice: 199.99, estimatedTime: 120 },
    { categoryName: 'Laptop', problemName: 'Bateria', basePrice: 119.99, estimatedTime: 90 },
    { categoryName: 'Laptop', problemName: 'Áudio', basePrice: 69.99, estimatedTime: 60 },
    { categoryName: 'Laptop', problemName: 'Botões', basePrice: 89.99, estimatedTime: 90 },
    { categoryName: 'Laptop', problemName: 'Conectividade', basePrice: 79.99, estimatedTime: 120 },
    { categoryName: 'Laptop', problemName: 'Software', basePrice: 49.99, estimatedTime: 90 }
  ]

  for (const priceData of basePricesData) {
    const category = categories.find(c => c.name === priceData.categoryName)
    const problemType = problemTypesFromDB.find(p => p.name === priceData.problemName)

    if (category && problemType) {
      await prisma.basePrice.upsert({
        where: {
          categoryId_problemTypeId: {
            categoryId: category.id,
            problemTypeId: problemType.id
          }
        },
        update: {},
        create: {
          categoryId: category.id,
          problemTypeId: problemType.id,
          basePrice: priceData.basePrice,
          estimatedTime: priceData.estimatedTime
        }
      })
    }
  }

  console.log('✅ Base de dados semeada com sucesso!')
  // Criar cupões de teste
  await prisma.coupon.upsert({
    where: { code: 'WELCOME10' },
    update: {},
    create: {
      sellerId: lojista.id,
      code: 'WELCOME10',
      name: 'Desconto de Boas-vindas',
      description: 'Desconto de 10% para novos clientes',
      type: 'PERCENTAGE',
      value: 10,
      minOrderValue: 50,
      maxDiscountValue: 100,
      usageLimit: 100,
      usageCount: 0,
      isActive: true,
      startsAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      allowedCountries: ['PT', 'ES']
    }
  })

  await prisma.coupon.upsert({
    where: { code: 'SAVE20' },
    update: {},
    create: {
      sellerId: lojista.id,
      code: 'SAVE20',
      name: 'Desconto Fixo 20€',
      description: 'Desconto fixo de 20€ em compras acima de 200€',
      type: 'FIXED_AMOUNT',
      value: 20,
      minOrderValue: 200,
      usageLimit: 50,
      usageCount: 0,
      isActive: true,
      startsAt: new Date(),
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      allowedCountries: ['PT']
    }
  })

  console.log('✅ Base de dados semeada com sucesso!')
  console.log('👤 Utilizadores criados:')
  console.log('   🔒 SUPERADMIN: <EMAIL> / Teste123123_')
  console.log('   Admin Teste: <EMAIL> / admin123')
  console.log('   Lojista: <EMAIL> / lojista123')
  console.log('   Cliente: <EMAIL> / cliente123')
  console.log('   Estafeta: <EMAIL> / estafeta123')
  console.log('🎫 Cupões criados: WELCOME10, SAVE20')
  console.log('')
  console.log('⚠️  IMPORTANTE: <NAME_EMAIL> NÃO PODE SER APAGADO!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
