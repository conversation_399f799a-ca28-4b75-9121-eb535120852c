import { useState, useEffect } from 'react'

export function useShopCart(subdomain: string) {
  const [cart, setCart] = useState<{[key: string]: number}>({})

  useEffect(() => {
    loadCart()
  }, [subdomain])

  const loadCart = () => {
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem(`cart_${subdomain}`)
      if (savedCart) {
        setCart(JSON.parse(savedCart))
      }
    }
  }

  const addToCart = (productId: string, quantity: number = 1) => {
    const newCart = { ...cart }
    newCart[productId] = (newCart[productId] || 0) + quantity
    setCart(newCart)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(`cart_${subdomain}`, JSON.stringify(newCart))
    }
  }

  const removeFromCart = (productId: string) => {
    const newCart = { ...cart }
    delete newCart[productId]
    setCart(newCart)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(`cart_${subdomain}`, JSON.stringify(newCart))
    }
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId)
      return
    }
    
    const newCart = { ...cart }
    newCart[productId] = quantity
    setCart(newCart)
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(`cart_${subdomain}`, JSON.stringify(newCart))
    }
  }

  const getCartItemCount = () => {
    return Object.values(cart).reduce((sum, 'count') => sum + count, 0)
  }

  const clearCart = () => {
    setCart({})
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`cart_${subdomain}`)
    }
  }

  return {
    cart,
    addToCart,
    removeFromCart,
    updateQuantity,
    getCartItemCount,
    clearCart, loadCart }
}
