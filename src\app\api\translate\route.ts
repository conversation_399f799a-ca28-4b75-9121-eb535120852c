import { NextRequest, NextResponse } from 'next/server'
import AutoTranslate from '@/components/ui/AutoTranslate'

export async function POST(request: NextRequest) {
  try {
    const { text, targetLang, sourceLang = 'PT' } = await request.json()

    if (!text || !targetLang) {
      return NextResponse.json(
        { message: "Texto e idioma de destino são obrigatórios" },
        { status: 400 }
      )
    }

    const apiKey = process.env.DEEPL_API_KEY
    if (!apiKey) {
      return NextResponse.json(
        { message: "Chave API DeepL não configurada" },
        { status: 500 }
      )
    }

    // Mapear códigos de idioma para DeepL
    const langMap: { [key: string]: string } = {
      'pt': 'PT',
      'en': 'EN-US',
      'es': 'ES',
      'fr': 'FR',
      'de': 'DE',
      'it': 'IT'
    }

    const deeplTargetLang = langMap[targetLang.toLowerCase()]
    if (!deeplTargetLang) {
      return NextResponse.json(
        { message: "Idioma não suportado" },
        { status: 400 }
      )
    }

    // Se o idioma de origem e destino são iguais, retornar o texto original
    if (sourceLang.toLowerCase() === targetLang.toLowerCase()) {
      return NextResponse.json({ translatedText: text })
    }

    const response = await fetch('https://api-free.deepl.com/v2/translate', {
      method: 'POST',
      headers: {
        'Authorization': `DeepL-Auth-Key ${apiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        text: text,
        target_lang: deeplTargetLang,
        source_lang: langMap[sourceLang.toLowerCase()] || 'PT'
      })
    })

    if (!response.ok) {
      console.error('Erro na API DeepL:', response.status, await response.text())
      return NextResponse.json(
        { message: "Erro na tradução" },
        { status: 500 }
      )
    }

    const data = await response.json()
    const translatedText = data.translations?.[0]?.text || text

    return NextResponse.json({
      translatedText,
      sourceLang,
      targetLang,
      usage: data.usage || null,
      detectedSourceLang: data.translations?.[0]?.detected_source_language || sourceLang
    })

  } catch (error) {
    console.error("Erro na tradução:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
