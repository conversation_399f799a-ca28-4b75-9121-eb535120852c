import { NextRequest } from 'next/server'
import { POST } from '../login/route'
import bcrypt from 'bcrypt'

// Mock Prisma
const mockPrisma = {
  user: {
    findFirst: jest.fn(),
  },
  profile: {
    findFirst: jest.fn(),
  },
}

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma),
}))

jest.mock('bcrypt')
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>

describe('/api/auth/login', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const mockRequest = (body: any) => ({
    json: () => Promise.resolve(body),
  }) as NextRequest

  it('should login successfully with valid credentials', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
      shopSubdomain: 'test-shop'
    }

    const mockUser = {
      id: 'user1',
      email: '<EMAIL>',
      name: '<PERSON>',
      password: 'hashedPassword',
      role: 'customer',
      profile: {
        customSubdomain: 'test-shop'
      }
    }

    mockPrisma.user.findFirst.mockResolvedValue(mockUser)
    mockedBcrypt.compare.mockResolvedValue(true)

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(response.status).toBe(200)
    expect(result.success).toBe(true)
    expect(result.user.email).toBe('<EMAIL>')
    expect(result.user.password).toBeUndefined() // Password should not be returned
  })

  it('should return error with invalid email', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    }

    mockPrisma.user.findFirst.mockResolvedValue(null)

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(response.status).toBe(401)
    expect(result.error).toBe('Credenciais inválidas')
  })

  it('should return error with invalid password', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'wrongpassword'
    }

    const mockUser = {
      id: 'user1',
      email: '<EMAIL>',
      password: 'hashedPassword',
      role: 'customer'
    }

    mockPrisma.user.findFirst.mockResolvedValue(mockUser)
    mockedBcrypt.compare.mockResolvedValue(false)

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(response.status).toBe(401)
    expect(result.error).toBe('Credenciais inválidas')
  })

  it('should return error when required fields missing', async () => {
    const loginData = {
      email: '<EMAIL>'
      // Missing password
    }

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(response.status).toBe(400)
    expect(result.error).toBe('Email e password são obrigatórios')
  })

  it('should handle shop subdomain correctly', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
      shopSubdomain: 'test-shop'
    }

    const mockUser = {
      id: 'user1',
      email: '<EMAIL>',
      name: 'João Silva',
      password: 'hashedPassword',
      role: 'customer'
    }

    mockPrisma.user.findFirst.mockResolvedValue(mockUser)
    mockedBcrypt.compare.mockResolvedValue(true)

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(result.user.shopSubdomain).toBe('test-shop')
  })

  it('should handle database errors gracefully', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    }

    mockPrisma.user.findFirst.mockRejectedValue(new Error('Database error'))

    const response = await POST(mockRequest(loginData))
    const result = await response.json()

    expect(response.status).toBe(500)
    expect(result.error).toBe('Erro interno do servidor')
  })
})
