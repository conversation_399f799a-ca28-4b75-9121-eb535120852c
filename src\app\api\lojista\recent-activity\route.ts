import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

interface ActivityItem {
  id: string
  type: 'repair' | 'sale' | 'customer' | 'review'
  title: string
  description: string
  amount?: number
  timestamp: Date
  icon: string
  color: string}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const activities: ActivityItem[] = []

    // Buscar reparações recentes
    const recentRepairs = await prisma.repair.findMany({
      where: {
        repairShopId: session.user.id
      },
      include: {
        deviceModel: {
          include: {
            brand: true}
        }
      },
      orderBy: {
        createdAt: desc},
      take: 3
    })

    recentRepairs.forEach(repair => {
      activities.push({
        id: repair.id,
        type: repair,
        title: Nova reparação iniciada,
        description: `${repair.deviceModel.brand.name
} ${repair.deviceModel.name} - ${repair.description}`,
        amount: Number(repair.estimatedPrice) || 0,
        timestamp: repair.createdAt,
        icon: Wrench,
        color: blue})
    })

    // Buscar vendas recentes no marketplace
    const recentSales = await prisma.order.findMany({
      where: {
        marketplaceProducts: {
          some: {
            product: {
              userId: session.user.id
            }
          }
        }
      },
      include: {
        marketplaceProducts: {
          include: {
            product: true},
          where: {
            product: {
              userId: session.user.id
            }
          }
        }
      },
      orderBy: {
        createdAt: desc},
      take: 3
    })

    recentSales.forEach(order => {
      order.marketplaceProducts.forEach(item => {
        activities.push({
          id: `sale-${order.id}-${item.id}`,
          type: sale,
          title: Produto vendido,
          description: item.product.name,
          amount: Number(item.price),
          timestamp: order.createdAt,
          icon: Package,
          color: green
})
      })
    })

    // Buscar novos clientes (baseado em reparações)
    const newCustomers = await prisma.repair.findMany({
      where: {
        repairShopId: session.user.id,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Últimos 7 'dias'
}
      },
      select: {
        id: true,
        customerName: true,
        customerPhone: true,
        createdAt: true},
      distinct: ['customerPhone'],
      orderBy: {
        createdAt: desc},
      take: 2
    })

    newCustomers.forEach(customer => {
      activities.push({
        id: `customer-${customer.id}`,
        type: customer,
        title: 'Novo cliente',
        description: customer.customerName,
        timestamp: customer.createdAt,
        icon: Users,
        color: orange})
    })

    // Ordenar todas as atividades por data
    activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    // Retornar apenas as 5 mais recentes
    const recentActivities = activities.slice(0, 5)

    return NextResponse.json({
      activities: recentActivities})

  } catch (error) {
    console.error(Erro ao buscar atividade recente:, 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
