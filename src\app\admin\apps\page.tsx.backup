'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Edit, Eye, Package, Users, TrendingUp, Euro, Save, X } from 'lucide-react'
import { SYSTEM_APPS } from '@/lib/app-definitions'

interface AppConfig {
  appId: string
  name: string
  description: string
  monthlyPrice: number
  isActive: boolean
  isPopular: boolean
  features: string[]
}

interface AppStats {
  totalInstalls: number
  paidInstalls: number
  trialInstalls: number
}

export default function AdminAppsPage() {
  const [apps, setApps] = useState<AppConfig[]>([])
  const [stats, setStats] = useState<Record<string, AppStats>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [editingApp, setEditingApp] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<AppConfig | null>(null)

  useEffect(() => {
    loadApps()
  }, [])

  const loadApps = async () => {
    try {
      setIsLoading(true)

      // Carregar configurações das apps (se existirem)
      const configResponse = await fetch('/api/admin/apps/config')
      const configData = configResponse.ok ? await configResponse.json() : { configs: [] }

      // Carregar estatísticas
      const statsResponse = await fetch('/api/admin/apps/stats')
      const statsData = statsResponse.ok ? await statsResponse.json() : { stats: {} }

      // Combinar apps do sistema com configurações personalizadas
      const appsWithConfig = SYSTEM_APPS.map(systemApp => {
        const config = configData.configs.find((c: any) => c.appId === systemApp.appId)
        return {
          appId: systemApp.appId,
          name: config?.name || systemApp.name,
          description: config?.description || systemApp.description,
          monthlyPrice: config?.monthlyPrice ?? systemApp.monthlyPrice,
          isActive: config?.isActive ?? systemApp.isActive,
          isPopular: config?.isPopular ?? systemApp.isPopular,
          features: config?.features || systemApp.features
        }
      })

      setApps(appsWithConfig)
      setStats(statsData.stats || {})
    } catch (error) {
      console.error('Erro ao carregar apps:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const startEdit = (app: AppConfig) => {
    setEditingApp(app.appId)
    setEditForm({ ...app })
  }

  const cancelEdit = () => {
    setEditingApp(null)
    setEditForm(null)
  }

  const saveEdit = async () => {
    if (!editForm) return

    try {
      const response = await fetch('/api/admin/apps/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editForm)
      })

      if (response.ok) {
        loadApps()
        cancelEdit()
        alert('App atualizada com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar app')
      }
    } catch (error) {
      console.error('Erro ao atualizar app:', error)
      alert('Erro ao atualizar app')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/admin" className="font-bold text-blue-600">
                Revify Admin
              </Link>
              <span className="text-gray-400">|</span>
              <h1 className="text-xl font-semibold text-gray-900">Gestão de Apps</h1>
            </div>
            <div className="text-sm text-gray-600">
              Gerir preços e conteúdo das apps do sistema
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Apps</p>
                <p className="text-2xl font-bold text-gray-900">{apps.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Euro className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Apps Pagas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {apps.filter(app => app.monthlyPrice > 0).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Instalações</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.values(stats).reduce((sum, stat) => sum + (stat?.totalInstalls || 0), 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Apps Ativas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {apps.filter(app => app.isActive).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Apps Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Lista de Apps ({apps.length})
            </h3>
          </div>

          {apps.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma app encontrada</h3>
              <p className="mt-1 text-sm text-gray-500">
                Comece criando a primeira app.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Descrição
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Preço
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Instalações
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {apps.map((app) => (
                    <tr key={app.appId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          {editingApp === app.appId ? (
                            <input
                              type="text"
                              value={editForm?.name || ''}
                              onChange={(e) => setEditForm(prev => prev ? {...prev, name: e.target.value} : null)}
                              className="text-sm font-medium text-gray-900 border rounded px-2 py-1 w-full"
                            />
                          ) : (
                            <div className="text-sm font-medium text-gray-900">{app.name}</div>
                          )}
                          <div className="text-sm text-gray-500">{app.appId}</div>
                          {app.isPopular && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                              Popular
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingApp === app.appId ? (
                          <textarea
                            value={editForm?.description || ''}
                            onChange={(e) => setEditForm(prev => prev ? {...prev, description: e.target.value} : null)}
                            className="text-sm text-gray-500 border rounded px-2 py-1 w-full h-20 resize-none"
                          />
                        ) : (
                          <div className="text-sm text-gray-500 max-w-xs truncate" title={app.description}>
                            {app.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingApp === app.appId ? (
                          <input
                            type="number"
                            step="0.01"
                            value={editForm?.monthlyPrice || 0}
                            onChange={(e) => setEditForm(prev => prev ? {...prev, monthlyPrice: parseFloat(e.target.value) || 0} : null)}
                            className="text-sm font-medium text-gray-900 border rounded px-2 py-1 w-20"
                          />
                        ) : (
                          app.monthlyPrice > 0 ? (
                            <div className="text-sm font-medium text-gray-900">
                              €{app.monthlyPrice.toFixed(2)}/mês
                            </div>
                          ) : (
                            <span className="text-sm text-green-600 font-medium">Grátis</span>
                          )
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          Total: {stats[app.appId]?.totalInstalls || 0}
                        </div>
                        <div className="text-xs text-gray-500">
                          Pagas: {stats[app.appId]?.paidInstalls || 0} | Trial: {stats[app.appId]?.trialInstalls || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingApp === app.appId ? (
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={editForm?.isActive || false}
                                onChange={(e) => setEditForm(prev => prev ? {...prev, isActive: e.target.checked} : null)}
                                className="mr-2"
                              />
                              <span className="text-xs">Ativa</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={editForm?.isPopular || false}
                                onChange={(e) => setEditForm(prev => prev ? {...prev, isPopular: e.target.checked} : null)}
                                className="mr-2"
                              />
                              <span className="text-xs">Popular</span>
                            </label>
                          </div>
                        ) : (
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            app.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {app.isActive ? 'Ativa' : 'Inativa'}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {editingApp === app.appId ? (
                            <>
                              <button
                                onClick={saveEdit}
                                className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                                title="Guardar"
                              >
                                <Save className="w-4 h-4" />
                              </button>
                              <button
                                onClick={cancelEdit}
                                className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                                title="Cancelar"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => startEdit(app)}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                              title="Editar"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

    </div>
  )
}
