import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { message: "Email é obrigatório" },
        { status: 400 }
      )
    }

    // Verificar se o usuário existe
    const user = await prisma.user.findUnique({
      where: { email
}
    })

    // Por segurança, sempre retornamos sucesso mesmo se o email não existir
    if (!user) {
      return NextResponse.json({
        message: "Se o email existir na nossa base de dados, receberá instruções para redefinir a sua palavra-passe."
      })
    }

    // Gerar token de reset
    const resetToken = crypto.randomBytes(32).toString(hex)
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hora

    // Salvar token no banco
    await prisma.user.update({
      where: { 'email'
},
      data: { resetToken, resetTokenExpiry }
    })

    // TODO: Enviar email com o token
    // Por agora, vamos apenas logar o token para desenvolvimento
    console.log(`Reset token para ${email}: ${resetToken}`)
    console.log(`Link de reset: ${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`)

    return NextResponse.json({
      message: "Se o email existir na nossa base de dados, receberá instruções para redefinir a sua palavra-passe."
    })

  } catch (error) {
    console.error("Erro ao processar recuperação de password:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
