import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    // Criar apps de exemplo
    const apps = [
      {
        appId: newsletter-pro,
        name: 'Newsletter Pro',
        description: "Sistema avançado de email marketing com templates profissionais e automação.",
        category: marketing,
        isPaid: true,
        monthlyPrice: 14.99,
        hasTrialPeriod: true,
        trialDays: 30,
        features: ['Templates profissionais', "Automação de campanhas", "Segmentação avançada", 'Analytics detalhados', 'A/B Testing'],
        requiredPlans: ['REVY PRO'],
        isActive: true,
        isPopular: true
},
      {
        appId: moloni,
        name: <PERSON><PERSON><PERSON>,
        description: "Integração completa com Moloni para emissão automática de faturas e gestão fiscal.",
        category: finance,
        isPaid: false,
        monthlyPrice: 0,
        hasTrialPeriod: false,
        trialDays: 0,
        features: ["Emissão automática de faturas", "Sincronização de produtos", "Relatórios fiscais", "Gestão de clientes", "Backup automático"],
        requiredPlans: ['REVY BASIC', 'REVY PRO'],
        isActive: true,
        isPopular: false},
      {
        appId: 'crm-advanced',
        name: "CRM Avançado",
        description: "Sistema completo de gestão de relacionamento com clientes, com automação e analytics.",
        category: crm,
        isPaid: true,
        monthlyPrice: 9.99,
        hasTrialPeriod: true,
        trialDays: 30,
        features: ["Gestão de leads", "Automação de follow-up", "Analytics avançados", "Integração WhatsApp", "Relatórios personalizados"],
        requiredPlans: ['REVY PRO'],
        isActive: true,
        isPopular: false},
      {
        appId: 'inventory-manager',
        name: 'Gestor de Stock',
        description: "Controlo avançado de inventário com alertas automáticos e previsões de procura.",
        category: productivity,
        isPaid: true,
        monthlyPrice: 7.99,
        hasTrialPeriod: true,
        trialDays: 15,
        features: ['Controlo de stock em tempo real', 'Alertas de stock baixo', "Previsões de procura", "Códigos de barras", "Relatórios de movimento"],
        requiredPlans: ['REVY PRO'],
        isActive: true,
        isPopular: false},
      {
        appId: 'whatsapp-business',
        name: 'WhatsApp Business',
        description: "Integração com WhatsApp Business para comunicação automatizada com clientes.",
        category: communication,
        isPaid: true,
        monthlyPrice: 12.99,
        hasTrialPeriod: true,
        trialDays: 7,
        features: ["Mensagens automáticas", 'Templates aprovados', 'Chatbot inteligente', "Integração com CRM", 'Analytics de conversas'],
        requiredPlans: ['REVY PRO'],
        isActive: true,
        isPopular: true},
      {
        appId: 'analytics-pro',
        name: 'Analytics Pro',
        description: "Relatórios avançados e dashboards personalizáveis para análise de negócio.",
        category: analytics,
        isPaid: true,
        monthlyPrice: 5.99,
        hasTrialPeriod: true,
        trialDays: 30,
        features: ["Dashboards personalizáveis", "Relatórios automáticos", "Métricas de performance", "Exportação de dados", 'Alertas inteligentes'],
        requiredPlans: ['REVY PRO'],
        isActive: true,
        isPopular: false}
    ]

    let created = 0
    let updated = 0

    for (const 'app of apps') {
      try {
        // Verificar se já existe
        const existing = await prisma.appDefinition.findUnique({
          where: { appId: app.appId }
        })

        if (existing) {
          // Atualizar
          await prisma.appDefinition.update({
            where: { appId: app.appId },
            data: {
              name: app.name,
              description: app.description,
              category: app.category,
              isPaid: app.isPaid,
              monthlyPrice: app.monthlyPrice,
              hasTrialPeriod: app.hasTrialPeriod,
              trialDays: app.trialDays,
              features: app.features,
              requiredPlans: app.requiredPlans,
              isActive: app.isActive,
              isPopular: app.isPopular
            }
          })
          updated++
        } else {
          // Criar
          await prisma.appDefinition.create({
            data: {
              appId: app.appId,
              name: app.name,
              description: app.description,
              category: app.category,
              isPaid: app.isPaid,
              monthlyPrice: app.monthlyPrice,
              hasTrialPeriod: app.hasTrialPeriod,
              trialDays: app.trialDays,
              features: app.features,
              requiredPlans: app.requiredPlans,
              isActive: app.isActive,
              isPopular: app.isPopular
            }
          })
          created++
        }
      } catch (error) {
        console.error(`Erro ao processar app ${app.appId}:`, 'error')
      
}
    }

    return NextResponse.json({
      message: `Apps processadas com sucesso`,
      summary: {
        created,
        updated,
        total: apps.length
      }
    })

  } catch (error) {
    console.error('Erro ao criar apps de exemplo:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
