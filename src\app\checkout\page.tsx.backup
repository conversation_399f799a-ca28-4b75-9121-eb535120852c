'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { CreditCard, Lock, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react'

function CheckoutContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [paymentCompleted, setPaymentCompleted] = useState(false)
  const [error, setError] = useState('')

  const paymentId = searchParams.get('payment')
  const sessionId = searchParams.get('session_id')
  const amount = searchParams.get('amount')
  const plan = searchParams.get('plan')

  useEffect(() => {
    if (paymentId || sessionId) {
      checkPaymentStatus()
    }
  }, [paymentId, sessionId])

  const checkPaymentStatus = async () => {
    try {
      setIsLoading(true)

      const params = new URLSearchParams()
      if (paymentId) params.append('payment_id', paymentId)
      if (sessionId) params.append('session_id', sessionId)

      const response = await fetch(`/api/payments/verify?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setPaymentCompleted(true)
        setTimeout(() => {
          router.push('/lojista/subscricao?success=true')
        }, 3000)
      } else {
        setError(data.message || 'Erro ao verificar pagamento')
      }

    } catch (error) {
      console.error('Erro ao verificar pagamento:', error)
      setError('Erro ao verificar pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePayment = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      // Redirecionar para Stripe Checkout
      const response = await fetch('/api/payments/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount: parseFloat(amount || '0'),
          plan: plan || 'REVY PRO',
          paymentId: paymentId
        })
      })

      const data = await response.json()

      if (data.url) {
        // Redirecionar para Stripe Checkout
        window.location.href = data.url
      } else {
        setError(data.message || 'Erro ao criar sessão de pagamento')
      }

    } catch (error) {
      console.error('Erro no pagamento:', error)
      setError('Erro ao processar pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  if (paymentCompleted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Pagamento Confirmado!</h2>
          <p className="text-gray-600 mb-4">
            Sua subscrição foi atualizada com sucesso.
          </p>
          <p className="text-sm text-gray-500">
            Redirecionando em alguns segundos...
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Erro no Pagamento</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/lojista/subscricao')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Voltar
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
          <p className="text-gray-600 mt-2">Complete o seu pagamento de forma segura</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Payment Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Detalhes do Pagamento</h2>
            
            {/* Order Summary */}
            <div className="mb-6">
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600">Plano:</span>
                <span className="font-medium">{plan || 'REVY PRO'}</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-600">Valor:</span>
                <span className="font-medium">€{amount || '9.90'}</span>
              </div>
              <div className="border-t border-gray-200 mt-4 pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-lg font-bold">€{amount || '9.90'}</span>
                </div>
              </div>
            </div>

            {/* Payment ID */}
            {paymentId && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">ID do Pagamento:</p>
                <p className="font-mono text-sm">{paymentId}</p>
              </div>
            )}
          </div>

          {/* Payment Form */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Informações de Pagamento
            </h2>

            <form onSubmit={handlePayment}>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Será redirecionado para o Stripe para completar o pagamento de forma segura.
                  </p>
                </div>

                {error && (
                  <div className="p-4 bg-red-50 rounded-lg">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                )}
              </div>

              <div className="mt-6">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processando...
                    </>
                  ) : (
                    <>
                      <Lock className="w-4 h-4 mr-2" />
                      Pagar €{amount || '9.90'}
                    </>
                  )}
                </button>
              </div>

              <div className="mt-4 text-center">
                <p className="text-xs text-gray-500 flex items-center justify-center">
                  <Lock className="w-3 h-3 mr-1" />
                  Pagamento seguro e encriptado
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>}>
      <CheckoutContent />
    </Suspense>
  )
}
