import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a app está instalada
    const installedApp = await prisma.$queryRaw`
      SELECT * FROM installed_apps 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = 'newsletter-pro' 
      AND "isActive" = true
    `

    if (!installedApp || installedApp.length === 0) {
      return NextResponse.json(
        { message: 'App Newsletter Pro não está instalada' },
        { status: 404 }
      )
    }

    // Buscar campanhas do usuário
    const campaigns = await prisma.$queryRaw`
      SELECT * FROM newsletter_campaigns 
      WHERE "userId" = ${session.user.id}
      ORDER BY "createdAt" DESC
    `

    return NextResponse.json({
      campaigns: campaigns.map((campaign: any) => ({
        id: campaign.id,
        name: campaign.name,
        subject: campaign.subject,
        templateId: campaign.templateId,
        listId: campaign.listId,
        status: campaign.status,
        sentAt: campaign.sentAt,
        openRate: campaign.openRate ? Number(campaign.openRate) : null,
        clickRate: campaign.clickRate ? Number(campaign.clickRate) : null,
        createdAt: campaign.createdAt
      }))
    })

  } catch (error) {
    console.error('Erro ao buscar campanhas:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, subject, templateId, listId, customerIds, recipientType } = await request.json()

    if (!name || !subject || !templateId) {
      return NextResponse.json(
        { message: 'Nome, assunto e template são obrigatórios' },
        { status: 400 }
      )
    }

    // Validar tipo de destinatários
    if (recipientType === 'list' && !listId) {
      return NextResponse.json(
        { message: 'Lista é obrigatória quando tipo é "list"' },
        { status: 400 }
      )
    }

    if (recipientType === 'customers' && (!customerIds || customerIds.length === 0)) {
      return NextResponse.json(
        { message: 'Clientes são obrigatórios quando tipo é "customers"' },
        { status: 400 }
      )
    }

    // Criar campanha
    const campaign = await prisma.$queryRaw`
      INSERT INTO newsletter_campaigns (
        "id", "userId", name, subject, "templateId", "listId",
        "recipientType", "customerIds", status, "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${session.user.id}, ${name}, ${subject}, ${templateId}, ${listId || null},
        ${recipientType || 'list'}, ${JSON.stringify(customerIds || [])}::jsonb, 'DRAFT', NOW(), NOW()
      ) RETURNING *
    ` as any[]

    // Calcular número de destinatários
    let recipientCount = 0
    if (recipientType === 'customers' && customerIds) {
      recipientCount = customerIds.length
    } else if (listId) {
      const listStats = await prisma.$queryRaw`
        SELECT "subscriberCount" FROM newsletter_lists
        WHERE id = ${listId} AND "userId" = ${session.user.id}
        LIMIT 1
      ` as any[]
      recipientCount = listStats[0]?.subscriberCount || 0
    }

    return NextResponse.json({
      message: 'Campanha criada com sucesso',
      campaign: {
        ...campaign[0],
        recipientCount
      }
    })

  } catch (error) {
    console.error('Erro ao criar campanha:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
