const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestShop() {
  try {
    console.log('🏪 Criando lojista teste...')

    // 1. Buscar plano existente com produtos recomendados
    let revyProPlan = await prisma.subscriptionPlan.findFirst({
      where: {
        recommendedProductsEnabled: true
      }
    })

    if (!revyProPlan) {
      console.log('❌ Nenhum plano com produtos recomendados encontrado!')
      console.log('💡 Crie um plano com recommendedProductsEnabled: true primeiro')
      return
    }

    console.log('✅ Usando plano:', revyProPlan.name)

    // 2. Criar usuário lojista
    const hashedPassword = await bcrypt.hash('123456', 12)
    
    const shopUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'iTech Mobile',
        password: hashedPassword,
        role: 'REPAIR_SHOP',
        isVerified: true,
        isFeatured: true,
        profile: {
          create: {
            companyName: 'iTech Mobile - Reparações',
            description: 'Especialistas em reparação de dispositivos móveis',
            phone: '+*********** 678',
            address: 'Rua da Tecnologia, 123',
            city: 'Porto',
            postalCode: '4000-001',
            country: 'PT',
            customSubdomain: 'itechmobile',
            workingBrands: ['apple', 'samsung', 'xiaomi'],
            workingCategories: ['smartphones', 'tablets', 'laptops'],
            workingProblems: ['screen', 'battery', 'water-damage'],
            serviceRadius: 50,
            averageRepairTime: 24,
            shippingEnabled: true,
            freeShippingThreshold: 50.0,
            freeShippingCountries: ['PT', 'ES'],
            shippingRates: [
              {
                country: 'PT',
                countryName: 'Portugal',
                type: 'FIXED',
                fixedRate: 5.99
              },
              {
                country: 'ES',
                countryName: 'Espanha',
                type: 'FIXED',
                fixedRate: 9.99
              }
            ]
          }
        }
      }
    })

    console.log('✅ Lojista criado:', shopUser.email)

    // 3. Criar subscrição ativa
    const subscription = await prisma.subscription.create({
      data: {
        userId: shopUser.id,
        planId: revyProPlan.id,
        status: 'ACTIVE',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
        autoRenew: true
      }
    })

    console.log('✅ Subscrição ativa criada!')

    // 4. Buscar categorias, marcas e modelos existentes
    const categories = await prisma.category.findMany({ take: 3 })
    const brands = await prisma.brand.findMany({ take: 3 })
    const deviceModels = await prisma.deviceModel.findMany({ take: 5 })

    if (categories.length === 0 || brands.length === 0) {
      console.log('⚠️  Não há categorias ou marcas suficientes. Criando dados básicos...')
      
      // Criar categorias básicas se não existirem
      if (categories.length === 0) {
        await prisma.category.createMany({
          data: [
            { name: 'Smartphones', description: 'Telemóveis e smartphones' },
            { name: 'Tablets', description: 'Tablets e iPads' },
            { name: 'Laptops', description: 'Computadores portáteis' }
          ]
        })
      }

      // Criar marcas básicas se não existirem
      if (brands.length === 0) {
        await prisma.brand.createMany({
          data: [
            { name: 'Apple', description: 'Produtos Apple' },
            { name: 'Samsung', description: 'Produtos Samsung' },
            { name: 'Xiaomi', description: 'Produtos Xiaomi' }
          ]
        })
      }

      // Recarregar dados
      const newCategories = await prisma.category.findMany({ take: 3 })
      const newBrands = await prisma.brand.findMany({ take: 3 })
      
      categories.push(...newCategories)
      brands.push(...newBrands)
    }

    // 5. Criar produtos de teste
    const products = [
      {
        name: 'iPhone 14 Pro - Ecrã Original',
        description: 'Ecrã original Apple para iPhone 14 Pro. Inclui instalação gratuita.',
        price: 299.99,
        originalPrice: 349.99,
        condition: 'NEW',
        stock: 10,
        images: ['/images/iphone-14-pro-screen.jpg'],
        categoryId: categories[0]?.id,
        brandId: brands[0]?.id,
        deviceModelId: deviceModels[0]?.id
      },
      {
        name: 'Samsung Galaxy S23 - Bateria Premium',
        description: 'Bateria de alta qualidade para Samsung Galaxy S23. Garantia de 2 anos.',
        price: 89.99,
        originalPrice: 119.99,
        condition: 'NEW',
        stock: 15,
        images: ['/images/samsung-s23-battery.jpg'],
        categoryId: categories[0]?.id,
        brandId: brands[1]?.id,
        deviceModelId: deviceModels[1]?.id
      },
      {
        name: 'MacBook Pro M2 - Teclado Português',
        description: 'Teclado português para MacBook Pro M2. Layout QWERTY português.',
        price: 199.99,
        condition: 'NEW',
        stock: 5,
        images: ['/images/macbook-keyboard.jpg'],
        categoryId: categories[2]?.id,
        brandId: brands[0]?.id,
        deviceModelId: deviceModels[2]?.id
      },
      {
        name: 'Xiaomi Mi 11 - Capa Protetora',
        description: 'Capa protetora transparente para Xiaomi Mi 11. Material TPU flexível.',
        price: 19.99,
        originalPrice: 29.99,
        condition: 'NEW',
        stock: 25,
        images: ['/images/xiaomi-case.jpg'],
        categoryId: categories[0]?.id,
        brandId: brands[2]?.id,
        deviceModelId: deviceModels[3]?.id
      },
      {
        name: 'iPad Air - Película de Vidro',
        description: 'Película de vidro temperado para iPad Air. Proteção total do ecrã.',
        price: 24.99,
        condition: 'NEW',
        stock: 20,
        images: ['/images/ipad-screen-protector.jpg'],
        categoryId: categories[1]?.id,
        brandId: brands[0]?.id,
        deviceModelId: deviceModels[4]?.id
      }
    ]

    for (const productData of products) {
      if (productData.categoryId && productData.brandId) {
        await prisma.marketplaceProduct.create({
          data: {
            ...productData,
            sellerId: shopUser.id,
            isActive: true
          }
        })
      }
    }

    console.log('✅ 5 produtos criados!')

    console.log('\n🎉 Lojista teste criado com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: 123456')
    console.log('🏪 Subdomínio: itechmobile')
    console.log('🌐 URL da loja: http://localhost:3000/shop/itechmobile')
    console.log('📋 Plano: Revy Pro (30 dias de produtos recomendados)')
    console.log('🛍️ Produtos: 5 produtos ativos no marketplace')

  } catch (error) {
    console.error('❌ Erro ao criar lojista teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestShop()
