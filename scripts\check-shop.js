const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getTestShop() {
  try {
    const shop = await prisma.user.findFirst({
      where: { role: 'REPAIR_SHOP' },
      include: {
        profile: true
      }
    });
    
    if (shop) {
      console.log('Loja encontrada:');
      console.log('ID:', shop.id);
      console.log('Nome:', shop.name);
      console.log('Email:', shop.email);
      console.log('Subdomínio:', shop.profile?.customSubdomain);
    } else {
      console.log('Nenhuma loja encontrada');
    }
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getTestShop();
