import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Login necessário" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')

    if (!sessionId) {
      return NextResponse.json(
        { message: "Session ID é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar encomenda pelo Stripe session ID
    const order = await prisma.marketplaceOrder.findFirst({
      where: {
        stripeSessionId: sessionId,
        customerId: session.user.id
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
                images: true}
            }
          }
        },
        address: true,
        seller: {
          select: {
            name: true,
            email: true}
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    const formattedOrder = {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      total: Number(order.total),
      createdAt: order.createdAt.toISOString(),
      seller: order.seller,
      address: order.address,
      items: order.items.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.price),
        product: item.product
      }))
    }

    return NextResponse.json(formattedOrder)

  } catch (error) {
    console.error("Erro ao buscar detalhes da encomenda:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
