import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id: orderId } = await params

    // Buscar encomenda do marketplace
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        marketplaceOrderItems: {
          some: {} // Apenas encomendas que têm itens do marketplace
}
      },
      include: {
        customer: {
          select: {
            name: true,
            email: true}
        },
        marketplaceOrderItems: {
          include: {
            product: {
              select: {
                name: true,
                seller: {
                  select: {
                    name: true,
                    email: true}
                }
              }
            }
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    // Obter o primeiro vendedor (pode haver múltiplos vendedores numa encomenda)
    const firstSeller = order.marketplaceOrderItems[0]?.product?.seller

    const formattedOrder = {
      id: order.id,
      orderNumber: `MP-${order.id.slice(-8)
}`,
      customerName: order.customer.name,
      customerEmail: order.customer.email,
      sellerName: firstSeller?.name || "Vendedor não encontrado",
      sellerEmail: firstSeller?.email || '',
      total: Number(order.total),
      status: order.status,
      createdAt: order.createdAt.toISOString(),
      itemCount: order.marketplaceOrderItems.length,
      items: order.marketplaceOrderItems.map(item => ({
        productName: item.product.name,
        quantity: item.quantity,
        price: Number(item.price)
      }))
    }

    return NextResponse.json({
      order: formattedOrder})

  } catch (error) {
    console.error('Erro ao buscar detalhes da encomenda:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { status } = await request.json()
    const { id: orderId } = await params

    // Atualizar status da encomenda
    await prisma.order.update({
      where: { id: orderId},
      data: { status
}
    })

    return NextResponse.json({
      message: 'Status atualizado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar status da encomenda:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
