import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: { userId: session.user.id }
    })

    return NextResponse.json(profile)

  } catch (error) {
    console.error('Erro ao buscar perfil:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    const profile = await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        companyName: data.companyName,
        companyNif: data.companyNif,
        phone: data.phone,
        description: data.description,
        workingBrands: data.workingBrands || [],
        workingCategories: data.workingCategories || [],
        workingProblems: data.workingProblems || [],
        serviceRadius: data.serviceRadius,
        averageRepairTime: data.averageRepairTime
      },
      create: {
        userId: session.user.id,
        companyName: data.companyName,
        companyNif: data.companyNif,
        phone: data.phone,
        description: data.description,
        workingBrands: data.workingBrands || [],
        workingCategories: data.workingCategories || [],
        workingProblems: data.workingProblems || [],
        serviceRadius: data.serviceRadius,
        averageRepairTime: data.averageRepairTime
      }
    })

    return NextResponse.json(profile)

  } catch (error) {
    console.error('Erro ao atualizar perfil:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
