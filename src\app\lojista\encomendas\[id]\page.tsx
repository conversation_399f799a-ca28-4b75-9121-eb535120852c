'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Package, Eye, Truck, CheckCircle, Clock, AlertCircle, User, ArrowLeft, MapPin, Phone, Mail, FileText, Loader } from 'lucide-react'

interface OrderItem {
  id: string
  quantity: number
  price: number
  product: {
    name: string
    images: string[]
  }
}

interface Order {
  id: string
  orderNumber: string
  status: string
  total: number
  createdAt: string
  updatedAt: string
  customer: {
    name: string
    email: string
    phone?: string}
  shippingAddress?: {
    name: string
    street: string
    city: string
    postalCode: string
    country: string}
  customerDetails?: {
    name: string
    phone: string
    nif?: string}
  deliveryInfo?: {
    method: string
    pickupAddress?: string
    deliveryAddress?: string}
  type: 'marketplace' | 'repair'
  items: OrderItem[]
  description?: string
  deviceInfo?: {
    brand: string
    model: string
    issue: string}
}

export default function OrderDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [moloniActive, setMoloniActive] = useState(false)
  const [issuingInvoice, setIssuingInvoice] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchOrderDetails()
      checkMoloniStatus()
    }
  }, [params.id])

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(`/api/lojista/orders/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setOrder(data.order)
      } else if (response.status === 404) {
        router.push('/lojista/encomendas')
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes da encomenda:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const checkMoloniStatus = async () => {
    try {
      const response = await fetch('/api/lojista/apps/moloni/config')
      if (response.ok) {
        const data = await response.json()
        setMoloniActive(data.config?.isConnected || 'false')
      }
    } catch (error) {
      console.error('Erro ao verificar status da Moloni:', 'error')
    }
  }

  const issueInvoice = async () => {
    if (!order) return

    setIssuingInvoice(true)
    try {
      const response = await fetch(/api/lojista/apps/moloni/issue-invoice, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ invoiceId: order.id })
      })

      if (response.ok) {
        const data = await response.json()
        alert(`Fatura emitida com sucesso! Número: ${data.invoiceNumber}`)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao emitir fatura')
      }
    } catch (error) {
      console.error('Erro ao emitir fatura:', 'error')
      alert('Erro ao emitir fatura')
    } finally {
      setIssuingInvoice(false)
    }
  }

  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return

    try {
      const response = await fetch(`/api/lojista/orders/${order.id}`, {
        method: PUT,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus})
      })

      if (response.ok) {
        setOrder({ ...order, status: newStatus})
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', 'error')
    }
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: Pendente,
          color: 'bg-yellow-100 text-yellow-800',
          icon: Clock}
      case 'PROCESSING':
        return {
          label: 'Em Processamento',
          color: 'bg-blue-100 text-blue-800',
          icon: Package}
      case 'SHIPPED':
        return {
          label: Enviado,
          color: 'bg-purple-100 text-purple-800',
          icon: Truck}
      case 'DELIVERED':
        return {
          label: Entregue,
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle}
      case 'CANCELLED':
        return {
          label: Cancelado,
          color: 'bg-red-100 text-red-800',
          icon: AlertCircle}
      default:
        return {
          label: Desconhecido,
          color: 'bg-gray-100 text-gray-800',
          icon: Package}
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Encomenda não encontrada</h2>
          <Link
            href="/lojista/encomendas"
            className="text-black hover:underline"
          >Voltar às encomendas</Link>
        </div>
      </div>
    )
  }

  const statusConfig = getStatusConfig(order.status)
  const StatusIcon = statusConfig.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/lojista/encomendas"
                className="flex items-center text-gray-600 hover:text-black transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-bold text-black">Revify</span>
              </Link>
              <span className="text-gray-400">|</span>
              <h1 className="text-xl font-semibold text-gray-900">
                Encomenda #{order.orderNumber}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Order Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Encomenda #{order.orderNumber}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {order.type === marketplace ? Marketplace : 'Reparação'}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.color}`}>
                <StatusIcon className="w-4 h-4 mr-2" />
                {statusConfig.label}
              </span>
              <select
                value={order.status}
                onChange={(e) => updateOrderStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black text-sm"
              >
                <option value="PENDING">Pendente</option>
                <option value="PROCESSING">Em Processamento</option>
                <option value="SHIPPED">Enviado</option>
                <option value="DELIVERED">Entregue</option>
                <option value="CANCELLED">Cancelado</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Data da Encomenda:</span>
              <p className="font-medium">
                {new Date(order.createdAt).toLocaleDateString('pt-PT', {
                  day: '2-digit',
                  month: '2-digit',
                  year: numeric,
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            <div>
              <span className="text-gray-600">Última Atualização:</span>
              <p className="font-medium">
                {new Date(order.updatedAt).toLocaleDateString('pt-PT', {
                  day: '2-digit',
                  month: '2-digit',
                  year: numeric,
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <p className="font-medium text-lg">€{order.total.toFixed(2)}</p>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <User className="w-5 h-5 mr-2" />Informações do Cliente</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-gray-600">Nome:</span>
              <p className="font-medium">{order.customer.name}</p>
            </div>
            <div>
              <span className="text-gray-600">Email:</span>
              <p className="font-medium">{order.customer.email}</p>
            </div>
            {order.customer.phone && (
              <div>
                <span className="text-gray-600">Telefone:</span>
                <p className="font-medium">{order.customer.phone}</p>
              </div>
            )}
            {order.customerDetails?.nif && (
              <div>
                <span className="text-gray-600">NIF:</span>
                <p className="font-medium">{order.customerDetails.nif}</p>
              </div>
            )}
          </div>
        </div>

        {/* Moloni Invoice Card */}
        {moloniActive && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2" />Faturação Moloni</h3>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">Emitir fatura automaticamente através da integração Moloni.</p>
              <div className="flex items-center space-x-4">
                <button
                  onClick={issueInvoice}
                  disabled={issuingInvoice}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {issuingInvoice ? (
                    <>
                      <Loader className="w-4 h-4 mr-2 animate-spin" />
                      Emitindo...
                    </>
                  ) : (
                    <>
                      <FileText className="w-4 h-4 mr-2" />
                      Emitir Fatura
                    </>
                  )}
                </button>
                <div className="text-sm text-gray-500">
                  <p>Cliente: {order.customer.name}</p>
                  <p>Valor: €{order.total.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Shipping Address for Marketplace Orders */}
        {order.type === marketplace && order.shippingAddress && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPin className="w-5 h-5 mr-2" />Endereço de Entrega</h3>
            <div className="space-y-2">
              <p className="font-medium">{order.shippingAddress.name}</p>
              <p className="text-gray-600">{order.shippingAddress.street}</p>
              <p className="text-gray-600">
                {order.shippingAddress.postalCode} {order.shippingAddress.city}
              </p>
              <p className="text-gray-600">{order.shippingAddress.country}</p>
            </div>
          </div>
        )}

        {/* Delivery Info for Repair Orders */}
        {order.type === 'repair' && order.deliveryInfo && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPin className="w-5 h-5 mr-2" />Informações de Entrega</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-gray-600">Método:</span>
                <p className="font-medium">
                  {order.deliveryInfo.method === 'STORE_PICKUP' ? 'Recolha na Loja' :
                   order.deliveryInfo.method === 'COURIER_PICKUP' ? Recolha por Estafeta :
                   order.deliveryInfo.method === 'MAIL_SEND' ? 'Envio por Correio' :
                   order.deliveryInfo.method}
                </p>
              </div>
              {order.deliveryInfo.pickupAddress && (
                <div>
                  <span className="text-gray-600">Endereço de Recolha:</span>
                  <p className="font-medium">{order.deliveryInfo.pickupAddress}</p>
                </div>
              )}
              {order.deliveryInfo.deliveryAddress && (
                <div>
                  <span className="text-gray-600">Endereço de Entrega:</span>
                  <p className="font-medium">{order.deliveryInfo.deliveryAddress}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Order Items or Repair Details */}
        {order.type === marketplace ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="w-5 h-5 mr-2" />Itens da Encomenda</h3>
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    {item.product?.images?.length > 0 ? (
                      <img
                        src={item.product.images[0]}
                        alt={item.product?.name || 'Produto'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                        Sem Imagem
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {item.product?.name || 'Produto não encontrado'}
                    </h4>
                    <p className="text-sm text-gray-600">
                      Quantidade: {item.quantity}
                    </p>
                    <p className="text-sm text-gray-600">
                      Preço unitário: €{item.price.toFixed(2)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      €{(item.quantity * item.price).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes da Reparação</h3>
            {order.deviceInfo && (
              <div className="space-y-4">
                <div>
                  <span className="text-gray-600">Dispositivo:</span>
                  <p className="font-medium">{order.deviceInfo.brand} {order.deviceInfo.model}</p>
                </div>
                <div>
                  <span className="text-gray-600">Problema:</span>
                  <p className="font-medium">{order.deviceInfo.issue}</p>
                </div>
                {order.description && (
                  <div>
                    <span className="text-gray-600">Descrição:</span>
                    <p className="font-medium">{order.description}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
