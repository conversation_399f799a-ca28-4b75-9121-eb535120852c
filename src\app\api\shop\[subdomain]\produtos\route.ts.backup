import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params
    const { searchParams } = new URL(request.url)
    const idsParam = searchParams.get('ids')

    if (!subdomain) {
      return NextResponse.json(
        { message: 'Subdomínio é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomínio
    const shop = await prisma.user.findFirst({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          customSubdomain: subdomain
        }
      },
      include: {
        subscription: {
          include: {
            plan: {
              select: {
                miniStore: true
              }
            }
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se a loja tem acesso à funcionalidade de mini-loja
    if (!shop.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: 'Loja online não disponível para esta loja' },
        { status: 403 }
      )
    }

    // Construir filtros para a consulta
    const whereClause: any = {
      sellerId: shop.id,
      isActive: true
    }

    // Se IDs específicos foram fornecidos, filtrar por eles
    if (idsParam) {
      const ids = idsParam.split(',').filter(id => id.trim())
      whereClause.id = {
        in: ids
      }
    } else {
      // Apenas produtos com stock para listagem geral
      whereClause.stock = {
        gt: 0
      }
    }

    // Buscar produtos da loja
    const products = await prisma.marketplaceProduct.findMany({
      where: whereClause,
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        brand: {
          select: {
            id: true,
            name: true
          }
        },
        deviceModel: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Formatar dados para o frontend
    const formattedProducts = products.map((product: any) => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      images: product.images,
      category: product.category,
      brand: product.brand,
      deviceModel: product.deviceModel,
      stock: product.stock,
      condition: product.condition,
      createdAt: product.createdAt
    }))

    return NextResponse.json({
      success: true,
      products: formattedProducts,
      total: formattedProducts.length
    })

  } catch (error) {
    console.error('Erro ao buscar produtos da loja:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
