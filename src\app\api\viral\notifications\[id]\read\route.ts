import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const { id } = await params

    // Por simplicidade, apenas retornar sucesso
    // Em uma implementação completa, armazenaria o estado de leitura na base de dados
    console.log(`Notificação ${id} marcada como lida para usuário ${session.user.id}`)

    return NextResponse.json({ success: true})

  } catch (error) {
    console.error(Erro ao marcar notificação como lida:, 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
