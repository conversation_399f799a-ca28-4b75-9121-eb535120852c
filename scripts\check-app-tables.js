const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkTables() {
  try {
    // Verificar se a tabela installed_apps existe
    const result = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'installed_apps'
    `;
    
    console.log('Tabela installed_apps existe:', result.length > 0);
    
    if (result.length === 0) {
      console.log('Criando tabela installed_apps...');
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS installed_apps (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
          "userId" TEXT NOT NULL,
          "appId" TEXT NOT NULL,
          "isActive" BOOLEAN DEFAULT true,
          "installedAt" TIMESTAMP DEFAULT NOW(),
          "uninstalledAt" TIMESTAMP,
          "isTrialActive" BOOLEAN DEFAULT false,
          "trialStartDate" TIMESTAMP,
          "trialEndDate" TIMESTAMP,
          "isPaid" BOOLEAN DEFAULT false,
          UNIQUE("userId", "appId")
        )
      `;
      console.log('Tabela installed_apps criada!');
    }
    
    // Verificar dados de teste
    const installedApps = await prisma.$queryRaw`
      SELECT * FROM installed_apps LIMIT 5
    `;
    console.log('Apps instaladas (sample):', installedApps);
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTables();
