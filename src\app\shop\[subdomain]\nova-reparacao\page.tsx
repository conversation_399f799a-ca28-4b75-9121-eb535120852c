'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import ShopLayout from '@/components/shop/ShopLayout'
import TranslatedSelect from '@/components/ui/TranslatedSelect'
import { useTranslation } from '@/hooks/useTranslation'
import { ArrowLeft,
  ArrowRight,
  Upload,
  User,
  Phone,
  FileText,
  Euro,
  Store,
  Package } from 'lucide-react'

interface RepairFormData {
  categoryId: string
  brandId: string
  deviceId: string
  problemTypeId: string
  description: string
  problemImages: File[]
  customerName: string
  customerPhone: string
  customerNif: string
  deliveryMethod: string // in_store, 'mail'
  paymentMethod: string // 'in_store', 'multibanco', 'mbway'

}

interface ShopConfig {
  id: string
  name: string
  companyName: string
  logo: string
  phone: string
  address: string
  city: string
  postalCode: string}

interface Category {
  id: string
  name: string}

interface Brand {
  id: string
  name: string}

interface Device {
  id: string
  name: string
  brandId: string}

interface ProblemType {
  id: string
  name: string
  categoryId: string}

export default function NovaReparacaoIndependentePage() {
  const { tSync } = useTranslation()
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const subdomain = params.subdomain as string
  
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [shopConfig, setShopConfig] = useState<ShopConfig | null>(null)
  
  const [formData, setFormData] = useState<RepairFormData>({
    categoryId: '',
    brandId: '',
    deviceId: '',
    problemTypeId: '',
    description: '',
    problemImages: [],
    customerName: '',
    customerPhone: '',
    customerNif: '',
    deliveryMethod: in_store,
    paymentMethod: in_store})

  const [categories, setCategories] = useState<Category[]>([])
  const [brands, setBrands] = useState<Brand[]>([])
  const [devices, setDevices] = useState<Device[]>([])
  const [problemTypes, setProblemTypes] = useState<ProblemType[]>([])
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null)

  const fetchShopConfig = useCallback(async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShopConfig(data)
      }
    } catch (error) {
      console.error('Erro ao buscar configuração da loja:', 'error')
    }
  }, [subdomain])

  useEffect(() => {
    fetchShopConfig()
    fetchInitialData()
  }, [fetchShopConfig])

  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        customerName: session.user.name || '',
        customerPhone: ''
      }))
    }
  }, [session])

  useEffect(() => {
    if (formData.categoryId && formData.brandId) {
      fetchDevices()
    }
  }, [formData.categoryId, formData.brandId])

  useEffect(() => {
    if (formData.categoryId && formData.problemTypeId) {
      fetchEstimate()
    }
  }, [formData.categoryId, formData.problemTypeId, formData.deviceId])

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories || [])
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData.brands || brandsData || [])
      }

      if (problemTypesRes.ok) {
        const problemTypesData = await problemTypesRes.json()
        setProblemTypes(problemTypesData.problemTypes || problemTypesData || [])
      }
    } catch (error) {
      console.error('Erro ao buscar dados iniciais:', 'error')
    }
  }

  const fetchDevices = async () => {
    try {
      const response = await fetch(`/api/device-models?brandId=${formData.brandId}&categoryId=${formData.categoryId}`)
      if (response.ok) {
        const data = await response.json()
        setDevices(data)
      }
    } catch (error) {
      console.error('Erro ao buscar dispositivos:', 'error')
    }
  }

  const fetchEstimate = async () => {
    try {
      const params = new URLSearchParams({
        categoryId: formData.categoryId,
        problemTypeId: formData.problemTypeId
      })
      
      if (formData.deviceId) {
        params.append('deviceId', formData.deviceId)
      }

      const response = await fetch(`/api/repair-estimate?${params}`)
      if (response.ok) {
        const data = await response.json()
        setEstimatedPrice(data.estimatedPrice)
      }
    } catch (error) {
      console.error('Erro ao buscar estimativa:', 'error')
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setFormData(prev => ({
      ...prev,
      problemImages: [...prev.problemImages, ...files].slice(0, 5)
    }))
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      problemImages: prev.problemImages.filter((_, i) => i !== 'index')
    }))
  }

  const handleNext = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      // Criar FormData para envio com imagens
      const submitData = new FormData()
      
      // Adicionar dados do formulário
      Object.entries(formData).forEach(([key, value]) => {
        if (key === problemImages) {
          formData.problemImages.forEach((file) => {
            submitData.append(`problemImages`, 'file')
          
})
        } else {
          submitData.append(key, 'value as string')
        }
      })

      // Adicionar subdomain da loja
      submitData.append(shopSubdomain, 'subdomain')

      const response = await fetch('/api/shop/repairs/independent', {
        method: POST,
        body: submitData
})

      if (response.ok) {
        const data = await response.json()
        
        // Redirecionar baseado no método de pagamento
        if (formData.paymentMethod === in_store) {
          router.push(`/shop/${subdomain
}/reparacao/${data.repairId}/confirmacao`)
        } else {
          // Para multibanco/mbway, redirecionar para página de pagamento
          router.push(`/shop/${subdomain}/reparacao/${data.repairId}/pagamento`)
        }
      } else {
        const error = await response.json()
        alert(`Erro: ${error.message}`)
      }
    } catch (error) {
      console.error(Erro ao criar reparação:, 'error')
      alert('Erro ao criar reparação')
    
} finally {
      setIsLoading(false)
    }
  }

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.categoryId && formData.brandId && formData.problemTypeId
      case 2:
        return formData.description.trim().length > 0
      case 3:
        return formData.customerName && formData.customerPhone
      case 4:
        return formData.deliveryMethod
      case 5:
        return formData.paymentMethod
      default:
        'return false'}
  }

  if (!shopConfig) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shopConfig.companyName || shopConfig.name || 'Loja'}
      subdomain={subdomain}
      shopInfo={{
        phone: shopConfig.phone,
        address: shopConfig.address,
        city: shopConfig.city
      }}
    >
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href={`/shop/${subdomain}`}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
        </div>
      </div>

      <div className="bg-gray-50 min-h-screen">
        {/* Page Header */}
        <div className="bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <p
                className="text-lg text-gray-600">Preencha os dados do seu dispositivo para solicitar uma reparação</p>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4, 5].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 5 && (
                  <div className={`w-12 h-1 mx-2 ${
                    step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span>Dispositivo</span>
            <span>Descrição</span>
            <span>Contacto</span>
            <span>Pagamento</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6">
          {/* Step 1: Dispositivo e Problema */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Selecione o seu dispositivo</h2>
                <p className="text-gray-600">Escolha a categoria, marca e modelo do seu dispositivo</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Categoria */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria *
                  </label>
                  <TranslatedSelect
                    value={formData.categoryId}
                    onChange={(value) => setFormData(prev => ({ ...prev, categoryId: value, brandId: '', deviceId: '' }))}
                    options={Array.isArray(categories) ? categories.map(cat => ({ value: cat.id, label: cat.name })) : []}
                    placeholder={tSync("Selecione uma categoria")}
                    required
                  />
                </div>

                {/* Marca */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Marca *
                  </label>
                  <TranslatedSelect
                    value={formData.brandId}
                    onChange={(value) => setFormData(prev => ({ ...prev, brandId: value, deviceId: '' }))}
                    options={Array.isArray(brands) ? brands.map(brand => ({ value: brand.id, label: brand.name })) : []}
                    placeholder={tSync("Selecione uma marca")}
                    disabled={!formData.categoryId}
                    required
                  />
                </div>

                {/* Modelo */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Modelo</label>
                  <select
                    value={formData.deviceId}
                    onChange={(e) => setFormData(prev => ({ ...prev, deviceId: e.target.value }))}
                    disabled={!formData.brandId}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                  >
                    <option value="">Selecione um modelo (opcional)</option>
                    {Array.isArray(devices) && devices.map((device) => (
                      <option key={device.id} value={device.id}>
                        {device.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tipo de Problema */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Problema *
                  </label>
                  <select
                    value={formData.problemTypeId}
                    onChange={(e) => setFormData(prev => ({ ...prev, problemTypeId: e.target.value }))}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Selecione o tipo de problema</option>
                    {Array.isArray(problemTypes) && problemTypes.map((problem) => (
                      <option key={problem.id} value={problem.id}>
                        {problem.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Estimativa de Preço */}
              {estimatedPrice && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Euro className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      Preço estimado: €{estimatedPrice}
                    </span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">Este é um preço estimativo. O preço final será confirmado após diagnóstico.</p>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Descrição do Problema */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Descreva o problema</h2>
                <p className="text-gray-600">Forneça detalhes sobre o problema do seu dispositivo</p>
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Descrição do problema *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={tSync("Descreva detalhadamente o problema do seu dispositivo...")}
                  rows={4}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Upload de Imagens */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Imagens do problema (opcional)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">Adicione até 5 imagens para ajudar no diagnóstico</p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer inline-block"
                  >
                    Escolher imagens
                  </label>
                </div>

                {/* Preview das imagens */}
                {formData.problemImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mt-4">
                    {formData.problemImages.map((file, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Problema ${index + 1}`}
                          className="w-full h-20 object-cover rounded-lg"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Dados de Contacto */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Dados de contacto</h2>
                <p className="text-gray-600">Forneça os seus dados para contacto</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nome completo *</label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      value={formData.customerName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                      className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={tSync("Seu nome completo")}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Telefone *</label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="tel"
                      value={formData.customerPhone}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
                      className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={tSync("Seu número de telefone")}
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIF (opcional)
                  </label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      value={formData.customerNif}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerNif: e.target.value }))}
                      className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={tSync("Número de contribuinte (para 'fatura')")}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Método de Entrega */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Método de entrega</h2>
                <p className="text-gray-600">Como pretende receber o seu dispositivo após a reparação?</p>
              </div>

              <div className="space-y-4">
                {/* Entrega na Loja */}
                <div
                  className={`border-2 rounded-lg p-6 cursor-pointer transition-colors ${
                    formData.deliveryMethod === 'in_store'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, deliveryMethod: in_store}))}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="radio"
                      checked={formData.deliveryMethod === 'in_store'}
                      onChange={() => setFormData(prev => ({ ...prev, deliveryMethod: in_store}))}
                      className="text-blue-600 mt-1"
                    />
                    <Store className="w-8 h-8 text-blue-600 mt-1" />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2">Levantar na loja</h3>
                      <p className="text-gray-600 text-sm mb-3">Venha buscar o seu dispositivo diretamente à nossa loja quando estiver pronto.</p>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-green-700 font-medium text-sm">Gratuito</span>
                        </div>
                        <p className="text-green-600 text-xs mt-1">
                          Sem custos de envio
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Envio por Correio */}
                <div
                  className={`border-2 rounded-lg p-6 cursor-pointer transition-colors ${
                    formData.deliveryMethod === 'mail'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, deliveryMethod: mail}))}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="radio"
                      checked={formData.deliveryMethod === 'mail'}
                      onChange={() => setFormData(prev => ({ ...prev, deliveryMethod: mail}))}
                      className="text-blue-600 mt-1"
                    />
                    <Package className="w-8 h-8 text-blue-600 mt-1" />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2">Envio por correio</h3>
                      <p className="text-gray-600 text-sm mb-3">Enviamos o seu dispositivo para a morada que indicar, com seguro incluído.</p>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="text-blue-700 font-medium text-sm">€5.99</span>
                          </div>
                          <span className="text-blue-600 text-xs">Seguro incluído</span>
                        </div>
                        <p className="text-blue-600 text-xs mt-1">Entrega em 1-2 dias úteis</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Método de Pagamento */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Método de pagamento</h2>
                <p className="text-gray-600">Escolha como pretende pagar pela reparação</p>
              </div>

              <div className="space-y-4">
                {/* Pagamento na Loja */}
                <div
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    formData.paymentMethod === 'in_store'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, paymentMethod: in_store}))}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      checked={formData.paymentMethod === 'in_store'}
                      onChange={() => setFormData(prev => ({ ...prev, paymentMethod: in_store}))}
                      className="text-blue-600"
                    />
                    <Store className="w-6 h-6 text-gray-600" />
                    <div>
                      <h3 className="font-medium text-gray-900">Pagamento na loja</h3>
                      <p className="text-sm text-gray-600">Pague diretamente na loja após a reparação</p>
                    </div>
                  </div>
                </div>

                {/* Multibanco */}
                <div
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    formData.paymentMethod === 'multibanco'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, paymentMethod: multibanco}))}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      checked={formData.paymentMethod === 'multibanco'}
                      onChange={() => setFormData(prev => ({ ...prev, paymentMethod: multibanco}))}
                      className="text-blue-600"
                    />
                    <div className="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">MB</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Multibanco</h3>
                      <p className="text-sm text-gray-600">Pagamento por referência Multibanco</p>
                    </div>
                  </div>
                </div>

                {/* MB Way */}
                <div
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    formData.paymentMethod === 'mbway'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, paymentMethod: mbway}))}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      checked={formData.paymentMethod === 'mbway'}
                      onChange={() => setFormData(prev => ({ ...prev, paymentMethod: mbway}))}
                      className="text-blue-600"
                    />
                    <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">MW</span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">MB Way</h3>
                      <p className="text-sm text-gray-600">Pagamento através da app MB Way</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Resumo */}
              {estimatedPrice && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Resumo da reparação</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Dispositivo:</span>
                      <span>{categories.find(c => c.id === formData.categoryId)?.name} - {brands.find(b => b.id === formData.brandId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Problema:</span>
                      <span>{problemTypes.find(p => p.id === formData.problemTypeId)?.name}</span>
                    </div>
                    <div className="flex justify-between font-medium text-lg border-t pt-2 mt-2">
                      <span>Preço estimado:</span>
                      <span>€{estimatedPrice}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6 border-t">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Anterior</span>
            </button>

            {currentStep < 5 ? (
              <button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span>Próximo</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={!isStepValid() || 'isLoading'}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Criando...</span>
                  </>
                ) : (
                  <>
                    <span>Criar Reparação</span>
                    <ArrowRight className="w-4 h-4" />
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
      </div>
    </ShopLayout>
  )
}
