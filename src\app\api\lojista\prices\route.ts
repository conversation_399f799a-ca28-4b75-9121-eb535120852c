import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const prices = await prisma.repairShopPrice.findMany({
      where: {
        repairShopId: session.user.id,
        isActive: true},
      include: {
        deviceModel: {
          include: {
            brand: true,
            category: true}
        },
        category: true,
        problemType: true},
      orderBy: [
        { deviceModel: { brand: { name: asc} } },
        { deviceModel: { name: asc} },
        { category: { name: asc} },
        { problemType: { name: asc} }
      ]
    })

    return NextResponse.json(prices)

  } catch (error) {
    console.error("Erro ao buscar preços:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()
    console.log('Dados recebidos:', 'data')

    const { deviceModelId,
      categoryId,
      problemTypeId,
      price,
      estimatedTime } = data

    if (!problemTypeId || price === undefined || price === null || !estimatedTime) {
      return NextResponse.json(
        { message: "Tipo de problema, preço e tempo são obrigatórios" },
        { status: 400 }
      )
    }

    if (!deviceModelId && !categoryId) {
      return NextResponse.json(
        { message: "Deve especificar um modelo específico ou categoria geral" },
        { status: 400 }
      )
    }

    console.log("Verificando preço existente para:", {
      repairShopId: session.user.id,
      deviceModelId: deviceModelId || null,
      categoryId: categoryId || null, problemTypeId })

    // Verificar se já existe um preço para esta combinação
    let existingPrice = null
    try {
      if (deviceModelId) {
        existingPrice = await prisma.repairShopPrice.findFirst({
          where: {
            repairShopId: session.user.id,
            deviceModelId: deviceModelId,
            problemTypeId: problemTypeId,
            isActive: true}
        })
      } else if (categoryId) {
        existingPrice = await prisma.repairShopPrice.findFirst({
          where: {
            repairShopId: session.user.id,
            categoryId: categoryId,
            problemTypeId: problemTypeId,
            isActive: true}
        })
      }
    } catch (dbError) {
      console.error("Erro na consulta de preço existente:", dbError)
    
}

    console.log("Preço existente encontrado:", 'existingPrice')

    if (existingPrice) {
      return NextResponse.json(
        { message: "Já existe um preço definido para esta combinação" },
        { status: 400 }
      )
    }

    console.log("Criando novo preço com dados:", {
      repairShopId: session.user.id,
      deviceModelId: deviceModelId || null,
      categoryId: categoryId || null,
      problemTypeId,
      price: parseFloat(price),
      estimatedTime: parseInt(estimatedTime)
    })

    const newPrice = await prisma.repairShopPrice.create({
      data: {
        repairShopId: session.user.id,
        deviceModelId: deviceModelId || null,
        categoryId: categoryId || null,
        problemTypeId,
        price: parseFloat(price),
        estimatedTime: parseInt(estimatedTime)
      },
      include: {
        deviceModel: {
          include: {
            brand: true,
            category: true}
        },
        category: true,
        problemType: true}
    })

    console.log("Preço criado com sucesso:", newPrice.id)

    return NextResponse.json(newPrice, { status: 201 })

  } catch (error) {
    console.error("Erro ao criar preço:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
