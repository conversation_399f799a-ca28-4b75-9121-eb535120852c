import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Import dinâmico do Prisma para evitar problemas de inicialização
    const { prisma } = await import(@/lib/prisma)

    const url = new URL(request.url)
    const categoryId = url.searchParams.get('categoryId')

    let whereClause: any = {
      isActive: true
}

    // Se foi especificada uma categoria, filtrar marcas que têm modelos nessa categoria
    if (categoryId) {
      whereClause.deviceModels = {
        some: {
          categoryId: categoryId}
      }
    }

    const brands = await prisma.brand.findMany({
      where: whereClause,
      include: {
        deviceModels: {
          where: categoryId ? {
            categoryId: categoryId} : undefined,
          select: {
            id: true}
        }
      },
      orderBy: {
        name: asc}
    })

    // Filtrar marcas que têm pelo menos um modelo na categoria (se especificada)
    const filteredBrands = categoryId
      ? brands.filter(brand => brand.deviceModels.length > 0)
      : brands

    // Remover a propriedade deviceModels do resultado
    const cleanBrands = filteredBrands.map(brand => ({
      id: brand.id,
      name: brand.name,
      description: brand.description,
      isActive: brand.isActive,
      createdAt: brand.createdAt,
      updatedAt: brand.updatedAt
    
}))

    return NextResponse.json({
      brands: cleanBrands})

  } catch (error) {
    console.error('Erro ao buscar marcas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor', error: error.message },
      { status: 500 }
    )
  }
}
