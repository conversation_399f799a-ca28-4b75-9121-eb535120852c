const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createAdminData() {
  try {
    console.log('📱 Criando categorias, marcas e modelos...')

    // Categorias
    const categories = [
      { name: 'Smartphones', description: 'Telemóveis e smartphones' },
      { name: 'Tablets', description: 'Tablets e iPads' },
      { name: 'Lapt<PERSON>', description: 'Computadores portáteis' },
      { name: 'Smartwatches', description: 'Relógios inteligentes' },
      { name: 'Consol<PERSON>', description: 'Consolas de jogos' },
      { name: 'Acessórios', description: 'Acessórios e periféricos' }
    ]

    console.log('📂 Criando categorias...')
    for (const category of categories) {
      await prisma.category.upsert({
        where: { name: category.name },
        update: {},
        create: category
      })
    }

    // Marcas
    const brands = [
      'Apple', 'Samsung', 'Xiaomi', 'Huawei', 'OnePlus', 
      'Google', 'Sony', 'LG', 'Motorola', 'Nokia',
      'Oppo', 'Vivo', 'Realme', 'Honor', 'Nothing'
    ]

    console.log('🏷️ Criando marcas...')
    for (const brandName of brands) {
      await prisma.brand.upsert({
        where: { name: brandName },
        update: {},
        create: { name: brandName }
      })
    }

    // Modelos por marca
    const deviceModels = {
      'Apple': [
        'iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15', 'iPhone 14 Pro Max',
        'iPhone 14 Pro', 'iPhone 14', 'iPhone 13 Pro Max', 'iPhone 13 Pro',
        'iPhone 13', 'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12',
        'iPad Pro 12.9"', 'iPad Pro 11"', 'iPad Air', 'iPad',
        'MacBook Pro 16"', 'MacBook Pro 14"', 'MacBook Air', 'iMac',
        'Apple Watch Series 9', 'Apple Watch SE', 'Apple Watch Ultra'
      ],
      'Samsung': [
        'Galaxy S24 Ultra', 'Galaxy S24+', 'Galaxy S24', 'Galaxy S23 Ultra',
        'Galaxy S23+', 'Galaxy S23', 'Galaxy Note 20 Ultra', 'Galaxy Note 20',
        'Galaxy A54', 'Galaxy A34', 'Galaxy A24', 'Galaxy A14',
        'Galaxy Tab S9 Ultra', 'Galaxy Tab S9+', 'Galaxy Tab S9',
        'Galaxy Watch 6', 'Galaxy Watch 5', 'Galaxy Buds Pro'
      ],
      'Xiaomi': [
        'Xiaomi 14 Ultra', 'Xiaomi 14', 'Xiaomi 13 Ultra', 'Xiaomi 13',
        'Redmi Note 13 Pro', 'Redmi Note 13', 'Redmi Note 12 Pro',
        'POCO X6 Pro', 'POCO X6', 'POCO F5 Pro', 'POCO F5',
        'Mi Pad 6', 'Mi Watch', 'Redmi Buds 4 Pro'
      ],
      'Huawei': [
        'P60 Pro', 'P60', 'Mate 60 Pro', 'Mate 60', 'Nova 11',
        'MatePad Pro', 'MatePad', 'Watch GT 4', 'FreeBuds Pro 3'
      ],
      'OnePlus': [
        'OnePlus 12', 'OnePlus 11', 'OnePlus Nord 3', 'OnePlus Nord CE 3',
        'OnePlus Pad', 'OnePlus Watch 2', 'OnePlus Buds Pro 2'
      ]
    }

    console.log('📱 Criando modelos de dispositivos...')

    // Buscar categoria padrão para smartphones
    const smartphoneCategory = await prisma.category.findUnique({
      where: { name: 'Smartphones' }
    })

    for (const [brandName, models] of Object.entries(deviceModels)) {
      const brand = await prisma.brand.findUnique({
        where: { name: brandName }
      })

      if (brand && smartphoneCategory) {
        for (const modelName of models) {
          await prisma.deviceModel.upsert({
            where: {
              brandId_name: {
                brandId: brand.id,
                name: modelName
              }
            },
            update: {},
            create: {
              name: modelName,
              brandId: brand.id,
              categoryId: smartphoneCategory.id
            }
          })
        }
      }
    }

    console.log('✅ Categorias, marcas e modelos criados com sucesso!')

  } catch (error) {
    console.error('❌ Erro ao criar dados administrativos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminData()
