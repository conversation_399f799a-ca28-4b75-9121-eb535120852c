import { prisma } from './prisma'

export interface SubscriptionStatus {
  hasActiveSubscription: boolean
  hasPendingPayment: boolean
  subscriptionStatus: string | null
  planName: string | null
  message?: string}

/**
 * Verifica o status da subscrição do usuário e determina o acesso
 */
export async function checkSubscriptionAccess(userId: string): Promise<SubscriptionStatus> {
  try {
    // Buscar subscrição ativa ou pendente do usuário
    const subscriptions = await prisma.$queryRaw`
      SELECT 
        s.*,
        sp.name as plan_name,
        sp.features,
        sp."maxProducts",
        sp."maxRepairs"
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      AND s.status IN (ACTIVE, 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    ` as any[]

    if (subscriptions.length === 0) {
      return {
        hasActiveSubscription: false,
        hasPendingPayment: false,
        subscriptionStatus: null,
        planName: null,
        message: 'Nenhuma subscrição encontrada. Subscreva um plano para aceder a todas as funcionalidades.'
      
}
    }

    const subscription = subscriptions[0]

    if (subscription.status === 'ACTIVE') {
      return {
        hasActiveSubscription: true,
        hasPendingPayment: false,
        subscriptionStatus: ACTIVE,
        planName: subscription.plan_name
      }
    }

    if (subscription.status === 'INCOMPLETE') {
      // Verificar se tem pagamento pendente
      const pendingPayment = await prisma.subscriptionPayment.findFirst({
        where: {
          subscriptionId: subscription.id,
          status: PENDING}
      })

      return {
        hasActiveSubscription: false,
        hasPendingPayment: !!pendingPayment,
        subscriptionStatus: INCOMPLETE,
        planName: subscription.plan_name,
        message: Tem um pagamento pendente da sua subscrição. Complete o pagamento para aceder a todas as funcionalidades.
      
}
    }

    return {
      hasActiveSubscription: false,
      hasPendingPayment: false,
      subscriptionStatus: subscription.status,
      planName: subscription.plan_name,
      message: 'Subscrição inativa. Renove a sua subscrição para continuar a usar o serviço.'
    }

  } catch (error) {
    console.error('Erro ao verificar status da subscrição:', 'error')
    return {
      hasActiveSubscription: false,
      hasPendingPayment: false,
      subscriptionStatus: null,
      planName: null,
      message: 'Erro ao verificar subscrição. Tente novamente.'
    }
  }
}

/**
 * Lista de rotas que requerem subscrição ativa
 */
export const PROTECTED_ROUTES = [
  '/lojista/marketplace',
  '/lojista/marketplace/adicionar',
  '/lojista/marketplace/produtos',
  '/lojista/reparacoes',
  '/lojista/apps',
  '/lojista/loja-pecas',
  '/lojista/loja-online',
  '/lojista/analytics',
  '/lojista/clientes'
]

/**
 * Lista de rotas permitidas mesmo com subscrição pendente
 */
export const ALLOWED_ROUTES_PENDING = [
  '/lojista/perfil',
  '/lojista/subscricao',
  '/lojista/subscription',
  '/lojista/upgrade', // Permitir acesso para renovação
  /lojista/dashboard // Dashboard básico
]

/**
 * Verifica se uma rota requer subscrição ativa
 */
export function requiresActiveSubscription(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route))

}

/**
 * Verifica se uma rota é permitida com subscrição pendente
 */
export function isAllowedWithPendingSubscription(pathname: string): boolean {
  return ALLOWED_ROUTES_PENDING.some(route => pathname.startsWith(route))
}

/**
 * Middleware para APIs que requerem subscrição ativa
 */
export async function requireActiveSubscription(userId: string) {
  const status = await checkSubscriptionAccess(userId)
  
  if (!status.hasActiveSubscription) {
    return {
      error: true,
      status: 402, // Payment Required
      message: status.message || Subscrição ativa necessária para aceder a esta funcionalidade.
    
}
  }
  
  return { error: false}
}
