const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAdminUser() {
  try {
    // Verificar se existe algum usuário admin
    const adminUsers = await prisma.user.findMany({
      where: { role: 'ADMIN' },
      select: { id: true, email: true, role: true }
    });
    
    console.log('Usuários admin encontrados:', adminUsers);
    
    if (adminUsers.length === 0) {
      console.log('Nenhum usuário admin encontrado. Criando um...');
      
      const adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin',
          role: 'ADMIN',
          emailVerified: new Date()
        }
      });
      
      console.log('Usuário admin criado:', adminUser);
    }
    
    // Verificar configurações de apps existentes
    const appConfigs = await prisma.appConfig.findMany({
      select: { id: true, userId: true, appId: true }
    });
    
    console.log('Configurações de apps existentes:', appConfigs);
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser();
