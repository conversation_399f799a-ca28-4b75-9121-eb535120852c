import AutoTranslate from './AutoTranslate'

/**
 * Componente T - Versão mais curta do AutoTranslate para uso rápido
 * Facilita a conversão de textos hardcoded para tradução automática
 * 
 * Uso:
 * <T><AutoTranslate text="Texto em português" /></T>
 * <T as="h1" className="..."><AutoTranslate text="Título" /></T>
 */

interface TProps {
  children: string
  as?: keyof JSX.IntrinsicElements
  className?: string
}

export default function T({ children, as, className }: TProps) {
  return <AutoTranslate text={children} as={as} className={className} />
}

// Export também como T para facilitar imports
export { T }
