'use client'

import { useState } from 'react'
import { AlertTriangle, Upload } from 'lucide-react'

interface RepairDisputeProps {
  repairId: string
  isOpen: boolean
  onClose: () => void
  onDisputeSubmitted: () => void
}

const DISPUTE_REASONS = [
  { value: 'SERVICE_QUALITY', label: 'Qualidade do Serviço' },
  { value: 'DELIVERY_DELAY', label: 'Atraso na Entrega' },
  { value: 'DAMAGE_CAUSED', label: 'Danos Causados' },
  { value: 'OVERCHARGE', label: 'Cobrança Excessiva' },
  { value: 'POOR_COMMUNICATION', label: 'Comunicação Deficiente' },
  { value: 'OTHER', label: 'Outro' }
]

const DISPUTE_OUTCOMES = [
  { value: 'FULL_REFUND', label: 'Reembolso Total' },
  { value: 'PARTIAL_REFUND', label: 'Reembolso Parcial' },
  { value: 'NEW_REPAIR', label: 'Nova Reparação' },
  { value: 'COMPENSATION', label: 'Compensação' }
]

export default function RepairDispute({ repairId, isOpen, onClose, onDisputeSubmitted }: RepairDisputeProps) {
  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [desiredOutcome, setDesiredOutcome] = useState('')
  const [evidence, setEvidence] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!isOpen) return null

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setEvidence(prev => [...prev, ...files].slice(0, 5)) // Máximo 5 arquivos
  }

  const removeFile = (index: number) => {
    setEvidence(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async () => {
    if (!reason || !description.trim() || !desiredOutcome || isSubmitting) return

    setIsSubmitting(true)
    try {
      const formData = new FormData()
      formData.append('reason', reason)
      formData.append('description', description.trim())
      formData.append('desiredOutcome', desiredOutcome)

      evidence.forEach((file, index) => {
        formData.append(`evidence_${index}`, file)
      })

      const response = await fetch(`/api/repairs/${repairId}/dispute`, {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        onDisputeSubmitted()
        onClose()
      }
    } catch (error) {
      console.error('Erro ao enviar disputa:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
            <h3 className="text-lg font-semibold text-red-900">
              Abrir Disputa
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
        </div>

        <div className="p-6">
      
      <div className="space-y-4">
        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Motivo da Disputa *
          </label>
          <select
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
          >
            <option value="">Selecione um motivo</option>
            {DISPUTE_REASONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Descrição Detalhada *
          </label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={6}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
            placeholder="Descreva detalhadamente o problema que está enfrentando..."
            maxLength={1000}
          />
          <div className="text-sm text-gray-500 mt-1">
            {description.length}/1000 caracteres
          </div>
        </div>

        {/* Desired Outcome */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            O que pretende? *
          </label>
          <select
            value={desiredOutcome}
            onChange={(e) => setDesiredOutcome(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
          >
            <option value="">Selecione o resultado desejado</option>
            {DISPUTE_OUTCOMES.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Evidence Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Evidências (opcional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
            <input
              type="file"
              multiple
              accept="image/*,.pdf"
              onChange={handleFileUpload}
              className="hidden"
              id="evidence-upload"
            />
            <label
              htmlFor="evidence-upload"
              className="cursor-pointer flex flex-col items-center"
            >
              <Upload className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-600">
                Clique para adicionar fotos ou documentos
              </span>
              <span className="text-xs text-gray-500 mt-1">
                Máximo 5 arquivos (JPG, PNG, PDF)
              </span>
            </label>
          </div>
          
          {evidence.length > 0 && (
            <div className="mt-3 space-y-2">
              {evidence.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <span className="text-sm text-gray-700 truncate">{file.name}</span>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remover
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <strong>Importante:</strong> Ao abrir uma disputa, nossa equipe irá analisar o caso e mediar a situação entre você e o prestador de serviço. Este processo pode levar até 7 dias úteis para resolução.
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            disabled={!reason || !description.trim() || !desiredOutcome || isSubmitting}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Enviando...' : 'Abrir Disputa'}
          </button>
        </div>
        </div>
      </div>
    </div>
  )
}
