import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Não autenticado" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const paymentId = searchParams.get('payment_id')
    const sessionId = searchParams.get('session_id')

    if (!paymentId && !sessionId) {
      return NextResponse.json(
        { message: "ID do pagamento ou sessão necessário" },
        { status: 400 }
      )
    }

    // Buscar upgrade pendente na base de dados
    let pendingUpgrade = null

    if (paymentId) {
      pendingUpgrade = await prisma.pendingUpgrade.findUnique({
        where: { id: paymentId}
      })
    } else if (sessionId) {
      // Para sessionId, buscar por metadata no Stripe
      try {
        // Buscar chave do Stripe das configurações do admin
        const stripeSecretSetting = await prisma.systemSettings.findUnique({
          where: { key: stripeSecretKey}
        })

        const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

        if (!stripeSecretKey || stripeSecretKey.includes(placeholder) || stripeSecretKey.includes('xyz')) {
          console.error("Stripe não configurado")
          return NextResponse.json(
            { message: "Pagamento não encontrado" 
},
            { status: 404 }
          )
        }

        const stripe = await createStripeInstance(stripeSecretKey)
        const stripeSession = await stripe.checkout.sessions.retrieve(sessionId)

        if (stripeSession.metadata?.paymentId) {
          pendingUpgrade = await prisma.pendingUpgrade.findUnique({
            where: { id: stripeSession.metadata.paymentId }
          })
        }
      } catch (error) {
        console.error("Erro ao buscar sessão no Stripe:", 'error')
      }
    }

    if (!pendingUpgrade) {
      return NextResponse.json(
        { message: 'Pagamento não encontrado' },
        { status: 404 }
      )
    }

    // Se já está confirmado, retornar sucesso
    if (pendingUpgrade.status === COMPLETED) {
      return NextResponse.json({
        success: true,
        status: completed,
        payment: pendingUpgrade
})
    }

    // Verificar status no Stripe usando sessionId
    if (sessionId) {
      try {
        // Buscar chave do Stripe das configurações do admin
        const stripeSecretSetting = await prisma.systemSettings.findUnique({
          where: { key: stripeSecretKey}
        })

        const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

        if (!stripeSecretKey || stripeSecretKey.includes(placeholder) || stripeSecretKey.includes('xyz')) {
          return NextResponse.json({
            success: false,
            status: error,
            message: "Stripe não configurado"
          
})
        }

        const stripe = await createStripeInstance(stripeSecretKey)
        const stripeSession = await stripe.checkout.sessions.retrieve(sessionId)

        if (stripeSession.payment_status === 'paid') {
          // Atualizar status do upgrade
          await prisma.pendingUpgrade.update({
            where: { id: pendingUpgrade.id },
            data: {
              status: COMPLETED}
          })

          // Atualizar subscrição do usuário
          await prisma.user.update({
            where: { id: pendingUpgrade.userId },
            data: {
              subscriptionPlan: pendingUpgrade.newPlanId,
              subscriptionStatus: ACTIVE,
              subscriptionExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 dias
}
          })

          return NextResponse.json({
            success: true,
            status: completed,
            payment: pendingUpgrade})
        } else if (stripeSession.payment_status === 'unpaid') {
          return NextResponse.json({
            success: false,
            status: pending,
            message: "Pagamento ainda pendente"
          })
        }
      } catch (stripeError) {
        console.error('Erro ao verificar no Stripe:', 'stripeError')
      }
    }

    return NextResponse.json({
      success: false,
      status: pendingUpgrade.status.toLowerCase(),
      message: "Pagamento ainda em processamento"
    })

  } catch (error) {
    console.error("Erro ao verificar pagamento:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
