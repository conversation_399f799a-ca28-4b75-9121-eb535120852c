'use client'

import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Trophy, Star, Users, Gift, TrendingUp, Award } from 'lucide-react'
import ClientReferralWidget from '@/components/viral/ClientReferralWidget'
import ClientBadgeShowcase from '@/components/viral/ClientBadgeShowcase'
import ClientShareWidget from '@/components/viral/ClientShareWidget'

export default function ClientProgressPage() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!session?.user) {
    redirect('/auth/signin')
  }

  // Verificar se é cliente
  if (session.user.role !== cliente && session.user.role !== 'customer') {
    redirect('/')
  
}

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <Trophy className="w-10 h-10 text-white" />
              </div>
            </div>
            <h1 className="text-4xl font-bold mb-4">
              O Teu Progresso
            </h1>
            <p className="text-xl text-indigo-100 max-w-2xl mx-auto">
              Acompanha as tuas conquistas, refere amigos e ganha recompensas exclusivas na Revify
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-xl shadow-sm border p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">3</div>
            <div className="text-sm text-gray-600">
              Amigos Referidos
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Gift className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">€15</div>
            <div className="text-sm text-gray-600">
              Total Poupado
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Award className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">5</div>
            <div className="text-sm text-gray-600">
              Badges Conquistados
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border p-6 text-center">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">Nível 2</div>
            <div className="text-sm text-gray-600">
              Nível Atual
            </div>
          </div>
        </div>

        {/* Main Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Referral Widget */}
          <div className="lg:col-span-1">
            <ClientReferralWidget />
          </div>

          {/* Badge Showcase */}
          <div className="lg:col-span-1">
            <ClientBadgeShowcase />
          </div>

          {/* Share Widget */}
          <div className="lg:col-span-1">
            <ClientShareWidget />
          </div>
        </div>

        {/* Progress Section */}
        <div className="bg-white rounded-xl shadow-sm border p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Progresso para Próximo Nível
            </h2>
            <div className="text-sm text-gray-600">
              3/5 referrals para Nível 3
            </div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 h-4 rounded-full" style={{ width: '60%' }}></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 mb-1">Nível 3</div>
              <div className="text-sm text-gray-600">
                15% desconto permanente
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 mb-1">Nível 4</div>
              <div className="text-sm text-gray-600">
                20% desconto + badge especial
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 mb-1">Nível 5</div>
              <div className="text-sm text-gray-600">
                25% desconto + acesso VIP
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Atividade Recente
          </h2>

          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <Gift className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  Referral completado
                </div>
                <div className="text-sm text-gray-600">
                  João Silva usou o teu código e ganhou 10% de desconto
                </div>
              </div>
              <div className="text-sm text-gray-500">2h atrás</div>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-yellow-50 rounded-lg">
              <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  Novo badge conquistado
                </div>
                <div className="text-sm text-gray-600">
                </div>
              </div>
              <div className="text-sm text-gray-500">1d atrás</div>
            </div>

            <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <Users className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  Amigo registado
                </div>
                <div className="text-sm text-gray-600">
                  Maria Santos juntou-se à Revify usando o teu link
                </div>
              </div>
              <div className="text-sm text-gray-500">3d atrás</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
