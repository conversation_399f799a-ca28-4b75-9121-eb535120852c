import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { action, response, refundAmount } = await request.json()

    if (!action || !['ACCEPT', 'ESCALATE'].includes(action)) {
      return NextResponse.json(
        { message: 'Ação inválida' },
        { status: 400 }
      )
    }

    // Verificar se a disputa existe e pertence a uma reparação da loja
    const dispute = await prisma.dispute.findFirst({
      where: {
        id: resolvedParams.id,
        repair: {
          repairShopId: session.user.id
        }
      },
      include: {
        repair: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            repairShop: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    if (!dispute) {
      return NextResponse.json(
        { message: 'Disputa não encontrada' },
        { status: 404 }
      )
    }

    if (dispute.status !== 'OPEN') {
      return NextResponse.json(
        { message: 'Esta disputa já foi processada' },
        { status: 400 }
      )
    }

    let newStatus = 'OPEN'
    let notificationTitle = ''
    let notificationMessage = ''

    if (action === 'ACCEPT') {
      newStatus = 'RESOLVED'
      notificationTitle = 'Disputa Resolvida'
      notificationMessage = `A loja aceitou a sua reclamação. ${response ? `Resposta: "${response}"` : ''}`
      
      // Se há valor de reembolso, processar
      if (refundAmount && refundAmount > 0) {
        // Aqui você implementaria a lógica de reembolso
        // Por exemplo, criar um registro de reembolso pendente
        await prisma.refund.create({
          data: {
            repairId: dispute.repairId,
            amount: refundAmount,
            status: 'PENDING',
            reason: 'DISPUTE_RESOLUTION'
          }
        })
      }

      // Atualizar status da reparação se necessário
      await prisma.repair.update({
        where: { id: dispute.repairId },
        data: { status: 'COMPLETED' } // ou outro status apropriado
      })

    } else if (action === 'ESCALATE') {
      newStatus = 'ESCALATED'
      notificationTitle = 'Disputa Escalada'
      notificationMessage = `A loja escalou a disputa para mediação da Revify. ${response ? `Justificação: "${response}"` : ''}`
    }

    // Atualizar a disputa
    const updatedDispute = await prisma.dispute.update({
      where: { id: resolvedParams.id },
      data: {
        status: newStatus,
        shopResponse: response,
        shopResponseAt: new Date(),
        refundAmount: refundAmount || null
      }
    })

    // Criar notificação para o cliente
    await prisma.notification.create({
      data: {
        userId: dispute.repair.customer.id,
        title: notificationTitle,
        message: notificationMessage,
        type: 'DISPUTE_UPDATE',
        metadata: {
          disputeId: dispute.id,
          repairId: dispute.repairId,
          action,
          response,
          refundAmount
        }
      }
    })

    // Se escalado, notificar admins
    if (action === 'ESCALATE') {
      const admins = await prisma.user.findMany({
        where: { role: 'ADMIN' },
        select: { id: true }
      })

      const adminNotifications = admins.map(admin => ({
        userId: admin.id,
        title: 'Disputa Escalada para Mediação',
        message: `Disputa #${dispute.id.slice(-8)} foi escalada pela loja ${dispute.repair.repairShop.name}. Mediação necessária.`,
        type: 'DISPUTE_ESCALATED',
        metadata: {
          disputeId: dispute.id,
          repairId: dispute.repairId,
          customerId: dispute.repair.customer.id,
          repairShopId: dispute.repair.repairShop.id,
          shopResponse: response
        }
      }))

      await prisma.notification.createMany({
        data: adminNotifications
      })
    }

    return NextResponse.json({
      message: 'Resposta à disputa enviada com sucesso',
      dispute: {
        id: updatedDispute.id,
        status: updatedDispute.status,
        shopResponse: updatedDispute.shopResponse,
        refundAmount: updatedDispute.refundAmount
      }
    })

  } catch (error) {
    console.error('Erro ao responder à disputa:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
