'use client'

import Link from 'next/link'
import { Shield, CheckCircle, Clock, Star,
  Wrench, Smartphone, Laptop, Tablet,
  ArrowRight, Phone, Award, Zap } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import MainHeader from '@/components/MainHeader'
import Footer from '@/components/Footer'
export default function ExtendedWarrantyPage() {
  const { t } = useTranslation()

  const plans = [
    {
      name: 'Básico',
      duration: '12 meses',
      price: '€29',
      color: blue,
      features: [
        Extensão de garantia por 12 meses,
        'Cobertura para defeitos de fabrico',
        Reparação gratuita em caso de falha,
        Suporte técnico prioritário,
        Sem limite de reparações
      ]
    },
    {
      name: Premium,
      duration: '24 meses',
      price: '€49',
      color: purple,
      popular: true,
      features: [
        'Extensão de garantia por 24 meses',
        'Cobertura para defeitos de fabrico',
        'Cobertura para danos acidentais (1x)',
        Reparação gratuita em caso de falha,
        Suporte técnico prioritário,
        Sem limite de reparações,
        Substituição temporária
      ]
    },
    {
      name: Ultimate,
      duration: '36 meses',
      price: '€79',
      color: emerald,
      features: [
        'Extensão de garantia por 36 meses',
        'Cobertura para defeitos de fabrico',
        'Cobertura para danos acidentais (2x)',
        Cobertura para danos por líquidos,
        Reparação gratuita em caso de falha,
        Suporte técnico 24/7,
        'Sem limite de reparações',
        Substituição temporária,
        Recolha e entrega gratuita
      ]
    }
  ]

  const deviceTypes = [
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: Smartphones,
      description: 'iPhone, Samsung, Huawei e todas as marcas',
      coverage: [Ecrã, 'Bateria', Câmaras, 'Motherboard']
    },
    {
      icon: <Laptop className="w-8 h-8" />,
      title: Laptops,
      description: 'MacBook, Dell, HP, Lenovo e outros',
      coverage: [Ecrã, 'Teclado', 'Bateria', Disco rígido]
    },
    {
      icon: <Tablet className="w-8 h-8" />,
      title: Tablets,
      description: 'iPad, Galaxy Tab, Surface e mais',
      coverage: [Ecrã táctil, 'Bateria', 'Conectores', 'Motherboard']
    }
  ]

  const benefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Proteção Total',
      description: 'Cobertura completa contra defeitos e danos acidentais'
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: 'Reparação Rápida',
      description: 'Prioridade máxima em todas as reparações'},
    {
      icon: <Award className="w-6 h-6" />,
      title: 'Qualidade Garantida',
      description: 'Peças originais e técnicos certificados'},
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Suporte 24/7',
      description: 'Assistência técnica disponível a qualquer hora'}
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('extendedWarrantyTitle')}
            </h1>
            <p className="text-xl text-indigo-100 max-w-3xl mx-auto mb-8">
              {t('extendedWarrantySubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#plans"
                className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Shield className="w-5 h-5" />
                <span>Ver Planos</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/contactos"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Falar Connosco</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Plans Section */}
      <section id="plans" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Escolha o Seu Plano
            </h2>
            <p className="text-xl text-gray-600">Proteção personalizada para cada necessidade</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all transform hover:-translate-y-2 border-2 ${
                  plan.popular ? 'border-purple-500' : 'border-gray-200'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-bold">
                      Mais Popular
                    </div>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="text-4xl font-bold text-gray-900 mb-2">{plan.price}</div>
                  <div className="text-gray-600">{plan.duration} de proteção</div>
                </div>
                
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-3">
                      <CheckCircle className={`w-5 h-5 text-${plan.color}-500 flex-shrink-0`} />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <button className={`w-full bg-${plan.color}-600 text-white py-4 rounded-2xl font-bold text-lg hover:bg-${plan.color}-700 transition-colors`}>
                  Escolher Plano
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Device Coverage */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Cobertura para Todos os Dispositivos</h2>
            <p className="text-xl text-gray-600">Proteção especializada para cada tipo de equipamento</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {deviceTypes.map((device, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-indigo-100 rounded-2xl flex items-center justify-center mb-6 text-indigo-600">
                  {device.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{device.title}</h3>
                <p className="text-gray-600 mb-6">{device.description}</p>
                <div className="space-y-3">
                  <div className="text-sm font-semibold text-gray-900 mb-2">Cobertura inclui:</div>
                  {device.coverage.map((item, idx) => (
                    <div key={idx} className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Vantagens da Garantia Estendida</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-6 text-indigo-600">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Perguntas Frequentes
            </h2>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Quando posso adquirir a garantia estendida?</h3>
              <p className="text-gray-600">A garantia estendida pode ser adquirida até 30 dias após a reparação do seu dispositivo.</p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">O que está coberto pelos danos acidentais?</h3>
              <p className="text-gray-600">Quedas, impactos, danos por líquidos e outros acidentes que possam danificar o seu dispositivo.</p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Como funciona a substituição temporária?</h3>
              <p className="text-gray-600">Fornecemos um dispositivo temporário enquanto o seu está a ser reparado, sem custos adicionais.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Proteja o Seu Dispositivo Hoje</h2>
          <p className="text-xl text-gray-300 mb-8">Tranquilidade total com a nossa garantia estendida</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="#plans"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Shield className="w-5 h-5" />
              <span>Escolher Plano</span>
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
