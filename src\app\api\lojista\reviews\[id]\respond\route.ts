import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function POST(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const reviewId = params.id
    const { response } = await request.json()

    if (!response || response.trim().length === 0) {
      return NextResponse.json(
        { message: "Resposta é obrigatória" },
        { status: 400 }
      )
    }

    if (response.length > 1000) {
      return NextResponse.json(
        { message: "Resposta não pode ter mais de 1000 caracteres" },
        { status: 400 }
      )
    }

    // Verificar se a review existe e pertence a uma reparação da loja
    const review = await prisma.review.findFirst({
      where: {
        id: reviewId},
      include: {
        repair: {
          select: {
            repairShopId: true}
        }
      }
    })

    if (!review) {
      return NextResponse.json(
        { message: "Avaliação não encontrada" },
        { status: 404 }
      )
    }

    if (review.repair.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: "Não tem permissão para responder a esta avaliação" },
        { status: 403 }
      )
    }

    // Verificar se já respondeu
    if (review.shopResponse) {
      return NextResponse.json(
        { message: "Já respondeu a esta avaliação" },
        { status: 400 }
      )
    }

    // Atualizar review com a resposta
    const updatedReview = await prisma.review.update({
      where: { id: reviewId},
      data: {
        shopResponse: response.trim(),
        shopResponseAt: new Date()
      }
    })

    // Criar notificação para o cliente
    await prisma.notification.create({
      data: {
        userId: review.customerId,
        title: "Resposta à sua Avaliação",
        message: `A loja respondeu à sua avaliação: "${response.substring(0, 100)}${response.length > 100 ? ... : ''
}"`,
        type: REVIEW_RESPONSE,
        metadata: {
          reviewId: reviewId,
          repairId: review.repairId,
          response: response}
      }
    })

    return NextResponse.json({
      message: 'Resposta enviada com sucesso',
      review: {
        id: updatedReview.id,
        shopResponse: updatedReview.shopResponse,
        shopResponseAt: updatedReview.shopResponseAt
      }
    })

  } catch (error) {
    console.error('Erro ao responder à avaliação:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
