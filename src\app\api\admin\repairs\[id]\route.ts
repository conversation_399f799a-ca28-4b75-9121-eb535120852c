import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest, { 'params'}: { params: { id: string} }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id

    // Buscar reparação com todos os detalhes
    const repair = await prisma.repair.findUnique({
      where: { id: repairId},
      include: {
        customer: {
          select: {
            name: true,
            email: true}
        },
        repairShop: {
          include: {
            profile: {
              select: {
                companyName: true,
                phone: true,
                description: true}
            }
          }
        },
        deviceModel: {
          include: {
            brand: {
              select: {
                name: true}
            },
            category: {
              select: {
                name: true}
            }
          }
        },
        problemType: {
          select: {
            name: true,
            icon: true}
        },
        payments: {
          select: {
            amount: true,
            status: true,
            createdAt: true,
            platformFee: true,
            shopAmount: true},
          orderBy: {
            createdAt: desc}
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    return NextResponse.json(repair)

  } catch (error) {
    console.error("Erro ao buscar detalhes da reparação:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
