'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { ArrowLeft, Edit, Package, Calendar, Truck } from 'lucide-react'
import Link from 'next/link'

interface SparePart {
  id: string
  name: string
  description: string
  sku: string
  price: number
  stock: number
  images: string[]
  category: {
    id: string
    name: string
  }
  brand?: {
    id: string
    name: string
  }
  deviceModel?: {
    id: string
    name: string
  }
  availability: boolean
  deliveryTime: number
  compatibility: string[]
  createdAt: string
  updatedAt: string
}

export default function SparePartDetailsPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [part, setPart] = useState<SparePart | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchPart()
    }
  }, [params.id])

  const fetchPart = async () => {
    try {
      const response = await fetch(`/api/admin/spare-parts/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setPart(data.part)
      } else if (response.status === 404) {
        router.push('/admin/spare-parts')
      }
    } catch (error) {
      console.error('Erro ao buscar peça:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleAvailability = async () => {
    if (!part) return

    try {
      const response = await fetch(`/api/admin/spare-parts/${part.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ availability: !part.availability })
      })

      if (response.ok) {
        setPart({ ...part, availability: !part.availability })
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar disponibilidade')
      }
    } catch (error) {
      console.error('Erro ao atualizar disponibilidade:', error)
      alert('Erro ao atualizar disponibilidade')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!part) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Peça não encontrada</h3>
          <p className="mt-1 text-sm text-gray-500">A peça solicitada não existe.</p>
          <div className="mt-6">
            <Link
              href="/admin/spare-parts"
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/spare-parts"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Detalhes da Peça</h1>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={toggleAvailability}
                className={`px-4 py-2 rounded-lg font-medium ${
                  part.availability
                    ? 'bg-red-100 text-red-800 hover:bg-red-200'
                    : 'bg-green-100 text-green-800 hover:bg-green-200'
                }`}
              >
                {part.availability ? 'Desativar' : 'Ativar'}
              </button>
              <Link
                href={`/admin/spare-parts/${part.id}/edit`}
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
              >
                <Edit className="w-4 h-4 mr-2" />
                Editar
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Imagens */}
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Imagens</h3>
              {part.images && part.images.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {part.images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`${part.name} - Imagem ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg border border-gray-200"
                    />
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-48 bg-gray-100 rounded-lg">
                  <Package className="w-12 h-12 text-gray-400" />
                </div>
              )}
            </div>

            {/* Compatibilidade */}
            {part.compatibility && part.compatibility.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Compatibilidade</h3>
                <div className="flex flex-wrap gap-2">
                  {part.compatibility.map((item, index) => (
                    <span
                      key={index}
                      className="inline-flex px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Informações */}
          <div className="space-y-6">
            {/* Informações Básicas */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Informações Básicas</h3>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  part.availability
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {part.availability ? 'Ativo' : 'Inativo'}
                </span>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-xl font-bold text-gray-900">{part.name}</h4>
                  {part.description && (
                    <p className="text-gray-600 mt-1">{part.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">SKU</label>
                    <p className="text-sm text-gray-900 font-mono">{part.sku}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Preço</label>
                    <p className="text-lg font-bold text-gray-900">€{Number(part.price).toFixed(2)}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stock</label>
                    <p className={`text-sm font-medium ${
                      part.stock > 10 ? 'text-green-600' :
                      part.stock > 0 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {part.stock} unidades
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tempo de Entrega</label>
                    <p className="text-sm text-gray-900 flex items-center">
                      <Truck className="w-4 h-4 mr-1" />
                      {part.deliveryTime} dias
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Categorização */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categorização</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Categoria</label>
                  <p className="text-sm text-gray-900">{part.category.name}</p>
                </div>
                {part.brand && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Marca</label>
                    <p className="text-sm text-gray-900">{part.brand.name}</p>
                  </div>
                )}
                {part.deviceModel && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Modelo</label>
                    <p className="text-sm text-gray-900">{part.deviceModel.name}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Datas */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Histórico</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                  <div>
                    <label className="block text-xs font-medium text-gray-700">Criado em</label>
                    <p className="text-sm text-gray-900">
                      {new Date(part.createdAt).toLocaleDateString('pt-PT', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                  <div>
                    <label className="block text-xs font-medium text-gray-700">Última atualização</label>
                    <p className="text-sm text-gray-900">
                      {new Date(part.updatedAt).toLocaleDateString('pt-PT', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
