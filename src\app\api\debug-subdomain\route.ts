import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'No session found' },
        { status: 401 }
      )
    }

    // Buscar perfil do usuário
    const profile = await prisma.profile.findUnique({
      where: { userId: session.user.id },
      select: {
        customSubdomain: true,
        customDomain: true,
        phone: true,
        userId: true
      }
    })

    // Buscar dados do usuário
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        subscription: {
          include: {
            plan: {
              select: {
                name: true,
                miniStore: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      debug: {
        session: {
          id: session.user.id,
          email: session.user.email,
          role: session.user.role
        },
        profile,
        user,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Debug subdomain error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: 'No session found' },
        { status: 401 }
      )
    }

    const { subdomain } = await request.json()

    if (!subdomain) {
      return NextResponse.json(
        { error: 'Subdomain is required' },
        { status: 400 }
      )
    }

    // Validar subdomínio
    const subdomainRegex = /^[a-z0-9-]+$/
    if (!subdomainRegex.test(subdomain)) {
      return NextResponse.json(
        { error: 'Subdomínio deve conter apenas letras minúsculas, números e hífens' },
        { status: 400 }
      )
    }

    // Verificar se já existe
    const existing = await prisma.profile.findFirst({
      where: {
        customSubdomain: subdomain,
        userId: { not: session.user.id }
      }
    })

    if (existing) {
      return NextResponse.json(
        { error: 'Este subdomínio já está em uso' },
        { status: 400 }
      )
    }

    // Atualizar/criar perfil
    const profile = await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        customSubdomain: subdomain
      },
      create: {
        userId: session.user.id,
        phone: '',
        address: '',
        city: '',
        postalCode: '',
        country: 'PT',
        customSubdomain: subdomain
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Subdomínio configurado com sucesso',
      profile: {
        customSubdomain: profile.customSubdomain
      }
    })

  } catch (error) {
    console.error('Debug subdomain POST error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
