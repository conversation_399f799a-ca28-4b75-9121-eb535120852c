#!/usr/bin/env node

/**
 * Script para conversão automática em massa de textos hardcoded para AutoTranslate
 * Processa múltiplos arquivos e faz as substituições automaticamente
 */

const fs = require('fs');
const path = require('path');

// Textos comuns que devem ser traduzidos automaticamente
const autoTranslateTexts = [
  // Navigation & UI
  'Entrar', 'Registar', 'Sair', 'Voltar', 'Continuar', 'Confirmar', 'Cancelar',
  'Salvar', 'Guardar', 'Editar', 'Eliminar', 'Adicionar', 'Remover', 'Ver',
  'Pesquisar', 'Filtrar', 'Ordenar', 'Fechar', 'Abrir', 'Enviar',
  
  // Business terms
  'Cliente', 'Técnico', 'Estafeta', 'Lojista', 'Administrador',
  'Reparação', 'Reparações', 'Dispositivo', 'Dispositivos', 'Produto', 'Produtos',
  'Serviço', 'Serviços', 'Encomenda', 'Encomendas', 'Carrinho', 'Pagamento',
  
  // Status
  'Ativo', 'Inativo', 'Pendente', 'Confirmado', 'Em Progresso', 'Concluído', 'Cancelado',
  'Disponível', 'Indisponível', 'Em Stock', 'Sem Stock',
  
  // Forms
  'Nome', 'Email', 'Telefone', 'Morada', 'Cidade', 'Código Postal', 'País',
  'Descrição', 'Preço', 'Quantidade', 'Total', 'Subtotal', 'Data', 'Estado',
  
  // Common phrases
  'Orçamentos gratuitos', 'Garantia de 6 meses', 'Acompanhamento em tempo real',
  'Técnicos qualificados', 'Preços transparentes', 'Entrega rápida',
  'Suporte 24/7', 'Satisfação garantida'
];

// Arquivos para processar
const filesToProcess = [
  'src/app/page.tsx',
  'src/components/Footer.tsx',
  'src/components/ModernFooter.tsx',
  'src/app/auth/signin/page.tsx',
  'src/app/auth/signup/page.tsx',
  'src/app/lojista/dashboard/page.tsx',
  'src/app/admin/dashboard/page.tsx'
];

function addAutoTranslateImport(content) {
  // Verificar se já tem o import
  if (content.includes("import AutoTranslate from '@/components/ui/AutoTranslate'")) {
    return content;
  }
  
  // Encontrar onde adicionar o import
  const lines = content.split('\n');
  let importIndex = -1;
  
  // Procurar por outros imports do React ou componentes
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes("import") && 
        (lines[i].includes("'react'") || 
         lines[i].includes("'@/components") || 
         lines[i].includes("'next/"))) {
      importIndex = i;
    }
  }
  
  if (importIndex !== -1) {
    lines.splice(importIndex + 1, 0, "import AutoTranslate from '@/components/ui/AutoTranslate'");
    return lines.join('\n');
  }
  
  return content;
}

function convertTextToAutoTranslate(content) {
  let updatedContent = content;
  
  autoTranslateTexts.forEach(text => {
    // Padrões para encontrar o texto
    const patterns = [
      // Texto entre aspas duplas: "Texto"
      new RegExp(`"${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"`, 'g'),
      // Texto entre aspas simples: 'Texto'
      new RegExp(`'${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}'`, 'g'),
      // Texto direto em JSX: >Texto<
      new RegExp(`>${text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}<`, 'g')
    ];
    
    patterns.forEach(pattern => {
      const replacement = pattern.source.includes('>') && pattern.source.includes('<') 
        ? `><AutoTranslate text="${text}" /><`
        : `<AutoTranslate text="${text}" />`;
        
      // Só substituir se não estiver já dentro de um AutoTranslate
      const matches = updatedContent.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const index = updatedContent.indexOf(match);
          const before = updatedContent.substring(Math.max(0, index - 50), index);
          
          // Não substituir se já estiver dentro de AutoTranslate
          if (!before.includes('<AutoTranslate')) {
            if (match.includes('>') && match.includes('<')) {
              updatedContent = updatedContent.replace(match, `><AutoTranslate text="${text}" /><`);
            } else {
              // Para strings em props, manter as aspas
              updatedContent = updatedContent.replace(match, `<AutoTranslate text="${text}" />`);
            }
          }
        });
      }
    });
  });
  
  return updatedContent;
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ Arquivo não encontrado: ${filePath}`);
    return;
  }
  
  console.log(`🔄 Processando: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Adicionar import se necessário
  content = addAutoTranslateImport(content);
  
  // Converter textos
  content = convertTextToAutoTranslate(content);
  
  if (content !== originalContent) {
    // Fazer backup
    fs.writeFileSync(`${filePath}.backup`, originalContent);
    
    // Salvar arquivo modificado
    fs.writeFileSync(filePath, content);
    console.log(`✅ ${filePath} - Convertido com sucesso (backup criado)`);
  } else {
    console.log(`ℹ️  ${filePath} - Nenhuma alteração necessária`);
  }
}

console.log('🚀 Iniciando conversão automática para AutoTranslate...\n');

filesToProcess.forEach(processFile);

console.log('\n✨ Conversão concluída!');
console.log('📋 Próximos passos:');
console.log('1. Verificar os arquivos modificados');
console.log('2. Testar a aplicação');
console.log('3. Remover arquivos .backup se tudo estiver OK');
console.log('4. Fazer commit das alterações');
