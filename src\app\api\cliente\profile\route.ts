import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!profile) {
      return NextResponse.json({
        customerName: session.user.name || '',
        customerPhone: '',
        customerNif: '',
        addresses: []
      })
    }

    return NextResponse.json({
      customerName: profile.customerName || session.user.name || '',
      customerPhone: profile.phone || '',
      customerNif: profile.customerNif || '',
      addresses: profile.addresses || []
    })

  } catch (error) {
    console.error('Erro ao buscar perfil do cliente:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()
    const { customerName, customerPhone, customerNif, addresses } = data

    // Validações básicas
    if (!customerName || !customerPhone) {
      return NextResponse.json(
        { message: Nome e telefone são obrigatórios 
},
        { status: 400 }
      )
    }

    // Verificar se já existe perfil
    const existingProfile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    let profile
    if (existingProfile) {
      // Atualizar perfil existente
      profile = await prisma.profile.update({
        where: {
          userId: session.user.id
        },
        data: {
          customerName,
          phone: customerPhone,
          customerNif,
          addresses: addresses || []
        }
      })
    } else {
      // Criar novo perfil
      profile = await prisma.profile.create({
        data: {
          userId: session.user.id,
          customerName,
          phone: customerPhone,
          customerNif,
          addresses: addresses || []
        }
      })
    }

    return NextResponse.json({
      message: Perfil atualizado com sucesso,
      profile: {
        customerName: profile.customerName,
        customerPhone: profile.phone,
        customerNif: profile.customerNif,
        addresses: profile.addresses
      
}
    })

  } catch (error) {
    console.error('Erro ao salvar perfil do cliente:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
