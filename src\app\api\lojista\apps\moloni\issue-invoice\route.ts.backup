import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { invoiceId } = await request.json()

    if (!invoiceId) {
      return NextResponse.json(
        { message: 'ID da fatura é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar configuração do Moloni
    const config = await prisma.$queryRaw`
      SELECT * FROM app_configs 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = 'moloni'
    `

    if (!config || config.length === 0) {
      return NextResponse.json(
        { message: 'Configuração Moloni não encontrada' },
        { status: 404 }
      )
    }

    const moloniConfig = config[0].settings

    // Verificar se é uma reparação ou venda
    const repair = await prisma.$queryRaw`
      SELECT r.*, u.name as customer_name, u.email as customer_email
      FROM repairs r
      JOIN users u ON r."customerId" = u.id
      WHERE r.id = ${invoiceId} AND r."repairShopId" = ${session.user.id}
    `

    const sale = await prisma.$queryRaw`
      SELECT o.*, u.name as customer_name, u.email as customer_email
      FROM orders o
      JOIN users u ON o."customerId" = u.id
      JOIN order_items oi ON o.id = oi."orderId"
      JOIN marketplace_products mp ON oi."productId" = mp.id
      WHERE o.id = ${invoiceId} AND mp."sellerId" = ${session.user.id}
      GROUP BY o.id, u.name, u.email
    `

    const invoice = repair.length > 0 ? repair[0] : sale.length > 0 ? sale[0] : null

    if (!invoice) {
      return NextResponse.json(
        { message: 'Fatura não encontrada' },
        { status: 404 }
      )
    }

    // Preparar dados da fatura para Moloni
    const invoiceData = {
      company_id: moloniConfig.companyId,
      document_set_id: moloniConfig.documentSeries,
      customer: {
        name: invoice.customer_name,
        email: invoice.customer_email
      },
      products: repair.length > 0 ? [
        {
          name: `Reparação - ${invoice.deviceBrand} ${invoice.deviceModel}`,
          description: invoice.problemDescription,
          price: Number(invoice.totalPrice),
          qty: 1
        }
      ] : [
        {
          name: 'Venda Marketplace',
          description: 'Venda através do marketplace',
          price: Number(invoice.total),
          qty: 1
        }
      ]
    }

    try {
      // Simular emissão de fatura no Moloni
      // Em produção, aqui faria uma chamada real à API do Moloni
      console.log('Emitindo fatura no Moloni:', invoiceData)

      // Marcar como fatura emitida
      if (repair.length > 0) {
        await prisma.$queryRaw`
          UPDATE repairs 
          SET "invoiceIssued" = true, "invoiceIssuedAt" = NOW()
          WHERE id = ${invoiceId}
        `
      } else {
        await prisma.$queryRaw`
          UPDATE orders 
          SET "invoiceIssued" = true, "invoiceIssuedAt" = NOW()
          WHERE id = ${invoiceId}
        `
      }

      return NextResponse.json({
        message: 'Fatura emitida com sucesso',
        moloniId: `DEMO${Date.now()}`,
        invoiceNumber: `${moloniConfig.documentSeries}${Date.now()}`
      })

    } catch (moloniError) {
      console.error('Erro na API Moloni:', moloniError)
      return NextResponse.json(
        { message: 'Erro ao emitir fatura no Moloni' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Erro ao emitir fatura:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
