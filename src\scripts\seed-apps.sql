-- Inserir apps de exemplo na tabela app_definitions
INSERT INTO app_definitions (
  "id", "appId", name, description, category,
  "isPaid", "monthlyPrice", "hasTrialPeriod", "trialDays",
  features, "requiredPlans", "isActive", "isPopular",
  "createdAt", "updatedAt"
) VALUES 
(
  gen_random_uuid(), 'newsletter-pro', 'Newsletter Pro', 
  'Sistema avançado de email marketing com templates profissionais e automação.',
  'marketing', true, 14.99, true, 30,
  '["Templates profissionais", "Automação de campanhas", "Segmentação avançada", "Analytics detalhados", "A/B Testing"]',
  '["REVY PRO"]', true, true, NOW(), NOW()
),
(
  gen_random_uuid(), 'moloni', 'Moloni', 
  'Integração completa com Moloni para emissão automática de faturas e gestão fiscal.',
  'finance', false, 0, false, 0,
  '["Emissão automática de faturas", "Sincronização de produtos", "Relatórios fiscais", "Gestão de clientes", "Backup automático"]',
  '["REVY BASIC", "REVY PRO"]', true, false, NOW(), NOW()
),
(
  gen_random_uuid(), 'crm-advanced', 'CRM Avançado', 
  'Sistema completo de gestão de relacionamento com clientes, com automação e analytics.',
  'crm', true, 9.99, true, 30,
  '["Gestão de leads", "Automação de follow-up", "Analytics avançados", "Integração WhatsApp", "Relatórios personalizados"]',
  '["REVY PRO"]', true, false, NOW(), NOW()
),
(
  gen_random_uuid(), 'inventory-manager', 'Gestor de Stock', 
  'Controlo avançado de inventário com alertas automáticos e previsões de procura.',
  'productivity', true, 7.99, true, 15,
  '["Controlo de stock em tempo real", "Alertas de stock baixo", "Previsões de procura", "Códigos de barras", "Relatórios de movimento"]',
  '["REVY PRO"]', true, false, NOW(), NOW()
),
(
  gen_random_uuid(), 'whatsapp-business', 'WhatsApp Business', 
  'Integração com WhatsApp Business para comunicação automatizada com clientes.',
  'communication', true, 12.99, true, 7,
  '["Mensagens automáticas", "Templates aprovados", "Chatbot inteligente", "Integração com CRM", "Analytics de conversas"]',
  '["REVY PRO"]', true, true, NOW(), NOW()
),
(
  gen_random_uuid(), 'analytics-pro', 'Analytics Pro', 
  'Relatórios avançados e dashboards personalizáveis para análise de negócio.',
  'analytics', true, 5.99, true, 30,
  '["Dashboards personalizáveis", "Relatórios automáticos", "Métricas de performance", "Exportação de dados", "Alertas inteligentes"]',
  '["REVY PRO"]', true, false, NOW(), NOW()
)
ON CONFLICT ("appId") DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  "monthlyPrice" = EXCLUDED."monthlyPrice",
  "updatedAt" = NOW();
