'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import {
  Wrench,
  Package,
  Euro,
  BarChart3,
  Plus,
  Settings,
  Building,
  Users,
  Crown,
  ArrowUp,
  FileText,
  Mail,
  QrCode,
  Calculator,
  Percent,
  Truck
} from 'lucide-react'

interface UserSubscription {
  plan: {
    name: string
  }
  currentPeriodEnd: string
  status: string
}

export default function LojistaDashboard() {
  const { data: session } = useSession()
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  const [installedApps, setInstalledApps] = useState<any[]>([])
  const [dashboardStats, setDashboardStats] = useState({
    totalProducts: 0,
    totalRepairs: 0,
    monthlyRevenue: 0,
    pendingOrders: 0,
    totalCustomers: 0,
    revenueGrowth: 0,
    customersGrowth: 0,
    repairsGrowth: 0
  })

  useEffect(() => {
    if (session?.user) {
      fetchSubscription()
      fetchInstalledApps()
      fetchDashboardStats()
    }
  }, [session])

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/lojista/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscription(data.subscription)
      }
    } catch (error) {
      console.error('Erro ao buscar subscrição:', error)
    }
  }

  const fetchInstalledApps = async () => {
    try {
      const response = await fetch('/api/lojista/apps/installed')
      if (response.ok) {
        const data = await response.json()
        setInstalledApps(data.apps)
      }
    } catch (error) {
      console.error('Erro ao buscar apps instaladas:', error)
    }
  }

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/lojista/dashboard-stats')
      if (response.ok) {
        const data = await response.json()
        setDashboardStats(data.stats)
      }
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error)
    }
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Bom dia'
    if (hour < 18) return 'Boa tarde'
    return 'Boa noite'
  }

  const getUserName = () => {
    return session?.user?.name || 'Lojista'
  }

  const menuItems = [
    {
      title: 'Reparações',
      items: [
        { name: 'Reparações Ativas', href: '/lojista/reparacoes', icon: Wrench, description: 'Gerir reparações em curso' },
        { name: 'Histórico', href: '/lojista/reparacoes/historico', icon: BarChart3, description: 'Reparações concluídas' },
      ]
    },
    {
      title: 'Marketplace',
      items: [
        { name: 'Meus Produtos', href: '/lojista/produtos', icon: Package, description: 'Gerir produtos à venda' },
        { name: 'Adicionar Produto', href: '/lojista/produtos/novo', icon: Plus, description: 'Adicionar novo produto' },
        { name: 'Cupões de Desconto', href: '/lojista/cupoes', icon: Percent, description: 'Criar e gerir cupões' },
        { name: 'Encomendas', href: '/lojista/encomendas', icon: Package, description: 'Gerir encomendas recebidas' },
        { name: 'Configurar Envios', href: '/lojista/configuracoes/envios', icon: Truck, description: 'Configurar custos de envio' },
      ]
    },
    {
      title: 'Premium Features',
      items: [
        { name: 'Reparações Independentes', href: '/lojista/reparacoes/independentes', icon: Wrench, description: 'Reparações walk-in e clientes habituais' },
        { name: 'Loja Online', href: '/lojista/loja-online', icon: Building, description: 'E-commerce próprio com subdomínio' },
      ]
    },
    {
      title: 'Loja de Peças',
      items: [
        { name: 'Comprar Peças', href: '/lojista/loja-pecas', icon: Package, description: 'Comprar peças para reparações' },
        { name: 'Encomendas de Peças', href: '/lojista/encomendas-pecas', icon: Package, description: 'Consultar encomendas de peças' },
      ]
    },
    {
      title: 'Negócio',
      items: [
        { name: 'Clientes', href: '/lojista/clientes', icon: Users, description: 'Gerir clientes e histórico' },
        { name: 'Perfil da Empresa', href: '/lojista/perfil', icon: Building, description: 'Gerir dados da empresa' },
        { name: 'Financeiro', href: '/lojista/financeiro', icon: Euro, description: 'Receitas e pagamentos' },
        { name: 'Configurações', href: '/lojista/configuracoes', icon: Settings, description: 'Configurações da conta' },
      ]
    }
  ]

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-8">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 rounded-2xl p-8 text-white shadow-xl">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-3">
                  {getGreeting()}, {getUserName()}!
                </h1>
                <p className="text-indigo-100 text-lg">
                  Gerir reparações, produtos e o seu negócio numa só plataforma.
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center mb-3">
                  <Crown className="w-6 h-6 mr-2 text-yellow-300" />
                  <span className="text-xl font-semibold">
                    {subscription?.plan?.name || 'Sem Plano'}
                  </span>
                </div>
                <Link
                  href="/lojista/subscricao"
                  className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-xl hover:bg-white/30 transition-all text-sm font-medium border border-white/20"
                >
                  <ArrowUp className="w-4 h-4 mr-2" />
                  Gerir Subscrição
                </Link>
                {subscription && (
                  <div className="text-xs text-indigo-200 mt-2">
                    Renova em {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Reparações Ativas</p>
              <p className="text-3xl font-bold text-gray-900">{dashboardStats.totalRepairs}</p>
              <p className={`text-xs mt-1 ${dashboardStats.repairsGrowth >= 0 ? 'text-indigo-600' : 'text-red-600'}`}>
                {dashboardStats.repairsGrowth >= 0 ? '+' : ''}{dashboardStats.repairsGrowth}% vs mês anterior
              </p>
            </div>
            <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl">
              <Wrench className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Produtos à Venda</p>
              <p className="text-3xl font-bold text-gray-900">{dashboardStats.totalProducts}</p>
              <p className="text-xs text-gray-500 mt-1">Sem dados de comparação</p>
            </div>
            <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl">
              <Package className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Clientes</p>
              <p className="text-3xl font-bold text-gray-900">{dashboardStats.totalCustomers}</p>
              <p className={`text-xs mt-1 ${dashboardStats.customersGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {dashboardStats.customersGrowth >= 0 ? '+' : ''}{dashboardStats.customersGrowth}% vs mês anterior
              </p>
            </div>
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl">
              <Users className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Receita Mensal</p>
              <p className="text-3xl font-bold text-gray-900">€{dashboardStats.monthlyRevenue.toFixed(2)}</p>
              <p className={`text-xs mt-1 ${dashboardStats.revenueGrowth >= 0 ? 'text-indigo-600' : 'text-red-600'}`}>
                {dashboardStats.revenueGrowth >= 0 ? '+' : ''}{dashboardStats.revenueGrowth}% vs mês anterior
              </p>
            </div>
            <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-xl">
              <Euro className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>
          </div>

          {/* Apps Instaladas */}
          {installedApps.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mt-8">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Apps Instaladas</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {installedApps.map((app) => (
                  <Link
                key={app.appId}
                href={`/lojista/apps/${app.appId}`}
                className="flex items-center p-4 border border-gray-200 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:border-indigo-200 transition-all group"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                  {app.appId === 'moloni' && <FileText className="w-6 h-6 text-white" />}
                  {app.appId === 'crm-advanced' && <Users className="w-6 h-6 text-white" />}
                  {app.appId === 'newsletter-pro' && <Mail className="w-6 h-6 text-white" />}
                  {app.appId === 'tax-calculator' && <Calculator className="w-6 h-6 text-white" />}
                  {!['moloni', 'crm-advanced', 'newsletter-pro', 'tax-calculator'].includes(app.appId) &&
                    <Package className="w-6 h-6 text-white" />}
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">
                    {app.appId === 'moloni' ? 'Moloni' :
                     app.appId === 'crm-advanced' ? 'CRM Avançado' :
                     app.appId === 'newsletter-pro' ? 'Newsletter Pro' :
                     app.appId === 'tax-calculator' ? 'Calculadora Impostos' :
                     app.appId}
                  </div>
                  <div className="text-xs text-gray-500">Configurar</div>
                </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Menu Sections */}
          <div className="space-y-12 mt-8">
            {menuItems.map((section, sectionIndex) => (
              <div key={sectionIndex}>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">{section.title}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {section.items.map((item, itemIndex) => {
                    const IconComponent = item.icon
                    return (
                      <Link
                    key={itemIndex}
                    href={item.href}
                    className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 hover:shadow-lg hover:border-indigo-200 transition-all group hover:-translate-y-1"
                  >
                    <div className="flex items-center mb-4">
                      <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl group-hover:scale-110 transition-transform">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="ml-4 text-lg font-semibold text-gray-900">{item.name}</h3>
                    </div>
                        <p className="text-sm text-gray-600 leading-relaxed">{item.description}</p>
                      </Link>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mt-12 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-8 border border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Ações Rápidas</h2>
            <div className="flex flex-wrap gap-4">
              <Link
                href="/lojista/produtos"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-xl hover:from-emerald-600 hover:to-green-700 transition-all text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Plus className="w-4 h-4 mr-2" />
                Gerir Produtos
              </Link>
              <Link
                href="/marketplace"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Package className="w-4 h-4 mr-2" />
                Comprar Peças
              </Link>
              <Link
                href="/lojista/precos"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Euro className="w-4 h-4 mr-2" />
                Gerir Preços
              </Link>
              <Link
                href="/lojista/perfil"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Building className="w-4 h-4 mr-2" />
                Perfil da Loja
              </Link>
              <Link
                href="/lojista/qrcode"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-xl hover:from-orange-600 hover:to-red-700 transition-all text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <QrCode className="w-4 h-4 mr-2" />
                Gerar QR Code
              </Link>
            </div>
          </div>
      </div>
    </div>
  )
}
