'use client'

import Link from 'next/link'
import { useTranslation } from '@/hooks/useTranslation'

export default function Footer() {
  const { t } = useTranslation()

  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold text-black mb-4">Revify</h3>
            <p className="text-gray-600 mb-6 max-w-md">
              A maior plataforma de reparações de dispositivos eletrónicos da Europa. Conectamos clientes a técnicos especializados com garantia e transparência total.
            </p>
            <div className="flex space-x-3">
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                <span className="text-gray-600 font-semibold">f</span>
              </div>
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                <span className="text-gray-600 font-semibold">in</span>
              </div>
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                <span className="text-gray-600 font-semibold">@</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Serviços</h4>
            <ul className="space-y-3">
              <li><Link href="/servicos/smartphones" className="text-gray-600 hover:text-gray-900 transition-colors">Reparação de Smartphones</Link></li>
              <li><Link href="/servicos/laptops" className="text-gray-600 hover:text-gray-900 transition-colors">Reparação de Laptops</Link></li>
              <li><Link href="/servicos/tablets" className="text-gray-600 hover:text-gray-900 transition-colors">Reparação de Tablets</Link></li>
              <li><Link href="/servicos/diagnostico-gratuito" className="text-gray-600 hover:text-gray-900 transition-colors">Diagnóstico Gratuito</Link></li>
              <li><Link href="/servicos/garantia-estendida" className="text-gray-600 hover:text-gray-900 transition-colors">Garantia Estendida</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Suporte</h4>
            <ul className="space-y-3">
              <li><Link href="/central-ajuda" className="text-gray-600 hover:text-gray-900 transition-colors">Centro de Ajuda</Link></li>
              <li><Link href="/como-funciona" className="text-gray-600 hover:text-gray-900 transition-colors">Como Funciona</Link></li>
              <li><Link href="/contactos" className="text-gray-600 hover:text-gray-900 transition-colors">Contactar Suporte</Link></li>
              <li><Link href="/tornar-se-parceiro" className="text-gray-600 hover:text-gray-900 transition-colors">Tornar-se Parceiro</Link></li>
              <li><Link href="/politica-privacidade" className="text-gray-600 hover:text-gray-900 transition-colors">Política de Privacidade</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
            <p className="text-gray-600 text-sm">© 2024 Revify. Todos os direitos reservados.</p>
          </div>

          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/termos" className="text-gray-600 hover:text-gray-900 text-sm transition-colors">Termos de Serviço</Link>
            <Link href="/politica-privacidade" className="text-gray-600 hover:text-gray-900 text-sm transition-colors">Política de Privacidade</Link>
            <Link href="/cookies" className="text-gray-600 hover:text-gray-900 text-sm transition-colors">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
