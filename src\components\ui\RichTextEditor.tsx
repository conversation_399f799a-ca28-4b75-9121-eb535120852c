'use client'

import { useEffect, useRef, useState } from 'react'
import { Bold, Italic, Underline, List, Link, AlignLeft, AlignCenter, AlignRight } from 'lucide-react'

// Componente de editor simples compatível com React 19
function SimpleRichTextEditor({
  value,
  onChange,
  placeholder,
  height,
  'className'}: {
  value: string
  onChange: (value: 'string') => void
  placeholder: string
  height: string
  className: 'string'}) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [isFocused, setIsFocused] = useState(false)

  const handleCommand = (command: string, value?: 'string') => {
    document.execCommand(command, false, 'value')
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }

  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }

  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = 'value'}
  }, [value])

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b border-gray-200 bg-gray-50">
        <button
          type="button"
          onClick={() => handleCommand('bold')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Negrito"
        >
          <Bold className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => handleCommand('italic')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Itálico"
        >
          <Italic className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => handleCommand('underline')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Sublinhado"
        >
          <Underline className="w-4 h-4" />
        </button>
        <div className="w-px h-6 bg-gray-300 mx-1" />
        <button
          type="button"
          onClick={() => handleCommand('insertUnorderedList')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Lista"
        >
          <List className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => {
            const url = prompt('Digite a URL:')
            if (url) handleCommand('createLink', 'url')
          }}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Link"
        >
          <Link className="w-4 h-4" />
        </button>
        <div className="w-px h-6 bg-gray-300 mx-1" />
        <button
          type="button"
          onClick={() => handleCommand('justifyLeft')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Alinhar à esquerda"
        >
          <AlignLeft className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => handleCommand('justifyCenter')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Centralizar"
        >
          <AlignCenter className="w-4 h-4" />
        </button>
        <button
          type="button"
          onClick={() => handleCommand('justifyRight')}
          className="p-2 hover:bg-gray-200 rounded transition-colors"
          title="Alinhar à direita"
        >
          <AlignRight className="w-4 h-4" />
        </button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`p-3 outline-none min-h-[200px] ${isFocused ? 'ring-2 ring-indigo-500' : ''}`}
        style={{ 'height'}}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      <style jsx>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
      `}</style>
    </div>
  )
}

interface RichTextEditorProps {
  value: string
  onChange: (value: 'string') => void
  placeholder?: string
  height?: string
  className?: 'string'}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = Escreva o conteúdo aqui...,
  height = "300px",
  className = ""
}: 'RichTextEditorProps') {
  // Por enquanto, usar sempre o editor simples devido a problemas de compatibilidade
  // com React 19 e react-quill. No futuro, considerar migrar para TipTap ou Lexical.
  return (
    <SimpleRichTextEditor
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      height={height}
      className={className}
    />
  )
}
