import { NextRequest } from 'next/server'
import { POST, GET, PUT } from '../route'

// Mock Prisma
const mockPrisma = {
  profile: {
    findFirst: jest.fn(),
  },
  customer: {
    findFirst: jest.fn(),
    create: jest.fn(),
  },
  brand: {
    findFirst: jest.fn(),
  },
  deviceModel: {
    findFirst: jest.fn(),
  },
  independentRepair: {
    create: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
}

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma),
}))

describe('/api/shop/[subdomain]/repairs', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST - Create Repair', () => {
    const mockRequest = (body: any) => ({
      json: () => Promise.resolve(body),
    }) as NextRequest

    const mockParams = { params: { subdomain: 'test-shop' } }

    it('should create repair successfully', async () => {
      const repairData = {
        customerName: '<PERSON>',
        customerEmail: '<EMAIL>',
        customerPhone: '123456789',
        deviceBrand: 'Apple',
        deviceModel: 'iPhone 14',
        problemDescription: 'Ecrã partido',
        deliveryMethod: 'pickup'
      }

      // Mock shop exists
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      // Mock customer creation
      mockPrisma.customer.create.mockResolvedValue({
        id: 'customer1',
        email: '<EMAIL>'
      })

      // Mock brand exists
      mockPrisma.brand.findFirst.mockResolvedValue({
        id: 'brand1',
        name: 'Apple'
      })

      // Mock device model exists
      mockPrisma.deviceModel.findFirst.mockResolvedValue({
        id: 'model1',
        name: 'iPhone 14',
        brandId: 'brand1'
      })

      // Mock repair count for number generation
      mockPrisma.independentRepair.count.mockResolvedValue(0)

      // Mock repair creation
      mockPrisma.independentRepair.create.mockResolvedValue({
        id: 'repair1',
        repairNumber: 'REP-TEST-SHOP-0001',
        customerName: 'João Silva',
        deviceBrand: 'Apple',
        deviceModel: 'iPhone 14',
        status: 'pending'
      })

      const response = await POST(mockRequest(repairData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.repairNumber).toBe('REP-TEST-SHOP-0001')
      expect(mockPrisma.independentRepair.create).toHaveBeenCalled()
    })

    it('should return error when required fields missing', async () => {
      const repairData = {
        customerEmail: '<EMAIL>'
        // Missing required fields
      }

      const response = await POST(mockRequest(repairData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Campos obrigatórios em falta')
    })

    it('should return error when shop not found', async () => {
      const repairData = {
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '123456789',
        deviceBrand: 'Apple',
        deviceModel: 'iPhone 14',
        problemDescription: 'Ecrã partido'
      }

      mockPrisma.profile.findFirst.mockResolvedValue(null)

      const response = await POST(mockRequest(repairData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Loja não encontrada')
    })

    it('should return error when brand not found', async () => {
      const repairData = {
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '123456789',
        deviceBrand: 'Unknown Brand',
        deviceModel: 'iPhone 14',
        problemDescription: 'Ecrã partido'
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      mockPrisma.brand.findFirst.mockResolvedValue(null)

      const response = await POST(mockRequest(repairData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Marca não encontrada')
    })
  })

  describe('GET - List Repairs', () => {
    const mockRequest = (searchParams: string = '') => ({
      url: `http://localhost/api/shop/test-shop/repairs${searchParams}`,
    }) as NextRequest

    const mockParams = { params: { subdomain: 'test-shop' } }

    it('should list repairs successfully', async () => {
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      const mockRepairs = [
        {
          id: 'repair1',
          repairNumber: 'REP-TEST-SHOP-0001',
          customerEmail: '<EMAIL>',
          deviceBrand: 'Apple',
          deviceModel: 'iPhone 14',
          status: 'pending'
        }
      ]

      mockPrisma.independentRepair.findMany.mockResolvedValue(mockRepairs)
      mockPrisma.independentRepair.count.mockResolvedValue(1)

      const response = await GET(mockRequest(), mockParams)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.repairs).toEqual(mockRepairs)
      expect(result.pagination.total).toBe(1)
    })

    it('should filter repairs by customer email', async () => {
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      mockPrisma.independentRepair.findMany.mockResolvedValue([])
      mockPrisma.independentRepair.count.mockResolvedValue(0)

      const response = await GET(
        mockRequest('?customerEmail=<EMAIL>'),
        mockParams
      )

      expect(mockPrisma.independentRepair.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            customerEmail: '<EMAIL>'
          })
        })
      )
    })
  })

  describe('PUT - Update Repair', () => {
    const mockRequest = (body: any) => ({
      json: () => Promise.resolve(body),
    }) as NextRequest

    const mockParams = { params: { subdomain: 'test-shop' } }

    it('should update repair successfully', async () => {
      const updateData = {
        repairId: 'repair1',
        status: 'in_progress',
        estimatedPrice: 89.99,
        notes: 'Peça encomendada'
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      mockPrisma.independentRepair.update.mockResolvedValue({
        id: 'repair1',
        status: 'in_progress',
        estimatedPrice: 89.99,
        notes: 'Peça encomendada'
      })

      const response = await PUT(mockRequest(updateData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(mockPrisma.independentRepair.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            id: 'repair1',
            profileId: 'shop1'
          },
          data: expect.objectContaining({
            status: 'in_progress',
            estimatedPrice: 89.99,
            notes: 'Peça encomendada'
          })
        })
      )
    })

    it('should return error when repair ID missing', async () => {
      const updateData = {
        status: 'in_progress'
        // Missing repairId
      }

      const response = await PUT(mockRequest(updateData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('ID da reparação é obrigatório')
    })
  })
})
