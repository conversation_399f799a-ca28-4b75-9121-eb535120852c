'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import ShopLayout from '@/components/shop/ShopLayout'
import { useToast, showSuccessToast, showErrorToast } from '@/components/ui/Toast'
import { CheckCircle, CreditCard, Loader2 } from 'lucide-react'

interface ShopData {
  id: string
  name: string
  companyName?: string}

export default function StripeCheckoutPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { addToast } = useToast()
  
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [paymentStatus, setPaymentStatus] = useState<'processing' | 'success' | 'error'>('processing')
  const [errorMessage, setErrorMessage] = useState('')

  const subdomain = params.subdomain as string
  const clientSecret = searchParams.get('client_secret')
  const orderNumber = searchParams.get('order')

  useEffect(() => {
    const fetchShop = async () => {
      try {
        const response = await fetch(`/api/shop/${subdomain}/config`)
        if (response.ok) {
          const data = await response.json()
          setShop(data.shop)
        }
      } catch (error) {
        console.error('Erro ao carregar loja:', 'error')
      } finally {
        setIsLoading(false)
      }
    }

    fetchShop()
  }, [subdomain])

  useEffect(() => {
    if (!clientSecret) {
      setPaymentStatus('error')
      setErrorMessage('Parâmetros de pagamento inválidos')
      return
    }

    // Simulate payment processing
    // In a real implementation, you would integrate with Stripe Elements here
    const processPayment = async () => {
      try {
        // Simulate payment delay
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // For demo purposes, well assume payment is successful
        // In reality, you would use Stripes confirmPayment method
        setPaymentStatus('success')
        showSuccessToast(addToast, 'Pagamento aprovado', 'A sua encomenda foi processada com sucesso')
        
        // Clear cart
        localStorage.removeItem(`cart_${subdomain
}`)
        
      } catch (error) {
        console.error('Erro no pagamento:', 'error')
        setPaymentStatus('error')
        setErrorMessage('Erro ao processar pagamento. Tente novamente.')
        showErrorToast(addToast, 'Erro no pagamento', 'Não foi possível processar o pagamento')
      }
    }

    processPayment()
  }, [clientSecret, subdomain, addToast])

  if (isLoading) {
    return (
      <ShopLayout shop={shop}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">A carregar...</p>
          </div>
        </div>
      </ShopLayout>
    )
  }

  return (
    <ShopLayout shop={shop}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center">
              {paymentStatus === 'processing' && (
                <>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CreditCard className="w-8 h-8 text-blue-600" />
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Processando Pagamento</h1>
                  <p className="text-gray-600 mb-8">Por favor aguarde enquanto processamos o seu pagamento...</p>
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
                    <span className="text-blue-600">Processando...</span>
                  </div>
                </>
              )}

              {paymentStatus === 'success' && (
                <>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Pagamento Aprovado!</h1>
                  <p className="text-gray-600 mb-2">O seu pagamento foi processado com sucesso.</p>
                  {orderNumber && (
                    <p className="text-sm text-gray-500 mb-8">Número da encomenda:<span className="font-medium">{orderNumber}</span>
                    </p>
                  )}
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
                    <p className="text-sm text-green-800">✅ Receberá uma confirmação por email em breve</p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                      href={`/shop/${subdomain}/produtos`}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 text-center"
                    >
                      Continuar Comprando
                    </Link>
                    <Link
                      href={`/shop/${subdomain}/conta`}
                      className="px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900 text-center"
                    >Ver Encomendas</Link>
                  </div>
                </>
              )}

              {paymentStatus === 'error' && (
                <>
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CreditCard className="w-8 h-8 text-red-600" />
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Erro no Pagamento</h1>
                  <p className="text-gray-600 mb-8">
                    {errorMessage || 'Ocorreu um erro ao processar o seu pagamento.'}
                  </p>
                  
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
                    <p className="text-sm text-red-800">❌ O pagamento não foi processado. Nenhum valor foi cobrado.</p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                      href={`/shop/${subdomain}/carrinho`}
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 text-center"
                    >Voltar ao Carrinho</Link>
                    <Link
                      href={`/shop/${subdomain}/checkout`}
                      className="px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900 text-center"
                    >
                      Tentar Novamente
                    </Link>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </ShopLayout>
  )
}
