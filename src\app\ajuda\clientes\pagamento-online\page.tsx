'use client'

import Link from 'next/link'
import MainHeader from '@/components/MainHeader'
import { ArrowLeft, CreditCard, Smartphone, Shield, AlertTriangle } from 'lucide-react'

export default function PagamentoOnlinePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader subtitle="Central de Ajuda" />

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=clientes" className="hover:text-gray-700">Para Clientes</Link>
              <span className="mx-2">›</span>
              <span>Como pagar online</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Como pagar online</h1>
            <p className="text-lg text-gray-600">Métodos de pagamento seguros e convenientes para as suas reparações e compras.</p>
          </div>

          <div className="prose max-w-none">
            <h2>Métodos de pagamento aceites</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="w-8 h-8 text-blue-600 mr-3" />
                  <h3 className="text-lg font-semibold text-blue-900">Cartão de Crédito/Débito</h3>
                </div>
                <ul className="text-blue-800 space-y-2">
                  <li>• Visa, Mastercard, American Express</li>
                  <li>• Pagamento instantâneo</li>
                  <li>• Proteção contra fraude</li>
                  <li>• Sem taxas adicionais</li>
                </ul>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="w-8 h-8 text-green-600 mr-3" />
                  <h3 className="text-lg font-semibold text-green-900">Multibanco</h3>
                </div>
                <ul className="text-green-800 space-y-2">
                  <li>• Referência MB gerada automaticamente</li>
                  <li>• Pagamento em qualquer ATM ou homebanking</li>
                  <li>• Válida por 3 dias</li>
                  <li>• Confirmação automática</li>
                </ul>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="w-8 h-8 text-purple-600 mr-3" />
                  <h3 className="text-lg font-semibold text-purple-900">Klarna</h3>
                </div>
                <ul className="text-purple-800 space-y-2">
                  <li>• Pagamento em 3 prestações sem juros</li>
                  <li>• Aprovação instantânea</li>
                  <li>• Sem custos adicionais</li>
                  <li>• Gestão através da app Klarna</li>
                </ul>
              </div>


            </div>

            <h2>Como efetuar o pagamento</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Para reparações</h3>
              <ol className="list-decimal list-inside text-blue-800 space-y-2">
                <li>Receba o orçamento da oficina</li>
                <li>Aprove o orçamento na plataforma</li>
                <li>Escolha o método de pagamento</li>
                <li>Complete o pagamento seguro</li>
                <li>Receba confirmação por email</li>
              </ol>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <h3 className="text-lg font-semibold text-green-900 mb-3">Para compras no marketplace</h3>
              <ol className="list-decimal list-inside text-green-800 space-y-2">
                <li>Adicione produtos ao carrinho</li>
                <li>Revise o pedido e entrega</li>
                <li>Proceda ao checkout</li>
                <li>Selecione método de pagamento</li>
                <li>Confirme e pague</li>
              </ol>
            </div>

            <h2>Segurança dos pagamentos</h2>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Shield className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Proteção garantida</h3>
                  <ul className="text-green-800 space-y-2">
                    <li><strong>Encriptação SSL:</strong>Todos os dados são protegidos</li>
                    <li><strong>PCI DSS:</strong>Certificação de segurança internacional</li>
                    <li><strong>3D Secure:</strong>Autenticação adicional para cartões</li>
                    <li><strong>Monitorização:</strong>Deteção automática de fraudes</li>
                    <li><strong>Reembolso:</strong>Proteção total do comprador</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Quando é cobrado o pagamento</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Reparações</h3>
                <ul className="text-sm space-y-1">
                  <li>• Após aprovação do orçamento</li>
                  <li>• Antes do início da reparação</li>
                  <li>• Pagamento único</li>
                  <li>• Reembolso se não reparado</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Marketplace</h3>
                <ul className="text-sm space-y-1">
                  <li>• No momento da compra</li>
                  <li>• Antes do envio</li>
                  <li>• Proteção até entrega</li>
                  <li>• Devolução em 14 dias</li>
                </ul>
              </div>
            </div>

            <h2>Faturas e recibos</h2>
            <div className="bg-blue-50 rounded-lg p-6">
              <ul className="space-y-2">
                <li><strong>Fatura automática:</strong>Emitida imediatamente após pagamento</li>
                <li><strong>Email:</strong>Enviada para o seu email registado</li>
                <li><strong>Download:</strong>Disponível na área de cliente</li>
                <li><strong>Dados fiscais:</strong>Pode adicionar NIF para fatura com IVA</li>
                <li><strong>Histórico:</strong> Todas as faturas ficam guardadas</li>
              </ul>
            </div>

            <h2>Problemas com pagamentos</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <AlertTriangle className="w-6 h-6 text-red-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-red-900 mb-2">O que fazer se...</h3>
                  <div className="text-red-800 space-y-3">
                    <div>
                      <h4 className="font-semibold">Pagamento rejeitado</h4>
                      <p className="text-sm">Verifique dados do cartão, limite disponível ou contacte o banco</p>
                    </div>
                    <div>
                      <h4 className="font-semibold">Cobrança duplicada</h4>
                      <p className="text-sm">Contacte o suporte imediatamente com comprovativo</p>
                    </div>
                    <div>
                      <h4 className="font-semibold">Não recebeu fatura</h4>
                      <p className="text-sm">Verifique spam ou descarregue na área de cliente</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <h2>Reembolsos</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Política de reembolsos</h3>
              <ul className="space-y-2">
                <li><strong>Reparação não efetuada:</strong>Reembolso total em 3-5 dias</li>
                <li><strong>Produto defeituoso:</strong>Reembolso após devolução</li>
                <li><strong>Cancelamento:</strong>Antes do início dos trabalhos</li>
                <li><strong>Processamento:</strong>Mesmo método de pagamento original</li>
              </ul>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda/clientes/acompanhar-reparacao"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />Anterior: Acompanhar reparação</Link>
              <Link
                href="/ajuda/clientes/marketplace-comprar"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >Próximo: Marketplace - Como comprar<ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
