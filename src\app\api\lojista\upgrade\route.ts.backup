import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle } = await request.json()

    if (!planId || !billingCycle) {
      return NextResponse.json(
        { message: 'Plano e ciclo de faturação são obrigatórios' },
        { status: 400 }
      )
    }

    // Verificar se o plano existe
    const plan = await prisma.$queryRaw`
      SELECT * FROM subscription_plans WHERE id = ${planId} AND "isActive" = true
    ` as any[]

    if (!plan || plan.length === 0) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    const planData = plan[0]

    // Verificar se já tem subscrição ativa
    const existingSubscription = await prisma.$queryRaw`
      SELECT * FROM subscriptions WHERE "userId" = ${session.user.id} AND status = 'ACTIVE'
    ` as any[]

    // Se já tem subscrição ativa e é o mesmo plano, bloquear
    if (existingSubscription.length > 0 && existingSubscription[0].planId === planId) {
      return NextResponse.json(
        { message: 'Você já possui este plano ativo' },
        { status: 400 }
      )
    }

    // Calcular datas
    const now = new Date()
    const periodEnd = new Date()
    if (billingCycle === 'MONTHLY') {
      periodEnd.setMonth(periodEnd.getMonth() + 1)
    } else {
      periodEnd.setFullYear(periodEnd.getFullYear() + 1)
    }

    const amount = billingCycle === 'MONTHLY' ? planData.monthlyPrice : planData.yearlyPrice

    // Criar registro de upgrade pendente (não muda o plano ainda)
    const pendingUpgrade = await prisma.$queryRaw`
      INSERT INTO pending_upgrades (
        "id", "userId", "currentPlanId", "newPlanId", "billingCycle",
        amount, currency, status, "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${session.user.id},
        ${existingSubscription.length > 0 ? existingSubscription[0].planId : null},
        ${planId}, ${billingCycle}::"BillingCycle", ${amount}, 'EUR', 'PENDING', NOW(), NOW()
      ) RETURNING *
    ` as any[]

    const upgradeId = pendingUpgrade[0].id

    // Criar URL de checkout Stripe (simulado para demo)
    const checkoutUrl = `http://localhost:3000/checkout?upgrade=${upgradeId}&plan=${planId}&cycle=${billingCycle}`

    return NextResponse.json({
      message: 'Redirecionando para pagamento...',
      checkoutUrl,
      upgrade: pendingUpgrade[0]
    })

  } catch (error) {
    console.error('Erro ao fazer upgrade:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
