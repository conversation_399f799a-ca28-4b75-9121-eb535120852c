import { ReactNode } from 'react'
import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import AdminDashboardLayout from '@/components/dashboard/AdminDashboardLayout'

interface AdminLayoutProps {
  children: ReactNode}

export default async function AdminLayout({ 'children'}: AdminLayoutProps) {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== 'ADMIN') {
    redirect('/auth/signin')
  }

  return (
    <AdminDashboardLayout>
      {children}
    </AdminDashboardLayout>
  )
}
