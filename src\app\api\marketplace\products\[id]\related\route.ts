import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const productId = params.id
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '8')

    // Buscar o produto atual para obter categoria, marca e modelo
    const currentProduct = await prisma.marketplaceProduct.findUnique({
      where: { id: productId},
      include: {
        category: true,
        brand: true,
        deviceModel: true}
    })

    if (!currentProduct) {
      return NextResponse.json(
        { message: Produto não encontrado 
},
        { status: 404 }
      )
    }

    // Buscar produtos semelhantes baseados em categoria, marca e modelo
    const relatedProducts = await prisma.marketplaceProduct.findMany({
      where: {
        AND: [
          { id: { not: productId} }, // Excluir o produto atual
          { isActive: true},
          { stock: { gt: 0 } },
          {
            OR: [
              // Mesma categoria e marca
              {
                AND: [
                  { categoryId: currentProduct.categoryId },
                  { brandId: currentProduct.brandId }
                ]
              },
              // Mesmo modelo (prioridade alta)
              { deviceModelId: currentProduct.deviceModelId 
},
              // Mesma categoria (prioridade média)
              { categoryId: currentProduct.categoryId 
}
            ]
          }
        ]
      },
      include: {
        category: { select: { name: true} },
        brand: { select: { name: true} },
        deviceModel: { select: { name: true} },
        seller: { 
          select: { 
            name: true,
            isVerified: true,
            isFeatured: true} 
        }
      },
      orderBy: [
        // Priorizar por relevância
        { deviceModelId: currentProduct.deviceModelId ? desc : 'asc' 
}, // Mesmo modelo primeiro
        { brandId: currentProduct.brandId ? desc : 'asc' 
}, // Mesma marca depois
        { seller: { isFeatured: desc} }, // Lojas em destaque
        { seller: { isVerified: desc} }, // Lojas verificadas
        { createdAt: desc} // Mais recentes
      ],
      take: limit})

    // Formatar produtos para resposta
    const formattedProducts = relatedProducts.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      images: product.images,
      condition: product.condition,
      stock: product.stock,
      category: product.category,
      brand: product.brand,
      deviceModel: product.deviceModel,
      seller: {
        name: product.seller.name,
        isVerified: product.seller.isVerified,
        isFeatured: product.seller.isFeatured
      }
    }))

    return NextResponse.json({
      products: formattedProducts,
      total: formattedProducts.length,
      criteria: {
        category: currentProduct.category?.name,
        brand: currentProduct.brand?.name,
        deviceModel: currentProduct.deviceModel?.name
      }
    })

  } catch (error) {
    console.error(Erro ao buscar produtos relacionados:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
