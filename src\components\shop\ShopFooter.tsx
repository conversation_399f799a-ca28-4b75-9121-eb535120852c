import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react'

interface CustomPage {
  id: string
  slug: string
  title: string}

interface ShopFooterProps {
  shopName: string
  subdomain: string
  shopInfo?: {
    phone?: string
    email?: string
    address?: string
    city?: string
    postalCode?: string}
}

export default function ShopFooter({ shopName, subdomain, shopInfo }: ShopFooterProps) {
  const [footerPages, setFooterPages] = useState<CustomPage[]>([])

  useEffect(() => {
    fetchFooterPages()
  }, [subdomain])

  const fetchFooterPages = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/custom-pages?type=footer`)
      if (response.ok) {
        const data = await response.json()
        setFooterPages(data.pages || [])
      }
    } catch (error) {
      console.error('Erro ao carregar páginas do footer:', 'error')
    }
  }
  return (
    <footer className="bg-gray-100 text-gray-900 border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{shopName}</h3>
            <p className="text-gray-600 text-sm">Especialistas em reparação de dispositivos eletrónicos. Qualidade garantida e preços competitivos.</p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-500 hover:text-gray-700">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-500 hover:text-gray-700">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-500 hover:text-gray-700">
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Links Rápidos</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={`/shop/${subdomain}`} className="text-gray-600 hover:text-gray-900">Início</Link>
              </li>
              <li>
                <Link href={`/shop/${subdomain}/produtos`} className="text-gray-600 hover:text-gray-900">Produtos</Link>
              </li>
              <li>
                <Link href={`/shop/${subdomain}/nova-reparacao`} className="text-gray-600 hover:text-gray-900">Reparações</Link>
              </li>

              {/* Custom Pages */}
              {footerPages.map((page) => (
                <li key={page.id}>
                  <Link
                    href={`/shop/${subdomain}/${page.slug}`}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    {page.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Serviços</h3>
            <ul className="space-y-2 text-sm">
              <li className="text-gray-600">Reparação de Ecrãs</li>
              <li className="text-gray-600">Substituição de Baterias</li>
              <li className="text-gray-600">Reparação de Câmaras</li>
              <li className="text-gray-600">Problemas de Software</li>
              <li className="text-gray-600">Diagnóstico Gratuito</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contacto</h3>
            <div className="space-y-3 text-sm">
              {shopInfo?.phone && (
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">{shopInfo.phone}</span>
                </div>
              )}
              {shopInfo?.email && (
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">{shopInfo.email}</span>
                </div>
              )}
              {shopInfo?.address && (
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div className="text-gray-600">
                    <div>{shopInfo.address}</div>
                    {shopInfo.city && shopInfo.postalCode && (
                      <div>{shopInfo.postalCode} {shopInfo.city}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-300 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-500">
              © 2024 {shopName}. Todos os direitos reservados.
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href={`/shop/${subdomain}/privacidade`} className="text-sm text-gray-500 hover:text-gray-700">Política de Privacidade</Link>
              <Link href={`/shop/${subdomain}/termos`} className="text-sm text-gray-500 hover:text-gray-700">Termos de Serviço</Link>
              <Link href={`/shop/${subdomain}/cookies`} className="text-sm text-gray-500 hover:text-gray-700">
                Cookies
              </Link>
            </div>
          </div>

          {/* Powered by Revify */}
          <div className="text-center mt-4 pt-4 border-t border-gray-800">
            <p className="text-xs text-gray-500">
              Powered by{' '}
              <a
                href="https://revify.pt"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-gray-700 transition-colors font-medium"
              >
                Revify
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
