'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useTranslation } from '@/hooks/useTranslation'
import Link from 'next/link'
import { ArrowLeft, ArrowRight, MapPin, Clock, Euro, Truck, Store, Mail, User, CreditCard, Star } from 'lucide-react'

interface RepairFormData {
  // Passo 1: Dispositivo e problema
  categoryId: string
  brandId: string
  deviceId: string
  problemTypeId: string
  description: string
  problemImages: File[]
  
  // Passo 2: Método de entrega
  deliveryMethod: string // STORE_PICKUP, 'COURIER_PICKUP', 'MAIL_SEND'
  pickupAddress: string
  deliveryAddress: string
  
  // Passo 3: Dados do cliente
  customerName: string
  customerPhone: string
  customerNif: string
  
  // Passo 4: Loja selecionada
  selectedShopId: string
}

function NovaReparacaoV2PageContent() {
  const router = useRouter()
  const { data: session } = useSession()
  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('card')
  
  const [formData, setFormData] = useState<RepairFormData>({
    categoryId: '',
    brandId: '',
    deviceId: '',
    problemTypeId: searchParams?.get('problemType') || '',
    description: '',
    problemImages: [],
    deliveryMethod: '',
    pickupAddress: '',
    deliveryAddress: '',
    customerName: '',
    customerPhone: '',
    customerNif: '',
    selectedShopId: ''
  })

  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [problemTypes, setProblemTypes] = useState<any[]>([])
  const [availableShops, setAvailableShops] = useState<any[]>([])
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null)
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null)

  useEffect(() => {
    fetchInitialData()
  }, [])

  // Preencher dados do cliente automaticamente
  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        customerName: session.user.name || ,
        customerPhone: session.user.profile?.phone || ''
      
}))
    }
  }, [session])

  useEffect(() => {
    if (formData.categoryId && formData.brandId) {
      fetchDevices()
    }
  }, [formData.categoryId, formData.brandId])

  useEffect(() => {
    if (formData.categoryId && formData.problemTypeId) {
      fetchEstimate()
    }
  }, [formData.categoryId, formData.problemTypeId, formData.deviceId])

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/admin/categories'),
        fetch('/api/admin/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) setCategories(await categoriesRes.json())
      if (brandsRes.ok) setBrands(await brandsRes.json())
      if (problemTypesRes.ok) setProblemTypes(await problemTypesRes.json())
    } catch (error) {
      console.error('Erro ao carregar dados:', 'error')
    }
  }

  const fetchDevices = async () => {
    if (formData.categoryId && formData.brandId) {
      try {
        const response = await fetch(`/api/admin/device-models?categoryId=${formData.categoryId}&brandId=${formData.brandId}`)
        if (response.ok) {
          setDevices(await response.json())
        }
      } catch (error) {
        console.error('Erro ao carregar modelos:', 'error')
      }
    }
  }

  const fetchEstimate = async () => {
    try {
      const response = await fetch(`/api/cliente/estimate-price?categoryId=${formData.categoryId}&problemTypeId=${formData.problemTypeId}&deviceId=${formData.deviceId}`)
      if (response.ok) {
        const estimate = await response.json()
        setEstimatedPrice(estimate.averagePrice)
        setEstimatedTime(estimate.averageTime)
      }
    } catch (error) {
      console.error('Erro ao buscar estimativa:', 'error')
    }
  }

  const handleInputChange = (field: keyof RepairFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: 'value'}))
  }

  const handleNextStep = async () => {
    if (canProceed()) {
      const nextStep = currentStep + 1
      setCurrentStep(nextStep)

      // Se for para o passo 4, buscar lojas disponíveis
      if (nextStep === 4) {
        await fetchAvailableShops()
      }
    }
  }

  const fetchAvailableShops = async () => {
    try {
      const response = await fetch(`/api/cliente/available-shops?categoryId=${formData.categoryId}&problemTypeId=${formData.problemTypeId}&deviceId=${formData.deviceId}`)
      if (response.ok) {
        const data = await response.json()
        setAvailableShops(data.shops || data)
      
}
    } catch (error) {
      console.error('Erro ao buscar lojas:', 'error')
    }
  }

  const handlePayment = async () => {
    setIsLoading(true)
    try {
      const selectedShop = availableShops.find(s => s.id === formData.selectedShopId)
      if (!selectedShop) return

      // Criar sessão de pagamento
      const response = await fetch(/api/payments/create-session, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        
},
        body: JSON.stringify({
          repairData: {
            ...formData,
            deviceName: devices.find(d => d.id === formData.deviceId)?.name,
            problemType: problemTypes.find(p => p.id === formData.problemTypeId)?.name
          },
          amount: selectedShop.price,
          shopId: formData.selectedShopId,
          deliveryMethod: formData.deliveryMethod, paymentMethod })
      })

      if (response.ok) {
        const { url, repairId } = await response.json()

        if (paymentMethod === 'multibanco') {
          // Para Multibanco, redirecionar para página de sucesso com dados
          window.location.href = `/cliente/reparacoes/${repairId}/sucesso?payment_method=multibanco`
        } else {
          // Para cartão e Klarna, redirecionar para Stripe checkout
          window.location.href = url
}
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao criar sessão de pagamento')
      }
    } catch (error) {
      console.error('Erro no pagamento:', 'error')
      alert('Erro ao processar pagamento')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreviousStep = () => {
    setCurrentStep(prev => prev - 1)
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return formData.categoryId && formData.brandId && formData.deviceId && formData.problemTypeId && formData.description
      case 2:
        return formData.deliveryMethod &&
               (formData.deliveryMethod === 'STORE_PICKUP' ||
                (formData.deliveryMethod === 'COURIER_PICKUP' && formData.pickupAddress && formData.deliveryAddress) ||
                (formData.deliveryMethod === 'MAIL_SEND' && formData.deliveryAddress))
      case 3:
        return formData.customerName && formData.customerPhone
      case 4:
        return formData.selectedShopId
      default:
        'return true'}
  }

  const steps = [
    { id: 1, title: Dispositivo, icon: MapPin},
    { id: 2, title: Entrega, icon: Truck},
    { id: 3, title: Dados, icon: User},
    { id: 4, title: Loja, icon: Store},
    { id: 5, title: Pagamento, icon: CreditCard}
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors">
                <ArrowLeft className="w-5 h-5 text-gray-700" />
              </Link>
              <h1 className="text-xl font-bold text-black">Nova Reparação</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Login Notice for Non-Authenticated Users */}
        {!session && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <User className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  {t('simulateFree')}
                </p>
              </div>
            </div>
          </div>
        )}
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  <step.icon className="w-5 h-5" />
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {currentStep === 1 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Dispositivo e Problema</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Categoria do Dispositivo *</label>
                    <select
                      value={formData.categoryId}
                      onChange={(e) => handleInputChange('categoryId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione a categoria</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Marca *</label>
                    <select
                      value={formData.brandId}
                      onChange={(e) => handleInputChange('brandId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione a marca</option>
                      {brands.map((brand) => (
                        <option key={brand.id} value={brand.id}>
                          {brand.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {formData.categoryId && formData.brandId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Modelo do Dispositivo *</label>
                    <select
                      value={formData.deviceId}
                      onChange={(e) => handleInputChange('deviceId', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="">Selecione o modelo</option>
                      {devices.map((device) => (
                        <option key={device.id} value={device.id}>
                          {device.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Problema *
                  </label>
                  <select
                    value={formData.problemTypeId}
                    onChange={(e) => handleInputChange('problemTypeId', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  >
                    <option value="">Selecione o problema</option>
                    {problemTypes.map((problem) => (
                      <option key={problem.id} value={problem.id}>
                        {problem.icon} {problem.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Descrição do Problema *</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="Descreva detalhadamente o problema..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Imagens do Problema (opcional)
                  </label>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || [])
                      setFormData(prev => ({ ...prev, problemImages: files}))
                    }}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                  {formData.problemImages.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.problemImages.map((file, index) => (
                        <div key={index} className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                          📷 {file.name}
                        </div>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-1">Máximo 5 imagens, até 5MB cada</p>
                </div>

                {estimatedPrice && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-blue-900 mb-2">Estimativa Inicial</h3>
                    <div className="flex items-center space-x-4 text-sm text-blue-700">
                      <div className="flex items-center">
                        <Euro className="w-4 h-4 mr-1" />
                        €{estimatedPrice}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {estimatedTime ? Math.round(estimatedTime / 60 * 10) / 10 : 0}h
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Método de Entrega</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div 
                    onClick={() => handleInputChange('deliveryMethod', 'STORE_PICKUP')}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.deliveryMethod === 'STORE_PICKUP' 
                        ? 'border-blue-600 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Store className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-medium text-gray-900">Entregar na Loja</h3>
                    <p className="text-sm text-gray-500">Sem custos adicionais</p>
                  </div>

                  <div 
                    onClick={() => handleInputChange('deliveryMethod', 'COURIER_PICKUP')}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.deliveryMethod === 'COURIER_PICKUP' 
                        ? 'border-blue-600 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Truck className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-medium text-gray-900">Recolha no Local</h3>
                    <p className="text-sm text-gray-500">Com custos de estafeta</p>
                  </div>

                  <div 
                    onClick={() => handleInputChange('deliveryMethod', 'MAIL_SEND')}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      formData.deliveryMethod === 'MAIL_SEND' 
                        ? 'border-blue-600 bg-blue-50' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Mail className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-medium text-gray-900">Envio pelos Correios</h3>
                    <p className="text-sm text-gray-500">Custos suportados pelo cliente</p>
                  </div>
                </div>

                {formData.deliveryMethod === 'COURIER_PICKUP' && (
                  <div className="space-y-4 mt-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Endereço de Recolha *</label>
                      <input
                        type="text"
                        value={formData.pickupAddress}
                        onChange={(e) => handleInputChange('pickupAddress', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder={tSync("Onde recolher o dispositivo")}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Endereço de Entrega *</label>
                      <input
                        type="text"
                        value={formData.deliveryAddress}
                        onChange={(e) => handleInputChange('deliveryAddress', e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder={tSync("Onde entregar o dispositivo")}
                      />
                    </div>
                  </div>
                )}

                {formData.deliveryMethod === 'MAIL_SEND' && (
                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Endereço de Entrega *</label>
                    <input
                      type="text"
                      value={formData.deliveryAddress}
                      onChange={(e) => handleInputChange('deliveryAddress', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder={tSync("Onde entregar o dispositivo reparado")}
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Dados do Cliente</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nome Completo *</label>
                    <input
                      type="text"
                      value={formData.customerName}
                      onChange={(e) => handleInputChange('customerName', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder={tSync("Seu nome completo")}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Telefone *</label>
                    <input
                      type="tel"
                      value={formData.customerPhone}
                      onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="+351 xxx xxx xxx"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIF (opcional)
                  </label>
                  <input
                    type="text"
                    value={formData.customerNif}
                    onChange={(e) => handleInputChange('customerNif', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="123456789"
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Escolher Loja de Reparação</h2>
              <div className="space-y-4">
                {availableShops.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-500">A procurar lojas disponíveis...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-4">
                    {availableShops.map((shop) => (
                      <div
                        key={shop.id}
                        onClick={() => handleInputChange('selectedShopId', shop.id)}
                        className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                          formData.selectedShopId === shop.id
                            ? 'border-blue-600 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{shop.name}</h3>
                              <div className="flex items-center space-x-1">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="text-sm font-medium text-gray-700">{shop.rating}</span>
                                <span className="text-sm text-gray-500">({shop.reviewCount} 'avaliações')</span>
                              </div>
                            </div>

                            <p className="text-gray-600 mb-3">{shop.description}</p>

                            <div className="flex items-center space-x-6 text-sm">
                              <div className="flex items-center space-x-1">
                                <MapPin className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">{shop.distance} km</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">{Math.round(shop.estimatedTime / 60 * 10) / 10}h</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Euro className="w-4 h-4 text-gray-400" />
                                <span className="text-gray-600">€{shop.price}</span>
                              </div>
                            </div>

                            {shop.availability.isOpen ? (
                              <div className="mt-2 text-sm text-green-600">✅ Disponível agora</div>
                            ) : (
                              <div className="mt-2 text-sm text-orange-600">
                                ⏰ Próxima disponibilidade: {new Date(shop.availability.nextAvailable).toLocaleString('pt-PT')}
                              </div>
                            )}
                          </div>

                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-600">€{shop.price}</div>
                            <div className="text-sm text-gray-500">
                              {shop.estimatedTimeFormatted ||
                                (shop.estimatedTime && typeof shop.estimatedTime === 'number'
                                  ? (shop.estimatedTime >= 60
                                      ? `${Math.floor(shop.estimatedTime / 60)}h ${shop.estimatedTime % 60 > 0 ? `${shop.estimatedTime % 60}min` : ''}`
                                      : `${shop.estimatedTime}min`)
                                  : '2h estimadas'
                                )
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 5 && (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-6">Pagamento</h2>
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo do Pedido</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Dispositivo:</span>
                      <span>{devices.find(d => d.id === formData.deviceId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Problema:</span>
                      <span>{problemTypes.find(p => p.id === formData.problemTypeId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Loja:</span>
                      <span>{availableShops.find(s => s.id === formData.selectedShopId)?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Método de entrega:</span>
                      <span>
                        {formData.deliveryMethod === 'STORE_PICKUP' && 'Entregar na loja'}
                        {formData.deliveryMethod === 'COURIER_PICKUP' && 'Recolha por estafeta'}
                        {formData.deliveryMethod === 'MAIL_SEND' && 'Envio pelos correios'}
                      </span>
                    </div>
                    <hr className="my-3" />
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Total:</span>
                      <span>€{availableShops.find(s => s.id === formData.selectedShopId)?.price}</span>
                    </div>
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-4">Método de Pagamento</h4>
                  <div className="space-y-3">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="card"
                        checked={paymentMethod === 'card'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <CreditCard className="w-5 h-5 mr-3 text-gray-600" />
                      <div>
                        <div className="font-medium text-gray-900">Cartão de Crédito/Débito</div>
                        <div className="text-sm text-gray-600">Visa, Mastercard, American Express</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="multibanco"
                        checked={paymentMethod === 'multibanco'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <div className="w-5 h-5 mr-3 bg-red-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">MB</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Multibanco</div>
                        <div className="text-sm text-gray-600">Referência Multibanco</div>
                      </div>
                    </label>

                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="klarna"
                        checked={paymentMethod === 'klarna'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="mr-3"
                      />
                      <div className="w-5 h-5 mr-3 bg-pink-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">K</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Klarna</div>
                        <div className="text-sm text-gray-600">Pague em 3x sem juros</div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">💳 Pagamento Seguro</h4>
                  <p className="text-sm text-blue-700">O pagamento é processado de forma segura. O valor fica em escrow até a reparação estar concluída. Só depois é transferido para a loja de reparação.</p>
                </div>

                <button
                  onClick={handlePayment}
                  disabled={isLoading}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold disabled:opacity-50"
                >
                  {isLoading ? 'A processar...' : `Pagar com ${
                    paymentMethod === 'card' ? 'Cartão' :
                    paymentMethod === 'multibanco' ? 'Multibanco' :
                    paymentMethod === 'klarna' ? 'Klarna' : 'Cartão'
                  } - €${availableShops.find(s => s.id === formData.selectedShopId)?.price}`}
                </button>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handlePreviousStep}
              disabled={currentStep === 1}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </button>

            <span className="text-sm text-gray-500">
              Passo {currentStep} de {steps.length}
            </span>

            <button
              onClick={handleNextStep}
              disabled={!canProceed() || 'isLoading'}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {currentStep === steps.length ? (isLoading ? 'A processar...' : Finalizar) : (
                <>Próximo<ArrowRight className="w-4 h-4 ml-2" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function NovaReparacaoV2Page() {
  const { tSync } = useTranslation()
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>}>
      <NovaReparacaoV2PageContent />
    </Suspense>
  )
}
