import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function GET() {
  try {
    // Verificar conexão com a base de dados
    const userCount = await prisma.user.count()
    
    // Verificar se existe o usuário admin
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { profile: true }
    })
    
    // Listar todos os usuários (apenas emails para debug)
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isVerified: true,
        password: true // Para verificar se tem password
      },
      take: 10
    })
    
    return NextResponse.json({
      success: true,
      database: {
        connected: true,
        userCount
      },
      adminUser: adminUser ? {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isVerified: adminUser.isVerified,
        hasPassword: !!adminUser.password,
        hasProfile: !!adminUser.profile
      } : null,
      allUsers: allUsers.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isVerified: user.isVerified,
        hasPassword: !!user.password
      })),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        hasGoogleClientId: !!process.env.GOOGLE_CLIENT_ID,
        hasGoogleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
        hasNextAuthSecret: !!process.env.NEXTAUTH_SECRET
      }
    })
  } catch (error) {
    console.error('Erro no debug de auth:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro no debug de auth',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    // Criar usuário admin se não existir
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: 'Usuário admin já existe',
        user: {
          email: existingUser.email,
          role: existingUser.role
        }
      })
    }

    // Criar hash da password
    const hashedPassword = await bcrypt.hash('admin123', 12)

    // Criar usuário admin
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin Teste',
        role: 'ADMIN',
        isVerified: true,
        profile: {
          create: {
            phone: '123456789'
          }
        }
      },
      include: {
        profile: true
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Usuário admin criado com sucesso',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      credentials: {
        email: '<EMAIL>',
        password: 'admin123'
      }
    })
  } catch (error) {
    console.error('Erro ao criar usuário admin:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao criar usuário admin',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}
