import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso a esta reparação
    const repair = await prisma.repair.findFirst({
      where: {
        id: resolvedParams.id,
        OR: [
          { customerId: session.user.id },
          { repairShopId: session.user.id }
        ]
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    // Buscar mensagens reais
    const messages = await prisma.message.findMany({
      where: {
        repairId: resolvedParams.id
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            role: true}
        }
      },
      orderBy: {
        createdAt: asc}
    })

    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      repairId: msg.repairId,
      senderId: msg.senderId,
      senderName: msg.sender.name || "Usuário",
      senderRole: msg.sender.role,
      message: msg.message,
      createdAt: msg.createdAt.toISOString()
    }))

    return NextResponse.json({ messages: formattedMessages})

  } catch (error) {
    console.error(Erro ao buscar mensagens:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { message } = await request.json()

    if (!message?.trim()) {
      return NextResponse.json(
        { message: "Mensagem é obrigatória" },
        { status: 400 }
      )
    }

    // Verificar se o usuário tem acesso a esta reparação
    const repair = await prisma.repair.findFirst({
      where: {
        id: resolvedParams.id,
        OR: [
          { customerId: session.user.id },
          { repairShopId: session.user.id }
        ]
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    // Criar a mensagem real
    const newMessage = await prisma.message.create({
      data: {
        repairId: resolvedParams.id,
        senderId: session.user.id,
        message: message.trim()
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            role: true}
        }
      }
    })

    return NextResponse.json({
      message: Mensagem enviada com sucesso,
      data: {
        id: newMessage.id,
        repairId: newMessage.repairId,
        senderId: newMessage.senderId,
        senderName: newMessage.sender.name || "Usuário",
        senderRole: newMessage.sender.role,
        message: newMessage.message,
        createdAt: newMessage.createdAt.toISOString()
      
}
    })

  } catch (error) {
    console.error('Erro ao enviar mensagem:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
