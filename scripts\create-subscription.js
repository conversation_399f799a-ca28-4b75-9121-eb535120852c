const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSubscription() {
  try {
    console.log('💳 Criando subscription para lojista...')
    
    // Encontrar o lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      }
    })

    if (!lojista) {
      console.log('❌ Lojista de teste não encontrado')
      return
    }

    console.log('✅ Lojista encontrado:', lojista.name)

    // Verificar se já existe um plano
    let plan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'REVY PRO' }
    })

    if (!plan) {
      // Criar plano REVY PRO
      plan = await prisma.subscriptionPlan.create({
        data: {
          name: 'REVY PRO',
          description: 'Plano profissional com todas as funcionalidades',
          monthlyPrice: 29.99,
          yearlyPrice: 299.99,
          features: [
            'Apps ilimitadas',
            'Suporte prioritário',
            'Analytics avançados',
            'Integrações premium'
          ],
          availableApps: [
            'moloni',
            'newsletter-pro',
            'crm-advanced',
            'inventory-manager',
            'whatsapp-business',
            'analytics-pro'
          ],
          maxProducts: -1,
          maxRepairs: -1,
          moloniIntegration: true,
          miniStore: true,
          individualRepairs: true,
          isActive: true
        }
      })
      console.log('✅ Plano REVY PRO criado')
    } else {
      console.log('✅ Plano REVY PRO já existe')
    }

    // Verificar se já existe subscription
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        userId: lojista.id,
        status: 'ACTIVE'
      }
    })

    if (existingSubscription) {
      console.log('✅ Subscription já existe')
      return
    }

    // Criar subscription
    const subscription = await prisma.subscription.create({
      data: {
        userId: lojista.id,
        planId: plan.id,
        status: 'ACTIVE',
        billingCycle: 'MONTHLY',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
        stripeSubscriptionId: 'test_subscription_' + Date.now()
      }
    })

    console.log('✅ Subscription criada:', subscription.id)
    console.log('📋 Apps disponíveis:', plan.availableApps)

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSubscription()
