'use client'

import { useState, useEffect } from 'react'
import { MapPin, Smartphone, Users, TrendingUp, Clock } from 'lucide-react'
interface MapActivity {
  id: string
  lat: number
  lng: number
  city: string
  type: 'repair' | 'shop' | 'delivery'
  count: number
  recent: boolean}

interface CityStats {
  city: string
  repairsToday: number
  activeShops: number
  avgRating: number}

export default function RealtimeMap() {
  const [activities, setActivities] = useState<MapActivity[]>([])
  const [cityStats, setCityStats] = useState<CityStats[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCity, setSelectedCity] = useState<string | null>(null)

  useEffect(() => {
    fetchMapData()
    
    // Atualizar a cada 15 segundos
    const interval = setInterval(fetchMapData, 15000)
    return () => clearInterval(interval)
  }, [])

  const fetchMapData = async () => {
    try {
      const response = await fetch('/api/viral/map-data')
      if (response.ok) {
        const data = await response.json()
        setActivities(data.activities)
        setCityStats(data.cityStats)
      
}
    } catch (error) {
      console.error('Erro ao carregar dados do mapa:', error)
      // Mock data para demonstração
      setActivities([
        { id: 1, lat: 38.7223, lng: -9.1393, city: Lisboa, type: repair, count: 12, recent: true
},
        { id: '2', lat: 41.1579, lng: -8.6291, city: Porto, type: repair, count: 8, recent: true},
        { id: '3', lat: 40.2033, lng: -8.4103, city: Coimbra, type: shop, count: 3, recent: false},
        { id: '4', lat: 37.0194, lng: -7.9322, city: Faro, type: delivery, count: 5, recent: true}
      ])
      setCityStats([
        { city: Lisboa, repairsToday: 45, activeShops: 23, avgRating: 4.8 },
        { city: Porto, repairsToday: 32, activeShops: 18, avgRating: 4.7 },
        { city: Coimbra, repairsToday: 15, activeShops: 8, avgRating: 4.9 },
        { city: Faro, repairsToday: 12, activeShops: 6, avgRating: 4.6 }
      ])
    } finally {
      setLoading(false)
    }
  }

  const getActivityColor = (type: string, recent: boolean) => {
    const baseColors = {
      repair: recent ? 'bg-green-500' : 'bg-green-300',
      shop: recent ? 'bg-blue-500' : 'bg-blue-300',
      delivery: recent ? 'bg-purple-500' : 'bg-purple-300'
    }
    return baseColors[type as keyof typeof baseColors] || 'bg-gray-400'
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'repair':
        return <Smartphone className="w-3 h-3" />
      case 'shop':
        return <Users className="w-3 h-3" />
      case 'delivery':
        return <MapPin className="w-3 h-3" />
      default:
        return <Clock className="w-3 h-3" />
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-2">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <MapPin className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Mapa em Tempo Real
            </h3>
            <p className="text-sm text-gray-600">
              Atividade da plataforma por região
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-blue-600">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium">
            Ao vivo
          </span>
        </div>
      </div>

      {/* Simplified Map Visualization */}
      <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-6 mb-6 h-64 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <svg viewBox="0 0 400 300" className="w-full h-full">
            {/* Simplified Portugal outline */}
            <path
              d="M50 50 L50 250 L150 250 L150 200 L120 180 L120 120 L100 100 L80 80 L50 50"
              fill="currentColor"
              className="text-blue-300"
            />
          </svg>
        </div>
        
        {/* Activity Points */}
        <div className="relative h-full">
          {activities.map((activity, index) => (
            <div
              key={activity.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
              style={{
                left: `${20 + index * 20}%`,
                top: `${30 + index * 15}%`
              }}
              onClick={() => setSelectedCity(selectedCity === activity.city ? null : activity.city)}
            >
              <div className={`relative ${getActivityColor(activity.type, activity.recent)} rounded-full p-2 text-white shadow-lg ${activity.recent ? 'animate-pulse' : ''}`}>
                {getActivityIcon(activity.type)}
                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {activity.count}
                </div>
              </div>
              
              {/* City Label */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium text-gray-700 whitespace-nowrap">
                  {activity.city}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="flex justify-center space-x-6 mb-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-600">
            Reparações
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-gray-600">
            Lojas
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
          <span className="text-gray-600">
            Entregas
          </span>
        </div>
      </div>

      {/* City Stats */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 flex items-center">
          <TrendingUp className="w-4 h-4 mr-2" />
          Estatísticas por cidade
        </h4>
        
        {cityStats.map((stat) => (
          <div
            key={stat.city}
            className={`p-3 rounded-lg border transition-all cursor-pointer ${
              selectedCity === stat.city
                ? 'border-blue-300 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedCity(selectedCity === stat.city ? null : stat.city)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span className="font-medium text-gray-900">{stat.city}</span>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Reparações: <strong>{stat.repairsToday}</strong>
                </span>
                <span>
                  Lojas: <strong>{stat.activeShops}</strong>
                </span>
                <span>
                  ⭐ <strong>{stat.avgRating}</strong>
                </span>
              </div>
            </div>
            
            {selectedCity === stat.city && (
              <div className="mt-3 pt-3 border-t border-gray-200 text-sm text-gray-600">
                <p>
                  pessoas repararam iPhone hoje nesta área: <strong>{Math.floor(stat.repairsToday * 0.4)}</strong>
                </p>
                <p className="mt-1">
                  Tempo médio de reparação: <strong>2.5h</strong>
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {cityStats.reduce((sum, stat) => sum + stat.repairsToday, 0)}
            </div>
            <div className="text-xs text-gray-600">
              Reparações hoje
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {cityStats.reduce((sum, stat) => sum + stat.activeShops, 0)}
            </div>
            <div className="text-xs text-gray-600">
              Lojas ativas
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {(cityStats.reduce((sum, stat) => sum + stat.avgRating, 0) / cityStats.length).toFixed(1)}
            </div>
            <div className="text-xs text-gray-600">
              Rating médio
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
