'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { CheckCircle, CreditCard, Calendar, Package } from 'lucide-react'

function SubscriptionSuccessContent() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [subscriptionData, setSubscriptionData] = useState<any>(null)

  const sessionId = searchParams.get('session_id')
  const subscriptionId = searchParams.get('subscription_id')
  const isMultibanco = searchParams.get('multibanco') === 'true'
  const entity = searchParams.get('entity')
  const reference = searchParams.get('reference')
  const amount = searchParams.get('amount')

  useEffect(() => {
    if (session?.user?.id) {
      if (isMultibanco) {
        // Para Multibanco, apenas mostrar os dados
        setSubscriptionData({
          paymentMethod: 'multibanco',
          entity,
          reference,
          amount,
          status: 'pending'
        })
        setIsLoading(false)
      } else if (sessionId) {
        // Para Stripe, verificar o status da sessão
        verifyStripeSession()
      } else {
        setIsLoading(false)
      }
    }
  }, [session, sessionId, isMultibanco])

  const verifyStripeSession = async () => {
    try {
      const response = await fetch(`/api/lojista/subscription/verify?session_id=${sessionId}`)
      if (response.ok) {
        const data = await response.json()
        setSubscriptionData(data)
      }
    } catch (error) {
      console.error('Erro ao verificar sessão:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-xl font-bold text-black">Revify</Link>
            <div className="text-sm text-gray-600">
              Olá, {session?.user?.name}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>

            {isMultibanco ? (
              <>
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  Referência Multibanco Gerada
                </h1>
                <p className="text-gray-600 mb-8">
                  A sua subscrição será ativada após o pagamento da referência Multibanco.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                  <h3 className="text-lg font-semibold text-blue-900 mb-4">
                    Dados para Pagamento
                  </h3>
                  <div className="space-y-3 text-left">
                    <div className="flex justify-between">
                      <span className="text-blue-700 font-medium">Entidade:</span>
                      <span className="text-blue-900 font-mono text-lg">{entity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700 font-medium">Referência:</span>
                      <span className="text-blue-900 font-mono text-lg">{reference}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700 font-medium">Valor:</span>
                      <span className="text-blue-900 font-mono text-lg">€{amount}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
                  <p className="text-yellow-800 text-sm">
                    <strong>Importante:</strong> A sua subscrição será ativada automaticamente
                    após a confirmação do pagamento (normalmente até 5 minutos).
                  </p>
                </div>
              </>
            ) : (
              <>
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  Subscrição Ativada com Sucesso!
                </h1>
                <p className="text-gray-600 mb-8">
                  Parabéns! A sua subscrição foi ativada e já pode começar a usar todas as funcionalidades.
                </p>

                {subscriptionData && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                    <h3 className="text-lg font-semibold text-green-900 mb-4">
                      Detalhes da Subscrição
                    </h3>
                    <div className="space-y-3 text-left">
                      <div className="flex items-center">
                        <Package className="h-5 w-5 text-green-600 mr-3" />
                        <span className="text-green-700">Plano: {subscriptionData.planName}</span>
                      </div>
                      <div className="flex items-center">
                        <CreditCard className="h-5 w-5 text-green-600 mr-3" />
                        <span className="text-green-700">Pagamento: Cartão de Crédito</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 text-green-600 mr-3" />
                        <span className="text-green-700">
                          Próxima renovação: {new Date(subscriptionData.currentPeriodEnd).toLocaleDateString('pt-PT')}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}

            <div className="space-y-4">
              <Link
                href="/lojista"
                className="w-full inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Ir para o Dashboard
              </Link>
              
              {isMultibanco && (
                <Link
                  href="/lojista/subscription/status"
                  className="w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Verificar Status do Pagamento
                </Link>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Precisa de ajuda? <Link href="/ajuda" className="text-blue-600 hover:text-blue-800">Contacte o nosso suporte</Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default function SubscriptionSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SubscriptionSuccessContent />
    </Suspense>
  )
}
