import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar todas as definições de apps
    const apps = await prisma.$queryRaw`
      SELECT 
        ad.*,
        COUNT(ia.id) as install_count,
        COUNT(CASE WHEN ia."isPaid" = true THEN 1 END) as paid_installs,
        COUNT(CASE WHEN ia."isTrialActive" = true THEN 1 END) as trial_installs
      FROM app_definitions ad
      LEFT JOIN installed_apps ia ON ad."appId" = ia."appId" AND ia."isActive" = true
      GROUP BY ad.id
      ORDER BY ad."createdAt" DESC
    ` as any[]

    const formattedApps = apps.map((app: any) => ({
      id: app.id,
      appId: app.appId,
      name: app.name,
      description: app.description,
      category: app.category,
      isPaid: app.isPaid,
      monthlyPrice: Number(app.monthlyPrice || 0),
      hasTrialPeriod: app.hasTrialPeriod,
      trialDays: app.trialDays,
      features: app.features,
      requiredPlans: app.requiredPlans,
      isActive: app.isActive,
      isPopular: app.isPopular,
      stats: {
        totalInstalls: Number(app.install_count || 0),
        paidInstalls: Number(app.paid_installs || 0),
        trialInstalls: Number(app.trial_installs || 0)
      },
      createdAt: app.createdAt,
      updatedAt: app.updatedAt
    }))

    return NextResponse.json({
      apps: formattedApps
    })

  } catch (error) {
    console.error('Erro ao buscar apps:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const {
      appId,
      name,
      description,
      category,
      isPaid,
      monthlyPrice,
      hasTrialPeriod,
      trialDays,
      features,
      requiredPlans,
      isActive,
      isPopular
    } = await request.json()

    if (!appId || !name || !category) {
      return NextResponse.json(
        { message: 'Campos obrigatórios: appId, name, category' },
        { status: 400 }
      )
    }

    // Verificar se appId já existe
    const existingApp = await prisma.$queryRaw`
      SELECT id FROM app_definitions
      WHERE "appId" = ${appId}
      LIMIT 1
    ` as any[]

    if (existingApp.length > 0) {
      return NextResponse.json(
        { message: 'App ID já existe' },
        { status: 400 }
      )
    }

    // Criar nova app
    const newApp = await prisma.$queryRaw`
      INSERT INTO app_definitions (
        "id", "appId", name, description, category,
        "isPaid", "monthlyPrice", "hasTrialPeriod", "trialDays",
        features, "requiredPlans", "isActive", "isPopular",
        "createdAt", "updatedAt"
      )
      VALUES (
        gen_random_uuid(), ${appId}, ${name}, ${description || ''}, ${category},
        ${isPaid || false}, ${monthlyPrice || 0}, ${hasTrialPeriod || false}, ${trialDays || 30},
        ${JSON.stringify(features || [])}, ${JSON.stringify(requiredPlans || [])},
        ${isActive !== false}, ${isPopular || false},
        NOW(), NOW()
      )
      RETURNING *
    ` as any[]

    return NextResponse.json({
      message: 'App criada com sucesso',
      app: newApp[0]
    })

  } catch (error) {
    console.error('Erro ao criar app:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
