'use client'

import { useState, useEffect } from 'react'
import { useSession, signIn, signOut } from 'next-auth/react'
import Link from 'next/link'
import { User, LogOut, Settings, ChevronDown, ShoppingCart } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import { useTranslation } from '@/hooks/useTranslation'
import AutoTranslate from '@/components/ui/AutoTranslate'
import LanguageSelector from '@/components/LanguageSelector'
import { usePlatformConfig } from '@/hooks/usePlatformConfig'

// Declaração global para TypeScript
declare global {
  interface Window {
    updateCartCount?: () => void
  }
}

interface MainHeaderProps {
  showCart?: boolean
}

export default function MainHeader({ showCart = false }: MainHeaderProps) {
  const { data: session, status } = useSession()
  const [cartCount, setCartCount] = useState(0)
  const { config: platformConfig } = usePlatformConfig()

  useEffect(() => {
    if (showCart && session) {
      fetchCartCount()
    }
  }, [showCart, session])

  const fetchCartCount = async () => {
    try {
      const response = await fetch('/api/marketplace/cart')
      if (response.ok) {
        const cartData = await response.json()
        setCartCount(cartData.items?.length || 0)
      }
    } catch (error) {
      console.error('Erro ao buscar carrinho:', error)
    }
  }

  // Expor função para atualizar carrinho globalmente
  useEffect(() => {
    if (showCart && session) {
      window.updateCartCount = fetchCartCount
    }
    return () => {
      if (window.updateCartCount) {
        delete window.updateCartCount
      }
    }
  }, [showCart, session])

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center">
              {platformConfig.platformLogo ? (
                <img
                  src={platformConfig.platformLogo}
                  alt={platformConfig.platformName}
                  className="h-8 w-auto"
                />
              ) : (
                <span className="text-xl font-bold text-black">
                  {platformConfig.platformName}
                </span>
              )}
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/marketplace" className="text-gray-700 hover:text-gray-900 font-medium transition-colors">
                <AutoTranslate text="Marketplace" />
              </Link>
              <Link href="/ajuda" className="text-gray-700 hover:text-gray-900 font-medium transition-colors">
                <AutoTranslate text="Central de Ajuda" />
              </Link>
              <Link href="/contactos" className="text-gray-700 hover:text-gray-900 font-medium transition-colors">
                <AutoTranslate text="Contactos" />
              </Link>
              <Link href="#" className="text-gray-700 hover:text-gray-900 font-medium transition-colors">
                <AutoTranslate text="Sobre Nós" />
              </Link>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <LanguageSelector />

            {status === 'loading' ? (
              <div className="animate-pulse">
                <div className="h-8 w-20 bg-gray-200 rounded"></div>
              </div>
            ) : session ? (
              <div className="flex items-center space-x-4">
                {/* Cart Icon */}
                {showCart && (
                  <Link
                    href="/marketplace/cart"
                    className="relative p-2 text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    <ShoppingCart className="w-6 h-6" />
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {cartCount}
                      </span>
                    )}
                  </Link>
                )}

                {/* User Dropdown */}
                <UserDropdown user={session.user} />
              </div>
            ) : (
              <>
                <button
                  onClick={() => signIn()}
                  className="text-gray-700 hover:text-gray-900 font-medium transition-colors"
                >
                  Entrar
                </button>
                <Link
                  href="/auth/signup"
                  className="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors font-medium"
                >
                  Registar
                </Link>
              </>
            )}
          </div>
        </div>
      </div>


    </header>
  )
}
