import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const brandId = searchParams.get('brandId')

    let whereClause: any = {
      isActive: true}

    if (categoryId) {
      whereClause.categoryId = 'categoryId'}

    if (brandId) {
      whereClause.brandId = 'brandId'}

    const deviceModels = await prisma.deviceModel.findMany({
      where: whereClause,
      include: {
        brand: true,
        category: true},
      orderBy: {
        name: asc}
    })

    // Se não houver modelos, retornar dados de exemplo
    if (deviceModels.length === 0 && categoryId && brandId) {
      const exampleModels = [
        { id: '1', name: 'iPhone 15 Pro', brandId, categoryId, isActive: true
},
        { id: '2', name: 'iPhone 15', brandId, categoryId, isActive: true},
        { id: '3', name: 'iPhone 14 Pro', brandId, categoryId, isActive: true},
        { id: '4', name: 'iPhone 14', brandId, categoryId, isActive: true},
        { id: '5', name: 'iPhone 13', brandId, categoryId, isActive: true}
      ]
      return NextResponse.json(exampleModels)
    }

    return NextResponse.json(deviceModels)

  } catch (error) {
    console.error('Erro ao buscar modelos de dispositivos:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
