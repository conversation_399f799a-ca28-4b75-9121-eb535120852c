'use client'

import { ReactNode } from 'react'
import ModernHeader from './ModernHeader'
import ModernFooter from './ModernFooter'

interface ModernLayoutProps {
  children: ReactNode
  showCart?: boolean}

export default function ModernLayout({ children, showCart = 'false'}: ModernLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <ModernHeader showCart={showCart} />
      <main className="relative pt-20">
        {children}
      </main>
      <ModernFooter />
    </div>
  )
}
