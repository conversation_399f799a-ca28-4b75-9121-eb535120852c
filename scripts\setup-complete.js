const { exec } = require('child_process')
const path = require('path')

async function runScript(scriptName) {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(__dirname, scriptName)
    exec(`node "${scriptPath}"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Erro ao executar ${scriptName}:`, error)
        reject(error)
        return
      }
      
      console.log(stdout)
      if (stderr) {
        console.error(stderr)
      }
      
      resolve()
    })
  })
}

async function setupComplete() {
  console.log('🚀 Configuração completa da plataforma Revify')
  console.log('=' .repeat(50))
  
  try {
    console.log('\n1️⃣ Criando superadmin...')
    await runScript('create-superadmin.js')
    
    console.log('\n2️⃣ Criando categorias, marcas e modelos...')
    await runScript('create-admin-data.js')
    
    console.log('\n3️⃣ Criando lojista de teste com produtos...')
    await runScript('create-test-lojista.js')
    
    console.log('\n🎉 Configuração completa!')
    console.log('=' .repeat(50))
    console.log('\n📋 RESUMO DOS DADOS CRIADOS:')
    console.log('\n👤 SUPERADMIN:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔑 Password: Teste123123_')
    console.log('   🔗 Login: http://localhost:3001/auth/signin')
    
    console.log('\n🏪 LOJISTA DE TESTE:')
    console.log('   📧 Email: <EMAIL>')
    console.log('   🔑 Password: teste123')
    console.log('   🌐 Loja: http://localhost:3001/shop/techfix-porto')
    console.log('   📦 Produtos: 6 produtos no marketplace')
    
    console.log('\n📱 DADOS ADMINISTRATIVOS:')
    console.log('   📂 Categorias: 6 (Smartphones, Tablets, Laptops, etc.)')
    console.log('   🏷️ Marcas: 15 (Apple, Samsung, Xiaomi, etc.)')
    console.log('   📱 Modelos: 80+ modelos de dispositivos')
    
    console.log('\n✅ Plataforma pronta para uso!')
    
  } catch (error) {
    console.error('\n❌ Erro durante a configuração:', error.message)
    process.exit(1)
  }
}

setupComplete()
