import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Teste simples de conexão
    const userCount = await prisma.user.count()
    
    return NextResponse.json({
      success: true,
      message: 'Conexão com a base de dados bem-sucedida',
      userCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Erro na conexão da base de dados:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro na conexão da base de dados',
      details: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
