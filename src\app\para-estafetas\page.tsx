'use client'

import { useState } from 'react'
import Link from 'next/link'
import ModernLayout from '@/components/ModernLayout'
import { Truck, Clock, Euro, MapPin, Star, Shield,
  CheckCircle, ArrowRight, Smartphone, Calendar,
  BarChart3, Target, Award, HeadphonesIcon,
  Zap, TrendingUp, Users, Package } from 'lucide-react'

export default function ParaEstafetasPage() {
  const [selectedRegion, setSelectedRegion] = useState('lisboa')

  const benefits = [
    {
      icon: <Euro className="w-8 h-8" />,
      title: 'Ganhos Atrativos',
      description: Ganhe entre €3-8 por entrega, com bónus por performance e fidelidade.,
      highlight: €500-1500/mês
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: 'Hor<PERSON><PERSON>s Flexíveis',
      description: '<PERSON>rabalhe quando quiser', defina seus próprios horários e disponibilidade.,
      highlight: 'Total flexibilidade'},
    {
      icon: <MapPin className="w-8 h-8" />,
      title: '<PERSON><PERSON><PERSON>',
      description: Sistema inteligente que otimiza suas rotas para máxima eficiência.,
      highlight: Até 30% 'mais entregas'},
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Seguro Incluído',
      description: Cobertura completa para você e os dispositivos durante as entregas.,
      highlight: 'Proteção total'}
  ]

  const howItWorks = [
    {
      step: '1',
      title: 'Registe-se',
      description: 'Crie sua conta e complete o processo de verificação em minutos.',
      icon: <Users className="w-6 h-6" />
    },
    {
      step: '2',
      title: 'Defina Disponibilidade',
      description: 'Configure seus horários e áreas de entrega preferidas.',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      step: '3',
      title: 'Receba Pedidos',
      description: 'Aceite entregas que se adequem ao seu horário e localização.',
      icon: <Smartphone className="w-6 h-6" />
    },
    {
      step: '4',
      title: 'Entregue e Ganhe',
      description: 'Faça as entregas e receba seus pagamentos semanalmente.',
      icon: <Euro className="w-6 h-6" />
    }
  ]

  const earnings = [
    {
      region: lisboa,
      name: Lisboa,
      baseRate: '€5.50',
      peakRate: '€8.00',
      avgMonthly: '€1.200',
      demand: 'Muito Alta'
    },
    {
      region: porto,
      name: Porto,
      baseRate: '€4.50',
      peakRate: '€7.00',
      avgMonthly: '€950',
      demand: Alta},
    {
      region: outras,
      name: 'Outras Cidades',
      baseRate: '€3.50',
      peakRate: '€5.50',
      avgMonthly: '€650',
      demand: Moderada}
  ]

  const requirements = [
    'Idade mínima de 18 anos',
    'Documento de identificação válido',
    'Meio de transporte próprio (carro, moto, 'bicicleta')',
    'Smartphone com internet',
    'Disponibilidade mínima de 10h/semana'
  ]

  return (
    <ModernLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 via-emerald-600 to-teal-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Truck className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Ganhe dinheiro entregando
            </h1>
            <p className="text-xl text-green-100 max-w-3xl mx-auto mb-8">Junte-se à rede de estafetas da Revify e ganhe até €1.500/mês com horários flexíveis e rotas otimizadas.</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/signup?type=estafeta"
                className="bg-white text-green-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <span>Começar Agora</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <button className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-white/10 transition-colors">
                Calcular Ganhos
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Por que ser estafeta Revify?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Mais de 2.000 estafetas já confiam na Revify para complementar sua renda</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <div className="text-white">
                    {benefit.icon}
                  </div>
                </div>
                <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold mb-3 inline-block">
                  {benefit.highlight}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {benefit.title}
                </h3>
                <p className="text-gray-600">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Como Funciona</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Processo simples em 4 passos para começar a ganhar dinheiro</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto">
                    <div className="text-white">
                      {step.icon}
                    </div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white border-4 border-green-500 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-bold text-sm">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Earnings Table */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Tabela de Ganhos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Valores transparentes baseados na região e horário de entrega</p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-green-50 to-emerald-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Região</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Tarifa Base</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Horário de Pico</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Média Mensal</th>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Demanda</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {earnings.map((earning, index) => (
                    <tr key={earning.region} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="font-medium text-gray-900">{earning.name}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-green-600 font-semibold">{earning.baseRate}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-green-700 font-semibold">{earning.peakRate}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-gray-900 font-semibold">{earning.avgMonthly}</div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                          earning.demand === 'Muito Alta' ? 'bg-red-100 text-red-800' :
                          earning.demand === 'Alta' ? 'bg-orange-100 text-orange-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {earning.demand}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Requirements */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Requisitos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Requisitos simples para se tornar estafeta Revify</p>
          </div>

          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
            <ul className="space-y-4">
              {requirements.map((requirement, index) => (
                <li key={index} className="flex items-center space-x-4">
                  <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700 text-lg">{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 via-emerald-600 to-teal-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Pronto para começar a ganhar?</h2>
          <p className="text-xl text-green-100 mb-8">Junte-se a milhares de estafetas que já complementam sua renda com a Revify.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup?type=estafeta"
              className="bg-white text-green-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <span>Registar Agora</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <HeadphonesIcon className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
