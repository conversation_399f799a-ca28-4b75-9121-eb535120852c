import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // Buscar reparações do cliente
    const [repairs, total] = await Promise.all([
      prisma.repair.findMany({
        where: {
          customerId: session.user.id
        },
        include: {
          deviceModel: {
            include: {
              brand: {
                select: {
                  name: true}
              },
              category: {
                select: {
                  name: true}
              }
            }
          },
          problemType: {
            select: {
              name: true,
              icon: true}
          },
          repairShop: {
            select: {
              name: true,
              email: true,
              profile: {
                select: {
                  companyName: true,
                  phone: true}
              }
            }
          },
          payments: {
            select: {
              amount: true,
              status: true,
              createdAt: true}
          }
        },
        orderBy: {
          createdAt: desc},
        skip: (page - 1) * limit,
        take: limit}),
      prisma.repair.count({
        where: { customerId: session.user.id }
      })
    ])

    return NextResponse.json({
      repairs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      
}
    })

  } catch (error) {
    console.error('Erro ao buscar reparações do cliente:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { repairData,
      amount,
      shopId,
      deliveryMethod } = await request.json()

    // Validações
    if (!repairData || !amount || !shopId) {
      return NextResponse.json(
        { message: Dados incompletos 
},
        { status: 400 }
      )
    }

    // Buscar dados da loja
    const repairShop = await prisma.user.findUnique({
      where: { id: shopId},
      include: { profile: true}
    })

    if (!repairShop) {
      return NextResponse.json(
        { message: Loja não encontrada 
},
        { status: 404 }
      )
    }

    // Verificar se o problemType existe
    const problemType = await prisma.problemType.findUnique({
      where: { id: repairData.problemTypeId }
    })

    if (!problemType) {
      console.error(ProblemType não encontrado:, repairData.problemTypeId)
      return NextResponse.json(
        { message: 'Tipo de problema não encontrado' 
},
        { status: 400 }
      )
    }

    console.log('Criando reparação com dados:', {
      customerId: session.user.id,
      repairShopId: shopId,
      deviceModelId: repairData.deviceId,
      problemTypeId: repairData.problemTypeId,
      description: repairData.description
    })

    // Criar reparação
    const repair = await prisma.repair.create({
      data: {
        customerId: session.user.id,
        repairShopId: shopId,
        deviceModelId: repairData.deviceId,
        problemTypeId: repairData.problemTypeId,
        description: repairData.description,
        status: CONFIRMED, // Marcar como confirmado para teste
        estimatedPrice: amount,
        deliveryMethod: deliveryMethod,
        pickupAddress: repairData.pickupAddress || null,
        deliveryAddress: repairData.deliveryAddress || null,
        customerName: repairData.customerName,
        customerPhone: repairData.customerPhone,
        customerNif: repairData.customerNif || null,
        paidAt: new Date(), // Marcar como pago
        confirmedAt: new Date()
      }
    })

    // Criar entrada de pagamento simulado
    await prisma.payment.create({
      data: {
        repairId: repair.id,
        amount: amount,
        currency: eur,
        status: COMPLETED,
        method: stripe_test,
        platformFee: amount * 0.05, // 5% de comissão
        shopAmount: amount * 0.95, // 95% para a loja
        escrowReleaseDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 dias
}
    })

    // Criar notificação para a loja
    await prisma.notification.create({
      data: {
        userId: shopId,
        title: Nova Reparação Recebida,
        message: `Recebeu uma nova reparação de ${repairData.customerName
}. Pagamento confirmado (teste).`,
        type: REPAIR_UPDATE,
        metadata: {
          repairId: repair.id,
          customerName: repairData.customerName,
          deviceName: repairData.deviceName,
          amount: amount}
      }
    })

    return NextResponse.json({
      message: 'Reparação criada com sucesso',
      repairId: repair.id
    })

  } catch (error) {
    console.error('Erro ao criar reparação:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
