const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')

async function fixChunkError() {
  console.log('🔧 Corrigindo ChunkLoadError...')
  
  try {
    // 1. Parar todos os processos Node.js
    console.log('1️⃣ Parando processos Node.js...')
    await execCommand('taskkill /f /im node.exe /t')
    
    // 2. Limpar cache do Next.js
    console.log('2️⃣ Limpando cache do Next.js...')
    if (fs.existsSync('.next')) {
      await execCommand('Remove-Item -Recurse -Force .next')
    }
    
    // 3. Limpar cache do npm
    console.log('3️⃣ Limpando cache do npm...')
    await execCommand('npm cache clean --force')
    
    // 4. Verificar se há package-lock.json conflituosos
    console.log('4️⃣ Verificando package-lock.json...')
    const parentLock = path.join('..', 'package-lock.json')
    if (fs.existsSync(parentLock)) {
      console.log('   ⚠️ Removendo package-lock.json do diretório pai...')
      fs.unlinkSync(parentLock)
    }
    
    // 5. Reinstalar dependências
    console.log('5️⃣ Reinstalando dependências...')
    await execCommand('npm install')
    
    // 6. Iniciar servidor
    console.log('6️⃣ Iniciando servidor...')
    console.log('🚀 Executando: npm run dev')
    console.log('📍 Servidor será iniciado em: http://localhost:3001')
    
    // Não aguardar o servidor, apenas iniciar
    exec('npm run dev', { cwd: process.cwd() })
    
    console.log('\n✅ Correção aplicada!')
    console.log('🌐 Aguarde alguns segundos e acesse: http://localhost:3001')
    console.log('🔄 Se ainda houver erro, tente refrescar a página (Ctrl+F5)')
    
  } catch (error) {
    console.error('❌ Erro durante a correção:', error.message)
  }
}

function execCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error && !error.message.includes('No tasks are running')) {
        console.log(`   ⚠️ ${error.message}`)
      }
      resolve(stdout)
    })
  })
}

fixChunkError()
