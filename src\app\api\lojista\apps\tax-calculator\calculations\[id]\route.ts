import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function DELETE(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o cálculo pertence ao usuário
    const calculation = await prisma.$queryRaw`
      SELECT * FROM tax_calculations 
      WHERE id = ${params.id} AND "userId" = ${session.user.id}
    `

    if (!calculation || calculation.length === 0) {
      return NextResponse.json(
        { message: "Cálculo não encontrado" },
        { status: 404 }
      )
    }

    // Excluir cálculo
    await prisma.$queryRaw`
      DELETE FROM tax_calculations 
      WHERE id = ${params.id} AND "userId" = ${session.user.id}
    `

    return NextResponse.json({
      message: "Cálculo excluído com sucesso"
    })

  } catch (error) {
    console.error("Erro ao excluir cálculo:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
