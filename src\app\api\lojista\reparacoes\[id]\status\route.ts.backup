import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendRepairStatusUpdateEmail } from '@/lib/email-service'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { status, notes, sendNotification = true } = await request.json()

    if (!status) {
      return NextResponse.json(
        { message: 'Status é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se a reparação existe e pertence ao lojista
    const existingRepair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        repairShopId: session.user.id
      },
      include: {
        brand: true,
        problemType: true,
        repairShop: {
          include: {
            profile: true
          }
        }
      }
    })

    if (!existingRepair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Atualizar status da reparação
    const updatedRepair = await prisma.repair.update({
      where: { id: params.id },
      data: {
        status,
        updatedAt: new Date(),
        // Adicionar nota ao histórico se fornecida
        ...(notes && {
          notes: existingRepair.notes 
            ? `${existingRepair.notes}\n\n[${new Date().toLocaleString('pt-PT')}] ${notes}`
            : `[${new Date().toLocaleString('pt-PT')}] ${notes}`
        })
      },
      include: {
        brand: true,
        problemType: true,
        repairShop: {
          include: {
            profile: true
          }
        }
      }
    })

    // Enviar email de notificação se solicitado e se o cliente tiver email
    let emailSent = false
    if (sendNotification && existingRepair.customerEmail) {
      try {
        emailSent = await sendRepairStatusUpdateEmail({
          customerName: existingRepair.customerName,
          customerEmail: existingRepair.customerEmail,
          trackingCode: existingRepair.trackingCode,
          deviceBrand: existingRepair.brand?.name || existingRepair.deviceBrand || 'N/A',
          deviceModel: existingRepair.deviceModel || 'N/A',
          problemType: existingRepair.problemType?.name || 'N/A',
          status: updatedRepair.status,
          shopName: updatedRepair.repairShop.name,
          shopPhone: updatedRepair.repairShop.profile?.phone || undefined,
          shopEmail: updatedRepair.repairShop.email,
          notes: notes || undefined
        })
      } catch (error) {
        console.error('Erro ao enviar email de notificação:', error)
        // Não falhar a atualização por causa do email
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Status da reparação atualizado com sucesso',
      repair: {
        id: updatedRepair.id,
        trackingCode: updatedRepair.trackingCode,
        status: updatedRepair.status,
        customerName: updatedRepair.customerName,
        updatedAt: updatedRepair.updatedAt.toISOString()
      },
      emailSent,
      notificationSent: sendNotification && !!existingRepair.customerEmail
    })

  } catch (error) {
    console.error('Erro ao atualizar status da reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
