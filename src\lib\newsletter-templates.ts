export const PREDEFINED_TEMPLATES = [
  {
    id: 'modern-business',
    name: 'Modern Business',
    description: 'Template moderno e profissional para empresas',
    category: business,
    thumbnail: '/templates/modern-business-thumb.png',
    content: `
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body { margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .content h2 { color: #333333; font-size: 24px; margin-bottom: 20px; }
        .content p { color: #666666; line-height: 1.6; margin-bottom: 20px; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #999999; font-size: 14px; }
        .social-links { margin: 20px 0; }
        .social-links a { display: inline-block; margin: 0 10px; color: #667eea; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{company_name}}</h1>
        </div>
        <div class="content">
            <h2>{{title}}</h2>
            <p>{{content}}</p>
            <a href="{{cta_link}}" class="cta-button">{{cta_text}}</a>
        </div>
        <div class="footer">
            <div class="social-links">
                <a href="{{facebook_link}}">Facebook</a>
                <a href="{{instagram_link}}">Instagram</a>
                <a href="{{website_link}}">Website</a>
            </div>
            <p>{{company_name}} - {{company_address}}</p>
            <p><a href="{{unsubscribe_link}}">Cancelar subscrição</a></p>
        </div>
    </div>
</body>
</html>
    `,
    variables: [
      { name: subject, label: Assunto, type: text, required: true},
      { name: company_name, label: 'Nome da Empresa', type: text, required: true},
      { name: title, label: 'Título Principal', type: text, required: true},
      { name: content, label: 'Conteúdo', type: textarea, required: true},
      { name: cta_text, label: 'Texto do Botão', type: text, default: 'Saber Mais' },
      { name: cta_link, label: 'Link do Botão', type: url, required: true},
      { name: facebook_link, label: 'Link Facebook', type: url},
      { name: instagram_link, label: 'Link Instagram', type: url},
      { name: website_link, label: 'Link Website', type: url},
      { name: company_address, label: 'Morada da Empresa', type: text}
    ]
  },
  {
    id: 'newsletter-classic',
    name: 'Newsletter Clássica',
    description: 'Template clássico para newsletters informativas',
    category: newsletter,
    thumbnail: '/templates/newsletter-classic-thumb.png',
    content: `
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body { margin: 0; padding: 0; font-family: Georgia, serif; background-color: #ffffff; }
        .container { max-width: 600px; margin: 0 auto; border: 1px solid #e0e0e0; }
        .header { background-color: #2c3e50; color: #ffffff; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; }
        .date { background-color: #34495e; color: #ffffff; padding: 10px; text-align: center; font-size: 14px; }
        .content { padding: 30px; }
        .article { margin-bottom: 30px; border-bottom: 1px solid #e0e0e0; padding-bottom: 20px; }
        .article:last-child { border-bottom: none; }
        .article h2 { color: #2c3e50; font-size: 20px; margin-bottom: 10px; }
        .article p { color: #555555; line-height: 1.6; margin-bottom: 15px; }
        .read-more { color: #3498db; text-decoration: none; font-weight: bold; }
        .footer { background-color: #ecf0f1; padding: 20px; text-align: center; color: #7f8c8d; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{newsletter_name}}</h1>
        </div>
        <div class="date">{{date}}</div>
        <div class="content">
            <div class="article">
                <h2>{{article1_title}}</h2>
                <p>{{article1_content}}</p>
                <a href="{{article1_link}}" class="read-more">Ler mais →</a>
            </div>
            <div class="article">
                <h2>{{article2_title}}</h2>
                <p>{{article2_content}}</p>
                <a href="{{article2_link}}" class="read-more">Ler mais →</a>
            </div>
            <div class="article">
                <h2>{{article3_title}}</h2>
                <p>{{article3_content}}</p>
                <a href="{{article3_link}}" class="read-more">Ler mais →</a>
            </div>
        </div>
        <div class="footer">
            <p>{{company_name}} - {{company_address}}</p>
            <p><a href="{{unsubscribe_link}}">Cancelar subscrição</a></p>
        </div>
    </div>
</body>
</html>
    `,
    variables: [
      { name: subject, label: Assunto, type: text, required: true},
      { name: newsletter_name, label: 'Nome da Newsletter', type: text, required: true},
      { name: date, label: Data, type: text, default: new Date().toLocaleDateString('pt-PT') },
      { name: article1_title, label: 'Título Artigo 1', type: text, required: true},
      { name: article1_content, label: 'Conteúdo Artigo 1', type: textarea, required: true},
      { name: article1_link, label: 'Link Artigo 1', type: url},
      { name: article2_title, label: 'Título Artigo 2', type: text},
      { name: article2_content, label: 'Conteúdo Artigo 2', type: textarea},
      { name: article2_link, label: 'Link Artigo 2', type: url},
      { name: article3_title, label: 'Título Artigo 3', type: text},
      { name: article3_content, label: 'Conteúdo Artigo 3', type: textarea},
      { name: article3_link, label: 'Link Artigo 3', type: url},
      { name: company_name, label: 'Nome da Empresa', type: text, required: true},
      { name: company_address, label: 'Morada da Empresa', type: text}
    ]
  },
  {
    id: promotional,
    name: Promocional,
    description: 'Template vibrante para promoções e ofertas especiais',
    category: promotion,
    thumbnail: '/templates/promotional-thumb.png',
    content: `
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #ff6b6b; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(45deg, #ff6b6b, #feca57); padding: 40px 20px; text-align: center; }
        .header h1 { color: #ffffff; margin: 0; font-size: 32px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .badge { background-color: #ffffff; color: #ff6b6b; padding: 10px 20px; border-radius: 25px; display: inline-block; margin-top: 15px; font-weight: bold; }
        .content { padding: 40px 30px; text-align: center; }
        .offer { background: linear-gradient(135deg, #feca57, #ff9ff3); padding: 30px; border-radius: 15px; margin: 20px 0; }
        .offer h2 { color: #ffffff; font-size: 28px; margin: 0 0 10px 0; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); }
        .discount { font-size: 48px; font-weight: bold; color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .cta-button { display: inline-block; background: linear-gradient(45deg, #ff6b6b, #ee5a52); color: #ffffff; padding: 20px 40px; text-decoration: none; border-radius: 50px; font-weight: bold; font-size: 18px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .urgency { background-color: #ff4757; color: #ffffff; padding: 15px; border-radius: 10px; margin: 20px 0; }
        .footer { background-color: #2f3542; color: #ffffff; padding: 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{promotion_title}}</h1>
            <div class="badge">{{badge_text}}</div>
        </div>
        <div class="content">
            <div class="offer">
                <h2>{{offer_title}}</h2>
                <div class="discount">{{discount_percentage}}% OFF</div>
            </div>
            <p style="font-size: 18px; color: #333333; margin: 20px 0;">{{description}}</p>
            <div class="urgency">
                <strong>⏰ {{urgency_text}}</strong>
            </div>
            <a href="{{cta_link}}" class="cta-button">{{cta_text}}</a>
            <p style="color: #666666; font-size: 14px; margin-top: 30px;">{{terms}}</p>
        </div>
        <div class="footer">
            <p>{{company_name}}</p>
            <p><a href="{{unsubscribe_link}}" style="color: #ffffff;">Cancelar subscrição</a></p>
        </div>
    </div>
</body>
</html>
    `,
    variables: [
      { name: subject, label: Assunto, type: text, required: true},
      { name: promotion_title, label: 'Título da Promoção', type: text, required: true},
      { name: badge_text, label: 'Texto do Badge', type: text, default: 'OFERTA ESPECIAL' },
      { name: offer_title, label: 'Título da Oferta', type: text, required: true},
      { name: discount_percentage, label: 'Percentagem de Desconto', type: number, required: true},
      { name: description, label: 'Descrição da Oferta', type: textarea, required: true},
      { name: urgency_text, label: 'Texto de Urgência', type: text, default: 'Oferta válida apenas por tempo limitado!' },
      { name: cta_text, label: 'Texto do Botão', type: text, default: 'APROVEITAR AGORA' },
      { name: cta_link, label: 'Link do Botão', type: url, required: true},
      { name: terms, label: 'Termos e Condições', type: textarea},
      { name: company_name, label: 'Nome da Empresa', type: text, required: true}
    ]
  }
]

export function getTemplateById(id: string) {
  return PREDEFINED_TEMPLATES.find(template => template.id === id)
}

export function renderTemplate(templateContent: string, variables: Record<string, string>) {
  let rendered = templateContent
  
  // Replace all variables in the format {{variable_name}}
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, g)
    rendered = rendered.replace(regex, value || '')
  
})
  
  // Add default unsubscribe link if not provided
  if (!variables.unsubscribe_link) {
    rendered = rendered.replace(/{{unsubscribe_link}}/g, #unsubscribe)
  
}
  
  'return rendered'}
