#!/usr/bin/env node

/**
 * Script para corrigir arquivos de API que não devem ter componentes React
 */

const fs = require('fs');
const path = require('path');

// Diretório de APIs
const apiDir = 'src/app/api';

function getAllApiFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllApiFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function fixApiFile(content) {
  let fixed = content;
  let changes = 0;

  // 1. Remover AutoTranslate components de strings de erro/resposta
  const autoTranslateInJsonRegex = /(<AutoTranslate text="([^"]+)" \/>)/g;
  fixed = fixed.replace(autoTranslateInJsonRegex, (match, component, text) => {
    changes++;
    return `"${text}"`;
  });

  // 2. Remover imports de AutoTranslate em arquivos de API
  const autoTranslateImportRegex = /import.*AutoTranslate.*from.*['"]@\/components\/ui\/AutoTranslate['"];\s*\n?/g;
  fixed = fixed.replace(autoTranslateImportRegex, '');
  if (content !== fixed) changes++;

  // 3. Remover imports de useTranslation em arquivos de API
  const useTranslationImportRegex = /import.*useTranslation.*from.*['"]@\/hooks\/useTranslation['"];\s*\n?/g;
  fixed = fixed.replace(useTranslationImportRegex, '');
  if (content !== fixed) changes++;

  // 4. Corrigir chamadas de tSync em arquivos de API (substituir por string direta)
  const tSyncCallRegex = /tSync\(["']([^"']+)["']\)/g;
  fixed = fixed.replace(tSyncCallRegex, (match, text) => {
    changes++;
    return `"${text}"`;
  });

  // 5. Remover declarações de tSync em arquivos de API
  const tSyncDeclarationRegex = /const\s+{\s*tSync\s*}\s*=\s*useTranslation\(\);\s*\n?/g;
  fixed = fixed.replace(tSyncDeclarationRegex, '');
  if (content !== fixed) changes++;

  return { content: fixed, changes };
}

function processApiFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  
  const originalContent = fs.readFileSync(filePath, 'utf8');
  
  // Verificar se o arquivo contém problemas de React em API
  if (originalContent.includes('AutoTranslate') || 
      originalContent.includes('useTranslation') || 
      originalContent.includes('tSync(')) {
    
    const { content: fixedContent, changes } = fixApiFile(originalContent);
    
    if (changes > 0) {
      console.log(`🔧 ${filePath} - ${changes} correções de API aplicadas`);
      fs.writeFileSync(filePath, fixedContent);
    }
  }
}

// Função principal
function main() {
  console.log('🔧 Corrigindo arquivos de API com componentes React...\n');
  
  const apiFiles = getAllApiFiles(apiDir);
  
  console.log(`📁 Processando ${apiFiles.length} arquivos de API...\n`);
  
  // Processar cada arquivo de API
  apiFiles.forEach(processApiFile);
  
  console.log('\n✅ Correção de arquivos de API concluída!');
  console.log('🎯 Arquivos de API não devem conter componentes React');
  console.log('🚀 APIs agora retornam strings simples em vez de componentes');
}

main();
