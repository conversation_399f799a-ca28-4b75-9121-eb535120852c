import { NextRequest, NextResponse } from 'next/server'
import { createStripeInstance, STRIPE_CONFIG } from '@/lib/stripe'
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { amount, currency = 'eur', paymentMethod, metadata = {}, 'subdomain'} = body

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: "Valor inválido" },
        { status: 400 }
      )
    }

    if (!subdomain) {
      return NextResponse.json(
        { error: "Subdomain é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar configurações da loja
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    let shopConfig = null
    try {
      const profile = await prisma.profile.findUnique({
        where: { 'subdomain'
},
        include: { shopConfig: true}
      })

      if (!profile?.shopConfig) {
        return NextResponse.json(
          { error: "Configurações de pagamento não encontradas" },
          { status: 404 }
        )
      }

      shopConfig = profile.shopConfig
    } finally {
      await prisma.$disconnect()
    }

    // Usar chaves Stripe da loja ou fallback para as globais
    const stripeSecretKey = shopConfig.stripeSecretKey || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      return NextResponse.json(
        { error: "Chaves Stripe não configuradas" },
        { status: 500 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    // Convert amount to cents
    const amountInCents = Math.round(amount * 100)

    let paymentMethodTypes = [card]
    
    // Add Klarna if requested and amount is eligible
    if (paymentMethod === 'klarna' && amount >= 1 && amount <= 1000) {
      paymentMethodTypes = ['klarna']
    
}

    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: currency.toLowerCase(),
      payment_method_types: paymentMethodTypes,
      metadata: {
        ...metadata,
        source: revify_shop},
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: never}
    })

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency
    })

  } catch (error) {
    console.error('Erro ao criar payment intent:', 'error')
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const paymentIntentId = searchParams.get('payment_intent_id')

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: "Payment Intent ID é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar chave do Stripe das configurações do admin como fallback
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: stripeSecretKey}
    })

    const adminStripeKey = stripeSecretSetting?.value

    if (!adminStripeKey || adminStripeKey.includes(placeholder) || adminStripeKey.includes('xyz')) {
      return NextResponse.json(
        { error: "Stripe não configurado no admin" 
},
        { status: 500 }
      )
    }

    const stripe = await createStripeInstance(adminStripeKey)
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    return NextResponse.json({
      success: true,
      paymentIntent: {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        metadata: paymentIntent.metadata
      }
    })

  } catch (error) {
    console.error('Erro ao buscar payment intent:', 'error')
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
