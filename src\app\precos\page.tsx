'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  ArrowRight, 
  Check, 
  X, 
  Star, 
  Users, 
  Zap, 
  Shield, 
  Smartphone, 
  Laptop, 
  Tablet, 
  Watch,
  Euro,
  Clock,
  Award
} from 'lucide-react'
import LexendLayout from '@/components/LexendLayout'
import AutoTranslate from '@/components/ui/AutoTranslate'

export default function PrecosPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')

  const plans = [
    {
      name: 'Gratuito',
      description: 'Perfeito para começar',
      price: { monthly: 0, yearly: 0 },
      features: [
        'Até 5 reparações por mês',
        'Acesso a técnicos básicos',
        'Suporte por email',
        'Garantia de 30 dias',
        'App móvel básica'
      ],
      limitations: [
        'Sem prioridade no atendimento',
        'Sem relatórios avançados',
        'Sem integração com outras apps'
      ],
      popular: false,
      cta: 'Come<PERSON><PERSON>'
    },
    {
      name: 'Pro',
      description: 'Para utilizadores regulares',
      price: { monthly: 19, yearly: 190 },
      features: [
        'Reparações ilimitadas',
        'Acesso a todos os técnicos',
        'Suporte prioritário 24/7',
        'Garantia estendida de 90 dias',
        'App móvel completa',
        'Relatórios detalhados',
        'Histórico completo',
        'Notificações em tempo real'
      ],
      limitations: [],
      popular: true,
      cta: 'Começar Teste Grátis'
    },
    {
      name: 'Business',
      description: 'Para empresas e lojas',
      price: { monthly: 49, yearly: 490 },
      features: [
        'Tudo do plano Pro',
        'Gestão de múltiplas lojas',
        'API completa',
        'Integração com sistemas',
        'Suporte dedicado',
        'Relatórios personalizados',
        'Análise avançada',
        'Treinamento da equipa',
        'SLA garantido'
      ],
      limitations: [],
      popular: false,
      cta: 'Contactar Vendas'
    }
  ]

  const devicePricing = [
    {
      device: 'Smartphone',
      icon: Smartphone,
      repairs: [
        { type: 'Ecrã', price: '€45-85', time: '2-4h' },
        { type: 'Bateria', price: '€25-45', time: '1-2h' },
        { type: 'Câmara', price: '€35-65', time: '2-3h' },
        { type: 'Botões', price: '€20-40', time: '1-2h' }
      ]
    },
    {
      device: 'Laptop',
      icon: Laptop,
      repairs: [
        { type: 'Ecrã', price: '€120-250', time: '4-8h' },
        { type: 'Teclado', price: '€45-85', time: '2-4h' },
        { type: 'Bateria', price: '€65-120', time: '2-3h' },
        { type: 'Motherboard', price: '€150-300', time: '1-2 dias' }
      ]
    },
    {
      device: 'Tablet',
      icon: Tablet,
      repairs: [
        { type: 'Ecrã', price: '€85-150', time: '3-6h' },
        { type: 'Bateria', price: '€45-75', time: '2-3h' },
        { type: 'Botões', price: '€25-45', time: '1-2h' },
        { type: 'Carregamento', price: '€30-55', time: '1-2h' }
      ]
    },
    {
      device: 'Smartwatch',
      icon: Watch,
      repairs: [
        { type: 'Ecrã', price: '€65-120', time: '2-4h' },
        { type: 'Bateria', price: '€35-65', time: '1-2h' },
        { type: 'Bracelete', price: '€15-35', time: '30min' },
        { type: 'Sensores', price: '€45-85', time: '2-3h' }
      ]
    }
  ]

  const faqs = [
    {
      question: 'Como funciona o sistema de preços?',
      answer: 'Os preços variam conforme o tipo de dispositivo, complexidade da reparação e técnico escolhido. Sempre apresentamos o preço final antes de confirmar.'
    },
    {
      question: 'Existe garantia nas reparações?',
      answer: 'Sim! Todas as reparações têm garantia mínima de 30 dias, podendo ser estendida até 90 dias nos planos pagos.'
    },
    {
      question: 'Posso cancelar a qualquer momento?',
      answer: 'Absolutamente. Não há contratos de permanência e pode cancelar a subscrição a qualquer momento.'
    },
    {
      question: 'Há custos adicionais?',
      answer: 'Não há custos ocultos. O preço apresentado inclui mão-de-obra e peças, salvo casos excecionais previamente acordados.'
    }
  ]

  return (
    <LexendLayout>
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gray-50 dark:bg-gray-900">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6">
              <span className="text-gray-900 dark:text-white">Preços </span>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Transparentes
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              <AutoTranslate text="Sem surpresas, sem custos ocultos. Escolha o plano perfeito para as suas necessidades." />
            </p>
          </div>
        </div>
      </section>

      {/* Billing Toggle */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-2 border border-gray-200 dark:border-gray-700">
              <div className="flex">
                <button
                  onClick={() => setBillingCycle('monthly')}
                  className={`px-6 py-2 rounded-xl font-medium transition-all ${
                    billingCycle === 'monthly'
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Mensal
                </button>
                <button
                  onClick={() => setBillingCycle('yearly')}
                  className={`px-6 py-2 rounded-xl font-medium transition-all relative ${
                    billingCycle === 'yearly'
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Anual
                  <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    -20%
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all border-2 ${
                  plan.popular
                    ? 'border-indigo-500 dark:border-indigo-400'
                    : 'border-gray-200 dark:border-gray-700'
                } p-8`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                      Mais Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {plan.description}
                  </p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900 dark:text-white">
                      €{plan.price[billingCycle]}
                    </span>
                    <span className="text-gray-600 dark:text-gray-300 ml-2">
                      /{billingCycle === 'monthly' ? 'mês' : 'ano'}
                    </span>
                  </div>
                  {billingCycle === 'yearly' && plan.price.yearly > 0 && (
                    <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                      Poupe €{(plan.price.monthly * 12) - plan.price.yearly} por ano
                    </p>
                  )}
                </div>

                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </div>
                  ))}
                  {plan.limitations.map((limitation, limitationIndex) => (
                    <div key={limitationIndex} className="flex items-center">
                      <X className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-500 dark:text-gray-400">{limitation}</span>
                    </div>
                  ))}
                </div>

                <button
                  className={`w-full py-3 px-6 rounded-xl font-medium transition-all ${
                    plan.popular
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                      : 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {plan.cta}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Device Pricing */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Preços por Dispositivo" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              <AutoTranslate text="Preços médios de reparação por tipo de dispositivo e problema" />
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {devicePricing.map((device, index) => {
              const IconComponent = device.icon
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all border border-gray-200 dark:border-gray-700">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      {device.device}
                    </h3>
                  </div>

                  <div className="space-y-4">
                    {device.repairs.map((repair, repairIndex) => (
                      <div key={repairIndex} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {repair.type}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {repair.time}
                          </div>
                        </div>
                        <div className="font-bold text-indigo-600 dark:text-indigo-400">
                          {repair.price}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              <AutoTranslate text="Os preços podem variar conforme a marca, modelo e complexidade da reparação" />
            </p>
            <Link
              href="/"
              className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
            >
              <span><AutoTranslate text="Obter Orçamento Grátis" /></span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Perguntas Frequentes" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              <AutoTranslate text="Esclarecemos as suas dúvidas sobre preços e planos" />
            </p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  <AutoTranslate text={faq.question} />
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  <AutoTranslate text={faq.answer} />
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            <AutoTranslate text="Pronto para começar?" />
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            <AutoTranslate text="Junte-se a milhares de utilizadores satisfeitos e experimente a Revify hoje mesmo." />
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
            >
              <span><AutoTranslate text="Começar Grátis" /></span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all"
            >
              <span><AutoTranslate text="Falar com Vendas" /></span>
            </Link>
          </div>
        </div>
      </section>
    </LexendLayout>
  )
}
