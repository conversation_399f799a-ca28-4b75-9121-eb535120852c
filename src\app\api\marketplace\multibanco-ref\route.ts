import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Login necessário" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')

    if (!sessionId) {
      return NextResponse.json(
        { message: "Session ID necessário" },
        { status: 400 }
      )
    }

    // Simular geração de referência Multibanco
    // Em produção, aqui você faria uma chamada para a API do Stripe
    // para obter os detalhes do payment_intent e gerar a referência
    
    // Gerar referência simulada
    const entity = 11249
    const reference = Math.floor(100000000 + Math.random() * 900000000).toString()
    const amount = (Math.random() * 1000 + 10).toFixed(2)

    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 2000))

    return NextResponse.json({
      entity,
      reference,
      amount,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 horas
      instructions: [
        'Dirija-se a um terminal Multibanco',
        'Selecione "Pagamentos/Outros Pagamentos"',
        "Introduza a Entidade e Referência",
        "Confirme o valor e efetue o pagamento"
      ]
    
})

  } catch (error) {
    console.error("Erro ao gerar referência Multibanco:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
