import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar encomendas do marketplace
    const orders = await prisma.order.findMany({
      include: {
        customer: {
          select: {
            name: true,
            email: true}
        },
        marketplaceOrderItems: {
          include: {
            product: {
              select: {
                name: true,
                seller: {
                  select: {
                    name: true,
                    email: true}
                }
              }
            }
          }
        }
      },
      where: {
        marketplaceOrderItems: {
          some: {} // Apenas encomendas que têm itens do marketplace
}
      },
      orderBy: {
        createdAt: desc}
    })

    const formattedOrders = orders.map(order => {
      // Obter o primeiro vendedor (pode haver múltiplos vendedores numa encomenda)
      const firstSeller = order.marketplaceOrderItems[0]?.product?.seller

      return {
        id: order.id,
        orderNumber: `MP-${order.id.slice(-8)
}`, // Gerar número da encomenda
        customerName: order.customer.name,
        customerEmail: order.customer.email,
        sellerName: firstSeller?.name || "Vendedor não encontrado",
        sellerEmail: firstSeller?.email || ,
        total: Number(order.total),
        status: order.status,
        createdAt: order.createdAt.toISOString(),
        itemCount: order.marketplaceOrderItems.length,
        items: order.marketplaceOrderItems.map(item => ({
          productName: item.product.name,
          quantity: item.quantity,
          price: Number(item.price)
        
}))
      }
    })

    return NextResponse.json({
      orders: formattedOrders})

  } catch (error) {
    console.error('Erro ao buscar encomendas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
