'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Star, ArrowLeft, Send, Camera } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
interface Rating {
  quality: number
  speed: number
  communication: number
  price: number}

export default function AvaliarPage({ 'params'}: { params: { id: string} }) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [repair, setRepair] = useState<any>(null)
  
  const [rating, setRating] = useState<Rating>({
    quality: 0,
    speed: 0,
    communication: 0,
    price: 0
  })
  
  const [comment, setComment] = useState('')
  const [photos, setPhotos] = useState<File[]>([])

  useEffect(() => {
    fetchRepair()
  }, [params.id])

  const fetchRepair = async () => {
    try {
      const response = await fetch(`/api/cliente/repairs/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data)
        
        // Verificar se já foi avaliado
        if (data.review) {
          alert(Esta reparação já foi avaliada)
          router.push(`/cliente/reparacoes/${params.id
}`)
          return
        }
        
        // Verificar se está concluída
        if (data.status !== COMPLETED&& data.status !==DELIVERED) {
          alert('Só pode avaliar reparações concluídas')
          router.push(`/cliente/reparacoes/${params.id
}`)
          return
        }
      }
    } catch (error) {
      console.error('Erro ao carregar reparação:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRatingChange = (category: keyof Rating, value: number) => {
    setRating(prev => ({ ...prev, [category]: 'value'}))
  }

  const handleSubmitReview = async () => {
    // Validações
    if (Object.values(rating).some(r => r === 0)) {
      alert(Por favor avalie todos os critérios)
      return
    
}

    if (!comment.trim()) {
      alert('Por favor escreva um comentário')
      return
    }

    setIsSaving(true)
    try {
      const formData = new FormData()
      formData.append('rating', JSON.stringify(rating))
      formData.append('comment', 'comment')
      
      photos.forEach((photo, index) => {
        formData.append(`photo_${index}`, 'photo')
      })

      const response = await fetch(`/api/cliente/repairs/${params.id}/review`, {
        method: POST,
        body: formData})

      if (response.ok) {
        alert('Avaliação enviada com sucesso!')
        router.push(`/cliente/reparacoes/${params.id}`)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao enviar avaliação')
      }
    } catch (error) {
      console.error('Erro ao enviar avaliação:', 'error')
      alert('Erro ao enviar avaliação')
    } finally {
      setIsSaving(false)
    }
  }

  const renderStars = (category: keyof Rating, label: string) => {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              onClick={() => handleRatingChange(category, 'star')}
              className={`w-8 h-8 ${
                star <= rating[category] ? 'text-yellow-400' : 'text-gray-300'
              } hover:text-yellow-400 transition-colors`}
            >
              <Star className="w-full h-full fill-current" />
            </button>
          ))}
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Avaliar Serviço</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href={`/cliente/reparacoes/${params.id}`} className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />Voltar à Reparação</Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Avaliar Serviço</h1>

          {repair && (
            <div className="mb-8 p-4 bg-gray-50 rounded-lg">
              <h2 className="font-semibold text-gray-900 mb-2">Detalhes da Reparação</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Dispositivo:</span>
                  <p className="text-gray-600">{repair.deviceModel?.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Problema:</span>
                  <p className="text-gray-600">{repair.problemType?.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Loja:</span>
                  <p className="text-gray-600">{repair.repairShop?.profile?.companyName}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Preço Final:</span>
                  <p className="text-gray-600">€{repair.finalPrice || repair.estimatedPrice}</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-8">
            {/* Avaliações por Categoria */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Como avalia o serviço?</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {renderStars('quality', 'Qualidade da Reparação')}
                {renderStars('speed', 'Rapidez do Serviço')}
                {renderStars('communication', 'Comunicação')}
                {renderStars('price', 'Relação Qualidade/Preço')}
              </div>
            </div>

            {/* Comentário */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Comentário *</label>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                placeholder="Conte-nos sobre a sua experiência..."
              />
            </div>

            {/* Upload de Fotos */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fotos do Resultado (opcional)
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => {
                    const files = Array.from(e.target.files || [])
                    setPhotos(files.slice(0, 5)) // Máximo 5 fotos
}}
                  className="hidden"
                  id="photo-upload"
                />
                <label
                  htmlFor="photo-upload"
                  className="cursor-pointer text-blue-600 hover:text-blue-800"
                >Clique para adicionar fotos</label>
                <p className="text-sm text-gray-500 mt-1">Máximo 5 fotos, até 5MB cada</p>
              </div>
              
              {photos.length > 0 && (
                <div className="mt-4 flex flex-wrap gap-2">
                  {photos.map((photo, index) => (
                    <div key={index} className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                      📷 {photo.name}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Avaliação Geral */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Avaliação Geral</h4>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold text-blue-900">
                  {Object.values(rating).reduce((a, b) => a + b, 0) / 4 || 0}
                </span>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`w-5 h-5 ${
                        star <= (Object.values(rating).reduce((a, b) => a + b, 0) / 4)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-blue-700">estrelas</span>
              </div>
            </div>
          </div>

          {/* Botão de Envio */}
          <div className="mt-8 flex justify-end">
            <button
              onClick={handleSubmitReview}
              disabled={isSaving || Object.values(rating).some(r => r === 0) || !comment.trim()}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-5 h-5 mr-2" />
              {isSaving ? 'A enviar...' : 'Enviar Avaliação'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
