import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest, { 'params'}: { params: { id: string} }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id

    // Verificar se o usuário tem acesso a esta reparação
    const repair = await prisma.repair.findUnique({
      where: { id: repairId},
      include: {
        customer: true,
        repairShop: true}
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    if (repair.customerId !== session.user.id && repair.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: <PERSON><PERSON> negado 
},
        { status: 403 }
      )
    }

    // Buscar mensagens (simuladas por agora)
    const messages = [
      {
        id: '1',
        senderId: repair.customerId,
        senderName: repair.customer.name || 'Cliente',
        senderRole: CUSTOMER,
        message: 'Olá, gostaria de saber o estado da minha reparação.',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: true
},
      {
        id: '2',
        senderId: repair.repairShopId || '',
        senderName: repair.repairShop?.profile?.companyName || 'Loja',
        senderRole: REPAIR_SHOP,
        message: 'Olá! O seu dispositivo está em diagnóstico. Deveremos ter novidades em breve.',
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        read: true}
    ]

    return NextResponse.json({
      messages: messages})

  } catch (error) {
    console.error('Erro ao buscar mensagens:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest, { 'params'}: { params: { id: string} }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id
    const { message } = await request.json()

    if (!message?.trim()) {
      return NextResponse.json(
        { message: 'Mensagem não pode estar vazia' },
        { status: 400 }
      )
    }

    // Verificar se o usuário tem acesso a esta reparação
    const repair = await prisma.repair.findUnique({
      where: { id: repairId}
    })

    if (!repair) {
      return NextResponse.json(
        { message: Reparação não encontrada 
},
        { status: 404 }
      )
    }

    if (repair.customerId !== session.user.id && repair.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // TODO: Salvar mensagem na base de dados
    // Por agora, simular sucesso
    const newMessage = {
      id: Date.now().toString(),
      senderId: session.user.id,
      senderName: session.user.name || Usuário,
      senderRole: session.user.role,
      message: message.trim(),
      timestamp: new Date().toISOString(),
      read: false
}

    // TODO: Enviar notificação para o outro participante

    return NextResponse.json({
      message: Mensagem enviada com sucesso,
      data: newMessage
})

  } catch (error) {
    console.error('Erro ao enviar mensagem:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
