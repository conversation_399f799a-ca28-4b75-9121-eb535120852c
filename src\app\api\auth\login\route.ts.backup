import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'

interface LoginData {
  email: string
  password: string
  shopSubdomain?: string
}

export async function POST(request: NextRequest) {
  try {
    const { email, password, shopSubdomain }: LoginData = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email e password são obrigatórios' },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          profile: true
        }
      })

      if (!user) {
        return NextResponse.json(
          { error: 'Credenciais inválidas' },
          { status: 401 }
        )
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password)
      if (!isValidPassword) {
        return NextResponse.json(
          { error: 'Credenciais inválidas' },
          { status: 401 }
        )
      }

      // Create session data (simplified - in production use proper JWT/session management)
      const sessionData = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        shopSubdomain: shopSubdomain || user.profile?.customSubdomain
      }

      // Set session cookie (simplified)
      const response = NextResponse.json({
        success: true,
        user: sessionData,
        message: 'Login realizado com sucesso'
      })

      // In production, set proper secure session cookie
      response.cookies.set('session', JSON.stringify(sessionData), {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7 // 7 days
      })

      return response

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error('Erro no login:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
