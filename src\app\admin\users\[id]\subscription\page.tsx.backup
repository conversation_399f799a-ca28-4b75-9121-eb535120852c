'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, Crown, Calendar, CreditCard } from 'lucide-react'
import Link from 'next/link'

interface Plan {
  id: string
  name: string
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
}

interface UserSubscription {
  id?: string
  planId?: string
  status?: string
  billingCycle?: string
  currentPeriodEnd?: string
  plan?: Plan
}

export default function UserSubscriptionPage() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [subscription, setSubscription] = useState<UserSubscription | null>(null)
  const [plans, setPlans] = useState<Plan[]>([])
  const [selectedPlan, setSelectedPlan] = useState('')
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    fetchData()
  }, [params.id])

  const fetchData = async () => {
    try {
      const [userRes, plansRes] = await Promise.all([
        fetch(`/api/admin/users/${params.id}`),
        fetch('/api/subscription-plans')
      ])

      if (userRes.ok) {
        const userData = await userRes.json()
        setUser(userData.user)
        setSubscription(userData.user.subscription || null)
        setSelectedPlan(userData.user.subscription?.planId || '')
        setBillingCycle(userData.user.subscription?.billingCycle || 'MONTHLY')
      }

      if (plansRes.ok) {
        const plansData = await plansRes.json()
        setPlans(plansData.plans)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!selectedPlan) {
      alert('Selecione um plano')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch(`/api/admin/users/${params.id}/subscription`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          planId: selectedPlan,
          billingCycle
        })
      })

      if (response.ok) {
        alert('Subscrição atualizada com sucesso!')
        fetchData()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar subscrição')
      }
    } catch (error) {
      console.error('Erro ao salvar:', error)
      alert('Erro ao salvar subscrição')
    } finally {
      setIsSaving(false)
    }
  }

  const cancelSubscription = async () => {
    if (!subscription?.id) return
    
    if (!confirm('Tem certeza que deseja cancelar esta subscrição?')) return

    try {
      const response = await fetch(`/api/admin/subscriptions/${subscription.id}/cancel`, {
        method: 'POST'
      })

      if (response.ok) {
        alert('Subscrição cancelada com sucesso!')
        fetchData()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao cancelar subscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar:', error)
      alert('Erro ao cancelar subscrição')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/users"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">
                Subscrição - {user?.name}
              </h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Current Subscription */}
        {subscription && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Crown className="w-5 h-5 mr-2 text-yellow-500" />
                Subscrição Atual
              </h2>
              <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                subscription.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                subscription.status === 'SUSPENDED' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {subscription.status === 'ACTIVE' ? 'Ativa' :
                 subscription.status === 'SUSPENDED' ? 'Suspensa' : subscription.status}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {subscription.plan?.name}
                </div>
                <div className="text-sm text-gray-600">Plano Atual</div>
              </div>

              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}
                </div>
                <div className="text-sm text-gray-600">Ciclo de Faturação</div>
              </div>

              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-semibold text-gray-900">
                  {subscription.currentPeriodEnd ? 
                    new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT') : 
                    'N/A'
                  }
                </div>
                <div className="text-sm text-gray-600">Próxima Renovação</div>
              </div>
            </div>

            {subscription.status === 'ACTIVE' && (
              <div className="mt-4 flex justify-end">
                <button
                  onClick={cancelSubscription}
                  className="px-4 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50"
                >
                  Cancelar Subscrição
                </button>
              </div>
            )}
          </div>
        )}

        {/* Plan Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {subscription ? 'Alterar Plano' : 'Atribuir Plano'}
          </h2>

          {/* Billing Cycle */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ciclo de Faturação
            </label>
            <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setBillingCycle('MONTHLY')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'MONTHLY'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Mensal
              </button>
              <button
                type="button"
                onClick={() => setBillingCycle('YEARLY')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'YEARLY'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Anual
              </button>
            </div>
          </div>

          {/* Plans Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPlan === plan.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                  <div className="mt-2">
                    <span className="text-2xl font-bold text-gray-900">
                      €{billingCycle === 'MONTHLY' ? 
                        Number(plan.monthlyPrice).toFixed(2) : 
                        Number(plan.yearlyPrice).toFixed(2)
                      }
                    </span>
                    <span className="text-gray-500 text-sm">
                      /{billingCycle === 'MONTHLY' ? 'mês' : 'ano'}
                    </span>
                  </div>
                </div>

                <div className="mt-4">
                  <ul className="text-sm text-gray-600 space-y-1">
                    {plan.features?.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {selectedPlan === plan.id && (
                  <div className="absolute top-2 right-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/users"
              className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </Link>
            <button
              onClick={handleSave}
              disabled={isSaving || !selectedPlan}
              className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isSaving ? 'Salvando...' : subscription ? 'Alterar Plano' : 'Atribuir Plano'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
