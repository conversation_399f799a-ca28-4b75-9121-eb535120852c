'use client'

import { useState, useEffect } from 'react'
import { MapPin, Smartphone, Users, Star, Zap, Clock } from 'lucide-react'
interface Activity {
  id: string
  type: 'repair_completed' | 'shop_opened' | 'milestone_reached' | 'badge_earned' | 'review_received'
  description: string
  location?: string
  timeAgo: string
  icon: React.ReactNode
  color: string}

export default function LiveActivityFeed() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchActivities()
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchActivities, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchActivities = async () => {
    try {
      const response = await fetch('/api/viral/activities')
      if (response.ok) {
        const data = await response.json()
        setActivities(data)
      
}
    } catch (error) {
      console.error('Erro ao carregar atividades:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'repair_completed':
        return <Smartphone className="w-4 h-4" />
      case 'shop_opened':
        return <Users className="w-4 h-4" />
      case 'milestone_reached':
        return <Zap className="w-4 h-4" />
      case 'badge_earned':
        return <Star className="w-4 h-4" />
      case 'review_received':
        return <Star className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'repair_completed':
        return 'text-green-600 bg-green-100'
      case 'shop_opened':
        return 'text-blue-600 bg-blue-100'
      case 'milestone_reached':
        return 'text-purple-600 bg-purple-100'
      case 'badge_earned':
        return 'text-yellow-600 bg-yellow-100'
      case 'review_received':
        return 'text-pink-600 bg-pink-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Atividade em Tempo Real
            </h3>
            <p className="text-sm text-gray-600">
              Vê o que está a acontecer na tua área
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-green-600">
          <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium">
            Ao vivo
          </span>
        </div>
      </div>

      {/* Activity Feed */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {activities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              Nenhuma atividade recente
            </p>
          </div>
        ) : (
          activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-900 leading-relaxed">
                  {activity.description}
                </p>
                <div className="flex items-center space-x-3 mt-1">
                  {activity.location && (
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <MapPin className="w-3 h-3" />
                      <span>{activity.location}</span>
                    </div>
                  )}
                  <span className="text-xs text-gray-500">
                    {activity.timeAgo}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Stats Footer */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {activities.filter(a => a.type === 'repair_completed').length}
            </div>
            <div className="text-xs text-gray-600">
              Reparações hoje
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {new Set(activities.map(a => a.location).filter(Boolean)).size}
            </div>
            <div className="text-xs text-gray-600">
              Cidades ativas
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {activities.filter(a => a.type === 'shop_opened').length}
            </div>
            <div className="text-xs text-gray-600">
              Novas lojas
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
