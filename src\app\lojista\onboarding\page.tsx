'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import AutoTranslate from '@/components/ui/AutoTranslate'
import { useSession } from 'next-auth/react'
import { CheckCircle, MapPin, Clock, Wrench } from 'lucide-react'

interface Brand {
  id: string
  name: string
}

interface Category {
  id: string
  name: string
}

interface ProblemType {
  id: string
  name: string
  description: string
  icon: string
}

interface SubscriptionPlan {
  id: string
  name: string
  description: string
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  isPopular?: boolean
}

export default function LojistaOnboardingPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  
  const [brands, setBrands] = useState<Brand[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [problemTypes, setProblemTypes] = useState<ProblemType[]>([])
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])

  const [formData, setFormData] = useState({
    companyName: '',
    companyNif: '',
    phone: '',
    description: '',
    companyType: '',
    legalDocument: null as File | null,
    workingBrands: [] as string[],
    workingCategories: [] as string[],
    workingProblems: [] as string[],
    serviceRadius: 10,
    averageRepairTime: 120,
    monthlyRepairs: 0,
    expectedGrowth: 0,
    selectedPlan: '',
    billingCycle: 'MONTHLY' as 'MONTHLY' | 'YEARLY',
    paymentMethod: 'card' as 'card' | 'multibanco<AutoTranslate text=", // Endereço da loja street:" />',
    city: '',
    postalCode: '',
    district: ''
  })

  useEffect(() => {
    if (session?.user.role !== 'REPAIR_SHOP') {
      router.push('/')
      return
    }
    fetchData()
  }, [session, router])

  const fetchData = async () => {
    try {
      const [brandsRes, categoriesRes, problemTypesRes, plansRes] = await Promise.all([
        fetch('/api/admin/brands'),
        fetch('/api/admin/categories'),
        fetch('/api/problem-types'),
        fetch('/api/subscription-plans')
      ])

      if (brandsRes.ok) setBrands(await brandsRes.json())
      if (categoriesRes.ok) setCategories(await categoriesRes.json())
      if (problemTypesRes.ok) setProblemTypes(await problemTypesRes.json())
      if (plansRes.ok) {
        const plansData = await plansRes.json()
        setPlans(plansData.plans)
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }

  const handleArrayToggle = (field: 'workingBrands' | 'workingCategories' | 'workingProblems', value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }))
  }

  const handleSubmit = async () => {
    // Validar se plano foi selecionado
    if (!formData.selectedPlan) {
      alert('Por favor, selecione um plano para continuar')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        // Redirecionar para checkout do plano
        const checkoutResponse = await fetch('/api/lojista/subscription/checkout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            planId: formData.selectedPlan,
            billingCycle: formData.billingCycle,
            paymentMethod: formData.paymentMethod
          })
        })

        if (checkoutResponse.ok) {
          const checkoutData = await checkoutResponse.json()
          if (checkoutData.checkoutUrl) {
            // Stripe checkout
            window.location.href = checkoutData.checkoutUrl
          } else if (checkoutData.redirectUrl) {
            // Multibanco redirect
            router.push(checkoutData.redirectUrl)
          } else {
            router.push('/lojista')
          }
        } else {
          const errorData = await checkoutResponse.json()
          console.error('Erro no checkout:', errorData)
          alert(errorData.message || 'Erro ao processar pagamento')
        }
      } else {
        alert('Erro ao salvar perfil')
      }
    } catch (error) {
      console.error('Erro ao salvar perfil:', error)
      alert('Erro ao salvar perfil')
    } finally {
      setIsLoading(false)
    }
  }

  const steps = [
    { number: 1, title: 'Informações da Empresa', icon: Wrench },
    { number: 2, title: 'Especialidades', icon: CheckCircle },
    { number: 3, title: 'Área de Atuação', icon: MapPin },
    { number: 4, title: 'Finalizar', icon: Clock }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-black"><AutoTranslate text="Configuração da Loja" /></h1>
            <div className="text-sm text-gray-600">
              Passo {currentStep} de {steps.length}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const StepIcon = step.icon
              const isCompleted = currentStep > step.number
              const isCurrent = currentStep === step.number

              return (
                <div key={step.number} className="flex flex-col items-center flex-1">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    isCompleted 
                      ? 'bg-green-600 text-white' 
                      : isCurrent 
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-400'
                  }`}>
                    <StepIcon className="w-6 h-6" />
                  </div>
                  <div className="text-sm text-center mt-2">
                    <div className={`font-medium ${isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                      {step.title}
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`absolute h-0.5 w-full mt-6 ${
                      isCompleted ? 'bg-green-600' : 'bg-gray-200'
                    }`} style={{ left: '50%', right: '-50%', zIndex: -1 }} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {currentStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900"><AutoTranslate text="Informações da Empresa" /></h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Nome da Empresa *" /></label>
                  <input
                    type="text"
                    required
                    value={formData.companyName}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                    placeholder="Nome da sua loja"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    NIF da Empresa
                  </label>
                  <input
                    type="text"
                    value={formData.companyNif}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyNif: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                    placeholder="*********"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Telefone *" /></label>
                  <input
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                    placeholder="+*********** 678"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Descrição da Loja" /></label>
                <textarea
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                  placeholder="Descreva os seus serviços e experiência..."
                />
              </div>

              {/* Seleção de Plano */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Escolha o Seu Plano</h3>

                {/* Billing Cycle Toggle */}
                <div className="mb-6 flex justify-center">
                  <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, billingCycle: 'MONTHLY'})}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        formData.billingCycle === 'MONTHLY'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      Mensal
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({...formData, billingCycle: 'YEARLY'})}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        formData.billingCycle === 'YEARLY'
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      Anual <span className="text-green-600 text-xs">(Poupe 20%)</span>
                    </button>
                  </div>
                </div>

                {/* Plans Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {plans.map((plan) => (
                    <div
                      key={plan.id}
                      className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        formData.selectedPlan === plan.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setFormData({...formData, selectedPlan: plan.id})}
                    >
                      {plan.isPopular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            Popular
                          </span>
                        </div>
                      )}

                      <div className="text-center">
                        <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                        <div className="mt-2">
                          <span className="text-2xl font-bold text-gray-900">
                            €{formData.billingCycle === 'MONTHLY' ? Number(plan.monthlyPrice).toFixed(2) : Number(plan.yearlyPrice).toFixed(2)}
                          </span>
                          <span className="text-gray-500 text-sm">
                            /{formData.billingCycle === 'MONTHLY' ? 'mês' : 'ano'}
                          </span>
                        </div>

                        {formData.billingCycle === 'YEARLY' && (
                          <div className="text-sm text-green-600 mt-1">
                            Poupe €{(Number(plan.monthlyPrice) * 12 - Number(plan.yearlyPrice)).toFixed(2)}/ano
                          </div>
                        )}
                      </div>

                      <div className="mt-4">
                        <ul className="text-sm text-gray-600 space-y-1">
                          {plan.features?.slice(0, 3).map((feature: string, index: number) => (
                            <li key={index} className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {formData.selectedPlan === plan.id && (
                        <div className="absolute top-2 right-2">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {!formData.selectedPlan && (
                  <p className="text-red-500 text-sm mt-2">Por favor, selecione um plano para continuar</p>
                )}
              </div>

              {/* Endereço da Loja */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4"><AutoTranslate text="Endereço da Loja" /></h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Rua/Morada *" /></label>
                    <input
                      type="text"
                      required
                      value={formData.street}
                      onChange={(e) => setFormData(prev => ({ ...prev, street: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                      placeholder="Rua da Loja, nº 123"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Cidade *" /></label>
                    <input
                      type="text"
                      required
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                      placeholder="Lisboa"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Código Postal *" /></label>
                    <input
                      type="text"
                      required
                      value={formData.postalCode}
                      onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                      placeholder="1000-001"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2">
                      Distrito
                    </label>
                    <input
                      type="text"
                      value={formData.district}
                      onChange={(e) => setFormData(prev => ({ ...prev, district: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                      placeholder="Lisboa"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Especialidades</h2>
              
              {/* Marcas */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Marcas com que trabalha</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {brands.map((brand) => (
                    <button
                      key={brand.id}
                      type="button"
                      onClick={() => handleArrayToggle('workingBrands', brand.id)}
                      className={`p-3 border-2 rounded-lg text-center transition-all ${
                        formData.workingBrands.includes(brand.id)
                          ? 'border-blue-600 bg-blue-50 text-blue-900'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm">{brand.name}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Categorias */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3"><AutoTranslate text="Tipos de dispositivos" /></h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      type="button"
                      onClick={() => handleArrayToggle('workingCategories', category.id)}
                      className={`p-3 border-2 rounded-lg text-center transition-all ${
                        formData.workingCategories.includes(category.id)
                          ? 'border-blue-600 bg-blue-50 text-blue-900'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm">{category.name}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Tipos de Problemas */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Tipos de problemas que resolve</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {problemTypes.map((problemType) => (
                    <button
                      key={problemType.id}
                      type="button"
                      onClick={() => handleArrayToggle('workingProblems', problemType.id)}
                      className={`p-4 border-2 rounded-lg text-center transition-all ${
                        formData.workingProblems.includes(problemType.id)
                          ? 'border-blue-600 bg-blue-50 text-blue-900'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-2">{problemType.icon}</div>
                      <div className="font-medium text-sm">{problemType.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900"><AutoTranslate text="Área de Atuação" /></h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Raio de Atuação (km)" /></label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={formData.serviceRadius}
                    onChange={(e) => setFormData(prev => ({ ...prev, serviceRadius: parseInt(e.target.value) }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                  />
                  <p className="text-sm text-gray-600 mt-1"><AutoTranslate text="Distância máxima para recolha/entrega por estafeta" /></p>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2"><AutoTranslate text="Tempo Médio de Reparação (minutos)" /></label>
                  <input
                    type="number"
                    min="15"
                    max="2880"
                    value={formData.averageRepairTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, averageRepairTime: parseInt(e.target.value) }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900"
                  />
                  <p className="text-sm text-gray-600 mt-1"><AutoTranslate text="Tempo estimado para completar uma reparação típica (ex: 120 minutos = 2 horas)" /></p>
                </div>
              </div>

              {/* Método de Pagamento */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4"><AutoTranslate text="Método de Pagamento" /></h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === 'card'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setFormData(prev => ({ ...prev, paymentMethod: 'card' }))}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900"><AutoTranslate text="Cartão de Crédito/Débito" /></h4>
                        <p className="text-sm text-gray-600"><AutoTranslate text="Pagamento seguro via Stripe" /></p>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === 'card'
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {formData.paymentMethod === 'card' && (
                          <div className="w-full h-full rounded-full bg-white scale-50"></div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div
                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === 'multibanco'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setFormData(prev => ({ ...prev, paymentMethod: 'multibanco' }))}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">Multibanco</h4>
                        <p className="text-sm text-gray-600"><AutoTranslate text="Referência para pagamento" /></p>
                      </div>
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === 'multibanco'
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {formData.paymentMethod === 'multibanco' && (
                          <div className="w-full h-full rounded-full bg-white scale-50"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              
              <h2 className="text-xl font-semibold text-gray-900"><AutoTranslate text="Configuração Completa!" /></h2>
              
              <p className="text-gray-600 max-w-md mx-auto"><AutoTranslate text="A sua loja está pronta para receber pedidos de reparação.  Pode alterar estas configurações a qualquer momento no seu perfil." /></p>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                <h3 className="font-medium text-blue-900 mb-2"><AutoTranslate text="Resumo da Configuração:" /></h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• {formData.workingBrands.length} marcas selecionadas</li>
                  <li>• {formData.workingCategories.length} tipos de dispositivos</li>
                  <li>• {formData.workingProblems.length} tipos de problemas</li>
                  <li>• Raio de atuação: {formData.serviceRadius}km</li>
                </ul>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
              className="px-4 py-2 border border-gray-300 text-gray-800 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>

            {currentStep < steps.length ? (
              <button
                onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
                disabled={
                  (currentStep === 1 && (!formData.companyName || !formData.phone || !formData.street || !formData.city || !formData.postalCode)) ||
                  (currentStep === 2 && (formData.workingBrands.length === 0 || formData.workingCategories.length === 0 || formData.workingProblems.length === 0))
                }
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Próximo
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isLoading}
                className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'A guardar...' : 'Finalizar Configuração'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
