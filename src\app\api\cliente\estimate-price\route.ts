import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const problemTypeId = searchParams.get('problemTypeId')
    const deviceId = searchParams.get('deviceId')

    if (!categoryId || !problemTypeId) {
      return NextResponse.json(
        { message: "Categoria e tipo de problema são obrigatórios" },
        { status: 400 }
      )
    }

    console.log('Buscando estimativa para:', { categoryId, problemTypeId, deviceId })

    // Buscar preços dos lojistas para esta combinação
    let repairShopPrices = []

    if (deviceId) {
      // Buscar preços específicos para o modelo do dispositivo
      repairShopPrices = await prisma.repairShopPrice.findMany({
        where: {
          deviceModelId: deviceId,
          problemTypeId: problemTypeId,
          isActive: true},
        include: {
          repairShop: {
            include: {
              profile: true}
          }
        }
      })
    }

    // Se não houver preços específicos para o modelo, buscar preços por categoria
    if (repairShopPrices.length === 0) {
      repairShopPrices = await prisma.repairShopPrice.findMany({
        where: {
          categoryId: categoryId,
          problemTypeId: problemTypeId,
          deviceModelId: null, // Preços gerais por categoria
          isActive: true},
        include: {
          repairShop: {
            include: {
              profile: true}
          }
        }
      })
    }

    console.log("Preços encontrados:", repairShopPrices.length)

    if (repairShopPrices.length === 0) {
      // Se não houver preços de lojistas, usar preços base como fallback
      const basePrice = await prisma.basePrice.findFirst({
        where: {
          categoryId: categoryId,
          problemTypeId: problemTypeId}
      })

      if (basePrice) {
        return NextResponse.json({
          averagePrice: basePrice.basePrice,
          averageTime: basePrice.estimatedTime,
          priceRange: {
            min: basePrice.basePrice * 0.8,
            max: basePrice.basePrice * 1.2
          },
          availableShops: 0,
          source: base_price})
      } else {
        return NextResponse.json({
          averagePrice: 50, // Preço padrão
          averageTime: 120, // 2 horas padrão
          priceRange: {
            min: 30,
            max: 80
          },
          availableShops: 0,
          source: default})
      }
    }

    // Calcular médias dos preços dos lojistas
    const prices = repairShopPrices.map(price => price.price)
    const times = repairShopPrices.map(price => price.estimatedTime)

    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const averageTime = times.reduce((sum, 'time') => sum + time, 0) / times.length

    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    return NextResponse.json({
      averagePrice: Math.round(averagePrice * 100) / 100, // Arredondar para 2 casas decimais
      averageTime: Math.round(averageTime),
      priceRange: {
        min: minPrice,
        max: maxPrice
},
      availableShops: repairShopPrices.length,
      source: repair_shops})

  } catch (error) {
    console.error('Erro ao calcular estimativa:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
