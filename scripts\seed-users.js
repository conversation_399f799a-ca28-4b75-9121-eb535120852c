const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 <PERSON><PERSON>do usuários de teste...')

  // Hash da password "123456"
  const hashedPassword = await bcrypt.hash('123456', 12)

  // 1. Admin
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: 'carlos.teix<PERSON>@sellrocket.io',
      name: '<PERSON>',
      password: await bcrypt.hash('Teste123123_', 12),
      role: 'ADMIN'
    }
  })
  console.log('✅ Admin criado:', admin.email)

  // 2. Cliente
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: 'CUSTOMER'
    }
  })
  console.log('✅ Cliente criado:', customer.email)

  // 3. Lojista
  const repairShop = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'TechRepair Lda',
      password: hashedPassword,
      role: 'REPAIR_SHOP'
    }
  })
  console.log('✅ Lojista criado:', repairShop.email)

  // 3.1. Demo Lojista com loja online
  const demoShopOwner = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Loja Demo',
      password: await bcrypt.hash('Teste123123_', 12),
      role: 'REPAIR_SHOP'
    }
  })
  console.log('✅ Demo lojista criado:', demoShopOwner.email)

  // 4. Estafeta
  const courier = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Pedro Santos',
      password: hashedPassword,
      role: 'COURIER'
    }
  })
  console.log('✅ Estafeta criado:', courier.email)

  // Criar perfil para o lojista
  await prisma.profile.upsert({
    where: { userId: repairShop.id },
    update: {},
    create: {
      userId: repairShop.id,
      companyName: 'TechRepair - Reparações de Eletrónicos',
      companyNif: '*********',
      description: 'Especialistas em reparação de smartphones, tablets e laptops. Mais de 10 anos de experiência.',
      phone: '+*********** 678',
      companyType: 'EMPRESA',
      serviceRadius: 15,
      averageRepairTime: 120,
      monthlyRepairs: 50,
      expectedGrowth: 25,
      workingCategories: ['1', '2', '3'], // IDs das categorias
      workingBrands: ['1', '2', '3'], // IDs das marcas
      workingProblems: ['1', '2', '3'], // IDs dos problemas
      businessHours: {
        monday: { open: '09:00', close: '18:00', closed: false },
        tuesday: { open: '09:00', close: '18:00', closed: false },
        wednesday: { open: '09:00', close: '18:00', closed: false },
        thursday: { open: '09:00', close: '18:00', closed: false },
        friday: { open: '09:00', close: '18:00', closed: false },
        saturday: { open: '09:00', close: '17:00', closed: false },
        sunday: { open: '10:00', close: '16:00', closed: true }
      }
    }
  })
  console.log('✅ Perfil do lojista criado')

  // Criar perfil para o cliente
  await prisma.profile.upsert({
    where: { userId: customer.id },
    update: {},
    create: {
      userId: customer.id,
      customerName: 'João Silva',
      phone: '+351 911 222 333',
      customerNif: '987654321',
      addresses: [
        {
          id: '1',
          label: 'Casa',
          street: 'Rua das Flores, 123, 2º Esq',
          city: 'Lisboa',
          postalCode: '1000-001',
          country: 'Portugal',
          isDefault: true,
          type: 'HOME',
          createdAt: new Date().toISOString()
        }
      ]
    }
  })
  console.log('✅ Perfil do cliente criado')

  // Criar perfil para o demo lojista
  await prisma.profile.upsert({
    where: { userId: demoShopOwner.id },
    update: {},
    create: {
      userId: demoShopOwner.id,
      companyName: 'TechFix Demo - Loja Online',
      companyNif: '*********',
      description: 'Loja de demonstração especializada em dispositivos eletrónicos recondicionados.',
      phone: '+*********** 679',
      companyType: 'EMPRESA',
      serviceRadius: 25,
      averageRepairTime: 90,
      monthlyRepairs: 75,
      expectedGrowth: 30,
      workingCategories: ['1', '2', '3'],
      workingBrands: ['1', '2', '3'],
      workingProblems: ['1', '2', '3'],
      customSubdomain: 'demo',
      businessHours: {
        monday: { open: '09:00', close: '18:00', closed: false },
        tuesday: { open: '09:00', close: '18:00', closed: false },
        wednesday: { open: '09:00', close: '18:00', closed: false },
        thursday: { open: '09:00', close: '18:00', closed: false },
        friday: { open: '09:00', close: '18:00', closed: false },
        saturday: { open: '09:00', close: '13:00', closed: false },
        sunday: { open: '10:00', close: '16:00', closed: true }
      }
    }
  })
  console.log('✅ Perfil do demo lojista criado')

  console.log('\n🎉 Usuários de teste criados com sucesso!')
  console.log('\n📋 Credenciais de teste:')
  console.log('👨‍💼 Admin: <EMAIL> / Teste123123_')
  console.log('👤 Cliente: <EMAIL> / 123456')
  console.log('🏪 Lojista: <EMAIL> / 123456')
  console.log('🏪 Demo Lojista: <EMAIL> / Teste123123_')
  console.log('🚚 Estafeta: <EMAIL> / 123456')
}

main()
  .catch((e) => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
