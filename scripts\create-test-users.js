const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUsers() {
  try {
    console.log('👥 Criando usuários de teste...')

    // 1. Criar categorias, marcas e modelos básicos se não existirem
    console.log('📋 Verificando dados básicos...')
    
    let categories = await prisma.category.findMany()
    if (categories.length === 0) {
      await prisma.category.createMany({
        data: [
          { name: 'Smartphones', description: 'Telemóveis e smartphones' },
          { name: 'Tablets', description: 'Tablets e iPads' },
          { name: 'Laptops', description: 'Computadores portáteis' },
          { name: 'Acessórios', description: 'Capas, películas e acessórios' }
        ]
      })
      categories = await prisma.category.findMany()
      console.log('✅ Categorias criadas')
    }

    let brands = await prisma.brand.findMany()
    if (brands.length === 0) {
      await prisma.brand.createMany({
        data: [
          { name: 'Apple' },
          { name: 'Samsung' },
          { name: '<PERSON><PERSON>' },
          { name: 'Huawei' }
        ]
      })
      brands = await prisma.brand.findMany()
      console.log('✅ Marcas criadas')
    }

    let deviceModels = await prisma.deviceModel.findMany()
    if (deviceModels.length === 0) {
      const smartphoneCategory = categories.find(c => c.name === 'Smartphones')
      const tabletCategory = categories.find(c => c.name === 'Tablets')
      const laptopCategory = categories.find(c => c.name === 'Laptops')
      
      const appleBrand = brands.find(b => b.name === 'Apple')
      const samsungBrand = brands.find(b => b.name === 'Samsung')
      const xiaomiBrand = brands.find(b => b.name === 'Xiaomi')

      await prisma.deviceModel.createMany({
        data: [
          { name: 'iPhone 14 Pro', brandId: appleBrand.id, categoryId: smartphoneCategory.id },
          { name: 'iPhone 13', brandId: appleBrand.id, categoryId: smartphoneCategory.id },
          { name: 'Galaxy S23', brandId: samsungBrand.id, categoryId: smartphoneCategory.id },
          { name: 'Galaxy A54', brandId: samsungBrand.id, categoryId: smartphoneCategory.id },
          { name: 'Mi 13', brandId: xiaomiBrand.id, categoryId: smartphoneCategory.id },
          { name: 'iPad Air', brandId: appleBrand.id, categoryId: tabletCategory.id },
          { name: 'MacBook Pro', brandId: appleBrand.id, categoryId: laptopCategory.id }
        ]
      })
      deviceModels = await prisma.deviceModel.findMany()
      console.log('✅ Modelos de dispositivos criados')
    }

    // 2. Criar lojista
    console.log('🏪 Criando lojista...')
    const hashedPasswordShop = await bcrypt.hash('loja123', 12)
    
    const shopUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'TechFix Lisboa',
        password: hashedPasswordShop,
        role: 'REPAIR_SHOP',
        isVerified: true,
        isFeatured: true,
        profile: {
          create: {
            phone: '+351 912 345 678',
            address: 'Rua da Tecnologia, 123',
            city: 'Lisboa',
            postalCode: '1000-001',
            country: 'PT',
            workingBrands: ['apple', 'samsung', 'xiaomi'],
            workingCategories: ['smartphones', 'tablets', 'laptops'],
            workingProblems: ['screen', 'battery', 'water-damage'],
            serviceRadius: 50,
            averageRepairTime: 24,
            shippingEnabled: true,
            freeShippingThreshold: 50.0,
            freeShippingCountries: ['PT'],
            shippingRates: [
              {
                country: 'PT',
                countryName: 'Portugal',
                type: 'FIXED',
                fixedRate: 7.0
              },
              {
                country: 'ES',
                countryName: 'Espanha',
                type: 'FIXED',
                fixedRate: 12.0
              }
            ]
          }
        }
      }
    })
    console.log('✅ Lojista criado: <EMAIL> / loja123')

    // 3. Criar produtos para o lojista
    console.log('🛍️ Criando produtos...')
    
    const smartphoneCategory = categories.find(c => c.name === 'Smartphones')
    const accessoryCategory = categories.find(c => c.name === 'Acessórios')
    const appleBrand = brands.find(b => b.name === 'Apple')
    const samsungBrand = brands.find(b => b.name === 'Samsung')
    const iphone14Model = deviceModels.find(m => m.name === 'iPhone 14 Pro')
    const galaxyS23Model = deviceModels.find(m => m.name === 'Galaxy S23')

    const products = [
      {
        name: 'iPhone 14 Pro - Ecrã Original OLED',
        description: 'Ecrã original Apple para iPhone 14 Pro. Qualidade premium com garantia de 12 meses. Instalação incluída.',
        price: 299.99,
        originalPrice: 349.99,
        condition: 'NEW',
        stock: 8,
        images: ['/images/products/iphone-14-pro-screen.jpg'],
        categoryId: smartphoneCategory?.id,
        brandId: appleBrand?.id,
        deviceModelId: iphone14Model?.id,
        sellerId: shopUser.id,
        isActive: true
      },
      {
        name: 'Samsung Galaxy S23 - Bateria Premium',
        description: 'Bateria de alta capacidade para Samsung Galaxy S23. 4000mAh com tecnologia de carregamento rápido.',
        price: 89.99,
        originalPrice: 119.99,
        condition: 'NEW',
        stock: 15,
        images: ['/images/products/samsung-s23-battery.jpg'],
        categoryId: smartphoneCategory?.id,
        brandId: samsungBrand?.id,
        deviceModelId: galaxyS23Model?.id,
        sellerId: shopUser.id,
        isActive: true
      },
      {
        name: 'Capa Silicone iPhone 14 Pro - Transparente',
        description: 'Capa protetora em silicone transparente. Proteção total com acesso a todas as funções.',
        price: 19.99,
        originalPrice: 29.99,
        condition: 'NEW',
        stock: 25,
        images: ['/images/products/iphone-case-transparent.jpg'],
        categoryId: accessoryCategory?.id,
        brandId: appleBrand?.id,
        deviceModelId: iphone14Model?.id,
        sellerId: shopUser.id,
        isActive: true
      },
      {
        name: 'Película de Vidro Temperado iPhone 14 Pro',
        description: 'Película de vidro temperado 9H. Proteção total do ecrã com toque suave e transparência perfeita.',
        price: 24.99,
        condition: 'NEW',
        stock: 30,
        images: ['/images/products/screen-protector.jpg'],
        categoryId: accessoryCategory?.id,
        brandId: appleBrand?.id,
        deviceModelId: iphone14Model?.id,
        sellerId: shopUser.id,
        isActive: true
      },
      {
        name: 'Carregador Wireless Samsung 15W',
        description: 'Carregador sem fios compatível com Galaxy S23. Carregamento rápido de 15W com indicador LED.',
        price: 39.99,
        originalPrice: 59.99,
        condition: 'NEW',
        stock: 12,
        images: ['/images/products/wireless-charger.jpg'],
        categoryId: accessoryCategory?.id,
        brandId: samsungBrand?.id,
        deviceModelId: galaxyS23Model?.id,
        sellerId: shopUser.id,
        isActive: true
      },
      {
        name: 'Kit Ferramentas Reparação Smartphone',
        description: 'Kit completo com 32 ferramentas para reparação de smartphones. Inclui chaves de precisão e ventosas.',
        price: 29.99,
        condition: 'NEW',
        stock: 8,
        images: ['/images/products/repair-kit.jpg'],
        categoryId: accessoryCategory?.id,
        sellerId: shopUser.id,
        isActive: true
      }
    ]

    for (const productData of products) {
      await prisma.marketplaceProduct.create({
        data: productData
      })
    }
    console.log('✅ 6 produtos criados')

    // 4. Criar cliente
    console.log('👤 Criando cliente...')
    const hashedPasswordCustomer = await bcrypt.hash('cliente123', 12)
    
    const customerUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'João Silva',
        password: hashedPasswordCustomer,
        role: 'CUSTOMER',
        isVerified: true,
        profile: {
          create: {
            phone: '+351 918 765 432',
            address: 'Avenida da Liberdade, 456',
            city: 'Lisboa',
            postalCode: '1250-096',
            country: 'PT'
          }
        }
      }
    })
    console.log('✅ Cliente criado: <EMAIL> / cliente123')

    console.log('\n🎉 Usuários de teste criados com sucesso!')
    console.log('\n📧 Credenciais:')
    console.log('🏪 Lojista: <EMAIL> / loja123')
    console.log('   - 6 produtos no marketplace')
    console.log('   - Envio: €7 para PT, €12 para ES')
    console.log('   - Portes grátis acima de €50')
    console.log('\n👤 Cliente: <EMAIL> / cliente123')
    console.log('   - Perfil completo')
    console.log('   - Pronto para fazer compras')

  } catch (error) {
    console.error('❌ Erro ao criar usuários de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUsers()
