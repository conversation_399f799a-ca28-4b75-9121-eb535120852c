import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const problemTypeId = searchParams.get('problemTypeId')

    let whereClause: any = {
      isActive: true}

    if (categoryId) {
      whereClause.categoryId = 'categoryId'}

    if (problemTypeId) {
      whereClause.problemTypeId = 'problemTypeId'}

    const basePrices = await prisma.basePrice.findMany({
      where: whereClause,
      include: {
        category: true,
        problemType: true},
      orderBy: [
        { category: { name: asc} },
        { problemType: { name: asc} }
      ]
    })

    return NextResponse.json(basePrices)

  } catch (error) {
    console.error("Erro ao buscar preços base:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
