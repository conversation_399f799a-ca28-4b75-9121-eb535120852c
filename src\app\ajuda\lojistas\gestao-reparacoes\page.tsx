'use client'

import Link from 'next/link'
import MainHeader from '@/components/MainHeader'
import { ArrowLeft, ClipboardList, Clock, MessageCircle, CreditCard, CheckCircle, AlertTriangle } from 'lucide-react'

export default function GestaoReparacoesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader subtitle="Central de Ajuda" />

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=lojistas" className="hover:text-gray-700">Para Lojistas</Link>
              <span className="mx-2">›</span>
              <span>Gestão de reparações</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Gestão de reparações</h1>
            <p className="text-lg text-gray-600">Guia completo para gerir reparações na plataforma Revify de forma eficiente.</p>
          </div>

          <div className="prose max-w-none">
            <h2>Fluxo de trabalho</h2>
            <p>O sistema de gestão de reparações da Revify foi desenhado para simplificar o seu trabalho  e melhorar a comunicação com os clientes.</p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <ClipboardList className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">1. Receber pedidos</h3>
                  <p className="text-blue-800 mb-3">Quando recebe um novo pedido:</p>
                  <ul className="list-disc list-inside text-blue-800 space-y-1">
                    <li>Notificação imediata por email e na plataforma</li>
                    <li>Detalhes do problema reportado pelo cliente</li>
                    <li>Informações de contacto e localização</li>
                    <li>Fotos do equipamento (se 'disponíveis')</li>
                    <li>Prazo máximo para resposta: 2 horas</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Clock className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">2. Diagnóstico</h3>
                  <p className="text-green-800 mb-3">Processo de análise:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Agendar recolha ou recepção do equipamento</li>
                    <li>Realizar diagnóstico técnico completo</li>
                    <li>Identificar peças necessárias</li>
                    <li>Calcular tempo de reparação</li>
                    <li>Atualizar estado para Em diagnóstico</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CreditCard className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900 mb-2">3. Orçamento</h3>
                  <p className="text-purple-800 mb-3">Criar orçamento detalhado:</p>
                  <ul className="list-disc list-inside text-purple-800 space-y-1">
                    <li>Custo da mão-de-obra</li>
                    <li>Preço das peças necessárias</li>
                    <li>Tempo estimado de reparação</li>
                    <li>Garantia oferecida</li>
                    <li>Envio automático ao cliente</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <MessageCircle className="w-6 h-6 text-yellow-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-yellow-900 mb-2">4. Comunicação</h3>
                  <p className="text-yellow-800 mb-3">Manter cliente informado:</p>
                  <ul className="list-disc list-inside text-yellow-800 space-y-1">
                    <li>Atualizações automáticas de estado</li>
                    <li>Chat integrado para dúvidas</li>
                    <li>Fotos do progresso (opcional)</li>
                    <li>Notificações de conclusão</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">5. Conclusão</h3>
                  <p className="text-green-800 mb-3">Finalizar reparação:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Marcar como concluída</li>
                    <li>Agendar entrega ou recolha</li>
                    <li>Emitir fatura automaticamente</li>
                    <li>Solicitar avaliação do cliente</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Estados das reparações</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Estados principais</h3>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Novo:</strong> Pedido recebido</li>
                  <li>• <strong>Aceite:</strong>Confirmado pela oficina</li>
                  <li>• <strong>Em diagnóstico:</strong> A analisar</li>
                  <li>• <strong>Orçamento enviado:</strong>Aguarda aprovação</li>
                  <li>• <strong>Em reparação:</strong> Trabalho em curso</li>
                  <li>• <strong>Concluída:</strong>Pronta para entrega</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Estados especiais</h3>
                <ul className="text-sm space-y-1">
                  <li>• <strong>Aguarda peças:</strong>Peças encomendadas</li>
                  <li>• <strong>Irreparável:</strong>Não é possível reparar</li>
                  <li>• <strong>Cancelada:</strong>Cliente cancelou</li>
                  <li>• <strong>Entregue:</strong>Devolvida ao cliente</li>
                </ul>
              </div>
            </div>

            <h2>Ferramentas do dashboard</h2>
            <div className="bg-blue-50 rounded-lg p-6 my-6">
              <h3 className="font-semibold mb-3">Funcionalidades disponíveis</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Gestão</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Lista de reparações ativas</li>
                    <li>• Filtros por estado e data</li>
                    <li>• Pesquisa por cliente</li>
                    <li>• Calendário de entregas</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Comunicação</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Chat integrado</li>
                    <li>• Templates de mensagens</li>
                    <li>• Notificações automáticas</li>
                    <li>• Histórico de comunicação</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Melhores práticas</h2>
            <div className="bg-green-50 rounded-lg p-6 my-6">
              <h3 className="font-semibold mb-3">Para o sucesso da sua oficina</h3>
              <ul className="space-y-2">
                <li><strong>Resposta rápida:</strong> Responda a novos pedidos em menos de 2 horas</li>
                <li><strong>Diagnóstico preciso:</strong>Seja detalhado na análise do problema</li>
                <li><strong>Orçamentos claros:</strong>Explique cada item do orçamento</li>
                <li><strong>Comunicação regular:</strong>Atualize o cliente sobre o progresso</li>
                <li><strong>Prazos realistas:</strong> Defina prazos que consegue cumprir</li>
                <li><strong>Qualidade garantida:</strong> Teste bem antes de entregar</li>
              </ul>
            </div>

            <h2>Problemas comuns</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <AlertTriangle className="w-6 h-6 text-red-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-red-900 mb-2">Situações difíceis</h3>
                  <div className="text-red-800 space-y-3">
                    <div>
                      <h4 className="font-semibold">Cliente não aprova orçamento</h4>
                      <p className="text-sm">Ofereça alternativas mais económicas ou explique melhor o valor</p>
                    </div>
                    <div>
                      <h4 className="font-semibold">Peças demoram a chegar</h4>
                      <p className="text-sm">Comunique imediatamente e ofereça alternativas</p>
                    </div>
                    <div>
                      <h4 className="font-semibold">Problema mais complexo que esperado</h4>
                      <p className="text-sm">Contacte o cliente para renegociar orçamento</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <h2>Métricas importantes</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Indicadores de performance</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Tempo</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Tempo de resposta médio</li>
                    <li>• Cumprimento de prazos</li>
                    <li>• Duração média de reparação</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Qualidade</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Avaliação média dos clientes</li>
                    <li>• Taxa de aprovação de orçamentos</li>
                    <li>• Número de reclamações</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda/lojistas/primeiros-passos-lojista"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior: Primeiros passos
              </Link>
              <Link
                href="/ajuda/lojistas/sistema-orcamentos"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >Próximo: Sistema de orçamentos<ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
