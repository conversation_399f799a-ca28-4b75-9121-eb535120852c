'use client'

import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useEffect } from 'react'
import {
  Home,
  LineChart,
  Package,
  Package2,
  PanelLeft,
  Settings,
  ShoppingCart,
  Users2,
  Shield,
  Zap,
  Languages,
  Store
} from 'lucide-react'

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'
import UserDropdown from '@/components/UserDropdown'
import NotificationDropdown from '@/components/NotificationDropdown'
import AutoTranslate from '@/components/ui/AutoTranslate'
import { SearchInput } from '@/components/ui/search'

interface AdminDashboardLayoutProps {
  children: React.ReactNode
}

export default function AdminDashboardLayout({ children }: AdminDashboardLayoutProps) {
  const { data: session } = useSession()

  return (
    <TooltipProvider>
      <main className="flex min-h-screen w-full flex-col bg-muted/40">
        <DesktopNav />
        <div className="flex flex-col sm:gap-4 sm:py-4 sm:pl-14">
          <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6">
            <MobileNav />
            <DashboardBreadcrumb />
            <div className="relative ml-auto flex-1 md:grow-0">
              <SearchInput placeholder="Pesquisar..." />
            </div>
            <NotificationDropdown />
            <UserDropdown user={session?.user} />
          </header>
          <main className="grid flex-1 items-start gap-2 p-4 sm:px-6 sm:py-0 md:gap-4 bg-muted/40">
            {children}
          </main>
        </div>
      </main>
    </TooltipProvider>
  )
}

function DesktopNav() {
  const pathname = usePathname()

  const navItems = [
    { href: '/admin', label: 'Dashboard', icon: Home },
    { href: '/admin/utilizadores', label: 'Utilizadores', icon: Users2 },
    { href: '/admin/lojas', label: 'Lojas', icon: Store },
    { href: '/admin/reparacoes', label: 'Reparações', icon: Package },
    { href: '/admin/viral-growth', label: 'Viral Growth', icon: Zap },
    { href: '/admin/configuracoes', label: 'Configurações', icon: Settings },
    { href: '/admin/translations', label: 'Traduções', icon: Languages },
  ]

  return (
    <aside className="fixed inset-y-0 left-0 z-10 hidden w-14 flex-col border-r bg-background sm:flex">
      <nav className="flex flex-col items-center gap-4 px-2 sm:py-5">
        <Link
          href="/admin"
          className="group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:h-8 md:w-8 md:text-base"
        >
          <Shield className="h-3 w-3 transition-all group-hover:scale-110" />
          <span className="sr-only">Revify Admin</span>
        </Link>

        {navItems.map((item) => (
          <NavItem 
            key={item.href} 
            href={item.href} 
            label={item.label}
            isActive={pathname === item.href}
          >
            <item.icon className="h-5 w-5" />
          </NavItem>
        ))}
      </nav>
    </aside>
  )
}

function MobileNav() {
  const pathname = usePathname()

  const navItems = [
    { href: '/admin', label: 'Dashboard', icon: Home },
    { href: '/admin/utilizadores', label: 'Utilizadores', icon: Users2 },
    { href: '/admin/lojas', label: 'Lojas', icon: Store },
    { href: '/admin/reparacoes', label: 'Reparações', icon: Package },
    { href: '/admin/viral-growth', label: 'Viral Growth', icon: Zap },
    { href: '/admin/configuracoes', label: 'Configurações', icon: Settings },
    { href: '/admin/translations', label: 'Traduções', icon: Languages },
  ]

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button size="icon" variant="outline" className="sm:hidden">
          <PanelLeft className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="sm:max-w-xs">
        <nav className="grid gap-6 text-lg font-medium">
          <Link
            href="/admin"
            className="group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base"
          >
            <Shield className="h-5 w-5 transition-all group-hover:scale-110" />
            <span className="sr-only">Revify Admin</span>
          </Link>
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center gap-4 px-2.5 ${
                pathname === item.href 
                  ? 'text-foreground' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <item.icon className="h-5 w-5" />
              <AutoTranslate text={item.label} />
            </Link>
          ))}
        </nav>
      </SheetContent>
    </Sheet>
  )
}

function NavItem({ 
  href, 
  label, 
  children, 
  isActive 
}: { 
  href: string
  label: string
  children: React.ReactNode
  isActive?: boolean
}) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Link
          href={href}
          className={`flex h-9 w-9 items-center justify-center rounded-lg transition-colors md:h-8 md:w-8 ${
            isActive
              ? 'bg-accent text-accent-foreground'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          {children}
          <span className="sr-only">{label}</span>
        </Link>
      </TooltipTrigger>
      <TooltipContent side="right">
        <AutoTranslate text={label} />
      </TooltipContent>
    </Tooltip>
  )
}

function DashboardBreadcrumb() {
  const pathname = usePathname()
  const segments = pathname.split('/').filter(Boolean)

  return (
    <Breadcrumb className="hidden md:flex">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/admin">
              <AutoTranslate text="Admin" />
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {segments.length > 1 && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                <AutoTranslate text={segments[segments.length - 1]} />
              </BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
