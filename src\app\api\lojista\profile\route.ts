import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!profile) {
      return NextResponse.json({
        profile: {
          companyName: '',
          companyNif: '',
          description: '',
          phone: '',
          companyType: '',
          legalDocument: '',
          address: '',
          city: '',
          postalCode: '',
          customSubdomain: null,
          workingCategories: [],
          workingBrands: [],
          workingProblems: [],
          serviceRadius: 10,
          averageRepairTime: 120,
          monthlyRepairs: 0,
          expectedGrowth: 0,
          businessHours: {
            monday: { open: '09:00', close: '18:00', closed: false},
            tuesday: { open: '09:00', close: '18:00', closed: false},
            wednesday: { open: '09:00', close: '18:00', closed: false},
            thursday: { open: '09:00', close: '18:00', closed: false},
            friday: { open: '09:00', close: '18:00', closed: false},
            saturday: { open: '09:00', close: '17:00', closed: false},
            sunday: { open: '10:00', close: '16:00', closed: true}
          }
        }
      })
    }

    return NextResponse.json({
      profile: {
        companyName: profile.companyName || '',
        companyNif: profile.companyNif || '',
        description: profile.description || '',
        phone: profile.phone || '',
        companyType: profile.companyType || '',
        legalDocument: profile.legalDocument || '',
        address: profile.street || '',
        city: profile.city || '',
        postalCode: profile.postalCode || '',
        customSubdomain: profile.customSubdomain || null,
        workingCategories: profile.workingCategories || [],
        workingBrands: profile.workingBrands || [],
        workingProblems: profile.workingProblems || [],
        serviceRadius: profile.serviceRadius || 10,
        averageRepairTime: profile.averageRepairTime || 120,
        monthlyRepairs: profile.monthlyRepairs || 0,
        expectedGrowth: profile.expectedGrowth || 0,
        businessHours: profile.businessHours || {
          monday: { open: '09:00', close: '18:00', closed: false},
          tuesday: { open: '09:00', close: '18:00', closed: false},
          wednesday: { open: '09:00', close: '18:00', closed: false},
          thursday: { open: '09:00', close: '18:00', closed: false},
          friday: { open: '09:00', close: '18:00', closed: false},
          saturday: { open: '09:00', close: '17:00', closed: false},
          sunday: { open: '10:00', close: '16:00', closed: true}
        }
      }
    })

  } catch (error) {
    console.error('Erro ao buscar perfil:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()
    console.log('Dados recebidos:', 'data')

    const { companyName,
      companyNif,
      description,
      phone,
      companyType,
      legalDocument,
      workingCategories,
      workingBrands,
      workingProblems,
      serviceRadius,
      averageRepairTime,
      monthlyRepairs,
      expectedGrowth,
      businessHours,
      address,
      street,
      city,
      postalCode,
      district } = data

    // Validações básicas
    if (!phone) {
      return NextResponse.json(
        { message: Telefone é obrigatório 
},
        { status: 400 }
      )
    }

    // Verificar se já existe perfil
    const existingProfile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    let profile
    if (existingProfile) {
      // Atualizar perfil existente
      profile = await prisma.profile.update({
        where: {
          userId: session.user.id
        },
        data: {
          companyName,
          companyNif,
          description,
          phone,
          companyType,
          legalDocument,
          workingCategories,
          workingBrands,
          workingProblems,
          serviceRadius,
          averageRepairTime,
          monthlyRepairs,
          expectedGrowth,
          businessHours,
          street: address || street,
          city,
          postalCode, district }
      })
    } else {
      // Criar novo perfil
      profile = await prisma.profile.create({
        data: {
          userId: session.user.id,
          companyName,
          companyNif,
          description,
          phone,
          companyType,
          legalDocument,
          workingCategories,
          workingBrands,
          workingProblems,
          serviceRadius,
          averageRepairTime,
          monthlyRepairs,
          expectedGrowth,
          businessHours,
          street: address || street,
          city,
          postalCode, district }
      })
    }

    console.log(Perfil salvo:, 'profile')

    return NextResponse.json({
      message: 'Perfil atualizado com sucesso',
      'profile'
})

  } catch (error) {
    console.error('Erro ao salvar perfil:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
