import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar estatísticas do cliente
    const [
      totalRepairs,
      activeRepairs,
      orders
    ] = await Promise.all([
      // Total de reparações
      prisma.repair.count({
        where: {
          customerId: session.user.id
        }
      }),
      // Reparações ativas (não concluídas nem canceladas)
      prisma.repair.count({
        where: {
          customerId: session.user.id,
          status: {
            notIn: ['COMPLETED', 'DELIVERED', 'CANCELLED']
          }
        }
      }),
      // Encomendas (assumindo que existe um modelo Order)
      0 // Por agora retornar 0, pode ser implementado quando o marketplace estiver pronto
    ])

    return NextResponse.json({
      totalRepairs,
      activeRepairs,
      orders,
      reviews: 0 // Por agora retornar 0, pode ser implementado quando o sistema de reviews estiver pronto
    })

  } catch (error) {
    console.error('Erro ao buscar estatísticas do dashboard:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
