import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET() {
  try {
    const brands = await prisma.brand.findMany({
      where: {
        isActive: true},
      include: {
        _count: {
          select: {
            deviceModels: true}
        }
      },
      orderBy: {
        name: asc}
    })

    // Se não houver marcas, criar algumas básicas
    if (brands.length === 0) {
      const basicBrands = [
        { name: Apple, isActive: true},
        { name: Samsung, isActive: true},
        { name: <PERSON><PERSON><PERSON>, isActive: true},
        { name: <PERSON><PERSON>, isActive: true},
        { name: OnePlus, isActive: true},
        { name: Google, isActive: true},
        { name: Sony, isActive: true},
        { name: LG, isActive: true}
      ]

      for (const brand of basicBrands) {
        await prisma.brand.create({
          data: brand
})
      }

      // Buscar novamente após criar
      const newBrands = await prisma.brand.findMany({
        where: { isActive: true},
        include: {
          _count: {
            select: {
              deviceModels: true}
          }
        },
        orderBy: { name: asc}
      })

      return NextResponse.json(newBrands.map(brand => ({
        id: brand.id,
        name: brand.name,
        isActive: brand.isActive,
        productCount: brand._count.deviceModels
      })))
    }

    return NextResponse.json(brands)

  } catch (error) {
    console.error(Erro ao buscar marcas:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, logo } = await request.json()

    if (!name) {
      return NextResponse.json(
        { message: "Nome da marca é obrigatório" },
        { status: 400 }
      )
    }

    // Verificar se a marca já existe
    const existingBrand = await prisma.brand.findUnique({
      where: { name
}
    })

    if (existingBrand) {
      return NextResponse.json(
        { message: "Esta marca já existe" },
        { status: 400 }
      )
    }

    const brand = await prisma.brand.create({
      data: {
        name,
        logo: logo || null,
        isActive: true},
      include: {
        _count: {
          select: {
            deviceModels: true}
        }
      }
    })

    return NextResponse.json(brand, { status: 201 })

  } catch (error) {
    console.error("Erro ao criar marca:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
