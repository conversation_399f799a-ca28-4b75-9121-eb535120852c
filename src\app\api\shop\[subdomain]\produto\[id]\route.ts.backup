import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string; id: string }> }
) {
  try {
    const { subdomain, id } = await params

    if (!subdomain || !id) {
      return NextResponse.json(
        { message: 'Subdomínio e ID do produto são obrigatórios' },
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomínio
    const shop = await prisma.user.findFirst({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          customSubdomain: subdomain
        }
      },
      include: {
        subscription: {
          include: {
            plan: {
              select: {
                miniStore: true
              }
            }
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se a loja tem acesso à funcionalidade de mini-loja
    if (!shop.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: 'Loja online não disponível para esta loja' },
        { status: 403 }
      )
    }

    // Buscar produto específico da loja
    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: id,
        sellerId: shop.id,
        isActive: true
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        brand: {
          select: {
            id: true,
            name: true
          }
        },
        deviceModel: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    // Formatar dados para o frontend
    const formattedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      images: product.images,
      category: product.category,
      brand: product.brand,
      deviceModel: product.deviceModel,
      stock: product.stock,
      condition: product.condition,
      createdAt: product.createdAt
    }

    return NextResponse.json({
      success: true,
      product: formattedProduct
    })

  } catch (error) {
    console.error('Erro ao buscar produto da loja:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
