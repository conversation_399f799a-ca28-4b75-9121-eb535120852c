'use client'

import Link from 'next/link'
import { ArrowLeft, User, Mail, Lock, CheckCircle } from 'lucide-react'

export default function CriarContaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="mr-8">
                <Link href="/" className="font-bold text-black text-xl">
                  Revify
                </Link>
                <div className="text-xs text-gray-600 font-light">Central de Ajuda</div>
              </div>
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar à Central de Ajuda
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=clientes" className="hover:text-gray-700">Para Clientes</Link>
              <span className="mx-2">›</span>
              <span>Como criar uma conta</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Como criar uma conta</h1>
            <p className="text-base text-gray-600">
              Aprenda a criar a sua conta na plataforma Revify em poucos passos simples.
            </p>
          </div>

          <div className="prose max-w-none">
            <h2>Passo a passo para criar conta</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <User className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-base font-semibold text-blue-900 mb-2">Passo 1: Aceder à página de registo</h3>
                  <p className="text-blue-800">
                    Clique no botão "Registar" no canto superior direito da página principal ou aceda diretamente a 
                    <Link href="/auth/register" className="underline ml-1">esta ligação</Link>.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Mail className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-base font-semibold text-green-900 mb-2">Passo 2: Preencher os dados</h3>
                  <p className="text-green-800 mb-3">Preencha o formulário com as seguintes informações:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Nome completo</li>
                    <li>Endereço de email válido</li>
                    <li>Palavra-passe segura (mínimo 8 caracteres)</li>
                    <li>Confirmação da palavra-passe</li>
                    <li>Número de telefone (opcional)</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Lock className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-base font-semibold text-purple-900 mb-2">Passo 3: Verificar email</h3>
                  <p className="text-purple-800">
                    Após submeter o formulário, receberá um email de verificação. Clique no link para ativar a sua conta.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CheckCircle className="w-6 h-6 text-yellow-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-base font-semibold text-yellow-900 mb-2">Passo 4: Completar perfil</h3>
                  <p className="text-yellow-800">
                    Após a verificação, faça login e complete o seu perfil com informações adicionais como morada e preferências.
                  </p>
                </div>
              </div>
            </div>

            <h2>Dicas importantes</h2>
            <ul>
              <li><strong>Palavra-passe segura:</strong> Use uma combinação de letras maiúsculas, minúsculas, números e símbolos.</li>
              <li><strong>Email válido:</strong> Certifique-se de que tem acesso ao email fornecido para verificação.</li>
              <li><strong>Dados corretos:</strong> Forneça informações precisas para evitar problemas futuros.</li>
            </ul>

            <h2>Problemas comuns</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3>Não recebi o email de verificação</h3>
              <p>Verifique a pasta de spam/lixo. Se ainda não encontrar, contacte o suporte.</p>
              
              <h3>Email já está em uso</h3>
              <p>Se já tem uma conta, use a opção "Esqueci-me da palavra-passe" para recuperar o acesso.</p>
              
              <h3>Erro ao submeter formulário</h3>
              <p>Verifique se todos os campos obrigatórios estão preenchidos corretamente.</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar à Central de Ajuda
              </Link>
              <Link
                href="/ajuda/clientes/fazer-reparacao"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                Próximo: Como fazer uma reparação
                <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
