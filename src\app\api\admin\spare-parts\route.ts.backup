import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const parts = await prisma.sparePart.findMany({
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        brand: {
          select: {
            id: true,
            name: true
          }
        },
        deviceModel: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      parts
    })

  } catch (error) {
    console.error('Erro ao buscar peças:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const {
      name,
      description,
      sku,
      price,
      stock,
      categoryId,
      brandId,
      deviceModelId,
      deliveryTime,
      images,
      compatibility
    } = await request.json()

    if (!name || !sku || !price || !categoryId || stock === undefined) {
      return NextResponse.json(
        { message: 'Campos obrigatórios: nome, SKU, preço, categoria e stock' },
        { status: 400 }
      )
    }

    // Verificar se SKU já existe
    const existingSku = await prisma.sparePart.findUnique({
      where: { sku }
    })

    if (existingSku) {
      return NextResponse.json(
        { message: 'SKU já existe' },
        { status: 400 }
      )
    }

    const part = await prisma.sparePart.create({
      data: {
        name,
        description: description || null,
        sku,
        price,
        stock,
        categoryId,
        brandId: brandId || null,
        deviceModelId: deviceModelId || null,
        deliveryTime: deliveryTime || 3,
        images: images || [],
        compatibility: compatibility || [],
        availability: true
      }
    })

    return NextResponse.json({
      message: 'Peça criada com sucesso',
      part
    })

  } catch (error) {
    console.error('Erro ao criar peça:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
