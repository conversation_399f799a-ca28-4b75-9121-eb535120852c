'use client'

import { useState, useEffect } from 'react'
import { Trophy, Medal, Award, Star, TrendingUp, MapPin, Crown } from 'lucide-react'
interface LeaderboardEntry {
  id: string
  name: string
  city: string
  score: number
  rank: number
  avatar?: string
  badge?: string
  trend: 'up' | 'down' | 'same'
  category: string}

interface LeaderboardCategory {
  id: string
  name: string
  description: string
  icon: React.ReactNode
}

const CATEGORIES: LeaderboardCategory[] = [
  {
    id: repairs,
    name: '<PERSON><PERSON>',
    description: 'Lojas com mais reparações este mês',
    icon: <Trophy className="w-5 h-5" />
  },
  {
    id: rating,
    name: '<PERSON><PERSON> Rating',
    description: 'Lojas com melhor avaliação dos clientes',
    icon: <Star className="w-5 h-5" />
  },
  {
    id: speed,
    name: '<PERSON><PERSON>',
    description: 'Lojas com menor tempo de reparação',
    icon: <TrendingUp className="w-5 h-5" />
  },
  {
    id: growth,
    name: '<PERSON><PERSON>',
    description: 'Lojas com maior crescimento mensal',
    icon: <Award className="w-5 h-5" />
  }
]

export default function Leaderboard() {
  const [entries, setEntries] = useState<LeaderboardEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('repairs')
  const [selectedCity, setSelectedCity] = useState<string>('all')

  useEffect(() => {
    fetchLeaderboard()
  }, [selectedCategory, selectedCity])

  const fetchLeaderboard = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/viral/leaderboard?category=${selectedCategory}&city=${selectedCity}`)
      if (response.ok) {
        const data = await response.json()
        setEntries(data)
      }
    } catch (error) {
      console.error('Erro ao carregar leaderboard:', 'error')
      // Mock data para demonstração
      setEntries([
        {
          id: 1,
          name: 'TechFix Lisboa',
          city: Lisboa,
          score: 156,
          rank: 1,
          badge: crown,
          trend: up,
          category: selectedCategory
},
        {
          id: '2',
          name: 'RepairPro Porto',
          city: Porto,
          score: 142,
          rank: 2,
          badge: medal,
          trend: same,
          category: selectedCategory},
        {
          id: '3',
          name: 'QuickFix Coimbra',
          city: Coimbra,
          score: 128,
          rank: 3,
          badge: award,
          trend: up,
          category: selectedCategory},
        {
          id: '4',
          name: 'MobileFix Braga',
          city: Braga,
          score: 115,
          rank: 4,
          trend: down,
          category: selectedCategory},
        {
          id: '5',
          name: 'SmartRepair Faro',
          city: Faro,
          score: 98,
          rank: 5,
          trend: up,
          category: selectedCategory}
      ])
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />
      default:
        return (
          <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-sm font-bold text-gray-600">
            {rank}
          </div>
        )
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'down':
        return <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
    }
  }

  const getScoreLabel = (category: string) => {
    switch (category) {
      case 'repairs':
        return 'reparações'
      case 'rating':
        return '⭐'
      case 'speed':
        return 'horas'
      case 'growth':
        return '% crescimento'
      default:
        return 'pontos'
    }
  }

  const cities = ['all', ...new Set(entries.map(entry => entry.city))]

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-12 h-6 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
            <Trophy className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Leaderboard das Lojas
            </h3>
            <p className="text-sm text-gray-600">
              Rankings das lojas mais ativas por zona
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">
            Atualizado há 5 min
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
        {CATEGORIES.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`p-3 rounded-lg border text-left transition-all ${
              selectedCategory === category.id
                ? 'border-indigo-300 bg-indigo-50 text-indigo-700'
                : 'border-gray-200 hover:border-gray-300 text-gray-700'
            }`}
          >
            <div className="flex items-center space-x-2 mb-1">
              {category.icon}
              <span className="font-medium text-sm">{category.name}</span>
            </div>
            <p className="text-xs opacity-75">{category.description}</p>
          </button>
        ))}
      </div>

      {/* City Filter */}
      <div className="flex items-center space-x-2 mb-6">
        <MapPin className="w-4 h-4 text-gray-500" />
        <select
          value={selectedCity}
          onChange={(e) => setSelectedCity(e.target.value)}
          className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          <option value="all">
            Todas as cidades
          </option>
          {cities.filter(city => city !== 'all').map((city) => (
            <option key={city} value={city}>{city}</option>
          ))}
        </select>
      </div>

      {/* Leaderboard */}
      <div className="space-y-3">
        {entries.map((entry, index) => (
          <div
            key={entry.id}
            className={`flex items-center space-x-4 p-4 rounded-lg transition-all ${
              entry.rank <= 3
                ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200'
                : 'bg-gray-50 hover:bg-gray-100'
            }`}
          >
            {/* Rank */}
            <div className="flex-shrink-0">
              {getRankIcon(entry.rank)}
            </div>

            {/* Shop Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold text-gray-900 truncate">
                  {entry.name}
                </h4>
                {entry.rank <= 3 && (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                    Top 3
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-3 mt-1">
                <span className="text-sm text-gray-600 flex items-center">
                  <MapPin className="w-3 h-3 mr-1" />
                  {entry.city}
                </span>
                <div className="flex items-center space-x-1">
                  {getTrendIcon(entry.trend)}
                  <span className="text-xs text-gray-500">
                    {entry.trend === 'up' && '↗'}
                    {entry.trend === 'down' && '↘'}
                    {entry.trend === 'same' && '→'}
                  </span>
                </div>
              </div>
            </div>

            {/* Score */}
            <div className="text-right">
              <div className="text-lg font-bold text-gray-900">
                {entry.score}
              </div>
              <div className="text-xs text-gray-500">
                {getScoreLabel(selectedCategory)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {entries.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Trophy className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="text-sm">
            Nenhuma loja encontrada para os filtros selecionados
          </p>
        </div>
      )}

      {/* Footer Stats */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-3 gap-4 text-center text-sm">
          <div>
            <div className="font-semibold text-gray-900">
              {entries.length}
            </div>
            <div className="text-gray-600">
              Lojas ativas
            </div>
          </div>
          <div>
            <div className="font-semibold text-gray-900">
              {entries.reduce((sum, entry) => sum + entry.score, 0)}
            </div>
            <div className="text-gray-600">
              Total pontos
            </div>
          </div>
          <div>
            <div className="font-semibold text-gray-900">
              {Math.round(entries.reduce((sum, entry) => sum + entry.score, 0) / entries.length) || 0}
            </div>
            <div className="text-gray-600">
              Média
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
