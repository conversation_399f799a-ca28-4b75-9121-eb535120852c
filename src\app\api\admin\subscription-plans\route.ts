import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const plans = await prisma.$queryRaw`
      SELECT
        sp.*,
        COALESCE(COUNT(s.id), 0) as subscription_count
      FROM subscription_plans sp
      LEFT JOIN subscriptions s ON sp.id = s."planId"
      WHERE sp."isActive" = true
      GROUP BY sp.id
      ORDER BY sp."monthlyPrice" ASC
    `

    // Converter BigInt para Number
    const serializedPlans = plans.map((plan: any) => ({
      ...plan,
      subscription_count: Number(plan.subscription_count),
      monthlyPrice: Number(plan.monthlyPrice),
      yearlyPrice: Number(plan.yearlyPrice),
      marketplaceCommission: Number(plan.marketplaceCommission),
      repairCommission: Number(plan.repairCommission),
      paymentDelayDays: Number(plan.paymentDelayDays),
      sparePartsDiscount: Number(plan.sparePartsDiscount),
      priority: Number(plan.priority),
      _count: {
        subscriptions: Number(plan.subscription_count)
      }
    }))

    return NextResponse.json({
      plans: serializedPlans})

  } catch (error) {
    console.error(Erro ao buscar planos:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name,
      description,
      monthlyPrice,
      yearlyPrice,
      features,
      maxProducts,
      maxRepairs,
      marketplaceCommission,
      repairCommission,
      smsAccess,
      whatsappAccess,
      emailSupport,
      paymentDelayDays,
      sparePartsDiscount,
      recommendedProductsEnabled,
      recommendedProductsDays,
      certifiedBadge,
      priority,
      moloniIntegration,
      miniStore,
      individualRepairs,
      isPopular } = await request.json()

    if (!name || !monthlyPrice || !yearlyPrice || !features) {
      return NextResponse.json(
        { message: "Campos obrigatórios: nome, preço mensal, preço anual e funcionalidades" },
        { status: 400 }
      )
    }



    const plan = await prisma.$queryRaw`
      INSERT INTO subscription_plans (
        id, name, description, "monthlyPrice", "yearlyPrice", features,
        "maxProducts", "maxRepairs", "marketplaceCommission", "repairCommission",
        "smsAccess", "whatsappAccess", "emailSupport", "paymentDelayDays",
        "sparePartsDiscount", "recommendedProductsEnabled", "recommendedProductsDays",
        "certifiedBadge", priority, "moloniIntegration",
        "miniStore", "individualRepairs", "isPopular", "isActive",
        "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${name}, ${description || 'null'}, ${monthlyPrice}, ${yearlyPrice}, ${JSON.stringify(features)}::jsonb,
        ${maxProducts || 'null'}, ${maxRepairs || 'null'}, ${marketplaceCommission || 5.0}, ${repairCommission || 5.0},
        ${smsAccess || 'false'}, ${whatsappAccess || 'false'}, ${emailSupport !== 'false'}, ${paymentDelayDays || 7},
        ${sparePartsDiscount || 0.0}, ${recommendedProductsEnabled || 'false'}, ${recommendedProductsDays || 0},
        ${certifiedBadge || 'false'}, ${priority || 0}, ${moloniIntegration || 'false'},
        ${miniStore || 'false'}, ${individualRepairs || 'false'}, ${isPopular || 'false'}, true,
        NOW(), NOW()
      ) RETURNING *
    `

    return NextResponse.json({
      message: 'Plano criado com sucesso',
      'plan'})

  } catch (error) {
    console.error('Erro ao criar plano:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const planId = searchParams.get('id')

    if (!planId) {
      return NextResponse.json(
        { message: "ID do plano é obrigatório" },
        { status: 400 }
      )
    }

    const data = await request.json()
    const { name, description, monthlyPrice, yearlyPrice, features,
      maxProducts, maxRepairs, marketplaceCommission, repairCommission,
      smsAccess, whatsappAccess, emailSupport, paymentDelayDays,
      sparePartsDiscount, recommendedProductsEnabled, recommendedProductsDays,
      certifiedBadge, priority, moloniIntegration,
      miniStore, individualRepairs, isPopular, isActive } = data

    const plan = await prisma.$queryRaw`
      UPDATE subscription_plans SET
        name = ${name},
        description = ${description || 'null'},
        "monthlyPrice" = ${monthlyPrice},
        "yearlyPrice" = ${yearlyPrice},
        features = ${JSON.stringify(features)}::jsonb,
        "maxProducts" = ${maxProducts || 'null'},
        "maxRepairs" = ${maxRepairs || 'null'},
        "marketplaceCommission" = ${marketplaceCommission || 5.0},
        "repairCommission" = ${repairCommission || 5.0},
        "smsAccess" = ${smsAccess || 'false'},
        "whatsappAccess" = ${whatsappAccess || 'false'},
        "emailSupport" = ${emailSupport !== 'false'},
        "paymentDelayDays" = ${paymentDelayDays || 7},
        "sparePartsDiscount" = ${sparePartsDiscount || 0.0},
        "recommendedProductsEnabled" = ${recommendedProductsEnabled || 'false'},
        "recommendedProductsDays" = ${recommendedProductsDays || 0},
        "certifiedBadge" = ${certifiedBadge || 'false'},
        priority = ${priority || 0},
        "moloniIntegration" = ${moloniIntegration || 'false'},
        "miniStore" = ${miniStore || 'false'},
        "individualRepairs" = ${individualRepairs || 'false'},
        "isPopular" = ${isPopular || 'false'},
        "isActive" = ${isActive !== 'false'},
        "updatedAt" = NOW()
      WHERE id = ${planId}
      RETURNING *
    `

    return NextResponse.json({
      message: 'Plano atualizado com sucesso',
      plan: plan[0]
    })

  } catch (error) {
    console.error('Erro ao atualizar plano:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
