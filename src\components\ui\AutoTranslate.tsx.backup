'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'

interface AutoTranslateProps {
  text: string
  className?: string
  fallback?: string
  showLoading?: boolean
  loadingText?: string
  as?: keyof JSX.IntrinsicElements
  children?: React.ReactNode
}

/**
 * Componente que traduz automaticamente qualquer texto usando DeepL
 * 
 * Uso:
 * <AutoTranslate text="Olá mundo" />
 * <AutoTranslate text="Produto em destaque" as="h1" className="text-2xl" />
 * <AutoTranslate text={produto.nome} fallback="Nome não disponível" />
 */
export default function AutoTranslate({
  text,
  className = '',
  fallback = '',
  showLoading = true,
  loadingText = '...',
  as: Component = 'span',
  children
}: AutoTranslateProps) {
  const { t, tSync, currentLanguage, isTranslating } = useTranslation()
  const [translatedText, setTranslatedText] = useState(text)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const translateText = async () => {
      if (!text || currentLanguage === 'pt') {
        setTranslatedText(text || fallback)
        return
      }

      // Verificar se já está em cache (tradução síncrona)
      const cachedTranslation = tSync(text)
      if (cachedTranslation !== text) {
        setTranslatedText(cachedTranslation)
        return
      }

      // Se não estiver em cache, traduzir assincronamente
      setIsLoading(true)
      try {
        const translated = await t(text)
        setTranslatedText(translated || fallback)
      } catch (error) {
        console.error('Erro na tradução:', error)
        setTranslatedText(text || fallback)
      } finally {
        setIsLoading(false)
      }
    }

    translateText()
  }, [text, currentLanguage, fallback, t, tSync])

  const displayText = isLoading && showLoading ? loadingText : translatedText

  return (
    <Component className={className}>
      {children ? children : displayText}
    </Component>
  )
}

/**
 * Hook para traduzir múltiplos textos de uma vez
 * Útil para listas de produtos, categorias, etc.
 */
export function useAutoTranslate() {
  const { tBatch, currentLanguage } = useTranslation()
  const [isTranslating, setIsTranslating] = useState(false)

  const translateTexts = async (texts: string[]): Promise<string[]> => {
    if (currentLanguage === 'pt') return texts

    setIsTranslating(true)
    try {
      const translated = await tBatch(texts)
      return translated
    } catch (error) {
      console.error('Erro na tradução em lote:', error)
      return texts
    } finally {
      setIsTranslating(false)
    }
  }

  return { translateTexts, isTranslating }
}

/**
 * Componente para traduzir listas de itens
 */
interface AutoTranslateListProps<T> {
  items: T[]
  textExtractor: (item: T) => string
  renderItem: (item: T, translatedText: string, index: number) => React.ReactNode
  className?: string
  loadingComponent?: React.ReactNode
}

export function AutoTranslateList<T>({
  items,
  textExtractor,
  renderItem,
  className = '',
  loadingComponent = <div>Traduzindo...</div>
}: AutoTranslateListProps<T>) {
  const { translateTexts, isTranslating } = useAutoTranslate()
  const [translatedTexts, setTranslatedTexts] = useState<string[]>([])

  useEffect(() => {
    const translateItems = async () => {
      const texts = items.map(textExtractor)
      const translated = await translateTexts(texts)
      setTranslatedTexts(translated)
    }

    translateItems()
  }, [items, textExtractor, translateTexts])

  if (isTranslating && translatedTexts.length === 0) {
    return loadingComponent
  }

  return (
    <div className={className}>
      {items.map((item, index) => 
        renderItem(item, translatedTexts[index] || textExtractor(item), index)
      )}
    </div>
  )
}
