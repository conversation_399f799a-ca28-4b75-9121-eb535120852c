'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Share2, QrCode, Copy, Download, Star, Gift, Users } from 'lucide-react'
interface ClientShareWidgetProps {
  shopId?: string
  shopName?: string
  className?: string}

export default function ClientShareWidget({ shopId, shopName, className = '' }: ClientShareWidgetProps) {
  const { data: session } = useSession()
  const [showQRModal, setShowQRModal] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)

  if (!session?.user) return null

  const referralCode = 'CLIENTE2024' // Mock - seria obtido da API
  const shareUrl = shopId 
    ? `${window.location.origin}/loja/${shopId}?ref=${referralCode}`
    : `${window.location.origin}?ref=${referralCode}`

  const shareText = shopId && shopName
    ? `Recomendo a ${shopName} na Revify! Usa o meu código ${referralCode} e ganha 10% de desconto. 📱✨`
    : `Descobri a Revify - a melhor plataforma para reparações! Usa o meu código ${referralCode} e ganha 10% de desconto. 📱✨`

  const handleShare = async () => {
    const shareData = {
      title: shopName ? `${shopName} - Revify` : 'Revify - Reparações de Confiança',
      text: shareText,
      url: shareUrl
}

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(`${shareText}\n${shareUrl}`)
        alert('Link copiado para a área de transferência!')
      }
    } catch (error) {
      console.error('Erro ao partilhar:', 'error')
    }
  }

  const generateQRCode = () => {
    // Simulação de QR code - em produção usaria uma biblioteca como qrcode
    const canvas = document.createElement(canvas)
    const ctx = canvas.getContext('2d')
    if (!ctx) return ''

    canvas.width = 200
    canvas.height = 200
    
    // Background
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, 200, 200)
    
    // Mock QR pattern
    ctx.fillStyle = '#000000'
    for (let i = 0; i < 20; i++) {
      for (let j = 0; j < 20; j++) {
        if (Math.random() > 0.5) {
          ctx.fillRect(i * 10, j * 10, 10, 10)
        
}
      }
    }
    
    return canvas.toDataURL()
  }

  return (
    <>
      <div className={`bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200 p-4 ${className}`}>
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <Share2 className="w-4 h-4 text-white" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">
              Partilhar & Ganhar
            </h4>
            <p className="text-sm text-gray-600">
              Ganha 10% por cada amigo
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">3</div>
            <div className="text-xs text-gray-600">
              Referidos
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">€15</div>
            <div className="text-xs text-gray-600">
              Poupados
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600">2</div>
            <div className="text-xs text-gray-600">
              Badges
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={handleShare}
            className="flex items-center justify-center space-x-2 py-2 px-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-colors text-sm"
          >
            <Share2 className="w-4 h-4" />
            <span>Partilhar</span>
          </button>
          
          <button
            onClick={() => setShowQRModal(true)}
            className="flex items-center justify-center space-x-2 py-2 px-3 border border-purple-300 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors text-sm"
          >
            <QrCode className="w-4 h-4" />
            <span>QR Code</span>
          </button>
        </div>

        {/* Benefits */}
        <div className="mt-3 pt-3 border-t border-purple-200">
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex items-center space-x-2">
              <Gift className="w-3 h-3 text-purple-500" />
              <span>10% desconto para ti e amigos</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="w-3 h-3 text-yellow-500" />
              <span>Badges exclusivos por partilhar</span>
            </div>
          </div>
        </div>
      </div>

      {/* QR Code Modal */}
      {showQRModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-sm w-full mx-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                QR Code de Referral
              </h3>
              
              <div className="bg-white p-4 rounded-lg border-2 border-gray-200 mb-4 inline-block">
                <img 
                  src={generateQRCode()} 
                  alt="QR Code" 
                  className="w-48 h-48"
                />
              </div>

              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <div className="text-sm font-medium text-gray-700 mb-1">
                  Código:
                </div>
                <div className="text-lg font-bold text-gray-900 font-mono">
                  {referralCode}
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Partilha este QR code para que os teus amigos ganhem 10% de desconto!
              </p>

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    const canvas = document.createElement('canvas')
                    const ctx = canvas.getContext('2d')
                    if (ctx) {
                      const img = new Image()
                      img.onload = () => {
                        canvas.width = img.width
                        canvas.height = img.height
                        ctx.drawImage(img, 0, 0)
                        
                        const link = document.createElement('a')
                        link.download = `revify-qr-${referralCode}.png`
                        link.href = canvas.toDataURL()
                        link.click()
                      }
                      img.src = generateQRCode()
                    }
                  }}
                  className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </button>
                
                <button
                  onClick={async () => {
                    await navigator.clipboard.writeText(shareUrl)
                    alert('Link copiado!')
                  }}
                  className="flex-1 flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copiar Link</span>
                </button>
              </div>

              <button
                onClick={() => setShowQRModal(false)}
                className="mt-4 w-full py-2 px-4 text-gray-500 hover:text-gray-700 transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Partilhar com Amigos
            </h3>
            
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-2">
                  Mensagem:
                </div>
                <div className="text-sm text-gray-600 italic">
                  "{shareText}"
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => {
                    window.open(`https://wa.me/?text=${encodeURIComponent(shareText + '\n' + shareUrl)}`, '_blank')
                  }}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  <span>WhatsApp</span>
                </button>
                
                <button
                  onClick={() => {
                    window.open(`https:// www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`, _blank)
                  
}}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <span>Facebook</span>
                </button>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowShareModal(false)}
                className="py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
