import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const { id: shopId } = await params

    const shop = await prisma.user.findUnique({
      where: {
        id: shopId,
        role: REPAIR_SHOP},
      include: {
        profile: {
          select: {
            companyName: true,
            description: true,
            phone: true,
            street: true,
            city: true,
            postalCode: true,
            serviceRadius: true,
            averageRepairTime: true,
            workingCategories: true,
            workingBrands: true,
            businessHours: true}
        },
        repairShopPrices: {
          select: {
            price: true,
            estimatedTime: true,
            problemType: {
              select: {
                name: true}
            }
          }
        },
        shopReviews: {
          select: {
            rating: true,
            comment: true,
            customer: {
              select: {
                name: true}
            },
            createdAt: true},
          orderBy: {
            createdAt: desc},
          take: 10
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: "Loja não encontrada" },
        { status: 404 }
      )
    }

    // Calcular rating médio
    const ratings = shop.shopReviews.map(r => r.rating)
    const averageRating = ratings.length > 0
      ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
      : 0

    // Encontrar preço mais baixo
    const lowestPrice = shop.repairShopPrices.length > 0
      ? Math.min(...shop.repairShopPrices.map(p => Number(p.price)))
      : 25

    // Calcular tempo médio de reparação
    const averageTime = shop.profile?.averageRepairTime ||
      (shop.repairShopPrices.length > 0
        ? shop.repairShopPrices.reduce((sum, p) => sum + (p.estimatedTime || 120), 0) / shop.repairShopPrices.length
        : 120)

    // Buscar nomes das categorias se existirem IDs
    let specialties = ["Reparações Gerais"]
    if (shop.profile?.workingCategories && Array.isArray(shop.profile.workingCategories)) {
      try {
        // Verificar se são IDs (strings 'longas') ou nomes
        const hasIds = shop.profile.workingCategories.some((cat: any) =>
          typeof cat === 'string' && cat.length > 20
        )

        if (hasIds) {
          // Buscar nomes das categorias
          const categories = await prisma.category.findMany({
            where: {
              id: { in: shop.profile.workingCategories 
}
            },
            select: { name: true}
          })
          specialties = categories.map(cat => cat.name)
        } else {
          // Já são nomes
          specialties = shop.profile.workingCategories
        }
      } catch (error) {
        console.error(Erro ao buscar categorias:, 'error')
        specialties = ["Reparações Gerais"]
      
}
    }

    const shopData = {
      id: shop.id,
      name: shop.name,
      email: shop.email,
      isVerified: shop.isVerified,
      profile: shop.profile,
      rating: Number(averageRating.toFixed(1)),
      reviewCount: shop.shopReviews.length,
      basePrice: lowestPrice,
      estimatedTime: averageTime,
      responseTime: averageTime < 60 ? `${averageTime}min` : `${Math.round(averageTime / 60)}h`,
      specialties: specialties,
      reviews: shop.shopReviews,
      prices: shop.repairShopPrices
    }

    return NextResponse.json(shopData)

  } catch (error) {
    console.error('Erro ao buscar loja:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
