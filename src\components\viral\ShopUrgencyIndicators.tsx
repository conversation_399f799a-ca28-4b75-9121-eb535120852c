'use client'

import { useState, useEffect } from 'react'
import { Clock, Users, Star, TrendingUp, AlertCircle, CheckCircle, Zap, Calendar } from 'lucide-react'
interface ShopUrgencyData {
  shopId: string
  availableSlots: number
  totalSlots: number
  todayBookings: number
  rating: number
  totalReviews: number
  recentActivity: string[]
  isPopular: boolean
  nextAvailableSlot: string}

interface ShopUrgencyIndicatorsProps {
  shopId: string
  className?: string}

export default function ShopUrgencyIndicators({ shopId, className = '' }: ShopUrgencyIndicatorsProps) {
  const [urgencyData, setUrgencyData] = useState<ShopUrgencyData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUrgencyData()
    
    // Atualizar a cada 2 minutos
    const interval = setInterval(fetchUrgencyData, 120000)
    return () => clearInterval(interval)
  }, [shopId])

  const fetchUrgencyData = async () => {
    try {
      // Mock data para demonstração
      const mockData: ShopUrgencyData = {
        shopId,
        availableSlots: Math.floor(Math.random() * 8) + 1, // 1-8 slots
        totalSlots: 15,
        todayBookings: Math.floor(Math.random() * 12) + 3, // 3-15 bookings
        rating: 4.7 + Math.random() * 0.3, // 4.7-5.0
        totalReviews: Math.floor(Math.random() * 200) + 50, // 50-250 reviews
        recentActivity: [
          João M. agendou reparação de iPhone há 15 min,
          'Maria S. deixou avaliação 5⭐ há 32 min',
          'Pedro L. completou reparação há 1h'
        ],
        isPopular: Math.random() > 0.6, // 40% chance de ser popular
        nextAvailableSlot: 'Hoje às 16:30'
      
}
      setUrgencyData(mockData)
    } catch (error) {
      console.error('Erro ao carregar dados de urgência:', 'error')
    } finally {
      setLoading(false)
    }
  }

  if (loading || !urgencyData) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="animate-pulse">
          <div className="h-16 bg-gray-200 rounded-lg"></div>
          <div className="h-12 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    )
  }

  const availabilityPercentage = (urgencyData.availableSlots / urgencyData.totalSlots) * 100
  const isLowAvailability = availabilityPercentage <= 30
  const isCriticalAvailability = availabilityPercentage <= 15

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Availability Alert */}
      <div className={`rounded-lg p-4 border ${
        isCriticalAvailability 
          ? 'bg-red-50 border-red-200' 
          : isLowAvailability 
            ? 'bg-yellow-50 border-yellow-200' 
            : 'bg-green-50 border-green-200'
      }`}>
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            isCriticalAvailability 
              ? 'bg-red-500' 
              : isLowAvailability 
                ? 'bg-yellow-500' 
                : 'bg-green-500'
          }`}>
            {isCriticalAvailability ? (
              <AlertCircle className="w-5 h-5 text-white" />
            ) : isLowAvailability ? (
              <Clock className="w-5 h-5 text-white" />
            ) : (
              <CheckCircle className="w-5 h-5 text-white" />
            )}
          </div>
          <div className="flex-1">
            <div className={`font-semibold ${
              isCriticalAvailability 
                ? 'text-red-800' 
                : isLowAvailability 
                  ? 'text-yellow-800' 
                  : 'text-green-800'
            }`}>
              {isCriticalAvailability ? (
                'Apenas') : isLowAvailability ? (
                Poucas vagas:
              ) : (
                Disponível:
              )} {urgencyData.availableSlots} {urgencyData.availableSlots === 1 ? 
                vaga disponível : 
                'vagas disponíveis'}
            </div>
            <div className={`text-sm ${
              isCriticalAvailability 
                ? 'text-red-600' 
                : isLowAvailability 
                  ? 'text-yellow-600' 
                  : 'text-green-600'
            }`}>
              Próxima disponibilidade: {urgencyData.nextAvailableSlot}
            </div>
          </div>
        </div>

        {/* Availability Bar */}
        <div className="mt-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-gray-600">
              Ocupação
            </span>
            <span className="text-xs text-gray-600">
              {urgencyData.totalSlots - urgencyData.availableSlots}/{urgencyData.totalSlots}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                isCriticalAvailability 
                  ? 'bg-red-500' 
                  : isLowAvailability 
                    ? 'bg-yellow-500' 
                    : 'bg-green-500'
              }`}
              style={{ 
                width: `${((urgencyData.totalSlots - urgencyData.availableSlots) / urgencyData.totalSlots) * 100}%` 
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Today's Activity */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <Users className="w-4 h-4 text-white" />
          </div>
          <div>
            <div className="font-semibold text-blue-800">
              {urgencyData.todayBookings} pessoas agendaram hoje
            </div>
            <div className="text-sm text-blue-600">
              Esta loja está em alta demanda
            </div>
          </div>
        </div>
      </div>

      {/* Rating & Social Proof */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
            <Star className="w-4 h-4 text-white fill-current" />
          </div>
          <div>
            <div className="font-semibold text-yellow-800">
              {urgencyData.rating.toFixed(1)}⭐ de satisfação
            </div>
            <div className="text-sm text-yellow-600">
              Baseado em {urgencyData.totalReviews} avaliações
            </div>
          </div>
        </div>
      </div>

      {/* Popular Badge */}
      {urgencyData.isPopular && (
        <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div>
              <div className="font-semibold text-orange-800">
                🔥 Loja Popular
              </div>
              <div className="text-sm text-orange-600">
                Uma das mais procuradas hoje
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity Ticker */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
            <Zap className="w-3 h-3 text-white" />
          </div>
          <span className="text-sm font-medium text-gray-700">
            Atividade Recente
          </span>
        </div>
        <div className="space-y-1">
          {urgencyData.recentActivity.slice(0, 2).map((activity, index) => (
            <div key={index} className="text-xs text-gray-600 flex items-center space-x-1">
              <div className="w-1 h-1 bg-green-500 rounded-full"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Urgency CTA */}
      {isLowAvailability && (
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="font-semibold text-indigo-800">
                ⚡ Agenda já!
              </div>
              <div className="text-sm text-indigo-600">
                Poucas vagas restantes para esta semana
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
