import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireActiveSubscription } from '@/lib/subscription-access'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se tem subscrição ativa
    const subscriptionCheck = await requireActiveSubscription(session.user.id)
    if (subscriptionCheck.error) {
      return NextResponse.json(
        { message: subscriptionCheck.message },
        { status: subscriptionCheck.status }
      )
    }

    // Buscar listas do usuário com contagem de subscritores
    const lists = await prisma.$queryRaw`
      SELECT 
        nl.*,
        COUNT(ns.id) as subscriber_count
      FROM newsletter_lists nl
      LEFT JOIN newsletter_subscribers ns ON nl.id = ns."listId" AND ns."isActive" = true
      WHERE nl."userId" = ${session.user.id}
      GROUP BY nl.id
      ORDER BY nl."createdAt" DESC
    `

    return NextResponse.json({
      lists: lists.map((list: any) => ({
        id: list.id,
        name: list.name,
        description: list.description,
        subscriberCount: Number(list.subscriber_count),
        createdAt: list.createdAt
      }))
    })

  } catch (error) {
    console.error(Erro ao buscar listas:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, description } = await request.json()

    if (!name) {
      return NextResponse.json(
        { message: "Nome da lista é obrigatório" },
        { status: 400 }
      )
    }

    // Criar lista
    const list = await prisma.$queryRaw`
      INSERT INTO newsletter_lists (
        "id", "userId", name, description, "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${session.user.id}, ${name}, ${description || 
}, NOW(), NOW()
      ) RETURNING *
    `

    return NextResponse.json({
      message: 'Lista criada com sucesso',
      list: list[0]
    })

  } catch (error) {
    console.error('Erro ao criar lista:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
