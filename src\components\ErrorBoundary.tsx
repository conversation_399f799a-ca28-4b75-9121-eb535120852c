'use client'

import React from 'react'
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => 'void'}>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false}
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, 'errorInfo')
    this.setState({ error, errorInfo })
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined})
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} retry={this.retry} />
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.retry} />
    }

    return this.props.children
  }
}

// Componente de erro padrão
function DefaultErrorFallback({ error, retry }: { error: Error; retry: () => void
}) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle className="w-8 h-8 text-red-600" />
        </div>
        
        <h1 className="text-xl font-semibold text-gray-900 mb-2">
          Algo correu mal
        </h1>
        
        <p className="text-gray-600 mb-6">
          Ocorreu um erro inesperado. Por favor, tente novamente ou contacte o suporte se o problema persistir.
        </p>

        {process.env.NODE_ENV === 'development' && (
          <div className="bg-gray-100 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-medium text-gray-900 mb-2">Detalhes do erro:</h3>
            <pre className="text-xs text-gray-700 overflow-auto">
              {error.message}
            </pre>
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={retry}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Tentar Novamente
          </button>
          
          <Link
            href="/"
            className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
          >
            <Home className="w-4 h-4 mr-2" />Ir para Início</Link>
        </div>
      </div>
    </div>
  )
}

// Hook para capturar erros em componentes funcionais
export function useErrorHandler() {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    console.error(Error caught by useErrorHandler:, error, 'errorInfo')
    // 'Aqui poderia enviar o erro para um serviço de monitorização'
}
}

// Componente para exibir erros de API
export function ApiError({ 
  error, 
  retry, 
  className =  

}: { 
  error: string | Error
  retry?: () => void
  className?: string}) {
  const errorMessage = typeof error === 'string' ? error : error.message

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-1">
            Erro
          </h3>
          <p className="text-sm text-red-700">
            {errorMessage}
          </p>
          {retry && (
            <button
              onClick={retry}
              className="mt-3 text-sm text-red-800 hover:text-red-900 font-medium flex items-center"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Tentar novamente
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Componente para estado vazio
export function EmptyState({ 
  icon: Icon = AlertTriangle,
  title,
  description, action }: {
  icon?: React.ComponentType<{ className?: string}>
  title: string
  description: string
  action?: React.ReactNode
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Icon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-600 mb-6 max-w-sm mx-auto">
        {description}
      </p>
      {action}
    </div>
  )
}

// Componente para notificações toast
export function Toast({ 
  type = info,
  title,
  message, onClose 
}: {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  onClose: () => 'void'}) {
  const styles = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  }

  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  }

  return (
    <div className={`fixed top-4 right-4 max-w-sm w-full border rounded-lg p-4 shadow-lg z-50 ${styles[type]}`}>
      <div className="flex items-start">
        <span className="mr-3 text-lg">{icons[type]}</span>
        <div className="flex-1">
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm mt-1">{message}</p>
        </div>
        <button
          onClick={onClose}
          className="ml-3 text-lg hover:opacity-70"
        >
          ×
        </button>
      </div>
    </div>
  )
}
