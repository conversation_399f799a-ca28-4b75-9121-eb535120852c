'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Truck, Package, Globe, Euro, Plus, Trash2, Save } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface ShippingRate {
  country: string
  countryName: string
  type: 'FIXED'
  fixedRate: number
}

const COUNTRIES = [
  { code: 'PT', name: 'Portugal' },
  { code: 'ES', name: 'Espanha' },
  { code: 'FR', name: '<PERSON><PERSON><PERSON>' },
  { code: 'IT', name: 'It<PERSON><PERSON>' },
  { code: 'DE', name: '<PERSON><PERSON><PERSON>' },
  { code: 'NL', name: '<PERSON><PERSON><PERSON>' },
  { code: 'BE', name: 'B<PERSON>lgica' },
  { code: 'LU', name: 'Luxemburgo' },
  { code: 'AT', name: 'Áustria' },
  { code: 'CH', name: '<PERSON><PERSON><PERSON>' }
]

export default function ShippingConfigPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  
  const [shippingEnabled, setShippingEnabled] = useState(false)
  const [freeShippingThreshold, setFreeShippingThreshold] = useState<number | null>(null)
  const [freeShippingCountries, setFreeShippingCountries] = useState<string[]>([])
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([])

  useEffect(() => {
    if (session?.user?.id) {
      fetchShippingConfig()
    }
  }, [session])

  const fetchShippingConfig = async () => {
    try {
      const response = await fetch('/api/lojista/shipping-config')
      if (response.ok) {
        const data = await response.json()
        setShippingEnabled(data.shippingEnabled || false)
        setFreeShippingThreshold(data.freeShippingThreshold || null)
        setFreeShippingCountries(data.freeShippingCountries || [])
        setShippingRates(data.shippingRates || [])
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addShippingRate = () => {
    setShippingRates([...shippingRates, {
      country: 'PT',
      countryName: 'Portugal',
      type: 'FIXED',
      fixedRate: 0
    }])
  }

  const updateShippingRate = (index: number, updates: Partial<ShippingRate>) => {
    const newRates = [...shippingRates]
    newRates[index] = { ...newRates[index], ...updates }

    // Atualizar nome do país
    if (updates.country) {
      const country = COUNTRIES.find(c => c.code === updates.country)
      newRates[index].countryName = country?.name || updates.country
    }

    setShippingRates(newRates)
  }

  const removeShippingRate = (index: number) => {
    setShippingRates(shippingRates.filter((_, i) => i !== index))
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/lojista/shipping-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          shippingEnabled,
          freeShippingThreshold,
          freeShippingCountries,
          shippingRates
        })
      })

      if (response.ok) {
        alert('Configurações salvas com sucesso!')
      } else {
        alert('Erro ao salvar configurações')
      }
    } catch (error) {
      console.error('Erro ao salvar:', error)
      alert('Erro ao salvar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Configurações de Envio</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Configurações de Envio</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Configurações de Envio</h1>
          <p className="text-gray-600">Configure os custos de envio para os seus produtos do marketplace</p>
        </div>

            <div className="space-y-6">
              {/* Ativar Envios */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Ativar Envios</h3>
                    <p className="text-gray-600">Permitir que os clientes comprem os seus produtos com envio</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={shippingEnabled}
                      onChange={(e) => setShippingEnabled(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              {shippingEnabled && (
                <>
                  {/* Portes Grátis */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Portes Grátis</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Valor mínimo para portes grátis (€)
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={freeShippingThreshold || ''}
                          onChange={(e) => setFreeShippingThreshold(e.target.value ? parseFloat(e.target.value) : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Ex: 50.00"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Deixe vazio se não quiser oferecer portes grátis
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Países com portes grátis
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {COUNTRIES.map((country) => (
                            <label key={country.code} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={freeShippingCountries.includes(country.code)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setFreeShippingCountries([...freeShippingCountries, country.code])
                                  } else {
                                    setFreeShippingCountries(freeShippingCountries.filter(c => c !== country.code))
                                  }
                                }}
                                className="mr-2"
                              />
                              <span className="text-sm">{country.name}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tarifas de Envio */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">Tarifas de Envio</h3>
                      <button
                        onClick={addShippingRate}
                        className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Adicionar País
                      </button>
                    </div>

                    <div className="space-y-4">
                      {shippingRates.map((rate, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  País
                                </label>
                                <select
                                  value={rate.country}
                                  onChange={(e) => updateShippingRate(index, { country: e.target.value })}
                                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                  {COUNTRIES.map((country) => (
                                    <option key={country.code} value={country.code}>
                                      {country.name}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Custo de envio (€)
                                </label>
                                <input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  value={rate.fixedRate || ''}
                                  onChange={(e) => updateShippingRate(index, { fixedRate: parseFloat(e.target.value) || 0 })}
                                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Ex: 5.99"
                                />
                              </div>
                            </div>

                            <button
                              onClick={() => removeShippingRate(index)}
                              className="text-red-600 hover:text-red-800 mt-6"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Botão Salvar */}
                  <div className="flex justify-end">
                    <button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="flex items-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
                    >
                      {isSaving ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      ) : (
                        <Save className="w-4 h-4 mr-2" />
                      )}
                      {isSaving ? 'Salvando...' : 'Salvar Configurações'}
                    </button>
                  </div>
                </>
              )}
            </div>
      </div>
    </div>
  )
}
