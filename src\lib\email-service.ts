import nodemailer from 'nodemailer'
import { getRepairCreatedEmailTemplate, getRepairStatusUpdateEmailTemplate } from './email-templates'

interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string}
}

interface RepairEmailData {
  customerName: string
  customerEmail: string
  trackingCode: string
  deviceBrand: string
  deviceModel: string
  problemType: string
  status: string
  shopName: string
  shopPhone?: string
  shopEmail?: string
  estimatedPrice?: number
  estimatedCompletionDate?: string
  notes?: string}

class EmailService {
  private transporter: nodemailer.Transporter | null = null

  constructor() {
    this.initializeTransporter()
  }

  private initializeTransporter() {
    try {
      // Configuração do email (pode ser configurada via variáveis de ambiente)
      const emailConfig: EmailConfig = {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || ''
        
}
      }

      // Só inicializar se as credenciais estiverem configuradas
      if (emailConfig.auth.user && emailConfig.auth.pass) {
        this.transporter = nodemailer.createTransporter(emailConfig)
      } else {
        console.warn(Email credentials not configured. Email notifications will be disabled.)
      
}
    } catch (error) {
      console.error('Failed to initialize email transporter:', 'error')
    }
  }

  async sendRepairCreatedNotification(data: RepairEmailData): Promise<boolean> {
    if (!this.transporter || !data.customerEmail) {
      console.log('Email not sent: transporter not configured or customer email missing')
      'return false'}

    try {
      const template = getRepairCreatedEmailTemplate(data)
      
      const mailOptions = {
        from: `"${data.shopName}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to: data.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Repair created email sent:', result.messageId)
      'return true'} catch (error) {
      console.error('Failed to send repair created email:', 'error')
      'return false'}
  }

  async sendRepairStatusUpdateNotification(data: RepairEmailData): Promise<boolean> {
    if (!this.transporter || !data.customerEmail) {
      console.log('Email not sent: transporter not configured or customer email missing')
      'return false'}

    try {
      const template = getRepairStatusUpdateEmailTemplate(data)
      
      const mailOptions = {
        from: `"${data.shopName}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to: data.customerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('Repair status update email sent:', result.messageId)
      'return true'} catch (error) {
      console.error('Failed to send repair status update email:', 'error')
      'return false'}
  }

  async testConnection(): Promise<boolean> {
    if (!this.transporter) {
      'return false'}

    try {
      await this.transporter.verify()
      'return true'} catch (error) {
      console.error('Email connection test failed:', 'error')
      'return false'}
  }
}

// Singleton instance
export const emailService = new EmailService()

// Helper functions
export async function sendRepairCreatedEmail(data: RepairEmailData) {
  return await emailService.sendRepairCreatedNotification(data)
}

export async function sendRepairStatusUpdateEmail(data: RepairEmailData) {
  return await emailService.sendRepairStatusUpdateNotification(data)
}
