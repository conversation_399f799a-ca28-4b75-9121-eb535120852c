'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import AutoTranslate from '@/components/ui/AutoTranslate'
import { ArrowLeft, Minus, Plus, Trash2, ShoppingBag } from 'lucide-react'

interface CartItem {
  id: string
  productId: string
  quantity: number
  product: {
    id: string
    name: string
    price: number
    images: string[]
    condition: string
    seller: {
      name: string
    }
  }
}

export default function CartPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [shippingCost, setShippingCost] = useState(0)
  const [shippingDetails, setShippingDetails] = useState<any[]>([])
  const [country, setCountry] = useState('PT')

  useEffect(() => {
    fetchCartItems()
  }, [])

  useEffect(() => {
    if (cartItems.length > 0) {
      calculateShipping()
    }
  }, [cartItems, country])

  const fetchCartItems = async () => {
    try {
      const response = await fetch(<AutoTranslate text="/api/marketplace/cart" />)
      if (response.ok) {
        const data = await response.json()
        setCartItems(data.items)
      }
    } catch (error) {
      console.error(<AutoTranslate text="Erro ao carregar carrinho:" />, error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateShipping = async () => {
    try {
      const items = cartItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity
      }))

      const response = await fetch('/api/marketplace/shipping/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ items, country })
      })

      if (response.ok) {
        const data = await response.json()
        setShippingCost(data.totalShipping)
        setShippingDetails(data.shippingDetails)
      }
    } catch (error) {
      console.error('Erro ao calcular envio:', error)
    }
  }

  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return

    try {
      const response = await fetch(<AutoTranslate text="/api/marketplace/cart" />, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          itemId,
          quantity: newQuantity
        })
      })

      if (response.ok) {
        setCartItems(items =>
          items.map(item =>
            item.id === itemId ? { ...item, quantity: newQuantity } : item
          )
        )
      }
    } catch (error) {
      console.error(<AutoTranslate text="Erro ao atualizar quantidade:" />, error)
    }
  }

  const removeItem = async (itemId: string) => {
    try {
      const response = await fetch(<AutoTranslate text="/api/marketplace/cart" />, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ itemId })
      })

      if (response.ok) {
        setCartItems(items => items.filter(item => item.id !== itemId))
      }
    } catch (error) {
      console.error('Erro ao remover item:', error)
    }
  }

  const total = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/marketplace"
                className="flex items-center text-gray-600 hover:text-black transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" /><AutoTranslate text="Voltar ao Marketplace" /></Link>
            </div>
            <h1 className="text-xl font-semibold text-gray-900"><AutoTranslate text="Carrinho de Compras" /></h1>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {cartItems.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2"><AutoTranslate text="Seu carrinho está vazio" /></h2>
            <p className="text-gray-600 mb-6"><AutoTranslate text="Adicione alguns produtos do marketplace para começar suas compras." /></p>
            <Link
              href="/marketplace"
              className="inline-flex items-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              Continuar Comprando
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Itens no Carrinho ({cartItems.length})
                  </h2>
                </div>

                <div className="divide-y divide-gray-200">
                  {cartItems.map((item) => (
                    <div key={item.id} className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          {item.product.images.length > 0 ? (
                            <img
                              src={item.product.images[0]}
                              alt={item.product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                              Sem Imagem
                            </div>
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-gray-900 mb-1">
                            {item.product.name}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">
                            Por {item.product.seller.name}
                          </p>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              item.product.condition === 'NEW' ? 'bg-green-100 text-green-800' :
                              item.product.condition === 'LIKE_NEW' ? 'bg-blue-100 text-blue-800' :
                              item.product.condition === 'GOOD' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {item.product.condition === 'NEW' ? 'Novo' :
                               item.product.condition === 'LIKE_NEW' ? 'Como Novo' :
                               item.product.condition === 'GOOD' ? <AutoTranslate text="Bom Estado" /> : 'Usado'}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                              <Minus className="w-4 h-4" />
                            </button>
                            <span className="w-8 text-center text-sm font-medium">
                              {item.quantity}
                            </span>
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </div>

                          {/* Price */}
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              €{(item.product.price * item.quantity).toFixed(2)}
                            </div>
                            <div className="text-xs text-gray-500">
                              €{item.product.price.toFixed(2)} cada
                            </div>
                          </div>

                          {/* Remove Button */}
                          <button
                            onClick={() => removeItem(item.id)}
                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Resumo do Pedido
                </h2>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600"><AutoTranslate text="Subtotal" /></span>
                    <span className="font-medium">€{total.toFixed(2)}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Envio</span>
                      <span className="font-medium">
                        {shippingCost > 0 ? `€${shippingCost.toFixed(2)}` : <AutoTranslate text="Grátis" />}
                      </span>
                    </div>

                    {/* Detalhes do envio por vendedor */}
                    {shippingDetails.length > 0 && (
                      <div className="text-xs text-gray-500 space-y-1">
                        {shippingDetails.map((detail, index) => (
                          <div key={index} className="flex justify-between">
                            <span>{detail.sellerName}</span>
                            <span>
                              {detail.shippingCost > 0 ? `€${detail.shippingCost.toFixed(2)}` : <AutoTranslate text="Grátis" />}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between">
                      <span className="text-base font-semibold text-gray-900"><AutoTranslate text="Total" /></span>
                      <span className="text-base font-semibold text-gray-900">
                        €{(total + shippingCost).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                <Link
                  href="/marketplace/checkout"
                  className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-colors text-center block font-medium"
                ><AutoTranslate text="Finalizar Compra" /></Link>

                <Link
                  href="/marketplace"
                  className="w-full mt-3 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors text-center block"
                >
                  Continuar Comprando
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
