'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  Search, Star, MapPin, Clock, ArrowRight, Euro, Wrench, Smartphone, Laptop, Tablet, Watch,
  Shield, Zap, Users, CheckCircle, TrendingUp, Award, Globe, Truck, RotateCcw,
  Play, ChevronRight, Sparkles, Target, Heart, MessageCircle
} from 'lucide-react'
import { useTranslation, useGeolocation } from '@/hooks/useTranslation'
import AutoTranslate from '@/components/ui/AutoTranslate'
import ModernLayout from '@/components/ModernLayout'

interface RepairShop {
  id: string
  name: string
  email: string
  profile?: {
    companyName: string
    phone: string
    description: string
  }
  price?: number
  estimatedTime?: number
  rating: number
  distance: number
  isAvailable: boolean
}

export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { t, currentLanguage, changeLanguage } = useTranslation()
  const { country } = useGeolocation()

  const [recommendedShops, setRecommendedShops] = useState<RepairShop[]>([])
  const [searchLocation, setSearchLocation] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null)
  const [deviceInput, setDeviceInput] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    fetchData()
    getUserLocation()
  }, [status])

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          }
          setUserLocation(location)
          // Recarregar lojas com a nova localização
          fetchShopsWithLocation(location)
        },
        (error) => {
          console.warn('Erro ao obter localização:', error.message)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      )
    }
  }

  const fetchShopsWithLocation = async (location: {lat: number, lng: number}) => {
    try {
      const shopsUrl = `/api/repair-shops?lat=${location.lat}&lng=${location.lng}&radius=20`
      const shopsRes = await fetch(shopsUrl)
      if (shopsRes.ok) {
        const shopsData = await shopsRes.json()
        const shops = shopsData.shops || shopsData
        setRecommendedShops(Array.isArray(shops) ? shops.slice(0, 4) : [])
      }
    } catch (error) {
      console.error('Erro ao carregar lojas com localização:', error)
    }
  }

  const handleSearch = async () => {
    if (!deviceInput.trim()) return

    setIsSearching(true)
    try {
      const searchParams = new URLSearchParams({
        device: deviceInput,
        location: searchLocation || 'Portugal'
      })
      router.push(`/simular-reparacao?${searchParams.toString()}`)
    } catch (error) {
      console.error('Erro na pesquisa:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleBookRepair = () => {
    router.push('/cliente/reparacoes/nova-v2')
  }

  const handleViewShop = (shopId: string) => {
    router.push(`/loja/${shopId}`)
  }



  const fetchData = async () => {
    try {
      console.log('Iniciando fetch dos dados...')



      // Buscar lojas recomendadas
      const shopsUrl = userLocation
        ? `/api/repair-shops?lat=${userLocation.lat}&lng=${userLocation.lng}&radius=20`
        : '/api/repair-shops'

      const shopsRes = await fetch(shopsUrl)
      if (shopsRes.ok) {
        try {
          const shopsData = await shopsRes.json()
          console.log('Lojas recebidas:', shopsData)
          const shops = shopsData.shops || shopsData
          setRecommendedShops(Array.isArray(shops) ? shops.slice(0, 4) : [])
        } catch (parseError) {
          console.error('Erro ao fazer parse das lojas:', parseError)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }







  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <ModernLayout>
      {/* Hero Section */}
      <section className="relative pt-16 pb-20 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-indigo-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm border border-indigo-200 rounded-full px-4 py-2">
                <Sparkles className="w-4 h-4 text-indigo-600" />
                <span className="text-sm font-medium text-indigo-700">Maior plataforma Tech repair da Europa</span>
              </div>

              <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                  <AutoTranslate text="Reparações de" />
                </span>
                <br />
                <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  <AutoTranslate text="Dispositivos" />
                </span>
                <br />
                <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
                  <AutoTranslate text="Eletrónicos" />
                </span>
                <br />
                <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent text-4xl lg:text-5xl">
                  de forma simplificada e segura
                </span>
              </h1>

              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                Conectamos você aos melhores técnicos especializados.
                Transparência total, garantia completa e preços justos.
              </p>

              {/* Search Bar */}
              <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-2">
                <div className="flex flex-col sm:flex-row gap-2">
                  <div className="flex-1 relative">
                    <Smartphone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Que dispositivo precisa reparar?"
                      value={deviceInput}
                      onChange={(e) => setDeviceInput(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 text-gray-900 placeholder-gray-500 bg-transparent focus:outline-none"
                    />
                  </div>
                  <div className="flex-1 relative">
                    <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Sua localização"
                      value={searchLocation}
                      onChange={(e) => setSearchLocation(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 text-gray-900 placeholder-gray-500 bg-transparent focus:outline-none"
                    />
                  </div>
                  <button
                    onClick={handleSearch}
                    disabled={isSearching}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-medium flex items-center space-x-2 group disabled:opacity-50"
                  >
                    {isSearching ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Search className="w-5 h-5" />
                        <span>Buscar</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900">50K+</div>
                  <div className="text-sm text-gray-600">
                    <AutoTranslate text="Reparações" />
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900">1.2K+</div>
                  <div className="text-sm text-gray-600">Técnicos</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900">98%</div>
                  <div className="text-sm text-gray-600">Satisfação</div>
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image/Video */}
            <div className="relative">
              <div className="relative bg-gradient-to-br from-indigo-100 to-purple-100 rounded-3xl p-8 shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-purple-600/10 rounded-3xl"></div>
                <div className="relative grid grid-cols-2 gap-6">
                  {/* Device Icons */}
                  {[
                    { icon: Smartphone, label: 'Smartphones', color: 'from-blue-500 to-cyan-500' },
                    { icon: Laptop, label: 'Laptops', color: 'from-purple-500 to-pink-500' },
                    { icon: Tablet, label: 'Tablets', color: 'from-green-500 to-emerald-500' },
                    { icon: Watch, label: 'Smartwatches', color: 'from-orange-500 to-red-500' }
                  ].map((device, index) => (
                    <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer">
                      <div className={`w-12 h-12 bg-gradient-to-br ${device.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                        <device.icon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2">{device.label}</h3>
                      <p className="text-sm text-gray-600">Reparação especializada</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Por que escolher a Revify?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              <AutoTranslate text="Somos a plataforma mais confiável da Europa para reparações de dispositivos eletrónicos" />
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: 'Garantia Total',
                description: 'Todas as reparações incluem garantia de 6 meses e proteção completa',
                color: 'from-green-500 to-emerald-500'
              },
              {
                icon: Zap,
                title: 'Rápido & Eficiente',
                description: 'Reparações realizadas em média em 24-48 horas pelos melhores técnicos',
                color: 'from-yellow-500 to-orange-500'
              },
              {
                icon: Target,
                title: 'Preços Transparentes',
                description: 'Orçamentos claros sem custos ocultos. Você sabe exatamente o que paga',
                color: 'from-blue-500 to-indigo-500'
              }
            ].map((feature, index) => (
              <div key={index} className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100">
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative">
                  <div className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Us Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Junte-se à Revify
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Escolha como quer fazer parte da maior plataforma Tech repair da Europa
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Cliente Card */}
            <div className="group relative bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Smartphone className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Cliente" /></h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  <AutoTranslate text="Repare seus dispositivos com técnicos qualificados, garantia total e preços transparentes." />
                </p>
                <ul className="text-sm text-gray-600 mb-8 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    <AutoTranslate text="Orçamentos gratuitos" />
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    <AutoTranslate text="Garantia de 6 meses" />
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    <AutoTranslate text="Acompanhamento em tempo real" />
                  </li>
                </ul>
                <button
                  onClick={() => window.location.href = '/auth/signup?type=cliente'}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all font-medium group-hover:shadow-lg"
                >
                  Começar Agora
                </button>
              </div>
            </div>

            {/* Lojista Card */}
            <div className="group relative bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Wrench className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Lojista" /></h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Expanda seu negócio, gerencie reparações e venda produtos numa plataforma completa.
                </p>
                <ul className="text-sm text-gray-600 mb-8 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Dashboard completo
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Marketplace integrado
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Gestão de clientes
                  </li>
                </ul>
                <button
                  onClick={() => window.location.href = '/para-lojistas'}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all font-medium group-hover:shadow-lg"
                >
                  Saber Mais
                </button>
              </div>
            </div>

            {/* Estafeta Card */}
            <div className="group relative bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Truck className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Estafeta" /></h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Ganhe dinheiro fazendo entregas de dispositivos reparados na sua região.
                </p>
                <ul className="text-sm text-gray-600 mb-8 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Horários flexíveis
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Pagamentos semanais
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    App dedicado
                  </li>
                </ul>
                <button
                  onClick={() => window.location.href = '/para-estafetas'}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-6 rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all font-medium group-hover:shadow-lg"
                >
                  Saber Mais
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como funciona?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Processo simples e transparente em apenas 3 passos
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: 'Solicite Orçamento',
                description: 'Descreva o problema do seu dispositivo e receba orçamentos de técnicos qualificados',
                icon: Search
              },
              {
                step: '02',
                title: 'Escolha o Técnico',
                description: 'Compare preços, avaliações e prazos. Escolha o técnico que melhor atende suas necessidades',
                icon: Users
              },
              {
                step: '03',
                title: 'Receba Reparado',
                description: 'Acompanhe o progresso em tempo real e receba seu dispositivo funcionando perfeitamente',
                icon: CheckCircle
              }
            ].map((step, index) => (
              <div key={index} className="relative group">
                {/* Connection Line */}
                {index < 2 && (
                  <div className="hidden md:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-indigo-200 to-purple-200 z-0"></div>
                )}

                <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 group-hover:border-indigo-200">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl text-white font-bold text-xl mb-6 group-hover:scale-110 transition-transform">
                    {step.step}
                  </div>
                  <step.icon className="w-8 h-8 text-indigo-600 mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <button
              onClick={handleBookRepair}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-2xl hover:from-indigo-700 hover:to-purple-700 transition-all font-bold text-lg flex items-center space-x-2 mx-auto group"
            >
              <Wrench className="w-5 h-5" />
              <span>Começar Agora</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
      </section>

      {/* Recommended Shops Section */}
      {recommendedShops.length > 0 && (
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Técnicos Recomendados
              </h2>
              <p className="text-xl text-gray-600">
                Os melhores profissionais da sua região
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recommendedShops.slice(0, 6).map((shop) => (
                <div key={shop.id} className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden group">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                        {shop.profile?.companyName || shop.name}
                      </h3>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium text-gray-700">{shop.rating || 5.0}</span>
                      </div>
                    </div>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {shop.profile?.description || 'Especialista em reparações de dispositivos eletrónicos'}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <MapPin className="w-4 h-4" />
                        <span>{shop.distance ? shop.distance.toFixed(1) : '0.0'} km</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>{shop.estimatedTime || 24}h</span>
                      </div>
                    </div>

                    <Link
                      href={`/loja/${shop.id}`}
                      className="mt-4 w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-2 px-4 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-medium text-center block"
                    >
                      Ver Perfil
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </ModernLayout>
  )
}
