import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Buscar o produto atual
    const currentProduct = await prisma.marketplaceProduct.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        brand: true,
        deviceModel: true
      }
    })

    if (!currentProduct) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    // Buscar produtos recomendados baseados em:
    // 1. Mesma categoria
    // 2. Mesma marca
    // 3. Mesmo modelo de dispositivo
    // 4. Faixa de preço similar

    const priceRange = {
      min: Number(currentProduct.price) * 0.7, // 30% abaixo
      max: Number(currentProduct.price) * 1.3  // 30% acima
    }

    // Query complexa para encontrar produtos similares
    const recommendations = await prisma.marketplaceProduct.findMany({
      where: {
        AND: [
          { id: { not: params.id } }, // Excluir o produto atual
          { isActive: true },
          { stock: { gt: 0 } },
          {
            OR: [
              // Mesma categoria (peso alto)
              { categoryId: currentProduct.categoryId },
              // Mesma marca (peso médio)
              { brandId: currentProduct.brandId },
              // Mesmo modelo (peso alto)
              { deviceModelId: currentProduct.deviceModelId },
              // Faixa de preço similar (peso baixo)
              {
                AND: [
                  { price: { gte: priceRange.min } },
                  { price: { lte: priceRange.max } }
                ]
              }
            ]
          }
        ]
      },
      include: {
        seller: {
          select: {
            id: true,
            name: true,
            profile: {
              select: {
                companyName: true
              }
            }
          }
        },
        category: {
          select: {
            id: true,
            name: true
          }
        },
        brand: {
          select: {
            id: true,
            name: true
          }
        },
        deviceModel: {
          select: {
            id: true,
            name: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        },
        _count: {
          select: {
            reviews: true,
            favorites: true
          }
        }
      },
      take: 20 // Buscar mais para depois ordenar e filtrar
    })

    // Calcular score de relevância para cada produto
    const scoredRecommendations = recommendations.map(product => {
      let score = 0

      // Mesma categoria: +50 pontos
      if (product.categoryId === currentProduct.categoryId) {
        score += 50
      }

      // Mesma marca: +30 pontos
      if (product.brandId === currentProduct.brandId) {
        score += 30
      }

      // Mesmo modelo: +40 pontos
      if (product.deviceModelId === currentProduct.deviceModelId) {
        score += 40
      }

      // Faixa de preço similar: +10 pontos
      const productPrice = Number(product.price)
      if (productPrice >= priceRange.min && productPrice <= priceRange.max) {
        score += 10
      }

      // Bonus por avaliações: +1 ponto por review, +5 por cada estrela média
      const avgRating = product.reviews.length > 0 
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length 
        : 0
      score += product._count.reviews + (avgRating * 5)

      // Bonus por favoritos: +2 pontos por favorito
      score += product._count.favorites * 2

      // Penalty por diferença de preço (quanto maior a diferença, menor o score)
      const priceDifference = Math.abs(productPrice - Number(currentProduct.price))
      const maxPrice = Math.max(productPrice, Number(currentProduct.price))
      const pricePenalty = (priceDifference / maxPrice) * 20
      score -= pricePenalty

      return {
        ...product,
        recommendationScore: Math.round(score),
        avgRating,
        reasons: [
          ...(product.categoryId === currentProduct.categoryId ? ['Mesma categoria'] : []),
          ...(product.brandId === currentProduct.brandId ? ['Mesma marca'] : []),
          ...(product.deviceModelId === currentProduct.deviceModelId ? ['Compatível'] : []),
          ...(productPrice >= priceRange.min && productPrice <= priceRange.max ? ['Preço similar'] : [])
        ]
      }
    })

    // Ordenar por score e pegar os top 8
    const topRecommendations = scoredRecommendations
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, 8)

    // Agrupar por tipo de recomendação
    const groupedRecommendations = {
      sameCategory: topRecommendations.filter(p => p.categoryId === currentProduct.categoryId).slice(0, 4),
      sameBrand: topRecommendations.filter(p => p.brandId === currentProduct.brandId).slice(0, 4),
      sameModel: topRecommendations.filter(p => p.deviceModelId === currentProduct.deviceModelId).slice(0, 4),
      similarPrice: topRecommendations.filter(p => {
        const price = Number(p.price)
        return price >= priceRange.min && price <= priceRange.max
      }).slice(0, 4)
    }

    return NextResponse.json({
      recommendations: topRecommendations,
      grouped: groupedRecommendations,
      currentProduct: {
        id: currentProduct.id,
        name: currentProduct.name,
        category: currentProduct.category?.name,
        brand: currentProduct.brand?.name,
        model: currentProduct.deviceModel?.name,
        price: Number(currentProduct.price)
      },
      criteria: {
        category: currentProduct.category?.name,
        brand: currentProduct.brand?.name,
        model: currentProduct.deviceModel?.name,
        priceRange
      }
    })

  } catch (error) {
    console.error('Erro ao buscar recomendações:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
