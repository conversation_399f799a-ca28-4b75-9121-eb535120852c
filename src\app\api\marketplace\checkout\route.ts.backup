import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { notifyMarketplaceOrder } from '@/lib/notifications'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    // Import dinâmico do Prisma para evitar problemas de inicialização
    const { prisma } = await import('@/lib/prisma')

    const { addressId, customerInfo, paymentMethod = 'card', coupon } = await request.json()

    if (!addressId) {
      return NextResponse.json(
        { message: 'Endereço de entrega é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se as chaves do Stripe estão configuradas
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    if (!stripeSecretSetting?.value || stripeSecretSetting.value === 'sk_test_TL') {
      // Modo demo - criar encomenda real mas sem pagamento Stripe
      const cartItems = await prisma.cartItem.findMany({
        where: {
          userId: session.user.id
        },
        include: {
          product: {
            include: {
              seller: true
            }
          }
        }
      })

      if (cartItems.length === 0) {
        return NextResponse.json(
          { message: 'Carrinho vazio' },
          { status: 400 }
        )
      }

      // Buscar endereços do perfil
      const profile = await prisma.profile.findUnique({
        where: { userId: session.user.id }
      })

      const addresses = (profile?.addresses as Array<{id: string, label: string, street: string, city: string, postalCode: string, country: string}>) || []
      const selectedAddress = addresses.find(addr => addr.id === addressId)

      if (!selectedAddress) {
        return NextResponse.json(
          { message: 'Endereço não encontrado' },
          { status: 404 }
        )
      }

      // Agrupar itens por vendedor
      const itemsBySeller = cartItems.reduce((acc, item) => {
        const sellerId = item.product.sellerId
        if (!acc[sellerId]) {
          acc[sellerId] = []
        }
        acc[sellerId].push(item)
        return acc
      }, {} as Record<string, typeof cartItems>)

      // Criar uma encomenda para cada vendedor
      const orderIds = []
      for (const [sellerId, items] of Object.entries(itemsBySeller)) {
        let orderTotal = items.reduce((sum, item) =>
          sum + (Number(item.product.price) * item.quantity), 0
        )

        // Aplicar desconto do cupão se aplicável
        let discountValue = 0
        if (coupon && coupon.id && sellerId === items[0].product.sellerId) {
          discountValue = coupon.discountValue || 0
          orderTotal = Math.max(0, orderTotal - discountValue)
        }

        // Gerar número da encomenda
        const orderNumber = `DEMO${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

        // Criar encomenda
        const order = await prisma.order.create({
          data: {
            customerId: session.user.id,
            status: paymentMethod === 'multibanco' ? 'PENDING_PAYMENT' : 'PENDING',
            paymentMethod: paymentMethod.toUpperCase(),
            total: orderTotal,
            subtotal: orderTotal + discountValue, // Subtotal antes do desconto
            discountValue: discountValue,
            couponId: coupon?.id || null,
            couponCode: coupon?.code || null,
            shippingName: customerInfo?.name || session.user.name || '',
            shippingStreet: selectedAddress.street,
            shippingCity: selectedAddress.city,
            shippingPostalCode: selectedAddress.postalCode,
            shippingCountry: selectedAddress.country || 'Portugal',
            marketplaceOrderItems: {
              create: items.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
                price: Number(item.product.price)
              }))
            }
          }
        })

        orderIds.push(order.id)

        // Incrementar contador do cupão se foi usado
        if (coupon?.id && discountValue > 0) {
          await prisma.coupon.update({
            where: { id: coupon.id },
            data: {
              usageCount: {
                increment: 1
              }
            }
          })
        }

        // Atualizar stock dos produtos
        for (const item of items) {
          await prisma.marketplaceProduct.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity
              }
            }
          })
        }
      }

      // Enviar notificações
      try {
        const customerProfile = await prisma.profile.findUnique({
          where: { userId: session.user.id }
        })

        const hasPremiumPlan = customerProfile?.smsNotifications || false

        // Enviar notificação para cada encomenda criada
        for (const orderId of orderIds) {
          const orderWithItems = await prisma.order.findUnique({
            where: { id: orderId },
            include: {
              marketplaceOrderItems: {
                include: {
                  product: true
                }
              }
            }
          })

          if (orderWithItems) {
            await notifyMarketplaceOrder(
              'confirmation',
              session.user.email!,
              customerProfile?.phone || null,
              {
                orderNumber: `DEMO${Date.now()}`,
                total: Number(orderWithItems.total),
                items: orderWithItems.marketplaceOrderItems
              },
              hasPremiumPlan
            )
          }
        }
      } catch (notificationError) {
        console.error('Erro ao enviar notificações:', notificationError)
        // Não falhar o checkout por causa das notificações
      }

      // Limpar carrinho
      await prisma.cartItem.deleteMany({
        where: { userId: session.user.id }
      })

      return NextResponse.json({
        checkoutUrl: `/marketplace/success?session_id=demo_${Date.now()}&demo=true&orders=${orderIds.join(',')}`
      })
    }

    // Buscar itens do carrinho
    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        product: {
          include: {
            seller: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (cartItems.length === 0) {
      return NextResponse.json(
        { message: 'Carrinho vazio' },
        { status: 400 }
      )
    }

    // Buscar endereços do perfil do usuário
    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    const addresses = (profile?.addresses as Array<{id: string, label: string, street: string, city: string, postalCode: string, country: string}>) || []
    const selectedAddress = addresses.find(addr => addr.id === addressId)

    if (!selectedAddress) {
      return NextResponse.json(
        { message: 'Endereço não encontrado' },
        { status: 404 }
      )
    }

    // Calcular total
    const subtotal = cartItems.reduce((sum, item) =>
      sum + (Number(item.product.price) * item.quantity), 0
    )
    const shipping = 0 // Envio grátis

    // Criar encomendas por vendedor
    const itemsBySeller = cartItems.reduce((acc, item) => {
      const sellerId = item.product.sellerId
      if (!acc[sellerId]) {
        acc[sellerId] = []
      }
      acc[sellerId].push(item)
      return acc
    }, {} as Record<string, typeof cartItems>)

    // Determinar status inicial baseado no método de pagamento
    const initialStatus = paymentMethod === 'multibanco' ? 'PENDING_PAYMENT' : 'PENDING'

    // Criar uma encomenda para cada vendedor
    const orderIds = []
    for (const [sellerId, items] of Object.entries(itemsBySeller)) {
      let orderTotal = items.reduce((sum, item) =>
        sum + (Number(item.product.price) * item.quantity), 0
      )

      // Aplicar desconto do cupão se aplicável
      let discountValue = 0
      if (coupon && coupon.id && sellerId === items[0].product.sellerId) {
        discountValue = coupon.discountValue || 0
        orderTotal = Math.max(0, orderTotal - discountValue)
      }

      // Gerar número da encomenda
      const orderNumber = `REV${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`

      // Criar encomenda
      const order = await prisma.order.create({
        data: {
          customerId: session.user.id,
          status: initialStatus,
          paymentMethod: paymentMethod.toUpperCase(),
          total: orderTotal,
          subtotal: orderTotal + discountValue, // Subtotal antes do desconto
          discountValue: discountValue,
          couponId: coupon?.id || null,
          couponCode: coupon?.code || null,
          shippingName: customerInfo?.name || session.user.name || '',
          shippingStreet: selectedAddress.street,
          shippingCity: selectedAddress.city,
          shippingPostalCode: selectedAddress.postalCode,
          shippingCountry: selectedAddress.country || 'Portugal',
          marketplaceOrderItems: {
            create: items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: Number(item.product.price)
            }))
          }
        }
      })

      orderIds.push(order.id)

      // Incrementar contador do cupão se foi usado
      if (coupon?.id && discountValue > 0) {
        await prisma.coupon.update({
          where: { id: coupon.id },
          data: {
            usageCount: {
              increment: 1
            }
          }
        })
      }

      // Atualizar stock dos produtos
      for (const item of items) {
        await prisma.marketplaceProduct.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity
            }
          }
        })
      }
    }

    // Limpar carrinho
    await prisma.cartItem.deleteMany({
      where: {
        userId: session.user.id
      }
    })

    // Para Multibanco, gerar referência diretamente
    if (paymentMethod === 'multibanco') {
      const multibancoRef = {
        entity: '11249',
        reference: Math.floor(100000000 + Math.random() * 900000000).toString(),
        amount: subtotal.toFixed(2),
        expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      }

      return NextResponse.json({
        success: true,
        orderIds,
        multibanco: multibancoRef,
        redirectUrl: `/marketplace/success?multibanco=true&entity=${multibancoRef.entity}&reference=${multibancoRef.reference}&amount=${multibancoRef.amount}`
      })
    }

    // Para outros métodos, usar Stripe Checkout
    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
      return NextResponse.json(
        { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    // Para outros métodos, usar Stripe Checkout
    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: cartItems.map(item => ({
        price_data: {
          currency: 'eur',
          product_data: {
            name: item.product.name,
            images: item.product.images.length > 0 ? [item.product.images[0]] : [],
            metadata: {
              productId: item.product.id,
              sellerId: item.product.sellerId
            }
          },
          unit_amount: Math.round(Number(item.product.price) * 100) // Stripe usa centavos
        },
        quantity: item.quantity
      })),
      mode: 'payment',
      success_url: `${process.env.NEXTAUTH_URL}/marketplace/success?session_id={CHECKOUT_SESSION_ID}&payment_method=${paymentMethod}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/marketplace/checkout`,
      customer_email: customerInfo.email,
      metadata: {
        userId: session.user.id,
        addressId: addressId,
        customerName: customerInfo.name,
        customerPhone: customerInfo.phone || '',
        paymentMethod
      }
    })

    return NextResponse.json({
      checkoutUrl: stripeSession.url
    })

  } catch (error) {
    console.error('Erro no checkout:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
