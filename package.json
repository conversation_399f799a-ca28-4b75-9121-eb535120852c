{"name": "revify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4040", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@auth/prisma-adapter": "^2.10.0", "@aws-sdk/client-s3": "^3.855.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.6.1", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@vonage/server-sdk": "^3.22.1", "ai": "^4.3.19", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^6.1.0", "i18next": "^25.3.2", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "next": "15.4.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "nodemailer": "^6.10.1", "prisma": "^6.12.0", "qrcode": "^1.5.4", "quill": "^2.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.1", "react-quill": "^2.0.0", "resend": "^4.7.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "twilio": "^5.7.3", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.76", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.5", "jest-environment-node": "^30.0.5", "typescript": "^5"}}