import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()
    
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email e password são obrigatórios'
      }, { status: 400 })
    }

    // Buscar usuário
    const user = await prisma.user.findUnique({
      where: { email },
      include: { profile: true }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado',
        debug: { email }
      })
    }

    if (!user.password) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não tem password definida',
        debug: { 
          email,
          hasPassword: false,
          role: user.role
        }
      })
    }

    // Testar password
    const isPasswordValid = await bcrypt.compare(password, user.password)

    return NextResponse.json({
      success: true,
      passwordValid: isPasswordValid,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isVerified: user.isVerified,
        hasProfile: !!user.profile
      },
      debug: {
        providedPassword: password,
        hashedPassword: user.password.substring(0, 20) + '...',
        passwordLength: password.length
      }
    })
  } catch (error) {
    console.error('Erro no teste de password:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro no teste de password',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}
