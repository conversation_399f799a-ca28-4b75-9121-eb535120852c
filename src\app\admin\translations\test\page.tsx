'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import TranslationPerformanceTest from '@/components/test/TranslationPerformanceTest'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
/**
 * Página de teste de performance para traduções
 * Acessível apenas para administradores
 */
export default function TranslationTestPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return

    if (!session?.user || session.user.role !== 'ADMIN') {
      router.push('/login')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    'return null'}

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-6">
            <Link 
              href="/admin/translations"
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 mr-6"
            >
              <ArrowLeft className="w-4 h-4" />
              Voltar ao Dashboard
            </Link>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Teste de Performance</h1>
              <p className="text-gray-600">Avalie a performance do sistema de traduções</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="py-8">
        <TranslationPerformanceTest />
      </div>
    </div>
  )
}
