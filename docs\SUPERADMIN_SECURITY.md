# 🔒 Sistema de Proteção do Superadmin

## 📋 Visão Geral

O sistema implementa múltiplas camadas de proteção para garantir que a conta do superadmin **<EMAIL>** nunca possa ser comprometida, editada ou apagada.

## 🛡️ Camadas de Proteção

### 1. **Identificação Dupla**
- **Campo `isSuperAdmin`**: Boolean no modelo User
- **Email protegido**: `<EMAIL>`
- **Verificação redundante**: Ambos os métodos são verificados

### 2. **Proteção a Nível de Base de Dados**
```typescript
// Campo no modelo User
isSuperAdmin <PERSON> @default(false) // Superadmin protegido
```

### 3. **Middleware de Proteção**
```typescript
// Arquivo: src/lib/superadmin-protection.ts
export async function protectSuperAdmin(
  userId: string, 
  operation: 'DELETE' | 'UPDATE_ROLE' | 'UPDATE_PERMISSIONS'
): Promise<{ allowed: boolean; message?: string }>
```

### 4. **Proteção nas APIs**
- **PUT /api/admin/users/[id]**: Impede edição do superadmin
- **DELETE /api/admin/users/[id]**: Impede eliminação do superadmin
- **Mensagens claras**: Retorna erros específicos

### 5. **Proteção na Interface**
- **Botão Editar**: Substituído por ícone de escudo para superadmin
- **Botão Apagar**: Não aparece para superadmin
- **Tooltips informativos**: Explicam porque não pode ser editado

## 🔑 Credenciais do Superadmin

```
Email: <EMAIL>
Password: Teste123123_
```

⚠️ **IMPORTANTE**: Estas credenciais devem ser alteradas em produção!

## 🚨 Operações Protegidas

### ❌ **Não Permitidas**
- Apagar a conta do superadmin
- Alterar o papel (role) do superadmin
- Alterar o email do superadmin
- Desativar a conta do superadmin
- Remover permissões do superadmin

### ✅ **Permitidas**
- Ver detalhes da conta
- Alterar password (pelo próprio superadmin)
- Ver logs de atividade

## 🔧 Implementação Técnica

### **Verificação de Superadmin**
```typescript
export async function isSuperAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { isSuperAdmin: true, email: true }
  })
  
  return user?.isSuperAdmin === true || user?.email === '<EMAIL>'
}
```

### **Proteção na Interface**
```typescript
// Função no componente admin
const isSuperAdmin = (user: User) => {
  return user.email === '<EMAIL>'
}

// Renderização condicional
{isSuperAdmin(user) ? (
  <div className="flex items-center text-yellow-600" title="Superadmin - Não editável">
    <Shield className="w-4 h-4" />
  </div>
) : (
  <button onClick={() => openEditModal(user)}>
    <Edit className="w-4 h-4" />
  </button>
)}
```

### **Proteção na API**
```typescript
// Verificação antes de qualquer operação
const protection = await protectSuperAdmin(userId, 'DELETE')
if (!protection.allowed) {
  return NextResponse.json(
    { message: protection.message },
    { status: 403 }
  )
}
```

## 📊 Logs de Segurança

O sistema regista automaticamente:
- Tentativas de acesso à conta do superadmin
- Tentativas de modificação bloqueadas
- Logins do superadmin

## 🔄 Processo de Seed

O superadmin é criado automaticamente no seed:
```typescript
const superAdmin = await prisma.user.upsert({
  where: { email: '<EMAIL>' },
  update: {
    role: 'ADMIN',
    isVerified: true,
    isSuperAdmin: true
  },
  create: {
    email: '<EMAIL>',
    name: 'Carlos Teixeira',
    password: hashedPassword,
    role: 'ADMIN',
    isVerified: true,
    isSuperAdmin: true
  }
})
```

## 🚀 Backup e Recuperação

### **Emails Protegidos**
```typescript
export const PROTECTED_EMAILS = [
  '<EMAIL>'
]
```

### **Recuperação de Emergência**
Se por algum motivo a conta for comprometida:
1. Executar o seed novamente
2. A conta será restaurada com as permissões corretas
3. O campo `isSuperAdmin` será definido como `true`

## ⚠️ Avisos Importantes

1. **Nunca remover** o campo `isSuperAdmin` do schema
2. **Nunca alterar** o email `<EMAIL>` no código
3. **Sempre testar** as proteções após mudanças no sistema
4. **Manter backup** das credenciais do superadmin
5. **Alterar password** em produção

## 🧪 Testes de Segurança

Para testar as proteções:
1. Login como admin normal
2. Tentar editar/apagar o superadmin
3. Verificar que as operações são bloqueadas
4. Confirmar mensagens de erro apropriadas

## 📞 Contacto de Emergência

Em caso de problemas com a conta do superadmin:
- **Email**: <EMAIL>
- **Sistema**: Executar seed para restaurar conta
