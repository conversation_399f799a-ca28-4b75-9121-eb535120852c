import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar estatísticas
    const [
      totalUsers,
      totalRepairs,
      totalOrders,
      totalRevenueResult
    ] = await Promise.all([
      prisma.user.count(),
      prisma.repair.count(),
      prisma.order.count(),
      prisma.payment.aggregate({
        where: {
          status: COMPLETED},
        _sum: {
          amount: true}
      })
    ])

    const stats = {
      totalUsers,
      totalRepairs,
      totalOrders,
      totalRevenue: totalRevenueResult._sum.amount || 0
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error("Erro ao buscar estatísticas:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
