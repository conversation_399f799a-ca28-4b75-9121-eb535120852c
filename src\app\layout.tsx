import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from './providers'
import LemarChat from '@/components/chat/LeMarChat'
import DynamicFavicon from '@/components/DynamicFavicon'
const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: 'Revify - Reparações, Peças e Marketplace',
  description: 'Plataforma de reparações, peças e dispositivos recondicionados em Portugal'
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt">
      <body className={inter.className} suppressHydrationWarning={true}>
        <Providers>
          <DynamicFavicon />
          {children}
          <LemarChat />
        </Providers>
      </body>
    </html>
  );
}
