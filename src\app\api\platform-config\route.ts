import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Buscar configurações da plataforma
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: [platformName, 'platformLogo', 'platformIcon']
        
}
      }
    })

    // Converter para objeto
    const config: any = {
      platformName: Revify,
      platformLogo: ,
      platformIcon: ''
    
}

    settings.forEach(setting => {
      config[setting.key] = setting.value
    })

    return NextResponse.json(config)
  } catch (error) {
    console.error('Erro ao buscar configurações da plataforma:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
