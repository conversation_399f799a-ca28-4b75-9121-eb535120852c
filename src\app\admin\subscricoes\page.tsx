'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Search, Filter, Eye, Edit, ArrowLeft, CreditCard, Calendar, User } from 'lucide-react'
import Link from 'next/link'
interface Subscription {
  id: string
  status: string
  billingCycle: string
  currentPeriodStart: string
  currentPeriodEnd: string
  cancelAtPeriodEnd: boolean
  canceledAt: string | null
  stripeSubscriptionId: string | null
  createdAt: string
  user: {
    id: string
    name: string
    email: string
    profile?: {
      companyName: string}
  }
  plan: {
    id: string
    name: string
    monthlyPrice: number
    yearlyPrice: number}
  payments: {
    id: string
    amount: number
    status: string
    createdAt: string}[]
}

export default function AdminSubscricoesPage() {
  const { data: session } = useSession()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchSubscriptions()
  }, [])

  useEffect(() => {
    filterSubscriptions()
  }, [subscriptions, searchTerm, statusFilter])

  const fetchSubscriptions = async () => {
    try {
      const response = await fetch('/api/admin/subscriptions')
      if (response.ok) {
        const data = await response.json()
        setSubscriptions(data.subscriptions)
      }
    } catch (error) {
      console.error('Erro ao buscar subscrições:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const filterSubscriptions = () => {
    let filtered = subscriptions

    if (searchTerm) {
      filtered = filtered.filter(sub =>
        sub.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.plan.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(sub => sub.status === 'statusFilter')
    }

    setFilteredSubscriptions(filtered)
  }

  const updateSubscriptionStatus = async (subscriptionId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}`, {
        method: PATCH,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 'status'})
      })

      if (response.ok) { fetchSubscriptions()
        if (selectedSubscription?.id === 'subscriptionId') {
          setSelectedSubscription({...selectedSubscription, status })
        }
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar status')
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', 'error')
      alert('Erro ao atualizar status')
    }
  }

  const cancelSubscription = async (subscriptionId: string) => {
    if (!confirm('Tem certeza que deseja cancelar esta subscrição?')) return

    try {
      const response = await fetch(`/api/admin/subscriptions/${subscriptionId}/cancel`, {
        method: POST})

      if (response.ok) {
        alert('Subscrição cancelada com sucesso!')
        fetchSubscriptions()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao cancelar subscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar subscrição:', 'error')
      alert('Erro ao cancelar subscrição')
    }
  }

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'ACTIVE': 'Ativa',
      'PAST_DUE': 'Em Atraso',
      'CANCELED': 'Cancelada',
      'INCOMPLETE': 'Incompleta',
      'TRIALING': 'Período de Teste',
      'UNPAID': 'Não Paga'
    }
    return statusMap[status] || 'status'}

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'PAST_DUE': 'bg-yellow-100 text-yellow-800',
      'CANCELED': 'bg-red-100 text-red-800',
      'INCOMPLETE': 'bg-gray-100 text-gray-800',
      'TRIALING': 'bg-blue-100 text-blue-800',
      'UNPAID': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const openDetails = (subscription: Subscription) => {
    setSelectedSubscription(subscription)
    setShowDetails(true)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Subscrições</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CreditCard className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Subscrições</p>
                <p className="text-2xl font-bold text-gray-900">{subscriptions.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Ativas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {subscriptions.filter(s => s.status === 'ACTIVE').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <User className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Canceladas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {subscriptions.filter(s => s.status === 'CANCELED').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CreditCard className="w-8 h-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Receita Mensal</p>
                <p className="text-2xl font-bold text-gray-900">
                  €{subscriptions
                    .filter(s => s.status === 'ACTIVE' && s.billingCycle === 'MONTHLY')
                    .reduce((sum, s) => sum + Number(s.plan.monthlyPrice), 0)
                    .toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Nome, email, plano..."
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              >
                <option value="ALL">Todos os status</option>
                <option value="ACTIVE">Ativa</option>
                <option value="PAST_DUE">Em Atraso</option>
                <option value="CANCELED">Cancelada</option>
                <option value="INCOMPLETE">Incompleta</option>
                <option value="TRIALING">Período de Teste</option>
                <option value="UNPAID">Não Paga</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setStatusFilter('ALL')
                }}
                className="w-full px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Limpar Filtros
              </button>
            </div>
          </div>
        </div>

        {/* Subscriptions Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Utilizador
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plano
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ciclo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Próxima Renovação</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSubscriptions.map((subscription) => (
                  <tr key={subscription.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {subscription.user.profile?.companyName || subscription.user.name}
                        </div>
                        <div className="text-sm text-gray-500">{subscription.user.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {subscription.plan.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                        {getStatusLabel(subscription.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      €{subscription.billingCycle === 'MONTHLY' 
                        ? Number(subscription.plan.monthlyPrice).toFixed(2)
                        : Number(subscription.plan.yearlyPrice).toFixed(2)
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openDetails(subscription)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {subscription.status === 'ACTIVE' && (
                          <button
                            onClick={() => cancelSubscription(subscription.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Cancelar
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredSubscriptions.length === 0 && (
            <div className="text-center py-12">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma subscrição encontrada</h3>
              <p className="mt-1 text-sm text-gray-500">Não há subscrições no sistema.</p>
            </div>
          )}
        </div>
      </div>

      {/* Subscription Details Modal */}
      {showDetails && selectedSubscription && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowDetails(false)}></div>
            <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Detalhes da Subscrição</h2>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Subscription Info */}
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-3">Informações da Subscrição</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Utilizador:</span>
                          <span>{selectedSubscription.user.profile?.companyName || selectedSubscription.user.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Email:</span>
                          <span>{selectedSubscription.user.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Plano:</span>
                          <span>{selectedSubscription.plan.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(selectedSubscription.status)}`}>
                            {getStatusLabel(selectedSubscription.status)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Ciclo:</span>
                          <span>{selectedSubscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Início:</span>
                          <span>{new Date(selectedSubscription.currentPeriodStart).toLocaleDateString('pt-PT')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Próxima Renovação:</span>
                          <span>{new Date(selectedSubscription.currentPeriodEnd).toLocaleDateString('pt-PT')}</span>
                        </div>
                        {selectedSubscription.stripeSubscriptionId && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Stripe ID:</span>
                            <span className="font-mono text-xs">{selectedSubscription.stripeSubscriptionId}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment History */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Histórico de Pagamentos</h3>
                    <div className="space-y-3">
                      {selectedSubscription.payments.map((payment) => (
                        <div key={payment.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              €{Number(payment.amount).toFixed(2)}
                            </div>
                            <div className="text-xs text-gray-600">
                              {new Date(payment.createdAt).toLocaleDateString('pt-PT')}
                            </div>
                          </div>
                          <div className="text-right">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              payment.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                              payment.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {payment.status === 'COMPLETED' ? 'Pago' :
                               payment.status === 'PENDING' ? 'Pendente' : 'Falhado'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {selectedSubscription.payments.length === 0 && (
                      <p className="text-gray-500 text-sm">Nenhum pagamento registado</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
