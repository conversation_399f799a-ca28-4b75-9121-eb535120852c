'use client'

import Link from 'next/link'
import { 
  Search, Users, Truck, CheckCircle, 
  Clock, Star, ArrowRight, Phone,
  Wrench, Smartphone, MapPin, CreditCard,
  Shield, Award, Zap
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import MainHeader from '@/components/MainHeader'
import Footer from '@/components/Footer'

export default function HowItWorksPage() {
  const { t } = useTranslation()

  const steps = [
    {
      number: 1,
      icon: <Search className="w-8 h-8" />,
      title: 'Descreva o Problema',
      description: 'Selecione o seu dispositivo e descreva o que está a acontecer. Receba uma estimativa de preço imediata.',
      details: [
        'Escolha a marca e modelo do dispositivo',
        'Selecione o tipo de problema',
        'Adicione fotos se necessário',
        'Receba orçamento instantâneo'
      ]
    },
    {
      number: 2,
      icon: <Users className="w-8 h-8" />,
      title: 'Escolha o Técnico',
      description: 'Veja os técnicos especializados perto de si, com avaliações reais e preços transparentes.',
      details: [
        'Compare técnicos na sua área',
        'Veja avaliações de outros clientes',
        'Confirme preços e prazos',
        'Escolha o melhor para si'
      ]
    },
    {
      number: 3,
      icon: <Truck className="w-8 h-8" />,
      title: 'Entrega e Recolha',
      description: 'Escolha como quer entregar o dispositivo: na loja, por estafeta ou pelos correios.',
      details: [
        'Entrega na loja do técnico',
        'Recolha por estafeta em casa',
        'Envio pelos correios',
        'Acompanhe o progresso em tempo real'
      ]
    },
    {
      number: 4,
      icon: <Wrench className="w-8 h-8" />,
      title: 'Reparação',
      description: 'O técnico repara o seu dispositivo com peças de qualidade e envia atualizações.',
      details: [
        'Diagnóstico detalhado',
        'Reparação com peças originais',
        'Testes de qualidade',
        'Atualizações em tempo real'
      ]
    },
    {
      number: 5,
      icon: <CheckCircle className="w-8 h-8" />,
      title: 'Receba Reparado',
      description: 'Receba o seu dispositivo reparado com garantia de 6 meses incluída.',
      details: [
        'Dispositivo testado e funcional',
        'Garantia de 6 meses',
        'Relatório de reparação',
        'Suporte pós-venda'
      ]
    }
  ]

  const userTypes = [
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: 'Para Clientes',
      description: 'Processo simples e transparente',
      features: [
        'Orçamentos instantâneos',
        'Técnicos verificados',
        'Acompanhamento em tempo real',
        'Garantia de 6 meses',
        'Múltiplas opções de entrega'
      ]
    },
    {
      icon: <Wrench className="w-8 h-8" />,
      title: 'Para Técnicos',
      description: 'Expanda o seu negócio',
      features: [
        'Mais clientes automaticamente',
        'Sistema de gestão integrado',
        'Pagamentos seguros',
        'Marketplace de peças',
        'Suporte dedicado'
      ]
    },
    {
      icon: <Truck className="w-8 h-8" />,
      title: 'Para Estafetas',
      description: 'Ganhe dinheiro extra',
      features: [
        'Horários flexíveis',
        'Pagamento por entrega',
        'Rotas otimizadas',
        'App móvel dedicada',
        'Suporte 24/7'
      ]
    }
  ]

  const benefits = [
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Rápido',
      description: 'Maioria das reparações no mesmo dia'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Seguro',
      description: 'Técnicos verificados e seguros'
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: 'Qualidade',
      description: 'Peças originais e garantia incluída'
    },
    {
      icon: <CreditCard className="w-6 h-6" />,
      title: 'Transparente',
      description: 'Preços claros sem surpresas'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Zap className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('howItWorksTitle')}
            </h1>
            <p className="text-xl text-emerald-100 max-w-3xl mx-auto mb-8">
              {t('howItWorksSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/cliente/reparacoes/nova-v2"
                className="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Wrench className="w-5 h-5" />
                <span>Começar Agora</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/sobre-nos"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Users className="w-5 h-5" />
                <span>Sobre Nós</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Como Funciona
            </h2>
            <p className="text-xl text-gray-600">
              Processo simples em 5 passos
            </p>
          </div>
          
          <div className="space-y-16">
            {steps.map((step, index) => (
              <div key={index} className={`flex flex-col lg:flex-row items-center gap-12 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
                <div className="flex-1">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-600 text-white rounded-full flex items-center justify-center text-xl font-bold mr-4">
                      {step.number}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">{step.title}</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">{step.description}</p>
                  <div className="space-y-3">
                    {step.details.map((detail, idx) => (
                      <div key={idx} className="flex items-center space-x-3">
                        <CheckCircle className="w-5 h-5 text-emerald-500 flex-shrink-0" />
                        <span className="text-gray-700">{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="flex-1 flex justify-center">
                  <div className="w-64 h-64 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-3xl flex items-center justify-center text-emerald-600">
                    <div className="w-24 h-24">
                      {step.icon}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* User Types Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Para Todos os Utilizadores
            </h2>
            <p className="text-xl text-gray-600">
              A Revify funciona para clientes, técnicos e estafetas
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {userTypes.map((type, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mb-6 text-emerald-600">
                  {type.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{type.title}</h3>
                <p className="text-gray-600 mb-6">{type.description}</p>
                <div className="space-y-3">
                  {type.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Porque Escolher a Revify?
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-6 text-emerald-600">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Perguntas Frequentes
            </h2>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Quanto tempo demora uma reparação?
              </h3>
              <p className="text-gray-600">
                A maioria das reparações fica pronta no mesmo dia. Reparações mais complexas podem demorar 2-3 dias.
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Como funciona a garantia?
              </h3>
              <p className="text-gray-600">
                Todas as reparações incluem garantia de 6 meses. Se algo correr mal, reparamos novamente sem custos.
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Posso acompanhar o progresso da reparação?
              </h3>
              <p className="text-gray-600">
                Sim! Recebe atualizações em tempo real sobre o estado da sua reparação através da nossa plataforma.
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Como são selecionados os técnicos?
              </h3>
              <p className="text-gray-600">
                Todos os técnicos passam por um processo rigoroso de verificação, incluindo certificações e avaliações.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Pronto para Começar?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Junte-se a milhares de clientes satisfeitos
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/cliente/reparacoes/nova-v2"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Wrench className="w-5 h-5" />
              <span>Reparar Agora</span>
            </Link>
            <Link
              href="/tornar-se-parceiro"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Users className="w-5 h-5" />
              <span>Ser Parceiro</span>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
