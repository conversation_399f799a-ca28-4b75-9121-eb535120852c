import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { approvedPrice, paymentMethod } = await request.json()

    // Verificar se a reparação pertence ao cliente
    const repair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        customerId: session.user.id
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    const originalPrice = repair.estimatedPrice ? Number(repair.estimatedPrice) : 0
    const newPrice = Number(approvedPrice)
    const difference = newPrice - originalPrice

    // Atualizar a reparação
    await prisma.repair.update({
      where: {
        id: params.id
      },
      data: {
        estimatedPrice: newPrice,
        finalPrice: newPrice
      }
    })

    // Se há diferença de preço, processar pagamento adicional
    if (difference > 0) {
      // Buscar chave do Stripe das configurações do admin
      const stripeSecretSetting = await prisma.systemSettings.findUnique({
        where: { key: 'stripeSecretKey' }
      })

      const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

      if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
        return NextResponse.json(
          { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
          { status: 400 }
        )
      }

      // Criar sessão de pagamento Stripe para a diferença
      const stripe = await createStripeInstance(stripeSecretKey)
      
      const session_stripe = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'eur',
              product_data: {
                name: `Diferença de Preço - Reparação #${params.id.slice(-8)}`,
                description: `Diferença entre preço original (€${originalPrice.toFixed(2)}) e preço final (€${newPrice.toFixed(2)})`
              },
              unit_amount: Math.round(difference * 100)
            },
            quantity: 1
          }
        ],
        mode: 'payment',
        success_url: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=success`,
        cancel_url: `${process.env.NEXTAUTH_URL}/cliente/reparacoes/${params.id}?payment=cancelled`,
        metadata: {
          repairId: params.id,
          type: 'price_difference',
          originalPrice: originalPrice.toString(),
          newPrice: newPrice.toString()
        }
      })

      // Criar registro de pagamento
      await prisma.payment.create({
        data: {
          repairId: params.id,
          amount: difference,
          method: 'stripe',
          status: 'PENDING',
          stripeSessionId: session_stripe.id,
          platformFee: difference * 0.05, // 5% de comissão
          shopAmount: difference * 0.95
        }
      })

      return NextResponse.json({
        message: 'Preço aprovado. Redirecionando para pagamento...',
        paymentUrl: session_stripe.url
      })
    } else if (difference < 0) {
      // Crédito para o cliente (implementar sistema de créditos)
      return NextResponse.json({
        message: `Preço aprovado. Você receberá um crédito de €${Math.abs(difference).toFixed(2)}.`
      })
    } else {
      // Sem diferença de preço
      return NextResponse.json({
        message: 'Preço aprovado com sucesso!'
      })
    }

  } catch (error) {
    console.error('Erro ao aprovar preço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
