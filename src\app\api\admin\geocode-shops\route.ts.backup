import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { geocodeRepairShops } from '@/lib/geocoding'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const result = await geocodeRepairShops()

    if (result.success) {
      return NextResponse.json({
        message: `Geocodificação concluída. ${result.processed} lojas processadas.`,
        processed: result.processed
      })
    } else {
      return NextResponse.json(
        { message: `Erro na geocodificação: ${result.error}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Erro na geocodificação:', error)
    return NextResponse.json(
      { message: '<PERSON>rro interno do servidor' },
      { status: 500 }
    )
  }
}
