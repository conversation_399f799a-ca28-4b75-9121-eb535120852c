import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const appId = searchParams.get('appId')

    if (!appId) {
      return NextResponse.json(
        { message: 'ID da app é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar status da app instalada
    const installedApp = await prisma.$queryRaw`
      SELECT 
        ia.*,
        ad.name as app_name,
        ad."monthlyPrice" as app_price,
        ad."hasTrialPeriod",
        ad."trialDays"
      FROM installed_apps ia
      JOIN app_definitions ad ON ia."appId" = ad."appId"
      WHERE ia."userId" = ${session.user.id}
      AND ia."appId" = ${appId}
      AND ia."isActive" = true
      LIMIT 1
    ` as any[]

    if (installedApp.length === 0) {
      return NextResponse.json(
        { message: 'App não instalada' },
        { status: 404 }
      )
    }

    const app = installedApp[0]
    const now = new Date()

    let status = 'unknown'
    let daysRemaining = 0
    let canUpgrade = false
    let trialExpired = false

    if (app.isPaid && !app.isTrialActive) {
      status = 'paid'
    } else if (app.isTrialActive && app.trialEndDate) {
      const trialEnd = new Date(app.trialEndDate)
      const timeDiff = trialEnd.getTime() - now.getTime()
      daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24))

      if (daysRemaining > 0) {
        status = 'trial_active'
      } else {
        status = 'trial_expired'
        trialExpired = true
        canUpgrade = true
        daysRemaining = 0
      }
    } else if (!app.isPaid && !app.isTrialActive) {
      status = 'free'
      canUpgrade = true
    }

    return NextResponse.json({
      appId: app.appId,
      appName: app.app_name,
      status: status,
      isPaid: app.isPaid,
      isTrialActive: app.isTrialActive,
      trialStartDate: app.trialStartDate,
      trialEndDate: app.trialEndDate,
      daysRemaining: daysRemaining,
      trialExpired: trialExpired,
      canUpgrade: canUpgrade,
      monthlyPrice: Number(app.app_price || 0),
      nextBillingDate: app.nextBillingDate,
      addonSubscriptionId: app.addonSubscriptionId
    })

  } catch (error) {
    console.error('Erro ao verificar status de trial:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar e desativar trials expirados
    await prisma.$queryRaw`
      UPDATE installed_apps 
      SET "isActive" = false, "uninstalledAt" = NOW()
      WHERE "userId" = ${session.user.id}
      AND "isTrialActive" = true
      AND "trialEndDate" < NOW()
      AND "isPaid" = false
    `

    return NextResponse.json({
      message: 'Trials expirados processados'
    })

  } catch (error) {
    console.error('Erro ao processar trials expirados:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
