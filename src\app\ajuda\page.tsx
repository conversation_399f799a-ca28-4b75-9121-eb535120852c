'use client'

import { useState } from 'react'
import Link from 'next/link'
import ModernLayout from '@/components/ModernLayout'
import AutoTranslate from '@/components/ui/AutoTranslate'
import {
  Search,
  Book,
  Users,
  Truck,
  Settings,
  CreditCard,
  Shield,
  Phone,
  Mail,
  MessageCircle,
  ChevronRight,
  ArrowLeft,
  HelpCircle,
  FileText,
  Video,
  Download
} from 'lucide-react'

const helpCategories = [
  {
    id: "clientes",
    title: "Para Clientes",
    description: "Como usar a plataforma como cliente",
    icon: Users,
    color: 'blue',
    articles: [
      { title: 'Como criar uma conta', slug: 'criar-conta', type: 'article' },
      { title: "Como fazer uma reparação", slug: 'fazer-reparacao', type: 'article' },
      { title: "Acompanhar o estado da reparação", slug: 'acompanhar-reparacao', type: 'article' },
      { title: 'Como pagar online', slug: "pagamento-online", type: 'article' },
      { title: "Marketplace - Como comprar", slug: "marketplace-comprar", type: 'article' },
      { title: "Gestão de perfil", slug: "gestao-perfil", type: 'article' }
    ]
  },
  {
    id: 'lojistas',
    title: 'Para Lojistas',
    description: "Guia completo para oficinas de reparação",
    icon: Settings,
    color: 'green',
    articles: [
      { title: 'Primeiros passos', slug: "primeiros-passos-lojista", type: 'article' },
      { title: "Gestão de reparações", slug: 'gestao-reparacoes', type: 'article' },
      { title: "Sistema de orçamentos", slug: 'sistema-orcamentos', type: 'article' },
      { title: "Marketplace - Vender produtos", slug: "marketplace-vender", type: 'article' },
      { title: 'App Store - Instalar apps', slug: 'appstore-instalar', type: 'article' },
      { title: 'Newsletter Pro', slug: 'newsletter-pro', type: 'article' },
      { title: "Integração Moloni", slug: 'integracao-moloni', type: 'article' },
      { title: "Gestão financeira", slug: 'gestao-financeira', type: 'article' }
    ]
  },
  {
    id: 'estafetas',
    title: 'Para Estafetas',
    description: "Como funciona o sistema de entregas",
    icon: Truck,
    color: 'orange',
    articles: [
      { title: "Como ser estafeta", slug: "como-ser-estafeta", type: 'article' },
      { title: 'Aceitar entregas', slug: 'aceitar-entregas', type: 'article' },
      { title: 'Sistema de pagamentos', slug: "pagamentos-estafeta", type: 'article' },
      { title: "Zonas de entrega", slug: "zonas-entrega", type: 'article' },
      { title: "App móvel", slug: "app-movel-estafeta", type: 'article' }
    ]
  },
  {
    id: 'pagamentos',
    title: "Pagamentos & Faturação",
    description: "Tudo sobre pagamentos e faturas",
    icon: CreditCard,
    color: 'purple',
    articles: [
      { title: "Métodos de pagamento", slug: "metodos-pagamento", type: 'article' },
      { title: "Faturação automática", slug: 'faturacao-automatica', type: 'article' },
      { title: 'Reembolsos', slug: 'reembolsos', type: 'article' },
      { title: "Subscrições e planos", slug: 'subscricoes-planos', type: 'article' }
    ]
  },
  {
    id: 'seguranca',
    title: "Segurança & Privacidade",
    description: "Proteção de dados e segurança",
    icon: Shield,
    color: 'red',
    articles: [
      { title: "Política de privacidade", slug: 'politica-privacidade', type: 'article' },
      { title: "Termos de serviço", slug: 'termos-servico', type: 'article' },
      { title: "Segurança da conta", slug: 'seguranca-conta', type: 'article' },
      { title: "RGPD e proteção de dados", slug: 'rgpd-protecao-dados', type: 'article' }
    ]
  }
]

const popularArticles = [
  { title: "Como fazer a primeira reparação", category: "Clientes", views: 1250, slug: "/ajuda/clientes/fazer-reparacao" },
  { title: "Configurar loja no marketplace", category: 'Lojistas', views: 980, slug: "/ajuda/lojistas/marketplace-vender" },
  { title: 'Instalar Newsletter Pro', category: 'Lojistas', views: 756, slug: "/ajuda/lojistas/newsletter-pro" },
  { title: "Métodos de pagamento disponíveis", category: 'Pagamentos', views: 654, slug: "/ajuda/pagamentos/metodos-pagamento" },
  { title: "Como ser estafeta", category: 'Estafetas', views: 543, slug: "/ajuda/estafetas/como-ser-estafeta" }
]

export default function AjudaPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [showSearchResults, setShowSearchResults] = useState(false)

  const filteredCategories = helpCategories.filter(category =>
    category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.articles.some(article =>
      article.title.toLowerCase().includes(searchTerm.toLowerCase())
    )
  )

  // Buscar artigos específicos para o dropdown
  const searchResults = searchTerm.length > 2 ? helpCategories.flatMap(category =>
    category.articles
      .filter(article => article.title.toLowerCase().includes(searchTerm.toLowerCase()))
      .map(article => ({ ...article, category: category.title, categoryId: category.id }))
  ).slice(0, 5) : []

  if (selectedCategory) {
    const category = helpCategories.find(c => c.id === selectedCategory)
    if (!category) return null

    return (
      <ModernLayout>
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => setSelectedCategory(null)}
                  className="flex items-center text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" /><AutoTranslate text="Voltar à Central de Ajuda" /></button>
              </div>
            </div>
          </div>
        </div>

        {/* Category Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center mb-12">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-${category.color}-100 mb-4`}>
              <category.icon className={`w-8 h-8 text-${category.color}-600`} />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{category.title}</h1>
            <p className="text-xl text-gray-600">{category.description}</p>
          </div>

          {/* Articles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {category.articles.map((article, index) => (
              <Link
                key={index}
                href={`/ajuda/${category.id}/${article.slug}`}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {article.type === 'video' ? (
                      <Video className="w-6 h-6 text-blue-600" />
                    ) : (
                      <FileText className="w-6 h-6 text-gray-600" />
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {article.title}
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {article.type === 'video' ? 'Vídeo tutorial' : 'Artigo'}
                      </span>
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Contact Support */}
          <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <HelpCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2"><AutoTranslate text="Não encontrou o que procurava?" /></h3>
            <p className="text-gray-600 mb-6"><AutoTranslate text="A nossa equipa de suporte está aqui para ajudar" /></p>
            <Link
              href="/contactos"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contactar Suporte
            </Link>
          </div>
        </div>
      </ModernLayout>
    )
  }

  return (
    <ModernLayout>

      {/* Hero Section - Estilo Intercom */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
          <div className="mb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
              <HelpCircle className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Como podemos<br />ajudar?
            </h1>
            <p className="text-xl text-gray-600 mb-10 max-w-2xl mx-auto"><AutoTranslate text="Encontre respostas rápidas para as suas questões ou explore os nossos guias detalhados" /></p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
              <input
                type="text"
                placeholder="Descreva o seu problema ou pesquise por palavras-chave..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setShowSearchResults(e.target.value.length > 2)
                }}
                onFocus={() => setShowSearchResults(searchTerm.length > 2)}
                onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                className="w-full pl-16 pr-6 py-5 text-lg border-2 border-gray-200 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-0 transition-all duration-200 shadow-sm hover:shadow-md"
              />

              {/* Search Results Dropdown */}
              {showSearchResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                  <div className="p-2">
                    <div className="text-xs text-gray-500 px-3 py-2 font-medium">
                      Resultados da pesquisa
                    </div>
                    {searchResults.map((result, index) => (
                      <Link
                        key={index}
                        href={`/ajuda/${result.categoryId}/${result.slug}`}
                        className="block w-full text-left px-3 py-3 hover:bg-gray-50 rounded-lg transition-colors"
                        onClick={() => setShowSearchResults(false)}
                      >
                        <div className="font-medium text-gray-900">{result.title}</div>
                        <div className="text-sm text-gray-500">{result.category}</div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* No Results */}
              {showSearchResults && searchTerm.length > 2 && searchResults.length === 0 && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50">
                  <div className="p-4 text-center text-gray-500">
                    <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <div className="font-medium">Nenhum resultado encontrado</div>
                    <div className="text-sm">Tente usar palavras-chave diferentes</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Popular Articles */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Artigos Populares</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {popularArticles.map((article, index) => (
              <Link key={index} href={article.slug} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium text-gray-900 mb-2">{article.title}</h3>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>{article.category}</span>
                  <span>{article.views} visualizações</span>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Categories Grid - Estilo Intercom */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4"><AutoTranslate text="Explore por categoria" /></h2>
            <p className="text-lg text-gray-600">Encontre rapidamente o que procura</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCategories.map((category) => {
              const colorClasses = {
                blue: 'bg-blue-50 border-blue-100 hover:border-blue-200 text-blue-600',
                green: 'bg-green-50 border-green-100 hover:border-green-200 text-green-600',
                purple: 'bg-purple-50 border-purple-100 hover:border-purple-200 text-purple-600',
                orange: 'bg-orange-50 border-orange-100 hover:border-orange-200 text-orange-600',
                red: 'bg-red-50 border-red-100 hover:border-red-200 text-red-600'
              }

              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`${colorClasses[category.color as keyof typeof colorClasses]} border-2 rounded-2xl p-8 text-left hover:shadow-lg transition-all duration-200 group`}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-14 h-14 rounded-2xl bg-white shadow-sm flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                      <category.icon className="w-7 h-7" />
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{category.title}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">{category.description}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <span>{category.articles.length} artigos disponíveis</span>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Contact Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Precisa de mais ajuda?" /></h2>
            <p className="text-gray-600"><AutoTranslate text="A nossa equipa está disponível para ajudar com qualquer questão" /></p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link
              href="/contactos"
              className="flex flex-col items-center p-6 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <MessageCircle className="w-8 h-8 text-blue-600 mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Chat ao Vivo</h3>
              <p className="text-sm text-gray-600 text-center">
                Fale connosco em tempo real
              </p>
            </Link>
            
            <Link
              href="mailto:<EMAIL>"
              className="flex flex-col items-center p-6 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <Mail className="w-8 h-8 text-green-600 mb-3" />
              <h3 className="font-medium text-gray-900 mb-2"><AutoTranslate text="Email" /></h3>
              <p className="text-sm text-gray-600 text-center">
                <EMAIL>
              </p>
            </Link>
            
            <Link
              href="tel:+351123456789"
              className="flex flex-col items-center p-6 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <Phone className="w-8 h-8 text-orange-600 mb-3" />
              <h3 className="font-medium text-gray-900 mb-2"><AutoTranslate text="Telefone" /></h3>
              <p className="text-sm text-gray-600 text-center">
                +351 123 456 789
              </p>
            </Link>
          </div>
        </div>
      </div>

    </ModernLayout>
  )
}
