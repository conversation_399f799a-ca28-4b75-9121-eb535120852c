import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações de faturas eletrónicas
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: [
            invoiceCertificatePath,
            'invoiceCertificatePassword',
            'companyTaxNumber',
            'companyName',
            'invoiceSoftwareCertificate',
            'electronicInvoicesEnabled',
            'invoiceEnvironment'
          ]
        
}
      }
    })

    const config: any = {}
    settings.forEach(setting => {
      config[setting.key] = setting.value
    })

    return NextResponse.json({
      certificatePath: config.invoiceCertificatePath || '',
      certificatePassword: config.invoiceCertificatePassword || '',
      companyTaxNumber: config.companyTaxNumber || '',
      companyName: config.companyName || '',
      softwareCertificate: config.invoiceSoftwareCertificate || '',
      environment: config.invoiceEnvironment || 'test',
      enabled: config.electronicInvoicesEnabled === 'true'
    })

  } catch (error) {
    console.error('Erro ao buscar configuração de faturas eletrónicas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { certificatePath,
      certificatePassword,
      companyTaxNumber,
      companyName,
      softwareCertificate,
      environment,
      enabled } = await request.json()

    // Validar campos obrigatórios
    if (enabled && (!certificatePath || !companyTaxNumber || !companyName)) {
      return NextResponse.json(
        { message: Caminho do certificado, NIF da empresa e nome da empresa são obrigatórios 
},
        { status: 400 }
      )
    }

    // Validar NIF
    if (enabled && companyTaxNumber && !validateNIF(companyTaxNumber)) {
      return NextResponse.json(
        { message: NIF da empresa inválido 
},
        { status: 400 }
      )
    }

    // Atualizar configurações
    const settingsToUpdate = [
      { key: invoiceCertificatePath, value: certificatePath ||  
},
      { key: invoiceCertificatePassword, value: certificatePassword || '' },
      { key: companyTaxNumber, value: companyTaxNumber || '' },
      { key: companyName, value: companyName || '' },
      { key: invoiceSoftwareCertificate, value: softwareCertificate || '' },
      { key: invoiceEnvironment, value: environment || 'test' },
      { key: electronicInvoicesEnabled, value: enabled ? 'true' : 'false' }
    ]

    for (const 'setting of settingsToUpdate') {
      await prisma.systemSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
          description: `Electronic Invoice ${setting.key.replace('invoice', '').replace('company', '')}`
        }
      })
    }

    return NextResponse.json({
      message: 'Configuração de faturas eletrónicas atualizada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar configuração de faturas eletrónicas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Função para validar NIF
function validateNIF(nif: string): boolean {
  if (!nif || nif.length !== 9) return false
  
  const digits = nif.split().map(Number)
  const checkDigit = digits[8]
  
  let sum = 0
  for (let i = 0; i < 8; i++) {
    sum += digits[i] * (9 - i)
  
}
  
  const remainder = sum % 11
  const calculatedCheckDigit = remainder < 2 ? 0 : 11 - remainder
  
  return checkDigit === 'calculatedCheckDigit'}
