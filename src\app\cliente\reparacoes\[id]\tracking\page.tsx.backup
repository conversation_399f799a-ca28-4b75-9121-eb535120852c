'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, CheckCircle, Clock, AlertCircle, Package, Truck, Home } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface TrackingStep {
  id: string
  status: string
  title: string
  description: string
  timestamp?: string
  completed: boolean
  current: boolean
  icon: React.ReactNode
}

export default function TrackingPage({ params }: { params: { id: string } }) {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [repair, setRepair] = useState<any>(null)
  const [trackingSteps, setTrackingSteps] = useState<TrackingStep[]>([])

  useEffect(() => {
    fetchRepairTracking()
    // Atualizar tracking a cada 30 segundos
    const interval = setInterval(fetchRepairTracking, 30000)
    return () => clearInterval(interval)
  }, [params.id])

  const fetchRepairTracking = async () => {
    try {
      const response = await fetch(`/api/cliente/repairs/${params.id}/tracking`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data.repair)
        setTrackingSteps(generateTrackingSteps(data.repair))
      }
    } catch (error) {
      console.error('Erro ao carregar tracking:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateTrackingSteps = (repair: any): TrackingStep[] => {
    const steps = [
      {
        id: 'CONFIRMED',
        status: 'CONFIRMED',
        title: 'Reparação Confirmada',
        description: 'Pagamento processado e reparação confirmada',
        icon: <CheckCircle className="w-6 h-6" />,
        completed: ['CONFIRMED', 'RECEIVED', 'DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'CONFIRMED'
      },
      {
        id: 'RECEIVED',
        status: 'RECEIVED',
        title: 'Dispositivo Recebido',
        description: 'Dispositivo chegou à loja de reparação',
        icon: <Package className="w-6 h-6" />,
        completed: ['RECEIVED', 'DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'RECEIVED'
      },
      {
        id: 'DIAGNOSIS',
        status: 'DIAGNOSIS',
        title: 'Diagnóstico',
        description: 'Técnico está a analisar o problema',
        icon: <AlertCircle className="w-6 h-6" />,
        completed: ['DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'DIAGNOSIS'
      },
      {
        id: 'WAITING_PARTS',
        status: 'WAITING_PARTS',
        title: 'Aguarda Peças',
        description: 'A aguardar chegada de peças necessárias',
        icon: <Clock className="w-6 h-6" />,
        completed: ['WAITING_PARTS', 'IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'WAITING_PARTS'
      },
      {
        id: 'IN_REPAIR',
        status: 'IN_REPAIR',
        title: 'Em Reparação',
        description: 'Técnico está a reparar o dispositivo',
        icon: <Package className="w-6 h-6" />,
        completed: ['IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'IN_REPAIR'
      },
      {
        id: 'TESTING',
        status: 'TESTING',
        title: 'Teste',
        description: 'A testar funcionamento após reparação',
        icon: <CheckCircle className="w-6 h-6" />,
        completed: ['TESTING', 'COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'TESTING'
      },
      {
        id: 'COMPLETED',
        status: 'COMPLETED',
        title: 'Reparação Concluída',
        description: 'Dispositivo reparado e pronto para entrega',
        icon: <CheckCircle className="w-6 h-6" />,
        completed: ['COMPLETED', 'DELIVERED'].includes(repair.status),
        current: repair.status === 'COMPLETED'
      },
      {
        id: 'DELIVERED',
        status: 'DELIVERED',
        title: 'Entregue',
        description: 'Dispositivo entregue ao cliente',
        icon: repair.deliveryMethod === 'STORE_PICKUP' ? <Home className="w-6 h-6" /> : <Truck className="w-6 h-6" />,
        completed: repair.status === 'DELIVERED',
        current: repair.status === 'DELIVERED'
      }
    ]

    // Filtrar steps que não se aplicam
    if (repair.status !== 'WAITING_PARTS') {
      return steps.filter(step => step.status !== 'WAITING_PARTS')
    }

    return steps
  }

  const getStatusColor = (step: TrackingStep) => {
    if (step.completed) return 'text-green-600'
    if (step.current) return 'text-blue-600'
    return 'text-gray-400'
  }

  const getStatusBgColor = (step: TrackingStep) => {
    if (step.completed) return 'bg-green-100'
    if (step.current) return 'bg-blue-100'
    return 'bg-gray-100'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Tracking</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href={`/cliente/reparacoes/${params.id}`} className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Voltar à Reparação
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Tracking da Reparação</h1>

          {repair && (
            <div className="mb-8 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Dispositivo:</span>
                  <p className="text-gray-600">{repair.deviceModel?.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Problema:</span>
                  <p className="text-gray-600">{repair.problemType?.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Loja:</span>
                  <p className="text-gray-600">{repair.repairShop?.profile?.companyName}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Status Atual:</span>
                  <p className="text-gray-600">{repair.status}</p>
                </div>
              </div>
            </div>
          )}

          {/* Timeline */}
          <div className="space-y-6">
            {trackingSteps.map((step, index) => (
              <div key={step.id} className="flex items-start">
                <div className="flex-shrink-0">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getStatusBgColor(step)}`}>
                    <div className={getStatusColor(step)}>
                      {step.icon}
                    </div>
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className={`text-lg font-medium ${getStatusColor(step)}`}>
                      {step.title}
                    </h3>
                    {step.timestamp && (
                      <span className="text-sm text-gray-500">
                        {new Date(step.timestamp).toLocaleString('pt-PT')}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 mt-1">{step.description}</p>
                  
                  {step.current && (
                    <div className="mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Em andamento
                    </div>
                  )}
                </div>
                
                {index < trackingSteps.length - 1 && (
                  <div className="absolute left-6 mt-12 w-0.5 h-6 bg-gray-300"></div>
                )}
              </div>
            ))}
          </div>

          {/* Estimated Completion */}
          {repair && repair.status !== 'DELIVERED' && (
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Tempo Estimado</h4>
              <p className="text-sm text-blue-700">
                A reparação deverá estar concluída em aproximadamente {repair.estimatedTime || '2-3'} dias úteis.
              </p>
            </div>
          )}

          {/* Quick Actions */}
          <div className="mt-8 flex flex-wrap gap-3">
            <Link
              href={`/cliente/reparacoes/${params.id}/chat`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Falar com a Loja
            </Link>
            {repair?.status === 'COMPLETED' && (
              <Link
                href={`/cliente/reparacoes/${params.id}/avaliar`}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Avaliar Serviço
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
