#!/usr/bin/env node

/**
 * Script final para limpar todos os problemas de sintaxe
 */

const fs = require('fs');
const path = require('path');

// Diretórios para processar
const dirsToProcess = [
  'src/app',
  'src/components'
];

// Extensões de arquivo para processar
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!file.startsWith('.') && file !== 'node_modules') {
        getAllFiles(filePath, fileList);
      }
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function finalCleanup(content) {
  let fixed = content;
  let changes = 0;

  // 1. Remover imports duplicados de AutoTranslate
  const duplicateImportRegex = /import AutoTranslate from '@\/components\/ui\/AutoTranslate'\s*\n\s*import AutoTranslate from '@\/components\/ui\/AutoTranslate'/g;
  fixed = fixed.replace(duplicateImportRegex, "import AutoTranslate from '@/components/ui/AutoTranslate'");
  if (content !== fixed) changes++;

  // 2. Corrigir placeholders malformados
  const malformedPlaceholderRegex = /placeholder="\{([^}]+)\}"/g;
  fixed = fixed.replace(malformedPlaceholderRegex, (match, content) => {
    changes++;
    return `placeholder={${content}}`;
  });

  // 3. Corrigir AutoTranslate em atributos que não devem ter
  const attributeAutoTranslateRegex = /(href|src|alt|id|className|type|name|value)=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(attributeAutoTranslateRegex, (match, attr, text) => {
    changes++;
    return `${attr}="${text}"`;
  });

  // 4. Corrigir AutoTranslate em URLs
  const urlAutoTranslateRegex = /url\(<AutoTranslate text="([^"]+)" \/>\)/g;
  fixed = fixed.replace(urlAutoTranslateRegex, (match, text) => {
    changes++;
    return `url('${text}')`;
  });

  // 5. Corrigir AutoTranslate aninhado
  const nestedAutoTranslateRegex = /<AutoTranslate text=<AutoTranslate text="([^"]+)" \/> \/>/g;
  fixed = fixed.replace(nestedAutoTranslateRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  // 6. Corrigir múltiplos AutoTranslate aninhados
  const multiNestedRegex = /<AutoTranslate text=<AutoTranslate text=<AutoTranslate text=<AutoTranslate text=<AutoTranslate text="([^"]+)" \/> \/> \/> \/> \/>/g;
  fixed = fixed.replace(multiNestedRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  // 7. Corrigir AutoTranslate em strings JavaScript
  const jsStringRegex = /(['"`])<AutoTranslate text="([^"]+)" \/>\1/g;
  fixed = fixed.replace(jsStringRegex, (match, quote, text) => {
    changes++;
    return `${quote}${text}${quote}`;
  });

  // 8. Corrigir quebras de linha em AutoTranslate
  const multilineRegex = /<AutoTranslate text="([^"]*\n[^"]*)" \/>/g;
  fixed = fixed.replace(multilineRegex, (match, text) => {
    const cleanText = text.replace(/\n\s*/g, ' ').trim();
    changes++;
    return `<AutoTranslate text="${cleanText}" />`;
  });

  // 9. Corrigir propriedades malformadas em componentes
  const malformedPropsRegex = /(\w+)=<AutoTranslate text="([^"]+)" \/>\s+(\w+)=true\s+(\w+)=true/g;
  fixed = fixed.replace(malformedPropsRegex, (match, prop1, text, prop2, prop3) => {
    changes++;
    return `${prop1}="${text}"`;
  });

  // 10. Remover propriedades inválidas de componentes HTML
  const invalidPropsRegex = /\s+(Selecione|uma|categoria|marca|Descreva|detalhadamente|o|problema|do|seu|dispositivo|Seu|nome|completo|número|de|telefone|Número|contribuinte)=true/g;
  fixed = fixed.replace(invalidPropsRegex, '');
  if (content !== fixed) changes++;

  return { content: fixed, changes };
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  
  const originalContent = fs.readFileSync(filePath, 'utf8');
  const { content: fixedContent, changes } = finalCleanup(originalContent);
  
  if (changes > 0) {
    console.log(`🔧 ${filePath} - ${changes} correções finais aplicadas`);
    fs.writeFileSync(filePath, fixedContent);
  }
}

// Função principal
function main() {
  console.log('🧹 Limpeza final de todos os problemas de sintaxe...\n');
  
  let allFiles = [];
  
  // Coletar todos os arquivos
  dirsToProcess.forEach(dir => {
    const files = getAllFiles(dir);
    allFiles = allFiles.concat(files);
  });
  
  console.log(`📁 Processando ${allFiles.length} arquivos...\n`);
  
  // Processar cada arquivo
  allFiles.forEach(processFile);
  
  console.log('\n✅ Limpeza final concluída!');
  console.log('🎯 Todos os problemas críticos de sintaxe foram corrigidos');
  console.log('🚀 O servidor deve compilar sem erros agora');
}

main();
