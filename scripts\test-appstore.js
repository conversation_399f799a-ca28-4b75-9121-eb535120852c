const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAppStore() {
  try {
    console.log('🔍 Testando AppStore...')
    
    // Encontrar o lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      }
    })

    if (!lojista) {
      console.log('❌ Lojista de teste não encontrado')
      return
    }

    console.log('✅ Lojista encontrado:', lojista.name, `(${lojista.email})`)

    // Verificar se existem apps instaladas
    const installedApps = await prisma.$queryRaw`
      SELECT "appId" FROM installed_apps
      WHERE "userId" = ${lojista.id} AND "isActive" = true
    `

    console.log('📱 Apps instaladas:', installedApps.length)
    installedApps.forEach((app, index) => {
      console.log(`${index + 1}. ${app.appId}`)
    })

    // Verificar subscription do lojista
    const subscription = await prisma.$queryRaw`
      SELECT
        s.*,
        sp."availableApps" as plan_available_apps,
        sp.name as plan_name
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${lojista.id}
      AND s.status = 'ACTIVE'
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    if (subscription.length > 0) {
      console.log('💳 Subscription ativa:', subscription[0].plan_name)
      console.log('📋 Apps disponíveis no plano:', subscription[0].plan_available_apps)
    } else {
      console.log('❌ Nenhuma subscription ativa encontrada')
    }

    // Verificar se a tabela installed_apps existe
    try {
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'installed_apps'
        );
      `
      console.log('🗄️ Tabela installed_apps existe:', tableExists[0].exists)
    } catch (error) {
      console.log('❌ Erro ao verificar tabela installed_apps:', error.message)
    }

    // Verificar se a tabela app_config existe
    try {
      const appConfigExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'app_config'
        );
      `
      console.log('🗄️ Tabela app_config existe:', appConfigExists[0].exists)
    } catch (error) {
      console.log('❌ Erro ao verificar tabela app_config:', error.message)
    }

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAppStore()
