const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createBasicPlan() {
  try {
    console.log('📋 Criando plano básico...')

    // Verificar se já existe um plano
    const existingPlan = await prisma.subscriptionPlan.findFirst()
    
    if (existingPlan) {
      console.log('ℹ️  Já existe um plano no sistema')
      console.log('✅ Plano existente:', existingPlan.name)
      return
    }

    // Criar plano Revy Pro
    const plan = await prisma.subscriptionPlan.create({
      data: {
        name: 'Revy Pro',
        description: 'Plano profissional com todas as funcionalidades',
        monthlyPrice: 49.99,
        yearlyPrice: 499.99,
        features: [
          'Reparações ilimitadas',
          'Marketplace de produtos',
          'Mini-loja personalizada',
          'Reparações independentes',
          'Integração Moloni',
          'Suporte prioritário'
        ],
        maxProducts: 100,
        maxRepairs: -1, // Ilimitado
        marketplaceCommission: 8.0,
        repairCommission: 5.0,
        smsAccess: true,
        whatsappAccess: true,
        emailSupport: true,
        paymentDelayDays: 7,
        sparePartsDiscount: 10.0,
        recommendedProductsEnabled: true,
        recommendedProductsDays: 30,
        certifiedBadge: true,
        priority: 2,
        moloniIntegration: true,
        miniStore: true,
        individualRepairs: true,
        isPopular: true,
        isActive: true
      }
    })

    console.log('✅ Plano criado com sucesso!')
    console.log('📋 Nome:', plan.name)
    console.log('💰 Preço mensal: €' + plan.monthlyPrice)
    console.log('🏪 Mini-loja:', plan.miniStore ? 'Sim' : 'Não')
    console.log('🔧 Reparações independentes:', plan.individualRepairs ? 'Sim' : 'Não')

  } catch (error) {
    console.error('❌ Erro ao criar plano:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createBasicPlan()
