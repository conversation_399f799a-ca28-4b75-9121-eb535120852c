import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function middleware(request: NextRequest) {
  const hostname = request.headers.get('host') || ''
  const pathname = request.nextUrl.pathname

  // Verificar se é localhost com parâmetro ?shop=
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    const shopParam = request.nextUrl.searchParams.get('shop')

    if (shopParam && !pathname.startsWith('/shop/') && !pathname.startsWith('/api/') && !pathname.startsWith('/_next/')) {
      // Redirecionar para a mini-loja do lojista
      const shopUrl = new URL(request.url)
      shopUrl.pathname = `/shop/${shopParam}${pathname === '/' ? '' : pathname}`
      shopUrl.searchParams.delete('shop') // Remover o parâmetro da URL

      return NextResponse.rewrite(shopUrl)
    }
  }

  // Verificar se é um subdomínio .revify.pt em produção
  const parts = hostname.split('.')
  if (parts.length >= 3 && parts[parts.length - 2] === 'revify' && parts[parts.length - 1] === 'pt') {
    const subdomain = parts[0]

    // Excluir subdomínios do sistema
    if (!['www', 'api', 'admin', 'app', 'mail', 'ftp'].includes(subdomain)) {
      if (!pathname.startsWith('/shop/') && !pathname.startsWith('/api/') && !pathname.startsWith('/_next/')) {
        const shopUrl = new URL(request.url)
        shopUrl.pathname = `/shop/${subdomain}${pathname === '/' ? '' : pathname}`

        return NextResponse.rewrite(shopUrl)
      }
    }
  }

  // Verificar autenticação para rotas protegidas
  if (pathname.startsWith('/admin') || pathname.startsWith('/lojista') || pathname.startsWith('/cliente') || pathname.startsWith('/estafeta')) {
    let token

    try {
      token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET,
        secureCookie: process.env.NODE_ENV === 'production'
      })

      if (!token) {
        return NextResponse.redirect(new URL('/auth/signin', request.url))
      }
    } catch (error) {
      console.error('Error getting token:', error)
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    // Verificar permissões específicas
    if (pathname.startsWith('/admin') && token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (pathname.startsWith('/lojista') && token.role !== 'REPAIR_SHOP') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (pathname.startsWith('/cliente') && token.role !== 'CUSTOMER') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (pathname.startsWith('/estafeta') && token.role !== 'COURIER') {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - auth (authentication pages)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api/auth|auth|_next/static|_next/image|favicon.ico).*)',
  ],
}
