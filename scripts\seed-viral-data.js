const { PrismaClient } = require('@prisma/client')

async function seedViralData() {
  const prisma = new PrismaClient()

  try {
    console.log('🌱 Criando dados de teste para o sistema viral...\n')

    // 1. Buscar usuários existentes
    const users = await prisma.user.findMany({
      take: 5,
      select: { id: true, name: true, role: true, email: true }
    })

    if (users.length === 0) {
      console.log('❌ Nenhum usuário encontrado. Execute o seed de usuários primeiro.')
      return
    }

    console.log(`👥 Encontrados ${users.length} usuários`)

    // 2. Criar atividades virais de exemplo
    const activities = [
      {
        type: 'USER_REGISTERED',
        description: `${users[0].name} juntou-se à plataforma como ${users[0].role}`,
        points: 10,
        isPublic: true
      },
      {
        type: 'SHOP_CREATED',
        description: `${users[1]?.name || users[0].name} criou uma nova loja`,
        points: 50,
        isPublic: true
      },
      {
        type: 'REPAIR_COMPLETED',
        description: `${users[0].name} completou uma reparação de iPhone`,
        points: 20,
        isPublic: true
      },
      {
        type: 'HIGH_RATING_RECEIVED',
        description: `${users[1]?.name || users[0].name} recebeu avaliação de 5.0⭐`,
        points: 15,
        isPublic: true
      },
      {
        type: 'REVIEW_LEFT',
        description: `${users[0].name} avaliou uma loja com 4.8⭐`,
        points: 10,
        isPublic: true
      }
    ]

    for (let i = 0; i < activities.length && i < users.length; i++) {
      const activity = activities[i]
      const user = users[i] || users[0]

      await prisma.viralActivity.create({
        data: {
          userId: user.id,
          type: activity.type,
          description: activity.description,
          points: activity.points,
          isPublic: activity.isPublic,
          metadata: {
            testData: true,
            userRole: user.role,
            userName: user.name
          },
          location: 'Lisboa'
        }
      })

      // Atualizar pontos do usuário
      await prisma.user.update({
        where: { id: user.id },
        data: {
          viralPoints: {
            increment: activity.points
          }
        }
      })

      console.log(`✅ Atividade criada: ${activity.description} (+${activity.points} pontos)`)
    }

    // 3. Criar badges de exemplo
    const badges = [
      {
        name: 'Primeiro Passo',
        description: 'Completou o primeiro registo na plataforma',
        category: 'MILESTONE',
        criteria: { type: 'USER_REGISTERED', count: 1 }
      },
      {
        name: 'Lojista Estreante',
        description: 'Criou a primeira loja na plataforma',
        category: 'MILESTONE',
        criteria: { type: 'SHOP_CREATED', count: 1 }
      },
      {
        name: 'Reparador Expert',
        description: 'Completou 10 reparações com sucesso',
        category: 'EXPERTISE',
        criteria: { type: 'REPAIR_COMPLETED', count: 10 }
      },
      {
        name: 'Estrela de Ouro',
        description: 'Recebeu 5 avaliações de 5 estrelas',
        category: 'PERFORMANCE',
        criteria: { type: 'HIGH_RATING_RECEIVED', count: 5 }
      }
    ]

    for (const badgeData of badges) {
      const badge = await prisma.badge.create({
        data: badgeData
      })
      console.log(`🏆 Badge criado: ${badge.name}`)
    }

    // 4. Atribuir alguns badges aos usuários
    const createdBadges = await prisma.badge.findMany({ take: 2 })

    for (let i = 0; i < Math.min(2, users.length, createdBadges.length); i++) {
      // Verificar se o badge já foi atribuído
      const existingUserBadge = await prisma.userBadge.findUnique({
        where: {
          userId_badgeId: {
            userId: users[i].id,
            badgeId: createdBadges[i].id
          }
        }
      })

      if (!existingUserBadge) {
        await prisma.userBadge.create({
          data: {
            userId: users[i].id,
            badgeId: createdBadges[i].id
          }
        })
        console.log(`🎖️ Badge "${createdBadges[i].name}" atribuído a ${users[i].name}`)
      } else {
        console.log(`⚠️ Badge "${createdBadges[i].name}" já atribuído a ${users[i].name}`)
      }
    }

    // 5. Criar códigos de referral para usuários
    for (const user of users.slice(0, 3)) {
      if (!user.referralCode) {
        const referralCode = Math.random().toString(36).substring(2, 8).toUpperCase()
        
        await prisma.user.update({
          where: { id: user.id },
          data: { referralCode }
        })

        // Criar referral ativo
        const referralType = user.role === 'REPAIR_SHOP' ? 'LOJISTA_REFERRAL' :
                           user.role === 'COURIER' ? 'ESTAFETA_REFERRAL' : 'CLIENTE_REFERRAL'

        await prisma.referral.create({
          data: {
            referrerId: user.id,
            referredId: user.id, // Temporário para satisfazer a constraint
            type: referralType,
            status: 'PENDING',
            reward: user.role === 'REPAIR_SHOP' ? 5 : 10
          }
        })

        console.log(`🔗 Código de referral criado para ${user.name}: ${referralCode}`)
      }
    }

    // 6. Criar conteúdo partilhável de exemplo
    const shareableContent = [
      {
        userId: users[0].id,
        type: 'BEFORE_AFTER',
        title: 'Reparação de Ecrã iPhone 13',
        description: 'Transformação incrível de um ecrã partido',
        imageUrl: 'https://example.com/before-after.jpg',
        likeCount: 15,
        shareCount: 8,
        metadata: {
          beforeImage: 'https://example.com/before.jpg',
          afterImage: 'https://example.com/after.jpg',
          views: 120
        }
      },
      {
        userId: users[1]?.id || users[0].id,
        type: 'REPAIR_STORY',
        title: 'Como salvei um MacBook da água',
        description: 'História de uma reparação desafiante',
        imageUrl: 'https://example.com/macbook-repair.jpg',
        likeCount: 23,
        shareCount: 12,
        metadata: {
          views: 89,
          difficulty: 'high'
        }
      }
    ]

    for (const content of shareableContent) {
      await prisma.shareableContent.create({
        data: content
      })
      console.log(`📱 Conteúdo partilhável criado: ${content.title}`)
    }

    console.log('\n🎉 Dados de teste do sistema viral criados com sucesso!')
    console.log('Execute "node scripts/test-viral-system.js" para ver os resultados.')

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Executar seed
seedViralData()
