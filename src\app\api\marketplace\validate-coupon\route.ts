import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function POST(request: NextRequest) {
  try {
    const { code, cartItems, subtotal } = await request.json()

    if (!code) {
      return NextResponse.json(
        { message: "Código do cupão é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar cupão
    const coupon = await prisma.coupon.findUnique({
      where: { code: code.toUpperCase() },
      include: {
        seller: {
          select: {
            id: true,
            name: true}
        }
      }
    })

    if (!coupon) {
      return NextResponse.json(
        { message: "Cupão não encontrado" },
        { status: 404 }
      )
    }

    // Verificar se está ativo
    if (!coupon.isActive) {
      return NextResponse.json(
        { message: "Cupão não está ativo" },
        { status: 400 }
      )
    }

    // Verificar data de início
    if (coupon.startsAt && new Date() < new Date(coupon.startsAt)) {
      return NextResponse.json(
        { message: "Cupão ainda não está válido" },
        { status: 400 }
      )
    }

    // Verificar data de expiração
    if (coupon.expiresAt && new Date() > new Date(coupon.expiresAt)) {
      return NextResponse.json(
        { message: "Cupão expirado" },
        { status: 400 }
      )
    }

    // Verificar limite de uso
    if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
      return NextResponse.json(
        { message: "Cupão esgotado" },
        { status: 400 }
      )
    }

    // Verificar valor mínimo do pedido
    if (coupon.minOrderValue && subtotal < Number(coupon.minOrderValue)) {
      return NextResponse.json(
        { message: `Valor mínimo do pedido: €${Number(coupon.minOrderValue).toFixed(2)}` },
        { status: 400 }
      )
    }

    // Verificar valor máximo do pedido
    if (coupon.maxOrderValue && subtotal > Number(coupon.maxOrderValue)) {
      return NextResponse.json(
        { message: `Valor máximo do pedido: €${Number(coupon.maxOrderValue).toFixed(2)}` },
        { status: 400 }
      )
    }

    // Verificar se o cupão é aplicável aos produtos no carrinho
    const sellerIds = [...new Set(cartItems.map((item: any) => item.product.sellerId))]
    
    if (!sellerIds.includes(coupon.sellerId)) {
      return NextResponse.json(
        { message: "Cupão não aplicável aos produtos no carrinho" },
        { status: 400 }
      )
    }

    // Calcular desconto
    let discountValue = 0
    if (coupon.type === PERCENTAGE) {
      discountValue = (subtotal * Number(coupon.value)) / 100
      if (coupon.maxDiscountValue && discountValue > Number(coupon.maxDiscountValue)) {
        discountValue = Number(coupon.maxDiscountValue)
      
}
    } else {
      discountValue = Number(coupon.value)
    }

    // Garantir que o desconto não seja maior que o subtotal
    discountValue = Math.min(discountValue, subtotal)

    return NextResponse.json({
      coupon: {
        id: coupon.id,
        code: coupon.code,
        name: coupon.name,
        type: coupon.type,
        value: Number(coupon.value),
        maxDiscountValue: coupon.maxDiscountValue ? Number(coupon.maxDiscountValue) : null,
        seller: coupon.seller
      
},
      'discountValue'})

  } catch (error) {
    console.error("Erro ao validar cupão:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
