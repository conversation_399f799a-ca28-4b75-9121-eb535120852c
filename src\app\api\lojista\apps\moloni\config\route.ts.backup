import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a app Moloni está instalada
    const installedApp = await prisma.$queryRaw`
      SELECT * FROM installed_apps 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = 'moloni' 
      AND "isActive" = true
    `

    if (!installedApp || installedApp.length === 0) {
      return NextResponse.json(
        { message: 'App Moloni não está instalada' },
        { status: 404 }
      )
    }

    // Buscar configuração do Moloni
    const config = await prisma.$queryRaw`
      SELECT * FROM app_configs 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = 'moloni'
    `

    const defaultConfig = {
      email: '',
      password: '',
      companyId: '',
      documentSeries: 'A',
      autoIssue: false,
      isConnected: false
    }

    return NextResponse.json({
      config: config.length > 0 ? {
        ...defaultConfig,
        ...config[0].settings
      } : defaultConfig
    })

  } catch (error) {
    console.error('Erro ao buscar configuração Moloni:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const configData = await request.json()

    // Salvar configuração
    await prisma.$queryRaw`
      INSERT INTO app_configs ("id", "userId", "appId", "settings", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${session.user.id}, 'moloni', ${JSON.stringify(configData)}::jsonb, NOW(), NOW())
      ON CONFLICT ("userId", "appId") 
      DO UPDATE SET "settings" = ${JSON.stringify(configData)}::jsonb, "updatedAt" = NOW()
    `

    return NextResponse.json({
      message: 'Configuração salva com sucesso',
      config: configData
    })

  } catch (error) {
    console.error('Erro ao salvar configuração Moloni:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
