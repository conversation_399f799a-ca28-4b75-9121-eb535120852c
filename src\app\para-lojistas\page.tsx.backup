'use client'

import { useState } from 'react'
import Link from 'next/link'
import ModernLayout from '@/components/ModernLayout'
import {
  Store, Users, TrendingUp, Euro, Clock, Shield,
  CheckCircle, Star, ArrowRight, Smartphone, Wrench,
  BarChart3, Calendar, CreditCard, Globe, Zap,
  Target, Award, HeadphonesIcon, Package
} from 'lucide-react'

export default function ParaLojistasPage() {
  const [selectedPlan, setSelectedPlan] = useState('professional')

  const features = [
    {
      icon: <Store className="w-8 h-8" />,
      title: 'Loja Online Completa',
      description: 'Tenha sua própria loja online com subdomínio personalizado e todas as funcionalidades de e-commerce.'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Gestão de Clientes',
      description: 'Sistema completo para gerir clientes, histórico de reparações e comunicação automatizada.'
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: 'Relató<PERSON>s Avançados',
      description: '<PERSON><PERSON><PERSON><PERSON> detalhadas de vendas, reparações e performance do seu negócio.'
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: 'Agendamento Online',
      description: 'Permita que clientes agendem reparações online com calendário integrado.'
    },
    {
      icon: <CreditCard className="w-8 h-8" />,
      title: 'Pagamentos Integrados',
      description: 'Aceite pagamentos por cartão, Multibanco e MB WAY de forma segura.'
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: 'Marketplace Revify',
      description: 'Venda seus produtos no maior marketplace de reparações da Europa.'
    }
  ]

  const plans = [
    {
      id: 'starter',
      name: 'Starter',
      price: '29',
      period: '/mês',
      description: 'Perfeito para começar',
      features: [
        'Até 50 reparações/mês',
        'Loja online básica',
        'Gestão de clientes',
        'Suporte por email',
        'Relatórios básicos'
      ],
      popular: false
    },
    {
      id: 'professional',
      name: 'Professional',
      price: '59',
      period: '/mês',
      description: 'Mais popular',
      features: [
        'Reparações ilimitadas',
        'Loja online completa',
        'Marketplace Revify',
        'Relatórios avançados',
        'Suporte prioritário',
        'Agendamento online',
        'Pagamentos integrados'
      ],
      popular: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: '99',
      period: '/mês',
      description: 'Para grandes volumes',
      features: [
        'Tudo do Professional',
        'API personalizada',
        'Integração ERP',
        'Suporte dedicado',
        'Treinamento incluído',
        'Relatórios personalizados'
      ],
      popular: false
    }
  ]

  const benefits = [
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: 'Aumente suas vendas em até 300%',
      description: 'Lojas parceiras relatam aumento significativo nas vendas após se juntarem à Revify.'
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Acesso a milhares de clientes',
      description: 'Conecte-se com clientes em toda a Europa através da nossa plataforma.'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'Pagamentos garantidos',
      description: 'Receba seus pagamentos de forma segura e pontual, sem preocupações.'
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: 'Automação completa',
      description: 'Automatize processos e foque no que realmente importa: reparar dispositivos.'
    }
  ]

  return (
    <ModernLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Store className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Transforme seu negócio
            </h1>
            <p className="text-xl text-indigo-100 max-w-3xl mx-auto mb-8">
              Junte-se à maior plataforma de reparações da Europa e multiplique seus resultados com tecnologia de ponta.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/auth/signup?type=lojista"
                className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <span>Começar Agora</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <button className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-white/10 transition-colors">
                Ver Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Por que escolher a Revify?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Mais de 5.000 lojistas confiam na Revify para gerir e expandir seus negócios
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <div className="text-white">
                    {benefit.icon}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {benefit.title}
                </h3>
                <p className="text-gray-600">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Funcionalidades Completas
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Tudo que precisa para gerir e expandir seu negócio de reparações
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center mb-6">
                  <div className="text-indigo-600">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Planos Transparentes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Escolha o plano ideal para o seu negócio. Sem taxas ocultas, sem surpresas.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`relative rounded-2xl p-8 border-2 transition-all ${
                  plan.popular
                    ? 'border-indigo-500 bg-gradient-to-br from-indigo-50 to-purple-50 scale-105'
                    : 'border-gray-200 bg-white hover:border-indigo-300'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Mais Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {plan.description}
                  </p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-5xl font-bold text-gray-900">
                      €{plan.price}
                    </span>
                    <span className="text-gray-600 ml-2">
                      {plan.period}
                    </span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  href="/auth/signup?type=lojista"
                  className={`w-full py-3 px-6 rounded-xl font-semibold text-center transition-colors block ${
                    plan.popular
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                >
                  Começar Agora
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Pronto para transformar seu negócio?
          </h2>
          <p className="text-xl text-indigo-100 mb-8">
            Junte-se a milhares de lojistas que já multiplicaram seus resultados com a Revify.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup?type=lojista"
              className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <span>Começar Gratuitamente</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <HeadphonesIcon className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
