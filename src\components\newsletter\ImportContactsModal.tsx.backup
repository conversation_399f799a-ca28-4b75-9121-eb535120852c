'use client'

import { useState } from 'react'
import { Upload, Users, Plus, X, FileText, Download } from 'lucide-react'

interface ImportContactsModalProps {
  listId: string
  onClose: () => void
  onSuccess: () => void
}

export default function ImportContactsModal({ listId, onClose, onSuccess }: ImportContactsModalProps) {
  const [activeTab, setActiveTab] = useState<'csv' | 'manual' | 'customers'>('csv')
  const [isLoading, setIsLoading] = useState(false)
  const [csvData, setCsvData] = useState<any[]>([])
  const [manualContacts, setManualContacts] = useState([{ name: '', email: '', phone: '' }])
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])
  const [customers, setCustomers] = useState<any[]>([])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      const lines = text.split('\n').filter(line => line.trim())
      
      if (lines.length === 0) return

      // Parse CSV (assumindo formato: nome,email,telefone)
      const headers = lines[0].split(',').map(h => h.trim().toLowerCase())
      const data = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim())
        const contact: any = {}
        
        headers.forEach((header, index) => {
          if (header.includes('nome') || header.includes('name')) {
            contact.name = values[index] || ''
          } else if (header.includes('email') || header.includes('e-mail')) {
            contact.email = values[index] || ''
          } else if (header.includes('telefone') || header.includes('phone') || header.includes('tel')) {
            contact.phone = values[index] || ''
          }
        })
        
        return contact
      }).filter(contact => contact.email)

      setCsvData(data)
    }
    reader.readAsText(file)
  }

  const addManualContact = () => {
    setManualContacts([...manualContacts, { name: '', email: '', phone: '' }])
  }

  const updateManualContact = (index: number, field: string, value: string) => {
    const updated = [...manualContacts]
    updated[index] = { ...updated[index], [field]: value }
    setManualContacts(updated)
  }

  const removeManualContact = (index: number) => {
    setManualContacts(manualContacts.filter((_, i) => i !== index))
  }

  const loadCustomers = async () => {
    try {
      const response = await fetch(`/api/lojista/apps/newsletter-pro/customers?listId=${listId}`)
      if (response.ok) {
        const data = await response.json()
        setCustomers(data.customers)
      }
    } catch (error) {
      console.error('Erro ao carregar clientes:', error)
    }
  }

  const handleImport = async () => {
    setIsLoading(true)
    
    try {
      let contacts = []
      let source = activeTab

      if (activeTab === 'csv') {
        contacts = csvData
      } else if (activeTab === 'manual') {
        contacts = manualContacts.filter(c => c.email.trim())
      } else if (activeTab === 'customers') {
        contacts = customers.filter(c => selectedCustomers.includes(c.id))
      }

      const response = await fetch(`/api/lojista/apps/newsletter-pro/lists/${listId}/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contacts,
          source
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`Importação concluída! ${result.summary.imported} contactos importados.`)
        onSuccess()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro na importação')
      }
    } catch (error) {
      console.error('Erro na importação:', error)
      alert('Erro na importação')
    } finally {
      setIsLoading(false)
    }
  }

  const downloadTemplate = () => {
    const csvContent = "nome,email,telefone\nJoão Silva,<EMAIL>,912345678\nMaria Santos,<EMAIL>,913456789"
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'template_contactos.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Importar Contactos</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('csv')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'csv'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FileText className="w-4 h-4 inline mr-2" />
            Upload CSV
          </button>
          <button
            onClick={() => setActiveTab('manual')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'manual'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Plus className="w-4 h-4 inline mr-2" />
            Manual
          </button>
          <button
            onClick={() => {
              setActiveTab('customers')
              loadCustomers()
            }}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'customers'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Users className="w-4 h-4 inline mr-2" />
            Clientes
          </button>
        </div>

        {/* Tab Content */}
        <div className="mb-6">
          {activeTab === 'csv' && (
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="space-y-2">
                  <label className="cursor-pointer">
                    <span className="text-sm font-medium text-gray-900">
                      Clique para fazer upload do ficheiro CSV
                    </span>
                    <input
                      type="file"
                      accept=".csv"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>
                  <p className="text-xs text-gray-500">
                    Formato: nome,email,telefone (máximo 10MB)
                  </p>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <button
                  onClick={downloadTemplate}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <Download className="w-4 h-4 mr-1" />
                  Descarregar template
                </button>
                {csvData.length > 0 && (
                  <span className="text-sm text-green-600">
                    {csvData.length} contactos carregados
                  </span>
                )}
              </div>

              {csvData.length > 0 && (
                <div className="max-h-40 overflow-y-auto border rounded-lg">
                  <table className="min-w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left">Nome</th>
                        <th className="px-3 py-2 text-left">Email</th>
                        <th className="px-3 py-2 text-left">Telefone</th>
                      </tr>
                    </thead>
                    <tbody>
                      {csvData.slice(0, 5).map((contact, index) => (
                        <tr key={index} className="border-t">
                          <td className="px-3 py-2">{contact.name}</td>
                          <td className="px-3 py-2">{contact.email}</td>
                          <td className="px-3 py-2">{contact.phone}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {csvData.length > 5 && (
                    <div className="px-3 py-2 text-xs text-gray-500 bg-gray-50">
                      E mais {csvData.length - 5} contactos...
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'manual' && (
            <div className="space-y-4">
              <div className="space-y-3">
                {manualContacts.map((contact, index) => (
                  <div key={index} className="flex space-x-3 items-center">
                    <input
                      type="text"
                      placeholder="Nome"
                      value={contact.name}
                      onChange={(e) => updateManualContact(index, 'name', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    />
                    <input
                      type="email"
                      placeholder="Email *"
                      value={contact.email}
                      onChange={(e) => updateManualContact(index, 'email', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      required
                    />
                    <input
                      type="tel"
                      placeholder="Telefone"
                      value={contact.phone}
                      onChange={(e) => updateManualContact(index, 'phone', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    />
                    {manualContacts.length > 1 && (
                      <button
                        onClick={() => removeManualContact(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
              
              <button
                onClick={addManualContact}
                className="flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <Plus className="w-4 h-4 mr-1" />
                Adicionar contacto
              </button>
            </div>
          )}

          {activeTab === 'customers' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                Selecione os clientes que deseja adicionar à lista:
              </div>
              
              <div className="max-h-64 overflow-y-auto border rounded-lg">
                {customers.map((customer) => (
                  <label key={customer.id} className="flex items-center p-3 hover:bg-gray-50 border-b last:border-b-0">
                    <input
                      type="checkbox"
                      checked={selectedCustomers.includes(customer.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedCustomers([...selectedCustomers, customer.id])
                        } else {
                          setSelectedCustomers(selectedCustomers.filter(id => id !== customer.id))
                        }
                      }}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.email}</div>
                      <div className="text-xs text-gray-400">
                        {customer.stats.orders} encomendas, {customer.stats.repairs} reparações
                      </div>
                    </div>
                  </label>
                ))}
              </div>
              
              {selectedCustomers.length > 0 && (
                <div className="text-sm text-green-600">
                  {selectedCustomers.length} clientes selecionados
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button
            onClick={handleImport}
            disabled={isLoading || (
              (activeTab === 'csv' && csvData.length === 0) ||
              (activeTab === 'manual' && manualContacts.filter(c => c.email.trim()).length === 0) ||
              (activeTab === 'customers' && selectedCustomers.length === 0)
            )}
            className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Importando...' : 'Importar Contactos'}
          </button>
        </div>
      </div>
    </div>
  )
}
