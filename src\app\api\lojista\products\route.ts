import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireActiveSubscription } from '@/lib/subscription-access'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const products = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: session.user.id
      },
      include: {
        category: {
          select: {
            name: true}
        },
        brand: {
          select: {
            name: true}
        },
        deviceModel: {
          select: {
            name: true}
        }
      },
      orderBy: {
        createdAt: desc}
    })

    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      condition: product.condition,
      stock: product.stock,
      isActive: product.isActive,
      images: product.images,
      category: product.category,
      brand: product.brand,
      deviceModel: product.deviceModel,
      createdAt: product.createdAt.toISOString()
    }))

    return NextResponse.json({
      products: formattedProducts})

  } catch (error) {
    console.error("Erro ao buscar produtos:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se tem subscrição ativa
    const subscriptionCheck = await requireActiveSubscription(session.user.id)
    if (subscriptionCheck.error) {
      return NextResponse.json(
        { message: subscriptionCheck.message },
        { status: subscriptionCheck.status }
      )
    }

    const { name,
      description,
      price,
      originalPrice,
      condition,
      stock,
      categoryId,
      brandId,
      deviceModelId,
      images } = await request.json()

    if (!name || !description || !price || !condition || !categoryId || !brandId) {
      return NextResponse.json(
        { message: "Dados obrigatórios em falta" },
        { status: 400 }
      )
    }

    const product = await prisma.marketplaceProduct.create({
      data: {
        sellerId: session.user.id,
        name,
        description,
        price: parseFloat(price),
        originalPrice: originalPrice ? parseFloat(originalPrice) : null,
        condition,
        stock: parseInt(stock) || 1,
        categoryId,
        brandId,
        deviceModelId: deviceModelId || null,
        images: images || []
      },
      include: {
        category: {
          select: {
            name: true}
        },
        brand: {
          select: {
            name: true}
        },
        deviceModel: {
          select: {
            name: true}
        }
      }
    })

    return NextResponse.json({
      message: Produto criado com sucesso,
      product: {
        id: product.id,
        name: product.name,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        condition: product.condition,
        stock: product.stock,
        isActive: product.isActive,
        images: product.images,
        category: product.category,
        brand: product.brand,
        deviceModel: product.deviceModel,
        createdAt: product.createdAt.toISOString()
      
}
    })

  } catch (error) {
    console.error('Erro ao criar produto:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
