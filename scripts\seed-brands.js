const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function seedBrands() {
  try {
    console.log('🌱 Seeding brands...')

    // Marcas básicas para marketplace de eletrônicos
    const brands = [
      {
        name: 'Apple',
        logo: null
      },
      {
        name: 'Samsung',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        logo: null
      },
      {
        name: 'OnePlus',
        logo: null
      },
      {
        name: 'Google',
        logo: null
      },
      {
        name: 'Sony',
        logo: null
      },
      {
        name: 'LG',
        logo: null
      },
      {
        name: 'Motorola',
        logo: null
      },
      {
        name: 'Nokia',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: 'Vivo',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: 'Honor',
        logo: null
      },
      {
        name: 'Nothing',
        logo: null
      },
      {
        name: 'Fairphone',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: '<PERSON>',
        logo: null
      },
      {
        name: '<PERSON>',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: '<PERSON><PERSON>',
        logo: null
      },
      {
        name: 'MSI',
        logo: null
      },
      {
        name: 'Microsoft',
        logo: null
      },
      {
        name: 'Nintendo',
        logo: null
      },
      {
        name: 'PlayStation',
        logo: null
      },
      {
        name: 'Xbox',
        logo: null
      },
      {
        name: 'Genérica',
        logo: null
      }
    ]

    for (const brand of brands) {
      const existing = await prisma.brand.findFirst({
        where: { name: brand.name }
      })

      if (!existing) {
        await prisma.brand.create({
          data: brand
        })
        console.log(`✅ Created brand: ${brand.name}`)
      } else {
        console.log(`⏭️ Brand already exists: ${brand.name}`)
      }
    }

    console.log('🎉 Brands seeded successfully!')

  } catch (error) {
    console.error('❌ Error seeding brands:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedBrands()
