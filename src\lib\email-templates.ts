interface RepairNotificationData {
  customerName: string
  trackingCode: string
  deviceBrand: string
  deviceModel: string
  problemType: string
  status: string
  shopName: string
  shopPhone?: string
  shopEmail?: string
  estimatedPrice?: number
  estimatedCompletionDate?: string
  notes?: string}

export const getRepairCreatedEmailTemplate = (data: RepairNotificationData) => {
  return {
    subject: `Reparação Recebida - ${data.trackingCode}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reparação Recebida</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { background: #f9fafb; padding: 20px; }
          .info-box { background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 10px 0; }
          .status { background: #dbeafe; color: #1e40af; padding: 8px 12px; border-radius: 4px; display: inline-block; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
          .tracking-code { font-size: 24px; font-weight: bold; color: #2563eb; text-align: center; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Reparação Recebida</h1>
            <p>Sua reparação foi registrada com sucesso</p>
          </div>
          
          <div class="content">
            <p>Olá <strong>${data.customerName}</strong>,</p>
            
            <p>Recebemos sua solicitação de reparação na <strong>${data.shopName}</strong>. Aqui estão os detalhes:</p>
            
            <div class="tracking-code">
              Código: ${data.trackingCode}
            </div>
            
            <div class="info-box">
              <h3>Detalhes do Dispositivo</h3>
              <p><strong>Dispositivo:</strong> ${data.deviceBrand} ${data.deviceModel}</p>
              <p><strong>Problema:</strong> ${data.problemType}</p>
              ${data.estimatedPrice ? `<p><strong>Orçamento Estimado:</strong> €${data.estimatedPrice.toFixed(2)}</p>` : ''}
              ${data.estimatedCompletionDate ? `<p><strong>Previsão de Conclusão:</strong> ${new Date(data.estimatedCompletionDate).toLocaleDateString('pt-PT')}</p>` : ''}
            </div>
            
            <div class="info-box">
              <h3>Status Atual</h3>
              <span class="status">Pendente</span>
              <p>Sua reparação foi recebida e está aguardando análise.</p>
            </div>
            
            <div class="info-box">
              <h3>Acompanhar Reparação</h3>
              <p>Use o código <strong>${data.trackingCode}</strong> para acompanhar o progresso em:</p>
              <p><a href="https:// revify.pt/track/${data.trackingCode}" style="color: #2563eb;">https://revify.pt/track/${data.trackingCode}</a></p>
            </div>
            
            <div class="info-box">
              <h3>Contacto</h3>
              <p><strong>${data.shopName}</strong></p>
              ${data.shopPhone ? `<p>Telefone: ${data.shopPhone}</p>` : 
}
              ${data.shopEmail ? `<p>Email: ${data.shopEmail}</p>` : ''}
            </div>
          </div>
          
          <div class="footer">
            <p>Esta é uma mensagem automática. Por favor, não responda a este email.</p>
            <p>© 2024 Revify - Plataforma de Reparações</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Reparação Recebida - ${data.trackingCode}
      
      Olá ${data.customerName},
      
      Recebemos sua solicitação de reparação na ${data.shopName}.
      
      Código de Rastreamento: ${data.trackingCode}
      Dispositivo: ${data.deviceBrand} ${data.deviceModel}
      Problema: ${data.problemType}
      ${data.estimatedPrice ? `Orçamento Estimado: €${data.estimatedPrice.toFixed(2)}` : ''}
      
      Acompanhe em: https:// revify.pt/track/${data.trackingCode}
      
      Contacto: ${data.shopName}
      ${data.shopPhone ? `Telefone: ${data.shopPhone}` : 
}
      ${data.shopEmail ? `Email: ${data.shopEmail}` : ''}
    `
  }
}

export const getRepairStatusUpdateEmailTemplate = (data: RepairNotificationData) => {
  const statusLabels = {
    PENDING: Pendente,
    IN_PROGRESS: 'Em Progresso',
    WAITING_PARTS: 'Aguardando Peças',
    COMPLETED: 'Concluída',
    DELIVERED: Entregue,
    CANCELLED: Cancelada}

  const statusColors = {
    PENDING: '#f59e0b',
    IN_PROGRESS: '#3b82f6',
    WAITING_PARTS: '#f97316',
    COMPLETED: '#10b981',
    DELIVERED: '#10b981',
    CANCELLED: '#ef4444'
  }

  const statusLabel = statusLabels[data.status as keyof typeof statusLabels] || data.status
  const statusColor = statusColors[data.status as keyof typeof statusColors] || '#6b7280'

  return {
    subject: `Atualização da Reparação - ${data.trackingCode}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Atualização da Reparação</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { background: #f9fafb; padding: 20px; }
          .info-box { background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 10px 0; }
          .status { background: ${statusColor}; color: white; padding: 8px 12px; border-radius: 4px; display: inline-block; }
          .footer { text-align: center; padding: 20px; color: #6b7280; font-size: 14px; }
          .tracking-code { font-size: 20px; font-weight: bold; color: #2563eb; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Atualização da Reparação</h1>
            <p>O status da sua reparação foi atualizado</p>
          </div>
          
          <div class="content">
            <p>Olá <strong>${data.customerName}</strong>,</p>
            
            <p>Temos uma atualização sobre sua reparação na <strong>${data.shopName}</strong>:</p>
            
            <div class="info-box">
              <p><strong>Código:</strong> <span class="tracking-code">${data.trackingCode}</span></p>
              <p><strong>Dispositivo:</strong> ${data.deviceBrand} ${data.deviceModel}</p>
            </div>
            
            <div class="info-box">
              <h3>Novo Status</h3>
              <span class="status">${statusLabel}</span>
              ${data.notes ? `<p style="margin-top: 10px;"><strong>Observações:</strong> ${data.notes}</p>` : ''}
            </div>
            
            ${data.status === 'COMPLETED' ? `
            <div class="info-box" style="background: #ecfdf5; border-color: #10b981;">
              <h3 style="color: #059669;">🎉 Reparação Concluída!</h3>
              <p>Sua reparação está pronta para retirada. Entre em contacto connosco para agendar a retirada.</p>
            </div>
            ` : ''}
            
            <div class="info-box">
              <h3>Acompanhar Reparação</h3>
              <p><a href="https:// revify.pt/track/${data.trackingCode}" style="color: #2563eb;">Clique aqui para ver todos os detalhes</a></p>
            </div>
            
            <div class="info-box">
              <h3>Contacto</h3>
              <p><strong>${data.shopName}</strong></p>
              ${data.shopPhone ? `<p>Telefone: ${data.shopPhone}</p>` : 
}
              ${data.shopEmail ? `<p>Email: ${data.shopEmail}</p>` : ''}
            </div>
          </div>
          
          <div class="footer">
            <p>Esta é uma mensagem automática. Por favor, não responda a este email.</p>
            <p>© 2024 Revify - Plataforma de Reparações</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Atualização da Reparação - ${data.trackingCode}
      
      Olá ${data.customerName},
      
      Temos uma atualização sobre sua reparação na ${data.shopName}:
      
      Código: ${data.trackingCode}
      Dispositivo: ${data.deviceBrand} ${data.deviceModel}
      Novo Status: ${statusLabel}
      ${data.notes ? `Observações: ${data.notes}` : ''}
      
      Acompanhe em: https:// revify.pt/track/${data.trackingCode}
      
      Contacto: ${data.shopName}
      ${data.shopPhone ? `Telefone: ${data.shopPhone}` : 
}
      ${data.shopEmail ? `Email: ${data.shopEmail}` : ''}
    `
  }
}
