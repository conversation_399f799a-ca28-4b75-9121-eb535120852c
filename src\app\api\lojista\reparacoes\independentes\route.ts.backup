import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso a reparações independentes
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!user?.subscription?.plan?.individualRepairs) {
      return NextResponse.json({
        hasAccess: false,
        repairs: [],
        message: 'Funcionalidade não disponível no seu plano'
      })
    }

    // Buscar reparações independentes do lojista
    // Reparações independentes são aquelas onde customerId = repairShopId
    const repairs = await prisma.repair.findMany({
      where: {
        repairShopId: session.user.id,
        customerId: session.user.id // Indica que é uma reparação independente
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        deviceModel: {
          include: {
            brand: true,
            category: true
          }
        },
        problemType: true
      }
    })

    // Formatar dados para o frontend
    const formattedRepairs = repairs.map(repair => ({
      id: repair.id,
      trackingCode: repair.trackingCode,
      customerName: repair.customerName,
      customerPhone: repair.customerPhone,
      deviceBrand: repair.deviceModel?.brand?.name || 'N/A',
      deviceModel: repair.deviceModel?.name || 'N/A',
      problemType: repair.problemType?.name || 'N/A',
      status: repair.status,
      estimatedPrice: Number(repair.estimatedPrice || 0),
      createdAt: repair.createdAt.toISOString(),
      estimatedCompletionDate: repair.estimatedCompletionDate?.toISOString()
    }))

    return NextResponse.json({
      hasAccess: true,
      repairs: formattedRepairs,
      total: formattedRepairs.length
    })

  } catch (error) {
    console.error('Erro ao buscar reparações independentes:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
