import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const category = await prisma.category.findUnique({
      where: { id: resolvedParams.id },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { message: 'Categoria não encontrada' },
        { status: 404 }
      )
    }

    return NextResponse.json(category)

  } catch (error) {
    console.error('Erro ao buscar categoria:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, description } = await request.json()

    if (!name) {
      return NextResponse.json(
        { message: 'Nome da categoria é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se a categoria existe
    const existingCategory = await prisma.category.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!existingCategory) {
      return NextResponse.json(
        { message: 'Categoria não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se outro categoria já tem este nome
    const duplicateCategory = await prisma.category.findFirst({
      where: {
        name,
        id: { not: resolvedParams.id }
      }
    })

    if (duplicateCategory) {
      return NextResponse.json(
        { message: 'Já existe uma categoria com este nome' },
        { status: 400 }
      )
    }

    const category = await prisma.category.update({
      where: { id: resolvedParams.id },
      data: {
        name,
        description: description || null,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    return NextResponse.json(category)

  } catch (error) {
    console.error('Erro ao atualizar categoria:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a categoria existe
    const category = await prisma.category.findUnique({
      where: { id: resolvedParams.id },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { message: 'Categoria não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se há modelos usando esta categoria
    if (category._count.deviceModels > 0) {
      return NextResponse.json(
        { message: 'Não é possível eliminar uma categoria que tem modelos associados' },
        { status: 400 }
      )
    }

    await prisma.category.delete({
      where: { id: resolvedParams.id }
    })

    return NextResponse.json({ message: 'Categoria eliminada com sucesso' })

  } catch (error) {
    console.error('Erro ao eliminar categoria:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
