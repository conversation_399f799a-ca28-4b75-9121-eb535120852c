import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar encomendas do marketplace onde o lojista é o vendedor
    const marketplaceOrders = await prisma.$queryRaw`
      SELECT DISTINCT
        o.*,
        u.name as customer_name,
        u.email as customer_email
      FROM orders o
      JOIN users u ON o."customerId" = u.id
      JOIN marketplace_order_items moi ON o.id = moi."orderId"
      JOIN marketplace_products mp ON moi."productId" = mp.id
      WHERE mp."sellerId" = ${session.user.id}
      ORDER BY o."createdAt" DESC
    `

    // Buscar itens das encomendas do marketplace
    const orderItems = await prisma.$queryRaw`
      SELECT
        moi.*,
        mp.name as product_name,
        mp.price as product_price,
        mp.images as product_images,
        o.id as order_id
      FROM marketplace_order_items moi
      JOIN marketplace_products mp ON moi."productId" = mp.id
      JOIN orders o ON moi."orderId" = o.id
      JOIN users u ON o."customerId" = u.id
      WHERE mp."sellerId" = ${session.user.id}
    `

    // Agrupar itens por encomenda
    const itemsByOrder = orderItems.reduce((acc: any, item: any) => {
      if (!acc[item.order_id]) {
        acc[item.order_id] = []
      }
      acc[item.order_id].push(item)
      return acc
}, {})

    // Combinar e formatar as encomendas
    const formattedMarketplaceOrders = marketplaceOrders.map((order: any) => ({
      id: order.id,
      orderNumber: order.orderNumber || `MP${order.id.slice(-8)}`,
      type: "marketplace",
      status: order.status,
      total: Number(order.total),
      customer: {
        name: order.customer_name,
        email: order.customer_email
      },
      items: itemsByOrder[order.id]?.map((item: any) => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.price || item.product_price || 0),
        product: {
          name: item.product_name,
          images: item.product_images || []
        }
      })) || [],
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      shippingAddress: {
        name: order.shippingName,
        street: order.shippingStreet,
        city: order.shippingCity,
        postalCode: order.shippingPostalCode,
        country: order.shippingCountry
      }
    }))

    return NextResponse.json({
      orders: formattedMarketplaceOrders,
      summary: {
        total: formattedMarketplaceOrders.length,
        marketplace: formattedMarketplaceOrders.length,
        pending: formattedMarketplaceOrders.filter(o => [PENDING, 'PROCESSING'].includes(o.status)).length,
        completed: formattedMarketplaceOrders.filter(o => ['DELIVERED', 'COMPLETED'].includes(o.status)).length
      
}
    })

  } catch (error) {
    console.error("Erro ao buscar encomendas:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
