import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { requireActiveSubscription } from '@/lib/subscription-access'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se tem subscrição ativa
    const subscriptionCheck = await requireActiveSubscription(session.user.id)
    if (subscriptionCheck.error) {
      return NextResponse.json(
        { message: subscriptionCheck.message },
        { status: subscriptionCheck.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    let whereClause: any = {
      repairShopId: session.user.id
    }

    console.log('Buscando reparações para lojista:', session.user.id)
    console.log('Where clause:', whereClause)

    // Filtrar por status se especificado
    if (status && status !== 'ALL') {
      whereClause.status = status
    }

    // Buscar reparações do lojista
    const [repairs, total] = await Promise.all([
      prisma.repair.findMany({
        where: whereClause,
        select: {
          id: true,
          status: true,
          description: true,
          estimatedPrice: true,
          finalPrice: true,
          createdAt: true,
          scheduledDate: true,
          customer: {
            select: {
              name: true,
              email: true
            }
          },
          deviceModel: {
            include: {
              brand: {
                select: {
                  name: true
                }
              },
              category: {
                select: {
                  name: true
                }
              }
            }
          },
          problemType: {
            select: {
              name: true,
              icon: true
            }
          },
          payments: {
            select: {
              amount: true,
              status: true,
              createdAt: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.repair.count({ where: whereClause })
    ])

    console.log('Reparações encontradas:', repairs.length)

    return NextResponse.json({
      repairs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erro ao buscar reparações do lojista:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
