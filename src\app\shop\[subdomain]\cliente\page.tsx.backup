'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { User, Package, ShoppingCart, MapPin, Phone, Mail, Calendar } from 'lucide-react'

interface CustomerData {
  name: string
  email: string
  phone: string
  address: string
  city: string
  postalCode: string
}

interface Order {
  id: string
  orderNumber: string
  total: number
  status: string
  createdAt: string
  items: Array<{
    productName: string
    quantity: number
    price: number
  }>
}

interface ShopData {
  id: string
  name: string
}

export default function AreaClientePage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('orders')
  const [customerData, setCustomerData] = useState<CustomerData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: ''
  })
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loginEmail, setLoginEmail] = useState('')

  const subdomain = params.subdomain as string

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
      checkLoginStatus()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}`)
      if (response.ok) {
        const data = await response.json()
        setShop(data.shop)
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkLoginStatus = () => {
    const savedEmail = localStorage.getItem(`customer_${subdomain}`)
    if (savedEmail) {
      setLoginEmail(savedEmail)
      setIsLoggedIn(true)
      fetchCustomerData(savedEmail)
      fetchOrders(savedEmail)
    } else {
      setIsLoading(false)
    }
  }

  const fetchCustomerData = async (email: string) => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/cliente?email=${email}`)
      if (response.ok) {
        const data = await response.json()
        setCustomerData(data.customer)
      }
    } catch (error) {
      console.error('Erro ao buscar dados do cliente:', error)
    }
  }

  const fetchOrders = async (email: string) => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/cliente/pedidos?email=${email}`)
      if (response.ok) {
        const data = await response.json()
        setOrders(data.orders || [])
      }
    } catch (error) {
      console.error('Erro ao buscar pedidos:', error)
    }
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!loginEmail) {
      alert('Por favor, insira seu email')
      return
    }

    // Simular login (em produção, seria uma verificação real)
    localStorage.setItem(`customer_${subdomain}`, loginEmail)
    setIsLoggedIn(true)
    fetchCustomerData(loginEmail)
    fetchOrders(loginEmail)
  }

  const handleLogout = () => {
    localStorage.removeItem(`customer_${subdomain}`)
    setIsLoggedIn(false)
    setLoginEmail('')
    setCustomerData({
      name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      postalCode: ''
    })
    setOrders([])
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: 'EUR'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getStatusLabel = (status: string) => {
    const labels = {
      PENDING: 'Pendente',
      CONFIRMED: 'Confirmado',
      PROCESSING: 'A Processar',
      SHIPPED: 'Enviado',
      DELIVERED: 'Entregue',
      CANCELLED: 'Cancelado'
    }
    return labels[status as keyof typeof labels] || status
  }

  const getStatusColor = (status: string) => {
    const colors = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      CONFIRMED: 'bg-blue-100 text-blue-800',
      PROCESSING: 'bg-purple-100 text-purple-800',
      SHIPPED: 'bg-orange-100 text-orange-800',
      DELIVERED: 'bg-green-100 text-green-800',
      CANCELLED: 'bg-red-100 text-red-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link href={`/shop/${subdomain}`} className="text-xl font-bold text-black">
                  {shop?.name}
                </Link>
                <span className="ml-3 text-xs text-gray-500 bg-blue-100 px-2 py-1 rounded">
                  Powered by Revify
                </span>
              </div>
              <Link
                href={`/shop/${subdomain}`}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Voltar à Loja
              </Link>
            </div>
          </div>
        </header>

        <div className="max-w-md mx-auto px-4 py-12">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-6">
              <User className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Área do Cliente</h1>
              <p className="text-gray-600">Acesse sua conta para ver pedidos e dados</p>
            </div>

            <form onSubmit={handleLogin}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={loginEmail}
                  onChange={(e) => setLoginEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <button
                type="submit"
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Acessar Conta
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Primeira vez aqui?{' '}
                <Link
                  href={`/shop/${subdomain}/produtos`}
                  className="text-blue-600 hover:text-blue-800"
                >
                  Comece a comprar
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/shop/${subdomain}`} className="text-xl font-bold text-black">
                {shop?.name}
              </Link>
              <span className="ml-3 text-xs text-gray-500 bg-blue-100 px-2 py-1 rounded">
                Powered by Revify
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Olá, {customerData.name || loginEmail}</span>
              <button
                onClick={handleLogout}
                className="text-sm text-gray-600 hover:text-gray-900"
              >
                Sair
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Minha Conta</h1>
          <p className="text-gray-600">Gerencie seus pedidos e informações pessoais</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('orders')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'orders'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Package className="w-4 h-4 inline mr-2" />
              Meus Pedidos
            </button>
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'profile'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <User className="w-4 h-4 inline mr-2" />
              Meus Dados
            </button>
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'orders' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {orders.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum pedido encontrado</h3>
                <p className="text-gray-600 mb-6">Você ainda não fez nenhum pedido nesta loja</p>
                <Link
                  href={`/shop/${subdomain}/produtos`}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Começar a Comprar
                </Link>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pedido
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orders.map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {order.orderNumber}
                            </div>
                            <div className="text-sm text-gray-500">
                              {order.items.length} {order.items.length === 1 ? 'item' : 'itens'}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(order.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {getStatusLabel(order.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatPrice(order.total)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Informações Pessoais</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Nome</p>
                  <p className="text-sm text-gray-600">{customerData.name || 'Não informado'}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Email</p>
                  <p className="text-sm text-gray-600">{customerData.email || loginEmail}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Telefone</p>
                  <p className="text-sm text-gray-600">{customerData.phone || 'Não informado'}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Morada</p>
                  <p className="text-sm text-gray-600">
                    {customerData.address 
                      ? `${customerData.address}, ${customerData.city} ${customerData.postalCode}`
                      : 'Não informado'
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-600">
                Para atualizar suas informações, entre em contacto connosco através do email da loja.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
