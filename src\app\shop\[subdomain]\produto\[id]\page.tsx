'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, ShoppingCart, Star, Package, Shield, Truck } from 'lucide-react'
import ShopLayout from '@/components/shop/ShopLayout'
import { useShopCart } from '@/hooks/useShopCart'
import { useToast, showSuccessToast } from '@/components/ui/Toast'
interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  category?: {
    name: string}
  brand?: {
    name: string}
  stock: number
  condition: string}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  logoUrl?: string}

interface ProdutoDetalhesContentProps {
  shop: ShopData
  subdomain: string}

function ProdutoDetalhesContent({ shop, subdomain }: ProdutoDetalhesContentProps) {
  const params = useParams()
  const { addToast } = useToast()
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)

  const productId = params.id as string

  // Hook para gerenciar carrinho
  const { addToCart, getCartItemCount } = useShopCart(subdomain)

  useEffect(() => {
    if (subdomain && productId) {
      fetchProduct()
    
}
  }, [subdomain, productId])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/produto/${productId}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data.product)
      }
    } catch (error) {
      console.error('Erro ao carregar produto:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddToCart = () => {
    addToCart(productId, 'quantity')
    showSuccessToast(addToast, 'Produto adicionado', `${quantity} ${quantity === 1 ? 'item adicionado' : 'itens adicionados'} ao carrinho!`)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: currency,
      currency: EUR}).format(price)
  }

  const getConditionLabel = (condition: string) => {
    const conditions = {
      NEW: Novo,
      USED: Usado,
      REFURBISHED: Recondicionado}
    return conditions[condition as keyof typeof conditions] || 'condition'}

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Produto não encontrado</h1>
          <Link
            href={`/shop/${subdomain}/produtos`}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />Voltar aos Produtos</Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/shop/${subdomain}`} className="text-xl font-bold text-black">
                {shop.name}
              </Link>
              <span className="ml-3 text-xs text-gray-500 bg-blue-100 px-2 py-1 rounded">
                Powered by Revify
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href={`/shop/${subdomain}/carrinho`}
                className="relative p-2 text-gray-600 hover:text-gray-900"
              >
                <ShoppingCart className="w-6 h-6" />
                {getCartItemCount() > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {getCartItemCount()}
                  </span>
                )}
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href={`/shop/${subdomain}`} className="text-gray-500 hover:text-gray-700">Início</Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <Link href={`/shop/${subdomain}/produtos`} className="text-gray-500 hover:text-gray-700">Produtos</Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">{product.name}</span>
              </li>
            </ol>
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Imagens */}
          <div>
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4">
              <img
                src={product.images[selectedImage] || '/images/placeholder-product.jpg'}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            
            {product.images.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-blue-600' : 'border-transparent'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Detalhes */}
          <div>
            <div className="mb-4">
              <span className="text-sm text-gray-500 uppercase tracking-wide">
                {product.brand?.name} • {product.category?.name}
              </span>
              <h1 className="text-2xl font-bold text-gray-900 mt-1">{product.name}</h1>
            </div>

            <div className="flex items-center mb-4">
              <div className="flex items-center">
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <Star className="w-5 h-5 text-gray-300" />
                <span className="text-sm text-gray-600 ml-2">4.5 (23 'avaliações')</span>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-3xl font-bold text-gray-900">
                  {formatPrice(product.price)}
                </span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-xl text-gray-500 line-through">
                    {formatPrice(product.originalPrice)}
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-4 text-sm">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  product.condition === 'NEW' ? 'bg-green-100 text-green-800' :
                  product.condition === 'USED' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {getConditionLabel(product.condition)}
                </span>
                <span className="text-gray-600">
                  {product.stock} em stock
                </span>
              </div>
            </div>

            {product.description && (
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-2">Descrição</h3>
                <p className="text-gray-600">{product.description}</p>
              </div>
            )}

            {/* Adicionar ao Carrinho */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center space-x-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quantidade</label>
                  <select
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {[...Array(Math.min(product.stock, 10))].map((_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <button
                onClick={handleAddToCart}
                disabled={product.stock === 0}
                className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                {product.stock === 0 ? 'Fora de Stock' : 'Adicionar ao Carrinho'}
              </button>
            </div>

            {/* Informações Adicionais */}
            <div className="border-t border-gray-200 pt-6 mt-6">
              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <Shield className="w-4 h-4 mr-2" />Garantia de 12 meses</div>
                <div className="flex items-center text-sm text-gray-600">
                  <Truck className="w-4 h-4 mr-2" />Envio grátis acima de €50</div>
                <div className="flex items-center text-sm text-gray-600">
                  <Package className="w-4 h-4 mr-2" />Devolução em 14 dias</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ProdutoDetalhesPage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const subdomain = params.subdomain as string

  // Hook para gerenciar carrinho
  const { getCartItemCount } = useShopCart(subdomain)

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShop(data)
      }
    } catch (error) {
      console.error(Erro ao carregar dados da loja:, 'error')
    
} finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800"
          >Voltar ao início</Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      logoUrl={shop.logoUrl}
      cartItemsCount={getCartItemCount()}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <ProdutoDetalhesContent shop={shop} subdomain={subdomain} />
    </ShopLayout>
  )
}
