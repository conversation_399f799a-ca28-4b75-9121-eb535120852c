'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Search, Filter, Eye, Edit, Clock, Euro, MapPin, Phone } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface Repair {
  id: string
  status: string
  customerName: string
  customerPhone: string
  description: string
  estimatedPrice: number
  finalPrice?: number
  createdAt: string
  scheduledDate?: string
  completedDate?: string
  deviceModel?: {
    name: string
    brand: { name: string }
  }
  problemType?: {
    name: string
    icon: string
  }
  repairShop?: {
    profile?: {
      companyName: string
      phone: string
    }
  }
  customer?: {
    name: string
    email: string
  }
}

export default function AdminRepairsPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [repairs, setRepairs] = useState<Repair[]>([])
  const [filteredRepairs, setFilteredRepairs] = useState<Repair[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')

  useEffect(() => {
    fetchRepairs()
  }, [])

  useEffect(() => {
    filterRepairs()
  }, [repairs, searchTerm, statusFilter])

  const fetchRepairs = async () => {
    try {
      const response = await fetch('/api/admin/repairs')
      if (response.ok) {
        const data = await response.json()
        setRepairs(data.repairs)
      }
    } catch (error) {
      console.error('Erro ao carregar reparações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterRepairs = () => {
    let filtered = repairs

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(repair =>
        repair.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repair.customerPhone?.includes(searchTerm) ||
        repair.deviceModel?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repair.repairShop?.profile?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repair.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filtrar por status
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(repair => repair.status === statusFilter)
    }

    setFilteredRepairs(filtered)
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING_PAYMENT': return 'Aguarda Pagamento'
      case 'PAID': return 'Pago'
      case 'CONFIRMED': return 'Confirmado'
      case 'RECEIVED': return 'Recebido'
      case 'DIAGNOSIS': return 'Diagnóstico'
      case 'WAITING_PARTS': return 'Aguarda Peças'
      case 'IN_REPAIR': return 'Em Reparação'
      case 'TESTING': return 'Teste'
      case 'COMPLETED': return 'Concluído'
      case 'DELIVERED': return 'Entregue'
      case 'CANCELLED': return 'Cancelado'
      default: return status
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'PENDING_PAYMENT': return 'bg-yellow-100 text-yellow-800'
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'CONFIRMED': return 'bg-blue-100 text-blue-800'
      case 'RECEIVED': return 'bg-purple-100 text-purple-800'
      case 'DIAGNOSIS': return 'bg-orange-100 text-orange-800'
      case 'WAITING_PARTS': return 'bg-gray-100 text-gray-800'
      case 'IN_REPAIR': return 'bg-indigo-100 text-indigo-800'
      case 'TESTING': return 'bg-cyan-100 text-cyan-800'
      case 'COMPLETED': return 'bg-green-100 text-green-800'
      case 'DELIVERED': return 'bg-emerald-100 text-emerald-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-black">Revify Admin</Link>
              <span className="ml-3 text-xs text-gray-500">Reparações</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header da tabela */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold text-gray-900">Gestão de Reparações</h1>
              <div className="text-sm text-gray-500">
                {filteredRepairs.length} reparações encontradas
              </div>
            </div>

            {/* Filtros */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar por cliente, dispositivo, loja ou ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                >
                  <option value="ALL">Todos os status</option>
                  <option value="PENDING_PAYMENT">Aguarda Pagamento</option>
                  <option value="CONFIRMED">Confirmado</option>
                  <option value="RECEIVED">Recebido</option>
                  <option value="DIAGNOSIS">Diagnóstico</option>
                  <option value="IN_REPAIR">Em Reparação</option>
                  <option value="COMPLETED">Concluído</option>
                  <option value="DELIVERED">Entregue</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tabela */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reparação
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Loja
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Valor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRepairs.map((repair) => (
                  <tr key={repair.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {repair.deviceModel?.brand?.name} {repair.deviceModel?.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {repair.problemType?.icon} {repair.problemType?.name}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          #{repair.id.slice(-8)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {repair.customerName}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          {repair.customerPhone}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {repair.repairShop?.profile?.companyName || 'Não atribuída'}
                      </div>
                      {repair.repairShop?.profile?.phone && (
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          {repair.repairShop.profile.phone}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(repair.status)}`}>
                        {getStatusLabel(repair.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        €{repair.finalPrice || repair.estimatedPrice}
                      </div>
                      {repair.finalPrice && repair.finalPrice !== repair.estimatedPrice && (
                        <div className="text-xs text-gray-500">
                          Est: €{repair.estimatedPrice}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{new Date(repair.createdAt).toLocaleDateString('pt-PT')}</div>
                      {repair.scheduledDate && (
                        <div className="text-xs text-blue-600">
                          <Clock className="w-3 h-3 inline mr-1" />
                          {new Date(repair.scheduledDate).toLocaleDateString('pt-PT')}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          href={`/admin/repairs/${repair.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="w-4 h-4" />
                        </Link>
                        <button className="text-gray-600 hover:text-gray-900">
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredRepairs.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">Nenhuma reparação encontrada</p>
            </div>
          )}

          {/* Paginação */}
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Mostrando {filteredRepairs.length} de {repairs.length} reparações
              </div>
              <div className="flex space-x-2">
                <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">
                  Anterior
                </button>
                <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">
                  Próximo
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
