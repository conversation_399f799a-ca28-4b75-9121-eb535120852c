import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ subdomain: string}> }
) {
  try {
    const { subdomain } = await params
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const category = searchParams.get('category') || ''
    const search = searchParams.get('search') || ''
    const sort = searchParams.get('sort') || 'newest'

    // Buscar usuário pelo subdomínio configurado no perfil
    const user = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain},
        role: REPAIR_SHOP},
      include: {
        profile: true}
    })

    if (!user) {
      return NextResponse.json(
        { message: <PERSON>ja não encontrada 
},
        { status: 404 }
      )
    }

    const sellerId = user.id

    // Construir filtros
    const where: any = {
      sellerId: sellerId,
      isActive: true,
      stock: {
        gt: 0
      }
    }

    if (category) {
      where.category = {
        name: {
          contains: category,
          mode: insensitive}
      }
    }

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: insensitive}
        },
        {
          description: {
            contains: search,
            mode: insensitive}
        }
      ]
    }

    // Definir ordenação
    let orderBy: any = { createdAt: desc}
    
    switch (sort) {
      case price_asc:
        orderBy = { price: asc
}
        break
      case 'price_desc':
        orderBy = { price: desc}
        break
      case 'name':
        orderBy = { name: asc}
        break
      case 'oldest':
        orderBy = { createdAt: asc}
        break
      default:
        orderBy = { createdAt: desc}
    }

    // Buscar produtos
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              name: true}
          },
          seller: {
            select: {
              name: true,
              profile: {
                select: {
                  companyName: true}
              }
            }
          }
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit}),
      prisma.product.count({ where
})
    ])

    // Formatar produtos
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: parseFloat(product.price.toString()),
      images: product.images,
      category: product.category,
      condition: product.condition,
      stock: product.stock,
      seller: {
        name: product.seller.profile?.companyName || product.seller.name
      },
      createdAt: product.createdAt
    }))

    return NextResponse.json({
      products: formattedProducts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      
}
    })

  } catch (error) {
    console.error('Erro ao buscar produtos da loja:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
