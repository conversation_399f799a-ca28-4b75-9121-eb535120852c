'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Search, Filter, Edit, Trash2, Eye, MoreVertical, ArrowLeft, Upload } from 'lucide-react'
import QuickEdit from '@/components/ui/QuickEdit'

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  condition: string
  stock: number
  isActive: boolean
  images: string[]
  category: {
    name: string
  }
  brand: {
    name: string
  }
  deviceModel?: {
    name: string
  }
  createdAt: string
}

export default function ProdutosPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/lojista/products')
      if (response.ok) {
        const data = await response.json()
        setProducts(data.products)
      }
    } catch (error) {
      console.error('Erro ao carregar produtos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleProductStatus = async (productId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/lojista/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        setProducts(products.map(product =>
          product.id === productId ? { ...product, isActive: !isActive } : product
        ))
      }
    } catch (error) {
      console.error('Erro ao atualizar produto:', error)
    }
  }

  const deleteProduct = async (productId: string) => {
    if (!confirm('Tem certeza que deseja eliminar este produto?')) return

    try {
      const response = await fetch(`/api/lojista/products/${productId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setProducts(products.filter(product => product.id !== productId))
      }
    } catch (error) {
      console.error('Erro ao eliminar produto:', error)
    }
  }

  const quickUpdateProduct = async (productId: string, field: string, value: string | number | boolean) => {
    try {
      const response = await fetch(`/api/lojista/products/${productId}/quick-update`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ field, value })
      })

      if (response.ok) {
        // Atualizar o produto na lista local
        setProducts(products.map(p =>
          p.id === productId ? { ...p, [field]: value } : p
        ))
        return true
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Erro ao atualizar produto')
        return false
      }
    } catch (error) {
      console.error('Erro ao atualizar produto:', error)
      alert('Erro ao atualizar produto')
      return false
    }
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && product.isActive) ||
                         (statusFilter === 'inactive' && !product.isActive)

    return matchesSearch && matchesStatus
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/lojista"
                className="flex items-center text-gray-600 hover:text-black transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-bold text-black">Revify</span>
              </Link>
              <span className="text-gray-400">|</span>
              <h1 className="text-xl font-semibold text-gray-900">Meus Produtos</h1>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/lojista/produtos/importar"
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <Upload className="w-4 h-4 mr-2" />
                Importar
              </Link>
              <Link
                href="/lojista/produtos/novo"
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Produto
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                placeholder="Pesquisar produtos..."
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
            >
              <option value="all">Todos os Status</option>
              <option value="active">Ativos</option>
              <option value="inactive">Inativos</option>
            </select>
          </div>
        </div>

        {/* Products Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-2">Nenhum produto encontrado</div>
              <p className="text-gray-400 mb-6">Comece adicionando seu primeiro produto ao marketplace.</p>
              <Link
                href="/lojista/produtos/novo"
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Produto
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Produto
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Categoria
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Preço
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stock
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredProducts.map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            {product.images.length > 0 ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                                Sem Imagem
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              <QuickEdit
                                value={product.name}
                                type="text"
                                onSave={(value) => quickUpdateProduct(product.id, 'name', value)}
                                placeholder="Nome do produto"
                              />
                            </div>
                            <div className="text-sm text-gray-500">
                              {product.brand.name} {product.deviceModel?.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{product.category.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          <QuickEdit
                            value={Number(product.price).toFixed(2)}
                            type="number"
                            step={0.01}
                            min={0}
                            onSave={(value) => quickUpdateProduct(product.id, 'price', Number(value))}
                            placeholder="0.00"
                          />
                        </div>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <div className="text-sm text-gray-500 line-through">
                            €{Number(product.originalPrice).toFixed(2)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <QuickEdit
                            value={product.stock}
                            type="number"
                            min={0}
                            onSave={(value) => quickUpdateProduct(product.id, 'stock', Number(value))}
                            placeholder="0"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <QuickEdit
                          value={product.isActive ? 'active' : 'inactive'}
                          type="select"
                          options={[
                            { value: 'active', label: 'Ativo' },
                            { value: 'inactive', label: 'Inativo' }
                          ]}
                          onSave={(value) => quickUpdateProduct(product.id, 'isActive', value === 'active')}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Link
                            href={`/marketplace/products/${product.id}`}
                            className="text-gray-400 hover:text-gray-600"
                            title="Ver no marketplace"
                          >
                            <Eye className="w-4 h-4" />
                          </Link>
                          <Link
                            href={`/lojista/produtos/${product.id}/editar`}
                            className="text-gray-400 hover:text-gray-600"
                            title="Editar"
                          >
                            <Edit className="w-4 h-4" />
                          </Link>
                          <button
                            onClick={() => toggleProductStatus(product.id, product.isActive)}
                            className="text-gray-400 hover:text-gray-600"
                            title={product.isActive ? 'Desativar' : 'Ativar'}
                          >
                            {product.isActive ? '⏸️' : '▶️'}
                          </button>
                          <button
                            onClick={() => deleteProduct(product.id)}
                            className="text-gray-400 hover:text-red-600"
                            title="Eliminar"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
