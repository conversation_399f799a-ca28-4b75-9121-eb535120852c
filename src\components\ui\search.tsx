import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input'

interface SearchProps {
  placeholder?: string
  onSearch?: (query: string) => void
}

export function SearchInput({ placeholder = "Search...", onSearch }: SearchProps) {
  return (
    <div className="relative">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder={placeholder}
        className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]"
        onChange={(e) => onSearch?.(e.target.value)}
      />
    </div>
  )
}
