import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { SYSTEM_APPS } from '@/lib/app-definitions'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    const { appId } = await request.json()

    if (!appId) {
      return NextResponse.json(
        { message: "ID da app é obrigatório" },
        { status: 400 }
      )
    }

    // Verificar se a app já está instalada
    const existingInstall = await prisma.$queryRaw`
      SELECT * FROM installed_apps
      WHERE "userId" = ${session.user.id}
      AND "appId" = ${appId}
      AND "isActive" = true
      LIMIT 1
    ` as any[]

    if (existingInstall.length > 0) {
      return NextResponse.json(
        { message: "App já está instalada" },
        { status: 400 }
      )
    }

    // Buscar plano atual do usuário
    const subscription = await prisma.$queryRaw`
      SELECT
        s.*,
        sp."availableApps" as plan_available_apps,
        sp.name as plan_name
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${session.user.id}
      AND s.status = ACTIVE
      ORDER BY s."createdAt" DESC
      LIMIT 1
    ` as any[]

    const availableApps = subscription.length > 0
      ? subscription[0].plan_available_apps || []
      : []

    const planName = subscription.length > 0
      ? subscription[0].plan_name
      : null

    // Buscar definição da app no sistema
    const app = SYSTEM_APPS.find(app => app.appId === 'appId')

    if (!app) {
      return NextResponse.json(
        { message: "App não encontrada" 
},
        { status: 404 }
      )
    }

    // Buscar configurações personalizadas da app (se existirem)
    let customConfig: any = {
}
    try {
      const appConfig = await prisma.appConfig.findFirst({
        where: { 'appId'}
      })
      if (appConfig) {
        customConfig = appConfig.settings || {}
      }
    } catch (error) {
      console.log('No custom config found for app:', 'appId')
    }
    const isAppIncludedInPlan = availableApps.includes(appId)
    const monthlyPrice = customConfig.monthlyPrice ?? app.monthlyPrice
    const isAppPaid = monthlyPrice > 0
    const hasTrialPeriod = app.hasTrialPeriod

    // Se a app não está incluída no plano e é paga
    if (!isAppIncludedInPlan && isAppPaid) {
      // Se tem período de trial, permitir instalação com trial
      if (hasTrialPeriod) {
        const trialEndDate = new Date()
        trialEndDate.setDate(trialEndDate.getDate() + app.trialDays)

        const installedApp = await prisma.$queryRaw`
          INSERT INTO installed_apps (
            "id", "userId", "appId", "isActive", "installedAt",
            "isTrialActive", "trialStartDate", "trialEndDate", "isPaid"
          )
          VALUES (
            gen_random_uuid(), ${session.user.id
}, ${appId}, true, NOW(),
            true, NOW(), ${trialEndDate}, 'false')
          ON CONFLICT ("userId", "appId")
          DO UPDATE SET
            "isActive" = true,
            "installedAt" = NOW(),
            "uninstalledAt" = NULL,
            "isTrialActive" = CASE
              WHEN installed_apps."isPaid" = true THEN false
              ELSE true
            END,
            "trialStartDate" = CASE
              WHEN installed_apps."isPaid" = true THEN NULL
              ELSE NOW()
            END,
            "trialEndDate" = CASE
              WHEN installed_apps."isPaid" = true THEN NULL
              ELSE ${trialEndDate}
            END
          RETURNING *
        ` as any[]

        return NextResponse.json({
          message: `App instalada com trial de ${app.trialDays} dias`,
          app: installedApp[0],
          trial: {
            isActive: true,
            endDate: trialEndDate,
            daysRemaining: app.trialDays
          }
        })
      } else {
        // App paga sem trial - requer upgrade de plano
        const plansWithApp = await prisma.$queryRaw`
          SELECT name FROM subscription_plans
          WHERE "availableApps" @> ${JSON.stringify([appId])}::jsonb
          AND "isActive" = true
        ` as any[]

        const requiredPlans = plansWithApp.map((p: any) => p.name)

        return NextResponse.json(
          {
            message: "Esta app requer um plano superior ou subscrição addon",
            requiredPlans,
            currentPlan: planName,
            appPrice: monthlyPrice,
            canSubscribeAddon: true},
          { status: 402 }
        )
      }
    }

    // App incluída no plano - instalar normalmente
    const installedApp = await prisma.$queryRaw`
      INSERT INTO installed_apps (
        "id", "userId", "appId", "isActive", "installedAt",
        "isTrialActive", "isPaid"
      )
      VALUES (
        gen_random_uuid(), ${session.user.id}, ${appId}, true, NOW(),
        false, true)
      ON CONFLICT ("userId", "appId")
      DO UPDATE SET
        "isActive" = true,
        "installedAt" = NOW(),
        "uninstalledAt" = NULL,
        "isTrialActive" = false,
        "isPaid" = true,
        "trialStartDate" = NULL,
        "trialEndDate" = NULL
      RETURNING *
    ` as any[]

    return NextResponse.json({
      message: 'App instalada com sucesso',
      app: installedApp[0]
    
})

  } catch (error) {
    console.error('Erro ao instalar app:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
