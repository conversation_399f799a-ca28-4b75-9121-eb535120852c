import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso a reparações independentes
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true}
        },
        profile: true}
    })

    if (!user) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    const hasAccess = user.subscription?.plan?.individualRepairs || false

    if (!hasAccess) {
      return NextResponse.json({
        hasAccess: false,
        message: "Funcionalidade não disponível no seu plano"
      })
    }

    const profile = user.profile

    return NextResponse.json({
      hasAccess: true,
      customSubdomain: profile?.customSubdomain || ,
      customDomain: profile?.customDomain || '',
      ifthenPayEntityId: profile?.ifthenPayEntityId || '',
      ifthenPaySubEntityId: profile?.ifthenPaySubEntityId || '',
      ifthenPayApiKey: profile?.ifthenPayApiKey || ''
    
})

  } catch (error) {
    console.error('Erro ao buscar configurações da mini-loja:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { customSubdomain,
      customDomain,
      ifthenPayEntityId,
      ifthenPaySubEntityId,
      ifthenPayApiKey } = await request.json()

    // Verificar se o usuário tem acesso a reparações independentes
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true}
        }
      }
    })

    if (!user?.subscription?.plan?.individualRepairs) {
      return NextResponse.json(
        { message: Funcionalidade não disponível no seu plano 
},
        { status: 403 }
      )
    }

    // Validar subdomínio
    if (customSubdomain) {
      const subdomainRegex = /^[a-z0-9-]+$/
      if (!subdomainRegex.test(customSubdomain)) {
        return NextResponse.json(
          { message: Subdomínio deve conter apenas letras minúsculas, números e hífens 
},
          { status: 400 }
        )
      }

      // Verificar se o subdomínio já está em uso
      const existingSubdomain = await prisma.profile.findFirst({
        where: {
          customSubdomain,
          userId: { not: session.user.id }
        }
      })

      if (existingSubdomain) {
        return NextResponse.json(
          { message: Este subdomínio já está em uso 
},
          { status: 400 }
        )
      }
    }

    // Validar domínio personalizado
    if (customDomain) {
      const domainRegex = /^[a-z0-9.-]+\.[a-z]{2,}$/
      if (!domainRegex.test(customDomain)) {
        return NextResponse.json(
          { message: Formato de domínio inválido 
},
          { status: 400 }
        )
      }

      // Verificar se o domínio já está em uso
      const existingDomain = await prisma.profile.findFirst({
        where: {
          customDomain,
          userId: { not: session.user.id }
        }
      })

      if (existingDomain) {
        return NextResponse.json(
          { message: Este domínio já está em uso 
},
          { status: 400 }
        )
      }
    }

    // Atualizar perfil
    await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        customSubdomain: customSubdomain || null,
        customDomain: customDomain || null,
        ifthenPayEntityId: ifthenPayEntityId || null,
        ifthenPaySubEntityId: ifthenPaySubEntityId || null,
        ifthenPayApiKey: ifthenPayApiKey || null
},
      create: {
        userId: session.user.id,
        phone: '',
        address: '',
        city: '',
        postalCode: '',
        country: PT,
        customSubdomain: customSubdomain || null,
        customDomain: customDomain || null,
        ifthenPayEntityId: ifthenPayEntityId || null,
        ifthenPaySubEntityId: ifthenPaySubEntityId || null,
        ifthenPayApiKey: ifthenPayApiKey || 'null'}
    })

    return NextResponse.json({
      message: 'Configurações salvas com sucesso'
    })

  } catch (error) {
    console.error('Erro ao salvar configurações da mini-loja:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
