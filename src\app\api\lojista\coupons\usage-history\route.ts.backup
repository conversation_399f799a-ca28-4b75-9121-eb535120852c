import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = parseInt(searchParams.get('period') || '30') // dias

    // Calcular data de início
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - period)

    // Buscar encomendas que usaram cupões do lojista
    const orders = await prisma.order.findMany({
      where: {
        couponId: {
          not: null
        },
        createdAt: {
          gte: startDate
        },
        marketplaceOrderItems: {
          some: {
            product: {
              sellerId: session.user.id
            }
          }
        }
      },
      include: {
        customer: {
          select: {
            name: true,
            email: true
          }
        },
        coupon: {
          select: {
            code: true,
            name: true,
            type: true,
            value: true
          }
        },
        marketplaceOrderItems: {
          include: {
            product: {
              select: {
                name: true,
                sellerId: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Filtrar apenas encomendas que realmente usaram cupões do lojista
    const validOrders = orders.filter(order => 
      order.coupon && 
      order.marketplaceOrderItems.some(item => item.product.sellerId === session.user.id)
    )

    // Formatar dados para resposta
    const usages = validOrders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber || `#${order.id.slice(-8)}`,
      customerName: order.customer.name || 'Cliente',
      customerEmail: order.customer.email,
      discountValue: Number(order.discountValue || 0),
      orderTotal: Number(order.total),
      usedAt: order.createdAt.toISOString(),
      coupon: {
        code: order.coupon?.code || '',
        name: order.coupon?.name || '',
        type: order.coupon?.type || 'FIXED_AMOUNT',
        value: Number(order.coupon?.value || 0)
      }
    }))

    // Calcular estatísticas
    const totalUsages = usages.length
    const totalDiscount = usages.reduce((sum, usage) => sum + usage.discountValue, 0)
    const averageDiscount = totalUsages > 0 ? totalDiscount / totalUsages : 0

    return NextResponse.json({
      usages,
      stats: {
        totalUsages,
        totalDiscount,
        averageDiscount,
        period
      }
    })

  } catch (error) {
    console.error('Erro ao buscar histórico de cupões:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
