'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import ShopLayout from '@/components/shop/ShopLayout'
import { useToast, showSuccessToast, showErrorToast } from '@/components/ui/Toast'
import { Search, Filter, ShoppingCart, Package } from 'lucide-react'
import { useShopCart } from '@/hooks/useShopCart'
interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  images: string[]
  category?: {
    id: string
    name: string}
  brand?: {
    id: string
    name: string}
  deviceModel?: {
    id: string
    name: string}
  stock: number}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  logoUrl?: string}

function ProdutosContent() {
  const params = useParams()
  const searchParams = useSearchParams()
  const { addToast } = useToast()

  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '')
  const [categoryFilter, setCategoryFilter] = useState(searchParams.get('category') || '')
  const [brandFilter, setBrandFilter] = useState(searchParams.get('brand') || '')
  const [modelFilter, setModelFilter] = useState(searchParams.get('model') || '')
  const [priceRange, setPriceRange] = useState({ min: '', max: '' })
  const [sortBy, setSortBy] = useState('newest')
  const [showFilters, setShowFilters] = useState(false)

  const subdomain = params.subdomain as string
  const { cart, addToCart: addToCartHook } = useShopCart(subdomain)

  useEffect(() => {
    if (subdomain) {
      fetchProducts()
    }
  }, [subdomain])

  useEffect(() => {
    filterProducts()
  }, [products, searchTerm, categoryFilter, brandFilter, modelFilter, priceRange, sortBy])

  const filterProducts = () => {
    let filtered = products

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand?.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter) {
      filtered = filtered.filter(product => product.category?.id === 'categoryFilter')
    }

    if (brandFilter) {
      filtered = filtered.filter(product => product.brand?.id === 'brandFilter')
    }

    if (modelFilter) {
      filtered = filtered.filter(product => product.deviceModel?.id === 'modelFilter')
    }

    // Filtro por preço
    if (priceRange.min) {
      const minPrice = parseFloat(priceRange.min)
      filtered = filtered.filter(product => product.price >= minPrice)
    
}

    if (priceRange.max) {
      const maxPrice = parseFloat(priceRange.max)
      filtered = filtered.filter(product => product.price <= 'maxPrice')
    }

    // Ordenação
    switch (sortBy) {
      case price_asc:
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price_desc':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'name_asc':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'name_desc':
        filtered.sort((a, b) => b.name.localeCompare(a.name))
        break
      case 'newest':
      default:
        // Manter ordem original (mais 'recentes primeiro')
        'break'
}

    setFilteredProducts(filtered)
  }



  const fetchProducts = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/produtos`)
      if (response.ok) {
        const data = await response.json()
        setProducts(data.products || [])
      }
    } catch (error) {
      console.error('Erro ao carregar produtos:', 'error')
    } finally {
      setIsLoading(false)
    }
  }



  const addToCart = (productId: string) => {
    const product = products.find(p => p.id === 'productId')

    if (!product) {
      showErrorToast(addToast, 'Erro', 'Produto não encontrado')
      return
    }

    if (product.stock <= 0) {
      showErrorToast(addToast, 'Stock esgotado', 'Este produto não está disponível')
      return
    }

    const currentQuantity = cart[productId] || 0
    if (currentQuantity >= product.stock) {
      showErrorToast(addToast, 'Stock insuficiente', `Apenas ${product.stock} unidades disponíveis`)
      return
    }

    addToCartHook(productId, 1)

    showSuccessToast(addToast, 'Produto adicionado', `${product.name} foi adicionado ao carrinho`)
  }



  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: currency,
      currency: EUR}).format(price)
  }

  const categories = [...new Set(products.map(p => p.category).filter(Boolean))]
  const brands = [...new Set(products.map(p => p.brand).filter(Boolean))]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link href={`/shop/${subdomain}`} className="text-gray-500 hover:text-gray-700">Início</Link>
                </li>
                <li>
                  <span className="text-gray-400">/</span>
                </li>
                <li>
                  <span className="text-gray-900 font-medium">Produtos</span>
                </li>
              </ol>
            </nav>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col space-y-4">
              {/* Search and Filter Toggle */}
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div className="flex-1 max-w-lg">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Pesquisar produtos..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                    <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Filter className="w-4 h-4" />
                    <span>Filtros</span>
                  </button>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  >
                    <option value="newest">Mais recentes</option>
                    <option value="price_asc">Preço: menor para maior</option>
                    <option value="price_desc">Preço: maior para menor</option>
                    <option value="name_asc">Nome: A-Z</option>
                    <option value="name_desc">Nome: Z-A</option>
                  </select>
                </div>
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Categoria</label>
                    <select
                      value={categoryFilter}
                      onChange={(e) => setCategoryFilter(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    >
                      <option value="">Todas as categorias</option>
                      {categories.map(category => (
                        <option key={category?.id} value={category?.id}>{category?.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Marca</label>
                    <select
                      value={brandFilter}
                      onChange={(e) => setBrandFilter(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    >
                      <option value="">Todas as marcas</option>
                      {brands.map(brand => (
                        <option key={brand?.id} value={brand?.id}>{brand?.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Preço mínimo</label>
                    <input
                      type="number"
                      value={priceRange.min}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                      placeholder="€0"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Preço máximo</label>
                    <input
                      type="number"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                      placeholder="€999"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Produtos ({filteredProducts.length})
            </h1>
          </div>

          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm || categoryFilter ? 'Nenhum produto encontrado' : 'Nenhum produto disponível'}
              </h3>
              <p className="text-gray-500">
                {searchTerm || categoryFilter
                  ? 'Tente ajustar os filtros de pesquisa.'
                  : 'Esta loja ainda não tem produtos disponíveis.'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow group">
                  <Link href={`/shop/${subdomain}/produto/${product.id}`}>
                    <div className="aspect-square relative bg-gray-100 cursor-pointer">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0]}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="w-12 h-12 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </Link>

                  <div className="p-4">
                    <Link href={`/shop/${subdomain}/produto/${product.id}`}>
                      <h3 className="font-medium text-gray-900 mb-1 line-clamp-2 hover:text-indigo-600 transition-colors cursor-pointer">{product.name}</h3>
                    </Link>
                    {product.brand && (
                      <p className="text-sm text-gray-500 mb-2">{product.brand.name}</p>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">{formatPrice(product.price)}</span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-sm text-gray-500 line-through">{formatPrice(product.originalPrice)}</span>
                        )}
                      </div>
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          addToCart(product.id)
                        }}
                        className="bg-gradient-to-r from-gray-900 to-black text-white p-2 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
                      >
                        <ShoppingCart className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="mt-2 text-xs text-gray-500">
                      Stock: {product.stock} unidades
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
  )
}

export default function LojaOnlineProdutosPage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const subdomain = params.subdomain as string

  // Hook para gerenciar carrinho
  const { getCartItemCount } = useShopCart(subdomain)

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShop(data)
      }
    } catch (error) {
      console.error(Erro ao carregar dados da loja:, 'error')
    
} finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <Link
            href="/"
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          >Voltar ao início</Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      logoUrl={shop.logoUrl}
      cartItemsCount={getCartItemCount()}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <ProdutosContent />
    </ShopLayout>
  )
}
