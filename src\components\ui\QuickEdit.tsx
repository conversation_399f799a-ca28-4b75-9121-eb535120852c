'use client'

import { useState, useRef, useEffect } from 'react'
import { Check, X, Edit2 } from 'lucide-react'

interface QuickEditProps {
  value: string | number
  type?: 'text' | 'number' | 'select'
  options?: { value: string; label: string}[]
  onSave: (value: string | number) => Promise<boolean>
  className?: string
  placeholder?: string
  min?: number
  max?: number
  step?: number
  disabled?: boolean
}

export default function QuickEdit({
  value,
  type = 'text',
  options = [],
  onSave,
  className = '',
  placeholder,
  min,
  max,
  step,
  disabled = false
}: QuickEditProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value.toString())
  const [isSaving, setIsSaving] = useState(false)
  const inputRef = useRef<HTMLInputElement | HTMLSelectElement>(null)

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select()
      }
    }
  }, [isEditing])

  useEffect(() => {
    setEditValue(value.toString())
  }, [value])

  const handleSave = async () => {
    if (editValue === value.toString()) {
      setIsEditing(false)
      return
    }

    setIsSaving(true)
    try {
      const newValue = type === 'number' ? parseFloat(editValue) : editValue
      const success = await onSave(newValue)
      
      if (success) {
        setIsEditing(false)
      } else {
        // Reverter valor em caso de erro
        setEditValue(value.toString())
      }
    } catch (error) {
      console.error('Erro ao salvar:', error)
      setEditValue(value.toString())
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditValue(value.toString())
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  if (disabled) {
    return (
      <span className={`text-gray-500 ${className}`}>
        {value}
      </span>
    )
  }

  if (isEditing) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {type === 'select' ? (
          <select
            ref={inputRef as React.RefObject<HTMLSelectElement>}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 min-w-0"
            disabled={isSaving}
          >
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type={type}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 min-w-0"
            placeholder={placeholder}
            min={min}
            max={max}
            step={step}
            disabled={isSaving}
          />
        )}
        
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="p-1 text-green-600 hover:text-green-700 disabled:opacity-50"
          title="Salvar"
        >
          <Check className="w-4 h-4" />
        </button>
        
        <button
          onClick={handleCancel}
          disabled={isSaving}
          className="p-1 text-red-600 hover:text-red-700 disabled:opacity-50"
          title="Cancelar"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    )
  }

  return (
    <div 
      className={`group flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded px-2 py-1 -mx-2 -my-1 ${className}`}
      onClick={() => setIsEditing(true)}
    >
      <span className="flex-1">
        {type === 'select' 
          ? options.find(opt => opt.value === value.toString())?.label || value
          : value}
      </span>
      <Edit2 className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
    </div>
  )
}
