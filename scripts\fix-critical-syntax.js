#!/usr/bin/env node

/**
 * Script para corrigir problemas críticos de sintaxe após conversão automática
 */

const fs = require('fs');
const path = require('path');

// Diretórios para processar
const dirsToProcess = [
  'src/app',
  'src/components'
];

// Extensões de arquivo para processar
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!file.startsWith('.') && file !== 'node_modules') {
        getAllFiles(filePath, fileList);
      }
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function fixCriticalSyntax(content) {
  let fixed = content;
  let changes = 0;

  // 1. Corrigir AutoTranslate em URLs e dados SVG
  const urlAutoTranslateRegex = /url\(<AutoTranslate text="([^"]+)" \/>\)/g;
  fixed = fixed.replace(urlAutoTranslateRegex, (match, text) => {
    changes++;
    return `url('${text}')`;
  });

  // 2. Corrigir AutoTranslate em bg-[url(...)]
  const bgUrlAutoTranslateRegex = /bg-\[url\(<AutoTranslate text="([^"]+)" \/>\)\]/g;
  fixed = fixed.replace(bgUrlAutoTranslateRegex, (match, text) => {
    changes++;
    return `bg-[url('${text}')]`;
  });

  // 3. Corrigir AutoTranslate aninhado (múltiplos)
  const nestedAutoTranslateRegex = /<AutoTranslate text=<AutoTranslate text=<AutoTranslate text=<AutoTranslate text=<AutoTranslate text="([^"]+)" \/> \/> \/> \/> \/>/g;
  fixed = fixed.replace(nestedAutoTranslateRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  // 4. Corrigir AutoTranslate em atributos HTML simples
  const htmlAttrAutoTranslateRegex = /(htmlFor|id|className|type|name)=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(htmlAttrAutoTranslateRegex, (match, attr, text) => {
    changes++;
    return `${attr}="${text}"`;
  });

  // 5. Corrigir placeholder com AutoTranslate
  const placeholderAutoTranslateRegex = /placeholder=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(placeholderAutoTranslateRegex, (match, text) => {
    changes++;
    return `placeholder="${text}"`;
  });

  // 6. Corrigir href com AutoTranslate
  const hrefAutoTranslateRegex = /href=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(hrefAutoTranslateRegex, (match, text) => {
    changes++;
    return `href="${text}"`;
  });

  // 7. Corrigir src com AutoTranslate
  const srcAutoTranslateRegex = /src=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(srcAutoTranslateRegex, (match, text) => {
    changes++;
    return `src="${text}"`;
  });

  // 8. Corrigir alt com AutoTranslate
  const altAutoTranslateRegex = /alt=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(altAutoTranslateRegex, (match, text) => {
    changes++;
    return `alt="${text}"`;
  });

  // 9. Corrigir title com AutoTranslate
  const titleAutoTranslateRegex = /title=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(titleAutoTranslateRegex, (match, text) => {
    changes++;
    return `title="${text}"`;
  });

  // 10. Corrigir value com AutoTranslate
  const valueAutoTranslateRegex = /value=<AutoTranslate text="([^"]+)" \/>/g;
  fixed = fixed.replace(valueAutoTranslateRegex, (match, text) => {
    changes++;
    return `value="${text}"`;
  });

  // 11. Corrigir AutoTranslate em strings JavaScript
  const jsStringAutoTranslateRegex = /'<AutoTranslate text="([^"]+)" \/>'/g;
  fixed = fixed.replace(jsStringAutoTranslateRegex, (match, text) => {
    changes++;
    return `'${text}'`;
  });

  const jsStringAutoTranslateRegex2 = /"<AutoTranslate text="([^"]+)" \/>"/g;
  fixed = fixed.replace(jsStringAutoTranslateRegex2, (match, text) => {
    changes++;
    return `"${text}"`;
  });

  // 12. Corrigir AutoTranslate em template literals
  const templateLiteralAutoTranslateRegex = /`<AutoTranslate text="([^"]+)" \/>`/g;
  fixed = fixed.replace(templateLiteralAutoTranslateRegex, (match, text) => {
    changes++;
    return `\`${text}\``;
  });

  // 13. Corrigir AutoTranslate em expressões JSX
  const jsxExpressionAutoTranslateRegex = /\{<AutoTranslate text="([^"]+)" \/>\}/g;
  fixed = fixed.replace(jsxExpressionAutoTranslateRegex, (match, text) => {
    changes++;
    return `"${text}"`;
  });

  // 14. Corrigir quebras de linha em AutoTranslate
  const multilineAutoTranslateRegex = /<AutoTranslate text="([^"]*\n[^"]*)" \/>/g;
  fixed = fixed.replace(multilineAutoTranslateRegex, (match, text) => {
    const cleanText = text.replace(/\n\s*/g, ' ').trim();
    changes++;
    return `<AutoTranslate text="${cleanText}" />`;
  });

  // 15. Corrigir AutoTranslate duplicado simples
  const duplicateSimpleRegex = /<AutoTranslate text=<AutoTranslate text="([^"]+)" \/> \/>/g;
  fixed = fixed.replace(duplicateSimpleRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  return { content: fixed, changes };
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  
  const originalContent = fs.readFileSync(filePath, 'utf8');
  const { content: fixedContent, changes } = fixCriticalSyntax(originalContent);
  
  if (changes > 0) {
    console.log(`🔧 ${filePath} - ${changes} correções críticas aplicadas`);
    fs.writeFileSync(filePath, fixedContent);
  }
}

// Função principal
function main() {
  console.log('🚨 Corrigindo problemas críticos de sintaxe...\n');
  
  let allFiles = [];
  
  // Coletar todos os arquivos
  dirsToProcess.forEach(dir => {
    const files = getAllFiles(dir);
    allFiles = allFiles.concat(files);
  });
  
  console.log(`📁 Processando ${allFiles.length} arquivos...\n`);
  
  // Processar cada arquivo
  allFiles.forEach(processFile);
  
  console.log('\n✅ Correções críticas finalizadas!');
}

main();
