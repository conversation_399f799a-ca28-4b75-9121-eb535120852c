'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, Clock, CheckCircle, AlertCircle, Package, Phone, Mail, MapPin, Edit } from 'lucide-react'
import RepairChat from '@/components/RepairChat'

interface Repair {
  id: string
  status: string
  description: string
  estimatedPrice?: number
  finalPrice?: number
  createdAt: string
  customerName: string
  customerPhone: string
  customerNif?: string
  deliveryMethod: string
  pickupAddress?: string
  deliveryAddress?: string
  deviceModel?: {
    name: string
    brand: {
      name: string
    }
    category: {
      name: string
    }
  }
  problemType?: {
    name: string
    icon?: string
  }
  customer: {
    name: string
    email: string
  }
  payments?: Array<{
    amount: number
    status: string
    method: string
    createdAt: string
    escrowReleaseDate?: string
    platformFee?: number
    shopAmount?: number
  }>
  trackingCode?: string
}

const STATUS_CONFIG = {
  CONFIRMED: { label: 'Confirmado', color: 'bg-indigo-100 text-indigo-800', icon: CheckCircle },
  RECEIVED: { label: 'Recebido', color: 'bg-blue-100 text-blue-800', icon: Package },
  DIAGNOSIS: { label: 'Diagnóstico', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
  WAITING_PARTS: { label: 'Aguarda Peças', color: 'bg-orange-100 text-orange-800', icon: Clock },
  IN_REPAIR: { label: 'Em Reparação', color: 'bg-purple-100 text-purple-800', icon: Clock },
  TESTING: { label: 'Teste', color: 'bg-indigo-100 text-indigo-800', icon: Clock },
  COMPLETED: { label: 'Concluído', color: 'bg-indigo-100 text-indigo-800', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
  CANCELLED: { label: 'Cancelado', color: 'bg-red-100 text-red-800', icon: AlertCircle }
}

export default function LojistaRepairDetailPage({ params }: { params: { id: string } }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [repair, setRepair] = useState<Repair | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChatOpen, setIsChatOpen] = useState(false)

  useEffect(() => {
    if (status === 'loading') return

    if (!session?.user || session.user.role !== 'REPAIR_SHOP') {
      router.push('/auth/signin')
      return
    }

    fetchRepairDetails()
  }, [session, status, router, params.id])

  const fetchRepairDetails = async () => {
    try {
      const response = await fetch(`/api/repairs/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data)
      } else {
        console.error('Reparação não encontrada')
      }
    } catch (error) {
      console.error('Erro ao carregar reparação:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleNotification = async (type: 'email' | 'whatsapp' | 'sms') => {
    if (!repair) return

    try {
      const response = await fetch(`/api/repairs/${repair.id}/notify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          message: `Atualização sobre a sua reparação #${repair.id.slice(-8)}`
        })
      })

      const result = await response.json()

      if (result.success) {
        alert(`${type.toUpperCase()} enviado com sucesso!`)
      } else {
        alert(`Erro ao enviar ${type}: ${result.message}`)
      }
    } catch (error) {
      console.error(`Erro ao enviar ${type}:`, error)
      alert(`Erro ao enviar ${type}`)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!repair) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Reparação não encontrada</h1>
          <Link href="/lojista/reparacoes" className="text-black hover:underline">
            Voltar às reparações
          </Link>
        </div>
      </div>
    )
  }

  const statusConfig = STATUS_CONFIG[repair.status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.CONFIRMED
  const StatusIcon = statusConfig.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/reparacoes"
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-700" />
              </Link>
              <h1 className="text-2xl font-bold text-black">#{repair.id.slice(-8)}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.color}`}>
                <StatusIcon className="w-4 h-4 mr-2" />
                {statusConfig.label}
              </span>
              <Link
                href={`/lojista/reparacoes/${repair.id}/edit`}
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                <Edit className="w-4 h-4 mr-2" />
                Editar
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informações do Dispositivo */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações do Dispositivo</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Dispositivo</div>
                  <div className="font-medium text-gray-900">
                    {repair.deviceModel?.brand?.name} {repair.deviceModel?.name}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Categoria</div>
                  <div className="font-medium text-gray-900">{repair.deviceModel?.category?.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Problema</div>
                  <div className="font-medium text-gray-900">{repair.problemType?.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Data de Criação</div>
                  <div className="font-medium text-gray-900">
                    {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                  </div>
                </div>
                <div className="md:col-span-2">
                  <div className="text-sm text-gray-600">Descrição do Problema</div>
                  <div className="font-medium text-gray-900">{repair.description}</div>
                </div>
              </div>
            </div>

            {/* Informações do Cliente */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações do Cliente</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Nome</div>
                  <div className="font-medium text-gray-900">{repair.customerName}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Email</div>
                  <div className="font-medium text-gray-900">{repair.customer.email}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Telefone</div>
                  <div className="font-medium text-gray-900">{repair.customerPhone}</div>
                </div>
                {repair.customerNif && (
                  <div>
                    <div className="text-sm text-gray-600">NIF</div>
                    <div className="font-medium text-gray-900">{repair.customerNif}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Informações de Entrega */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações de Entrega</h2>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-600">Método de Entrega</div>
                  <div className="font-medium text-gray-900">
                    {repair.deliveryMethod === 'STORE_PICKUP' && 'Recolha na Loja'}
                    {repair.deliveryMethod === 'COURIER_PICKUP' && 'Recolha por Estafeta'}
                    {repair.deliveryMethod === 'MAIL_SEND' && 'Envio por Correio'}
                  </div>
                </div>
                {repair.pickupAddress && (
                  <div>
                    <div className="text-sm text-gray-600">Endereço de Recolha</div>
                    <div className="font-medium text-gray-900">{repair.pickupAddress}</div>
                  </div>
                )}
                {repair.deliveryAddress && (
                  <div>
                    <div className="text-sm text-gray-600">Endereço de Entrega</div>
                    <div className="font-medium text-gray-900">{repair.deliveryAddress}</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Preços */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Preços</h2>
              <div className="space-y-3">
                {repair.estimatedPrice && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Preço Estimado:</span>
                    <span className="font-medium">€{Number(repair.estimatedPrice).toFixed(2)}</span>
                  </div>
                )}
                {repair.finalPrice && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Preço Final:</span>
                    <span className="font-medium">€{Number(repair.finalPrice).toFixed(2)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Pagamentos */}
            {repair.payments && repair.payments.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Pagamentos</h2>
                <div className="space-y-4">
                  {repair.payments.map((payment, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium text-lg">€{Number(payment.amount).toFixed(2)}</div>
                          <div className="text-sm text-gray-500">
                            {payment.method === 'stripe_test' ? 'Cartão de Crédito (Teste)' : payment.method}
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          payment.status === 'COMPLETED' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {payment.status === 'COMPLETED' ? 'Pago' : 'Pendente'}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Data do Pagamento:</span>
                          <div className="font-medium">
                            {new Date(payment.createdAt).toLocaleDateString('pt-PT')}
                          </div>
                        </div>
                        {payment.escrowReleaseDate && (
                          <div>
                            <span className="text-gray-600">Transferência Prevista:</span>
                            <div className="font-medium">
                              {new Date(payment.escrowReleaseDate).toLocaleDateString('pt-PT')}
                            </div>
                          </div>
                        )}
                        {payment.platformFee && (
                          <div>
                            <span className="text-gray-600">Comissão Plataforma:</span>
                            <div className="font-medium">€{Number(payment.platformFee).toFixed(2)}</div>
                          </div>
                        )}
                        {payment.shopAmount && (
                          <div>
                            <span className="text-gray-600">Valor para Loja:</span>
                            <div className="font-medium text-green-600">€{Number(payment.shopAmount).toFixed(2)}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tracking (se for envio por correio) */}
            {repair.deliveryMethod === 'MAIL_SEND' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Tracking dos CTT</h2>
                {repair.trackingCode ? (
                  <div className="space-y-3">
                    <div>
                      <span className="text-gray-600">Código de Tracking:</span>
                      <div className="font-medium">{repair.trackingCode}</div>
                    </div>
                    <a
                      href={`https://www.ctt.pt/feapl_2/app/open/cttexpresso/objectSearch/objectSearch.jspx?objects=${repair.trackingCode}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Rastrear nos CTT
                    </a>
                  </div>
                ) : (
                  <div className="text-gray-500">
                    Código de tracking será adicionado quando o envio for processado.
                  </div>
                )}
              </div>
            )}

            {/* Opções de Notificação */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Notificar Cliente</h3>
              <div className="grid grid-cols-3 gap-2">
                <button
                  onClick={() => handleNotification('email')}
                  className="flex items-center justify-center px-2 py-2 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
                >
                  <Mail className="w-3 h-3 mr-1" />
                  Email
                </button>
                <button
                  onClick={() => handleNotification('whatsapp')}
                  className="flex items-center justify-center px-2 py-2 text-xs bg-green-50 text-green-700 rounded hover:bg-green-100 transition-colors"
                >
                  <Phone className="w-3 h-3 mr-1" />
                  WhatsApp
                </button>
                <button
                  onClick={() => handleNotification('sms')}
                  className="flex items-center justify-center px-2 py-2 text-xs bg-gray-50 text-gray-700 rounded hover:bg-gray-100 transition-colors"
                >
                  <Phone className="w-3 h-3 mr-1" />
                  SMS
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Component */}
      <RepairChat
        repairId={repair.id}
        isOpen={isChatOpen}
        onToggle={() => setIsChatOpen(!isChatOpen)}
      />
    </div>
  )
}
