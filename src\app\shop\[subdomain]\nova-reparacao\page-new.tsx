'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { useTranslation } from '@/hooks/useTranslation'
import { ArrowLeft, ArrowRight, MapPin, Clock, Euro, Store, Mail, User, CreditCard, Star, Upload, X, CheckCircle } from 'lucide-react'

interface RepairFormData {
  // Passo 1: Dispositivo e problema
  categoryId: string
  brandId: string
  deviceId: string
  problemTypeId: string
  description: string
  problemImages: File[]
  
  // Passo 2: Dados do cliente
  customerName: string
  customerPhone: string
  customerEmail: string
  customerNif: string
  
  // Passo 3: Método de pagamento
  paymentMethod: string // IN_STORE, 'MULTIBANCO', 'MBWAY'

}

interface ShopData {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  hasIndividualRepairs: boolean
  paymentMethods?: {
    multibanco: boolean
    mbway: boolean}
}

export default function NovaReparacaoIndependentePage() {
  const { tSync } = useTranslation()
  const params = useParams()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [trackingCode, setTrackingCode] = useState('')
  const [isComplete, setIsComplete] = useState(false)
  
  const [formData, setFormData] = useState<RepairFormData>({
    categoryId: '',
    brandId: '',
    deviceId: '',
    problemTypeId: '',
    description: '',
    problemImages: [],
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    customerNif: '',
    paymentMethod: IN_STORE})

  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [problemTypes, setProblemTypes] = useState<any[]>([])
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null)
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null)

  const subdomain = params.subdomain as string

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
      fetchInitialData()
    }
  }, [subdomain])

  useEffect(() => {
    if (formData.categoryId && formData.brandId) {
      fetchDevices()
    }
  }, [formData.categoryId, formData.brandId])

  useEffect(() => {
    if (formData.categoryId && formData.problemTypeId) {
      fetchEstimate()
    }
  }, [formData.categoryId, formData.problemTypeId, formData.deviceId])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}`)
      if (response.ok) {
        const data = await response.json()
        setShop(data.shop)
        
        if (!data.shop.hasIndividualRepairs) {
          router.push(`/shop/${subdomain}`)
        }
      } else {
        router.push(`/shop/${subdomain}`)
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', 'error')
      router.push(`/shop/${subdomain}`)
    }
  }

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(Array.isArray(categoriesData) ? categoriesData : categoriesData.categories || [])
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(Array.isArray(brandsData) ? brandsData : brandsData.brands || [])
      }

      if (problemTypesRes.ok) {
        const problemTypesData = await problemTypesRes.json()
        setProblemTypes(Array.isArray(problemTypesData) ? problemTypesData : problemTypesData.problemTypes || [])
      }
    } catch (error) {
      console.error('Erro ao carregar dados iniciais:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchDevices = async () => {
    if (!formData.categoryId || !formData.brandId) return

    try {
      const response = await fetch(`/api/device-models?categoryId=${formData.categoryId}&brandId=${formData.brandId}`)
      if (response.ok) {
        const data = await response.json()
        setDevices(Array.isArray(data) ? data : data.models || [])
      }
    } catch (error) {
      console.error('Erro ao carregar modelos:', 'error')
    }
  }

  const fetchEstimate = async () => {
    if (!formData.categoryId || !formData.problemTypeId || !shop?.id) return

    try {
      const response = await fetch('/api/repair-estimate', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          categoryId: formData.categoryId,
          problemTypeId: formData.problemTypeId,
          deviceId: formData.deviceId,
          repairShopId: shop.id
        })
      })

      if (response.ok) {
        const data = await response.json()
        setEstimatedPrice(data.estimatedPrice)
        setEstimatedTime(data.estimatedTime)
      }
    } catch (error) {
      console.error('Erro ao buscar estimativa:', 'error')
    }
  }

  const handleSubmit = async () => {
    if (!shop) return

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/shop/${subdomain}/nova-reparacao`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          estimatedPrice,
          estimatedDays: estimatedTime || 7
        })
      })

      if (response.ok) {
        const data = await response.json()
        setTrackingCode(data.trackingCode)
        setIsComplete(true)
      } else {
        const error = await response.json()
        alert(`Erro: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro ao criar reparação:', 'error')
      alert('Erro ao criar reparação')
    } finally {
      setIsSubmitting(false)
    }
  }

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceedStep1 = () => {
    return formData.categoryId && formData.brandId && formData.problemTypeId && formData.description.trim()
  }

  const canProceedStep2 = () => {
    return formData.customerName.trim() && formData.customerPhone.trim() && formData.customerEmail.trim()
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: currency,
      currency: EUR}).format(price)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <p className="text-gray-600 mb-6">Esta loja não existe ou não oferece reparações independentes.</p>
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >Voltar ao Início</Link>
        </div>
      </div>
    )
  }

  if (isComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Reparação Solicitada!</h1>
          <p className="text-gray-600 mb-6">Sua reparação foi registrada com sucesso. Use o código abaixo para acompanhar o progresso.</p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-blue-600 mb-1">Código de Rastreamento:</p>
            <p className="text-2xl font-bold text-blue-900">{trackingCode}</p>
          </div>
          <div className="space-y-3">
            <Link
              href="/track"
              className="block w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >Acompanhar Reparação</Link>
            <Link
              href={`/shop/${subdomain}`}
              className="block w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >Voltar à Loja</Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/shop/${subdomain}`} className="text-xl font-bold text-black">
                {shop.name}
              </Link>
              <span className="ml-3 text-xs text-gray-500 bg-blue-100 px-2 py-1 rounded">
                Powered by Revify
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/track"
                className="text-sm text-gray-600 hover:text-gray-900"
              >Rastrear Reparação</Link>
              <Link
                href={`/shop/${subdomain}`}
                className="text-sm text-gray-600 hover:text-gray-900"
              >Voltar à Loja</Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Solicitar Reparação</h1>
          <p className="text-xl text-blue-100 mb-2">
            Reparação rápida e profissional na {shop.name}
          </p>
          <p className="text-blue-200">Preencha os dados abaixo e receba um orçamento personalizado</p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-8">
        {/* Progress Steps */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                1
              </div>
              <span className={`ml-3 text-sm font-medium ${
            </div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                2
              </div>
              <span className={`ml-3 text-sm font-medium ${
                currentStep >= 2 ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Dados Pessoais
              </span>
            </div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                3
              </div>
              <span className={`ml-3 text-sm font-medium ${
            </div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= 4 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
              }`}>
                4
              </div>
              <span className={`ml-3 text-sm font-medium ${
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Passo 1: Dispositivo e Problema */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Qual é o seu dispositivo e problema?</h2>
                <p className="text-gray-600 mb-6">Selecione o tipo de dispositivo e descreva o problema para recebermos um orçamento preciso.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Categoria *</label>
                  <select
                    value={formData.categoryId}
                    onChange={(e) => setFormData({ ...formData, categoryId: e.target.value, brandId: '', deviceId: '' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Marca *</label>
                  <select
                    value={formData.brandId}
                    onChange={(e) => setFormData({ ...formData, brandId: e.target.value, deviceId: '' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!formData.categoryId}
                  >
                    <option value="">Selecione uma marca</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                </div>

                {devices.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Modelo</label>
                    <select
                      value={formData.deviceId}
                      onChange={(e) => setFormData({ ...formData, deviceId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Selecione um modelo</option>
                      {devices.map((device) => (
                        <option key={device.id} value={device.id}>
                          {device.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Problema *
                  </label>
                  <select
                    value={formData.problemTypeId}
                    onChange={(e) => setFormData({ ...formData, problemTypeId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione o problema</option>
                    {problemTypes.map((problemType) => (
                      <option key={problemType.id} value={problemType.id}>
                        {problemType.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Descrição do Problema *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={tSync("Descreva detalhadamente o problema do seu dispositivo...")}
                  required
                />
              </div>

              {estimatedPrice && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-blue-900">Estimativa de Preço</h3>
                      <p className="text-2xl font-bold text-blue-600">{formatPrice(estimatedPrice)}</p>
                    </div>
                    {estimatedTime && (
                      <div className="text-right">
                        <h3 className="text-sm font-medium text-blue-900">Tempo Estimado</h3>
                        <p className="text-lg font-semibold text-blue-600">{estimatedTime} dias</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Passo 2: Dados Pessoais */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Dados de Contacto</h2>
                <p className="text-gray-600 mb-6">Precisamos dos seus dados para entrar em contacto sobre a reparação.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nome Completo *</label>
                  <input
                    type="text"
                    value={formData.customerName}
                    onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={tSync("Seu nome completo")}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Telefone *</label>
                  <input
                    type="tel"
                    value={formData.customerPhone}
                    onChange={(e) => setFormData({ ...formData, customerPhone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={tSync("Seu número de telefone")}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                  <input
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => setFormData({ ...formData, customerEmail: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={tSync("<EMAIL>")}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NIF (Opcional)
                  </label>
                  <input
                    type="text"
                    value={formData.customerNif}
                    onChange={(e) => setFormData({ ...formData, customerNif: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={tSync("Número de contribuinte")}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Passo 3: Método de Pagamento */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Método de Pagamento</h2>
                <p className="text-gray-600 mb-6">Escolha como pretende pagar pela reparação.</p>
              </div>

              <div className="space-y-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="IN_STORE"
                      checked={formData.paymentMethod === 'IN_STORE'}
                      onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value })}
                      className="mr-3"
                    />
                    <div className="flex items-center">
                      <Store className="w-5 h-5 text-gray-600 mr-3" />
                      <div>
                        <h3 className="font-medium text-gray-900">Pagamento na Loja</h3>
                        <p className="text-sm text-gray-600">Pague diretamente na loja quando levantar o dispositivo</p>
                      </div>
                    </div>
                  </label>
                </div>

                {shop?.paymentMethods?.multibanco && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="MULTIBANCO"
                        checked={formData.paymentMethod === 'MULTIBANCO'}
                        onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value })}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
                        <div>
                          <h3 className="font-medium text-gray-900">Multibanco</h3>
                          <p className="text-sm text-gray-600">Pagamento por referência Multibanco</p>
                        </div>
                      </div>
                    </label>
                  </div>
                )}

                {shop?.paymentMethods?.mbway && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="MBWAY"
                        checked={formData.paymentMethod === 'MBWAY'}
                        onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value })}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
                        <div>
                          <h3 className="font-medium text-gray-900">MB WAY</h3>
                          <p className="text-sm text-gray-600">Pagamento via MB WAY</p>
                        </div>
                      </div>
                    </label>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Passo 4: Confirmação */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Confirmação dos Dados</h2>
                <p className="text-gray-600 mb-6">Verifique todos os dados antes de submeter a solicitação.</p>
              </div>

              <div className="space-y-6">
                {/* Resumo do Dispositivo */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Dispositivo e Problema</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Categoria:</span> {categories.find(c => c.id === formData.categoryId)?.name}</p>
                    <p><span className="font-medium">Marca:</span> {brands.find(b => b.id === formData.brandId)?.name}</p>
                    {formData.deviceId && (
                      <p><span className="font-medium">Modelo:</span> {devices.find(d => d.id === formData.deviceId)?.name}</p>
                    )}
                    <p><span className="font-medium">Problema:</span> {problemTypes.find(p => p.id === formData.problemTypeId)?.name}</p>
                    <p><span className="font-medium">Descrição:</span> {formData.description}</p>
                  </div>
                </div>

                {/* Resumo dos Dados Pessoais */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Dados de Contacto</h3>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Nome:</span> {formData.customerName}</p>
                    <p><span className="font-medium">Telefone:</span> {formData.customerPhone}</p>
                    <p><span className="font-medium">Email:</span> {formData.customerEmail}</p>
                    {formData.customerNif && (
                      <p><span className="font-medium">NIF:</span> {formData.customerNif}</p>
                    )}
                  </div>
                </div>

                {/* Resumo do Pagamento */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Método de Pagamento</h3>
                  <div className="text-sm">
                    <p>
                      {formData.paymentMethod === 'IN_STORE' && 'Pagamento na Loja'}
                      {formData.paymentMethod === 'MULTIBANCO' && 'Multibanco'}
                      {formData.paymentMethod === 'MBWAY' && 'MB WAY'}
                    </p>
                  </div>
                </div>

                {/* Estimativa Final */}
                {estimatedPrice && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-blue-900">Preço Estimado</h3>
                        <p className="text-2xl font-bold text-blue-600">{formatPrice(estimatedPrice)}</p>
                      </div>
                      {estimatedTime && (
                        <div className="text-right">
                          <h3 className="font-medium text-blue-900">Tempo Estimado</h3>
                          <p className="text-lg font-semibold text-blue-600">{estimatedTime} dias</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Informações da Loja */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 mb-2">Loja Responsável</h3>
                  <div className="space-y-1 text-sm text-green-800">
                    <p className="font-medium">{shop.name}</p>
                    {shop.phone && <p>📞 {shop.phone}</p>}
                    <p>📧 {shop.email}</p>
                    {shop.address && (
                      <p>📍 {shop.address}, {shop.city} {shop.postalCode}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </button>

            {currentStep < 4 ? (
              <button
                onClick={nextStep}
                disabled={
                  (currentStep === 1 && !canProceedStep1()) ||
                  (currentStep === 2 && !canProceedStep2())
                }
                className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >Próximo<ArrowRight className="w-4 h-4 ml-2" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enviando...
                  </>
                ) : (
                  <>Confirmar Reparação<CheckCircle className="w-4 h-4 ml-2" />
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
