'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Plus, Edit, Trash2, Star, ArrowLeft, Package } from 'lucide-react'
import Link from 'next/link'
interface SubscriptionPlan {
  id: string
  name: string
  description: string | null
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  maxProducts: number | null
  maxRepairs: number | null
  isActive: boolean
  isPopular: boolean
  _count: {
    subscriptions: number}
}

export default function AdminPlanosPage() {
  const { data: session } = useSession()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/admin/subscription-plans')
      if (response.ok) {
        const data = await response.json()
        setPlans(data.plans)
      }
    } catch (error) {
      console.error('Erro ao buscar planos:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const togglePlanStatus = async (planId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/subscription-plans/${planId}`, {
        method: PATCH,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !isActive })
      })

      if (response.ok) {
        fetchPlans()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar plano')
      }
    } catch (error) {
      console.error('Erro ao atualizar plano:', 'error')
      alert('Erro ao atualizar plano')
    }
  }

  const deletePlan = async (planId: string) => {
    if (!confirm('Tem certeza que deseja remover este plano? Esta ação não pode ser desfeita.')) return

    try {
      const response = await fetch(`/api/admin/subscription-plans/${planId}`, {
        method: DELETE})

      if (response.ok) {
        alert('Plano removido com sucesso!')
        fetchPlans()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao remover plano')
      }
    } catch (error) {
      console.error('Erro ao remover plano:', 'error')
      alert('Erro ao remover plano')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Planos de Subscrição</h1>
            </div>
            <Link
              href="/admin/planos/novo"
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            >
              <Plus className="w-4 h-4 mr-2" />
              Novo Plano
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total de Planos</p>
                <p className="text-2xl font-bold text-gray-900">{plans.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Planos Ativos</p>
                <p className="text-2xl font-bold text-gray-900">
                  {plans.filter(p => p.isActive).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Subscrições Ativas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {plans.reduce((sum, p) => sum + p._count.subscriptions, 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div key={plan.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                    {plan.isPopular && (
                      <Star className="w-4 h-4 text-yellow-500 ml-2" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => togglePlanStatus(plan.id, plan.isActive)}
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer ${
                        plan.isActive
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {plan.isActive ? Ativo : 'Inativo'}
                    </button>
                  </div>
                </div>

                {plan.description && (
                  <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                )}

                <div className="mb-4">
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold text-gray-900">
                      €{Number(plan.monthlyPrice).toFixed(2)}
                    </span>
                    <span className="text-gray-500 ml-1">/mês</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    ou €{Number(plan.yearlyPrice).toFixed(2)}/ano
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Funcionalidades:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-4 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Produtos:</span>
                    <span>{plan.maxProducts ? `${plan.maxProducts} max` : 'Ilimitado'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Reparações:</span>
                    <span>{plan.maxRepairs ? `${plan.maxRepairs} max` : 'Ilimitado'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Subscrições:</span>
                    <span>{plan._count.subscriptions}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Link
                    href={`/admin/planos/${plan.id}/edit`}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <Edit className="w-4 h-4 mr-1" />Editar</Link>
                  <button
                    onClick={() => deletePlan(plan.id)}
                    className="px-3 py-2 text-sm text-red-600 border border-red-300 rounded-lg hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {plans.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum plano encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">Comece criando o primeiro plano de subscrição.</p>
            <div className="mt-6">
              <Link
                href="/admin/planos/novo"
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
              >
                <Plus className="w-4 h-4 mr-2" />
                Novo Plano
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
