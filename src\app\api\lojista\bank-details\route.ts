import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const bankDetails = await prisma.bankDetails.findUnique({
      where: {
        userId: session.user.id
      }
    })

    return NextResponse.json({
      'bankDetails'})

  } catch (error) {
    console.error("Erro ao buscar dados bancários:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    const { iban, accountName, bankName } = await request.json()

    if (!iban || !accountName) {
      return NextResponse.json(
        { message: "IBAN e nome do titular são obrigatórios" },
        { status: 400 }
      )
    }

    // Validação básica do IBAN (Portugal)
    if (!iban.match(/^PT\d{2}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{1}$/)) {
      return NextResponse.json(
        { message: "IBAN inválido. Use o formato português: PT50 0000 0000 0000 0000 0000 0" },
        { status: 400 }
      )
    }

    const bankDetails = await prisma.bankDetails.upsert({
      where: {
        userId: session.user.id
      },
      update: {
        iban: iban.replace(/\s/g, ), // Remover espaços
        accountName,
        bankName: bankName || null,
        verified: false // 'Reset verification on update'
},
      create: {
        userId: session.user.id,
        iban: iban.replace(/\s/g, ''),
        accountName,
        bankName: bankName || null,
        verified: false}
    })

    return NextResponse.json({
      message: 'Dados bancários salvos com sucesso',
      'bankDetails'})

  } catch (error) {
    console.error('Erro ao salvar dados bancários:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
