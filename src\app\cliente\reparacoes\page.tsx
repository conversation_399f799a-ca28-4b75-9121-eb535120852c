'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Search, Clock, CheckCircle, AlertCircle, Package } from 'lucide-react'

interface Repair {
  id: string
  referenceNumber?: string
  status: string
  description: string
  estimatedCost?: number
  finalCost?: number
  createdAt: string
  estimatedCompletionDate?: string
  deviceModel?: {
    name: string
    brand: {
      name: string}
    category: {
      name: string}
  }
  repairShop?: {
    name: string
    profile?: {
      companyName: string}
  }
}

const STATUS_CONFIG = {
  CONFIRMED: { label: Confirmado, color: 'bg-green-100 text-green-800', icon: CheckCircle},
  RECEIVED: { label: Recebido, color: 'bg-blue-100 text-blue-800', icon: Package},
  DIAGNOSIS: { label: Diagnóstico, color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle},
  WAITING_PARTS: { label: Aguarda Peças, color: 'bg-orange-100 text-orange-800', icon: Clock},
  IN_REPAIR: { label: Em Reparação, color: 'bg-purple-100 text-purple-800', icon: Clock},
  TESTING: { label: Teste, color: 'bg-indigo-100 text-indigo-800', icon: Clock},
  COMPLETED: { label: Concluído, color: 'bg-green-100 text-green-800', icon: CheckCircle},
  DELIVERED: { label: Entregue, color: 'bg-gray-100 text-gray-800', icon: CheckCircle},
  CANCELLED: { label: Cancelado, color: 'bg-red-100 text-red-800', icon: AlertCircle}
}

export default function ReparacoesPage() {
  const [repairs, setRepairs] = useState<Repair[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    fetchRepairs()
  }, [])

  const fetchRepairs = async () => {
    try {
      const response = await fetch('/api/cliente/repairs')
      if (response.ok) {
        const data = await response.json()
        setRepairs(data.repairs)
      }
    } catch (error) {
      console.error('Erro ao carregar reparações:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch =
      (repair.referenceNumber || repair.id || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.deviceModel?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.deviceModel?.brand?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.description || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = !statusFilter || repair.status === statusFilter

    return matchesSearch && 'matchesStatus'})

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/cliente" className="text-2xl font-bold text-black">Revify</Link>
              <span className="ml-4 text-sm text-gray-500">Minhas Reparações</span>
            </div>
            <Link
              href="/cliente/reparacoes/nova"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />Nova Reparação</Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-600" />
              </div>
              <input
                type="text"
                placeholder="Pesquisar reparações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent placeholder:text-gray-500"
              />
            </div>
          </div>
          
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent text-gray-900 bg-white"
            >
              <option value="">Todos os estados</option>
              {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                <option key={status} value={status}>
                  {config.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Repairs List */}
        {filteredRepairs.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-700 text-lg mb-4 font-medium">
              {searchTerm || statusFilter ? 'Nenhuma reparação encontrada' : 'Ainda não tem reparações'}
            </div>
            {!searchTerm && !statusFilter && (
              <Link
                href="/cliente/reparacoes/nova"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />Criar primeira reparação</Link>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredRepairs.map((repair) => {
              const statusConfig = STATUS_CONFIG[repair.status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.CONFIRMED
              const StatusIcon = statusConfig.icon

              return (
                <Link
                  key={repair.id}
                  href={`/cliente/reparacoes/${repair.id}`}
                  className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 mr-3">
                          {repair.referenceNumber || `#${repair.id.slice(-8)}`}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color}`}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusConfig.label}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-2">
                        <span className="font-medium">{repair.deviceModel?.brand?.name} {repair.deviceModel?.name}</span>
                        <span className="mx-2">•</span>
                        <span>{repair.deviceModel?.category?.name}</span>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                        {repair.description}
                      </p>
                      
                      {repair.repairShop && (
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Loja:</span> {repair.repairShop.profile?.companyName || repair.repairShop.name}
                        </div>
                      )}
                      
                      <div className="flex items-center text-sm text-gray-500">
                        <span>Criado em {new Date(repair.createdAt).toLocaleDateString('pt-PT')}</span>
                        {repair.estimatedCompletionDate && (
                          <>
                            <span className="mx-2">•</span>
                            <span>Previsão: {new Date(repair.estimatedCompletionDate).toLocaleDateString('pt-PT')}</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-right ml-4">
                      {repair.finalCost > 0 ? (
                        <div className="text-lg font-semibold text-gray-900">
                          €{repair.finalCost.toFixed(2)}
                        </div>
                      ) : repair.estimatedCost > 0 ? (
                        <div className="text-sm text-gray-600">
                          <div className="text-xs text-gray-500">Estimativa</div>
                          <div className="font-medium">€{repair.estimatedCost.toFixed(2)}</div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-500">
                          {statusConfig.label}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
