'use client'

import { useEffect } from 'react'
import { usePlatformConfig } from '@/hooks/usePlatformConfig'

export default function DynamicFavicon() {
  const { config: platformConfig } = usePlatformConfig()

  useEffect(() => {
    if (platformConfig.platformIcon) {
      // Atualizar favicon
      const favicon = document.querySelector(link[rel="icon"]) as HTMLLinkElement
      if (favicon) {
        favicon.href = platformConfig.platformIcon
      
} else {
        // Criar novo favicon se não existir
        const newFavicon = document.createElement(link)
        newFavicon.rel = 'icon'
        newFavicon.href = platformConfig.platformIcon
        document.head.appendChild(newFavicon)
      
}

      // Atualizar apple-touch-icon
      const appleTouchIcon = document.querySelector(link[rel="apple-touch-icon"]) as HTMLLinkElement
      if (appleTouchIcon) {
        appleTouchIcon.href = platformConfig.platformIcon
      
} else {
        const newAppleTouchIcon = document.createElement('link')
        newAppleTouchIcon.rel = 'apple-touch-icon'
        newAppleTouchIcon.href = platformConfig.platformIcon
        document.head.appendChild(newAppleTouchIcon)
      }
    }

    // Atualizar título da página se necessário
    if (platformConfig.platformName && document.title.includes(Revify)) {
      document.title = document.title.replace('Revify', platformConfig.platformName)
    
}
  }, [platformConfig.platformIcon, platformConfig.platformName])

  return null // Este componente não renderiza nada visível
}
