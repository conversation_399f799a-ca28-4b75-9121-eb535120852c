import { NextRequest, NextResponse } from 'next/server'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { message: 'Assinatura Stripe em falta' },
        { status: 400 }
      )
    }

    // Buscar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const webhookSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeWebhookSecret' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
    const webhookSecret = webhookSecretSetting?.value || process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !webhookSecret) {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        webhookSecret
      )
    } catch (err) {
      console.error('Erro na verificação do webhook:', err)
      return NextResponse.json(
        { message: 'Webhook inválido' },
        { status: 400 }
      )
    }

    console.log('Webhook recebido:', event.type)

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session)
        break

      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
        break

      default:
        console.log(`Evento não tratado: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Erro no webhook Stripe:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    const repairId = session.metadata?.repairId

    if (repairId) {
      // Processar pagamento de reparação
      await handleRepairPayment(session, repairId)
    } else if (session.metadata?.userId && session.metadata?.addressId) {
      // Processar compra do marketplace
      await handleMarketplaceOrder(session)
    } else {
      console.error('Metadados insuficientes no checkout')
      return
    }
  } catch (error) {
    console.error('Erro ao processar checkout completo:', error)
  }
}

async function handleRepairPayment(session: Stripe.Checkout.Session, repairId: string) {
  try {

    // Atualizar status da reparação
    await prisma.repair.update({
      where: { id: repairId },
      data: {
        status: 'PAID',
        stripePaymentIntentId: session.payment_intent as string,
        paidAt: new Date()
      }
    })

    // Criar entrada de pagamento em escrow
    await prisma.payment.create({
      data: {
        repairId: repairId,
        amount: (session.amount_total || 0) / 100, // Converter de centavos
        currency: session.currency || 'eur',
        stripePaymentIntentId: session.payment_intent as string,
        status: 'ESCROW', // Pagamento em escrow
        platformFee: ((session.amount_total || 0) / 100) * 0.05, // 5% de comissão
        shopAmount: ((session.amount_total || 0) / 100) * 0.95, // 95% para a loja
        escrowReleaseDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 dias
      }
    })

    console.log(`Pagamento processado para reparação ${repairId}`)

  } catch (error) {
    console.error('Erro ao processar checkout completo:', error)
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    const repairId = paymentIntent.metadata?.repairId

    if (!repairId) {
      console.error('RepairId não encontrado nos metadados do PaymentIntent')
      return
    }

    // Confirmar que o pagamento foi bem-sucedido
    await prisma.repair.update({
      where: { id: repairId },
      data: {
        status: 'CONFIRMED',
        confirmedAt: new Date()
      }
    })

    // Criar notificação para a loja
    const repair = await prisma.repair.findUnique({
      where: { id: repairId },
      include: { repairShop: true, customer: true }
    })

    if (repair) {
      await prisma.notification.create({
        data: {
          userId: repair.repairShopId,
          title: 'Nova Reparação Recebida',
          message: `Recebeu uma nova reparação de ${repair.customerName}. Pagamento confirmado.`,
          type: 'NEW_REPAIR',
          relatedId: repairId
        }
      })
    }

    console.log(`Pagamento confirmado para reparação ${repairId}`)

  } catch (error) {
    console.error('Erro ao processar pagamento bem-sucedido:', error)
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    const repairId = paymentIntent.metadata?.repairId

    if (!repairId) {
      console.error('RepairId não encontrado nos metadados do PaymentIntent')
      return
    }

    // Marcar reparação como falhada
    await prisma.repair.update({
      where: { id: repairId },
      data: {
        status: 'PAYMENT_FAILED'
      }
    })

    console.log(`Pagamento falhado para reparação ${repairId}`)

  } catch (error) {
    console.error('Erro ao processar pagamento falhado:', error)
  }
}

async function handleMarketplaceOrder(session: Stripe.Checkout.Session) {
  try {
    const { userId, addressId, customerName, customerPhone } = session.metadata!

    // Buscar endereço do perfil
    const profile = await prisma.profile.findUnique({
      where: { userId: userId }
    })

    const addresses = (profile?.addresses as Array<{id: string, label: string, street: string, city: string, postalCode: string, country: string}>) || []
    const selectedAddress = addresses.find(addr => addr.id === addressId)

    if (!selectedAddress) {
      console.error('Endereço não encontrado:', addressId)
      return
    }

    // Buscar itens do carrinho
    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: userId
      },
      include: {
        product: {
          include: {
            seller: true
          }
        }
      }
    })

    if (cartItems.length === 0) {
      console.error('Carrinho vazio para usuário:', userId)
      return
    }

    // Agrupar itens por vendedor
    const itemsBySeller = cartItems.reduce((acc, item) => {
      const sellerId = item.product.sellerId
      if (!acc[sellerId]) {
        acc[sellerId] = []
      }
      acc[sellerId].push(item)
      return acc
    }, {} as Record<string, typeof cartItems>)

    // Criar uma encomenda para cada vendedor
    for (const [sellerId, items] of Object.entries(itemsBySeller)) {
      const orderTotal = items.reduce((sum, item) =>
        sum + (Number(item.product.price) * item.quantity), 0
      )

      // Gerar número da encomenda
      const orderNumber = `MP${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`

      // Criar encomenda
      const order = await prisma.order.create({
        data: {
          customerId: userId,
          status: 'PENDING',
          total: orderTotal,
          shippingName: customerName || '',
          shippingStreet: selectedAddress.street,
          shippingCity: selectedAddress.city,
          shippingPostalCode: selectedAddress.postalCode,
          shippingCountry: selectedAddress.country || 'Portugal',
          marketplaceOrderItems: {
            create: items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: Number(item.product.price)
            }))
          }
        }
      })

      // Criar notificação para o vendedor
      await prisma.notification.create({
        data: {
          userId: sellerId,
          title: 'Nova Encomenda Recebida',
          message: `Recebeu uma nova encomenda #${orderNumber} no valor de €${orderTotal.toFixed(2)}`,
          type: 'ORDER',
          relatedId: order.id
        }
      })

      // Atualizar stock dos produtos
      for (const item of items) {
        await prisma.marketplaceProduct.update({
          where: {
            id: item.productId
          },
          data: {
            stock: {
              decrement: item.quantity
            }
          }
        })
      }
    }

    // Criar notificação para o cliente
    await prisma.notification.create({
      data: {
        userId: userId,
        title: 'Compra Confirmada',
        message: `Sua compra foi confirmada! Você receberá atualizações sobre o envio em breve.`,
        type: 'ORDER'
      }
    })

    // Limpar carrinho
    await prisma.cartItem.deleteMany({
      where: {
        userId: userId
      }
    })

    console.log('Encomenda do marketplace processada com sucesso para usuário:', userId)

  } catch (error) {
    console.error('Erro ao processar encomenda do marketplace:', error)
  }
}
