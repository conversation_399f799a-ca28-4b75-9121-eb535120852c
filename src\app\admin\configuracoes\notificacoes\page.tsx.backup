'use client'

import { useState, useEffect } from 'react'
import { Mail, MessageCircle, Phone, Save, Eye, EyeOff } from 'lucide-react'

interface NotificationSettings {
  email: {
    enabled: boolean
    provider: string
    smtpHost: string
    smtpPort: string
    smtpUser: string
    smtpPassword: string
    fromEmail: string
    fromName: string
  }
  whatsapp: {
    enabled: boolean
    provider: string
    apiKey: string
    apiUrl: string
    phoneNumber: string
  }
  sms: {
    enabled: boolean
    provider: string
    apiKey: string
    apiSecret: string
    fromNumber: string
  }
}

export default function NotificationSettingsPage() {
  const [settings, setSettings] = useState<NotificationSettings>({
    email: {
      enabled: false,
      provider: 'smtp',
      smtpHost: '',
      smtpPort: '587',
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '',
      fromName: 'FixItNow'
    },
    whatsapp: {
      enabled: false,
      provider: 'whatsapp_business',
      apiKey: '',
      apiUrl: '',
      phoneNumber: ''
    },
    sms: {
      enabled: false,
      provider: 'twilio',
      apiKey: '',
      apiSecret: '',
      fromNumber: ''
    }
  })

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [showPasswords, setShowPasswords] = useState({
    smtpPassword: false,
    whatsappApiKey: false,
    smsApiKey: false,
    smsApiSecret: false
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings/notifications')
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/settings/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      })

      if (response.ok) {
        alert('Configurações salvas com sucesso!')
      } else {
        alert('Erro ao salvar configurações')
      }
    } catch (error) {
      console.error('Erro ao salvar:', error)
      alert('Erro ao salvar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-black">Configurações de Notificações</h1>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Guardando...' : 'Guardar'}
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="space-y-6">
          {/* Email Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <Mail className="w-6 h-6 text-blue-600 mr-3" />
              <h2 className="text-lg font-semibold text-gray-900">Configurações de Email</h2>
              <label className="ml-auto flex items-center">
                <input
                  type="checkbox"
                  checked={settings.email.enabled}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, enabled: e.target.checked }
                  }))}
                  className="mr-2"
                />
                <span className="text-sm">Ativado</span>
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Servidor SMTP
                </label>
                <input
                  type="text"
                  value={settings.email.smtpHost}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, smtpHost: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="smtp.gmail.com"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Porta
                </label>
                <input
                  type="text"
                  value={settings.email.smtpPort}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, smtpPort: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="587"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Usuário
                </label>
                <input
                  type="text"
                  value={settings.email.smtpUser}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, smtpUser: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Senha
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.smtpPassword ? 'text' : 'password'}
                    value={settings.email.smtpPassword}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      email: { ...prev.email, smtpPassword: e.target.value }
                    }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="senha-do-app"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('smtpPassword')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPasswords.smtpPassword ? (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Remetente
                </label>
                <input
                  type="email"
                  value={settings.email.fromEmail}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, fromEmail: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome Remetente
                </label>
                <input
                  type="text"
                  value={settings.email.fromName}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    email: { ...prev.email, fromName: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="FixItNow"
                />
              </div>
            </div>
          </div>

          {/* WhatsApp Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <MessageCircle className="w-6 h-6 text-green-600 mr-3" />
              <h2 className="text-lg font-semibold text-gray-900">Configurações do WhatsApp</h2>
              <label className="ml-auto flex items-center">
                <input
                  type="checkbox"
                  checked={settings.whatsapp.enabled}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    whatsapp: { ...prev.whatsapp, enabled: e.target.checked }
                  }))}
                  className="mr-2"
                />
                <span className="text-sm">Ativado</span>
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.whatsappApiKey ? 'text' : 'password'}
                    value={settings.whatsapp.apiKey}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      whatsapp: { ...prev.whatsapp, apiKey: e.target.value }
                    }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="sua-api-key"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('whatsappApiKey')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPasswords.whatsappApiKey ? (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  URL da API
                </label>
                <input
                  type="url"
                  value={settings.whatsapp.apiUrl}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    whatsapp: { ...prev.whatsapp, apiUrl: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="https://api.whatsapp.com/send"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Número de Telefone
                </label>
                <input
                  type="tel"
                  value={settings.whatsapp.phoneNumber}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    whatsapp: { ...prev.whatsapp, phoneNumber: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="+351912345678"
                />
              </div>
            </div>
          </div>

          {/* SMS Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <Phone className="w-6 h-6 text-purple-600 mr-3" />
              <h2 className="text-lg font-semibold text-gray-900">Configurações de SMS</h2>
              <label className="ml-auto flex items-center">
                <input
                  type="checkbox"
                  checked={settings.sms.enabled}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    sms: { ...prev.sms, enabled: e.target.checked }
                  }))}
                  className="mr-2"
                />
                <span className="text-sm">Ativado</span>
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Key (Twilio)
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.smsApiKey ? 'text' : 'password'}
                    value={settings.sms.apiKey}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      sms: { ...prev.sms, apiKey: e.target.value }
                    }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="sua-api-key"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('smsApiKey')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPasswords.smsApiKey ? (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Secret
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.smsApiSecret ? 'text' : 'password'}
                    value={settings.sms.apiSecret}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      sms: { ...prev.sms, apiSecret: e.target.value }
                    }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="seu-api-secret"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('smsApiSecret')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPasswords.smsApiSecret ? (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Número Remetente
                </label>
                <input
                  type="tel"
                  value={settings.sms.fromNumber}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    sms: { ...prev.sms, fromNumber: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="+351912345678"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
