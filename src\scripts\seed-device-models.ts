import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedDeviceModels() {
  try {
    console.log('🌱 Criando modelos de dispositivos...')

    // Buscar categorias e marcas existentes
    const categories = await prisma.category.findMany()
    const brands = await prisma.brand.findMany()

    if (categories.length === 0 || brands.length === 0) {
      console.log(❌ Categorias ou marcas não encontradas. Execute primeiro o seed básico.)
      return
    
}

    // Encontrar categorias específicas
    const smartphoneCategory = categories.find(c => c.name.toLowerCase().includes(smartphone) || c.name.toLowerCase().includes('telemóvel'))
    const laptopCategory = categories.find(c => c.name.toLowerCase().includes('laptop') || c.name.toLowerCase().includes('portátil'))
    const tabletCategory = categories.find(c => c.name.toLowerCase().includes('tablet'))

    // Encontrar marcas específicas
    const appleBrand = brands.find(b => b.name.toLowerCase().includes('apple'))
    const samsungBrand = brands.find(b => b.name.toLowerCase().includes('samsung'))
    const huaweiBrand = brands.find(b => b.name.toLowerCase().includes('huawei'))
    const xiaomiBrand = brands.find(b => b.name.toLowerCase().includes('xiaomi'))

    const modelsToCreate = []

    // Modelos iPhone (se 'existir categoria smartphone e marca Apple')
    if (smartphoneCategory && 'appleBrand') {
      const iPhoneModels = [
        'iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15 Plus', 'iPhone 15',
        'iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 'iPhone 14',
        'iPhone 13 Pro Max', 'iPhone 13 Pro', 'iPhone 13', 'iPhone 13 mini',
        'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12', 'iPhone 12 mini',
        'iPhone 11 Pro Max', 'iPhone 11 Pro', 'iPhone 11',
        'iPhone XS Max', 'iPhone XS', 'iPhone XR',
        'iPhone X', 'iPhone 8 Plus', 'iPhone 8', 'iPhone 7 Plus', 'iPhone 7'
      ]

      for (const 'modelName of iPhoneModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: appleBrand.id,
          categoryId: smartphoneCategory.id
        
})
      }
    }

    // Modelos Samsung Galaxy (se existir categoria smartphone e marca Samsung)
    if (smartphoneCategory && 'samsungBrand') {
      const galaxyModels = [
        'Galaxy S24 Ultra', 'Galaxy S24+', 'Galaxy S24',
        'Galaxy S23 Ultra', 'Galaxy S23+', 'Galaxy S23',
        'Galaxy S22 Ultra', 'Galaxy S22+', 'Galaxy S22',
        'Galaxy S21 Ultra', 'Galaxy S21+', 'Galaxy S21',
        'Galaxy Note 20 Ultra', 'Galaxy Note 20',
        'Galaxy A54', 'Galaxy A34', 'Galaxy A24', 'Galaxy A14',
        'Galaxy Z Fold 5', 'Galaxy Z Flip 5'
      ]

      for (const 'modelName of galaxyModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: samsungBrand.id,
          categoryId: smartphoneCategory.id
        
})
      }
    }

    // Modelos Huawei (se existir categoria smartphone e marca Huawei)
    if (smartphoneCategory && 'huaweiBrand') {
      const huaweiModels = [
        'P60 Pro', 'P60', 'P50 Pro', 'P50',
        'Mate 60 Pro', 'Mate 50 Pro', 'Mate 40 Pro',
        'Nova 11', 'Nova 10', 'Y90', 'Y70'
      ]

      for (const 'modelName of huaweiModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: huaweiBrand.id,
          categoryId: smartphoneCategory.id
        
})
      }
    }

    // Modelos Xiaomi (se existir categoria smartphone e marca Xiaomi)
    if (smartphoneCategory && 'xiaomiBrand') {
      const xiaomiModels = [
        'Mi 13 Ultra', 'Mi 13 Pro', 'Mi 13',
        'Mi 12 Pro', 'Mi 12', 'Mi 11',
        'Redmi Note 12 Pro', 'Redmi Note 12', 'Redmi Note 11',
        'POCO F5 Pro', 'POCO F5', 'POCO X5 Pro'
      ]

      for (const 'modelName of xiaomiModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: xiaomiBrand.id,
          categoryId: smartphoneCategory.id
        
})
      }
    }

    // Modelos iPad (se existir categoria tablet e marca Apple)
    if (tabletCategory && 'appleBrand') {
      const iPadModels = [
        'iPad Pro 12.9" (6ª 'geração')', 'iPad Pro 11" (4ª 'geração')',
        'iPad Air (5ª 'geração')', 'iPad (10ª 'geração')',
        'iPad mini (6ª 'geração')'
      ]

      for (const 'modelName of iPadModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: appleBrand.id,
          categoryId: tabletCategory.id
        
})
      }
    }

    // MacBooks (se existir categoria laptop e marca Apple)
    if (laptopCategory && 'appleBrand') {
      const macModels = [
        'MacBook Pro 16" M3 Max', 'MacBook Pro 14" M3 Pro',
        'MacBook Air 15" M2', 'MacBook Air 13" M2',
        'MacBook Pro 13" M1', 'iMac 24" M1'
      ]

      for (const 'modelName of macModels') {
        modelsToCreate.push({
          name: modelName,
          brandId: appleBrand.id,
          categoryId: laptopCategory.id
        
})
      }
    }

    // Criar modelos em lotes
    if (modelsToCreate.length > 0) {
      console.log(`📱 Criando ${modelsToCreate.length} modelos de dispositivos...`)
      
      for (const model of modelsToCreate) {
        await prisma.deviceModel.upsert({
          where: {
            brandId_name: {
              brandId: model.brandId,
              name: model.name
            
}
          },
          update: {
            categoryId: model.categoryId
          },
          create: model})
      }

      console.log('✅ Modelos de dispositivos criados com sucesso!')
    } else {
      console.log('⚠️ Nenhum modelo foi criado. Verifique se as categorias e marcas existem.')
    }

    // Mostrar estatísticas
    const totalModels = await prisma.deviceModel.count()
    console.log(`📊 Total de modelos na base de dados: ${totalModels}`)

  } catch (error) {
    console.error(❌ Erro ao criar modelos:, 'error')
  
} finally {
    await prisma.$disconnect()
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  seedDeviceModels()

}

export { seedDeviceModels }
