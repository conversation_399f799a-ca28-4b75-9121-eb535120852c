'use client'

import { useState, useEffect } from 'react'
import { useSession, signIn } from 'next-auth/react'
import Link from 'next/link'
import { Menu, X, ShoppingCart, ArrowRight, Sparkles } from 'lucide-react'
import AutoTranslate from '@/components/ui/AutoTranslate'
import UserDropdown from '@/components/UserDropdown'
import LanguageSelector from '@/components/LanguageSelector'

interface ModernHeaderProps {
  showCart?: boolean
}

export default function ModernHeader({ showCart = false }: ModernHeaderProps) {
  const { data: session, status } = useSession()
  const [cartCount, setCartCount] = useState(0)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    if (showCart && session) {
      fetchCartCount()
    }
  }, [showCart, session])

  const fetchCartCount = async () => {
    try {
      const response = await fetch(<AutoTranslate text="/api/marketplace/cart" />)
      if (response.ok) {
        const cartData = await response.json()
        setCartCount(cartData.items?.length || 0)
      }
    } catch (error) {
      console.error(<AutoTranslate text="Erro ao buscar carrinho:" />, error)
    }
  }

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-b border-gray-100 ${
      isScrolled
        ? 'bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg'
        : 'bg-white/90 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Link href="/" className="flex items-center space-x-2 group">
              <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Revify
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link
              href="/marketplace"
              className="text-gray-700 hover:text-indigo-600 font-medium transition-colors relative group"
            >
              <AutoTranslate text="Marketplace" />
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </Link>
            <Link
              href="/ajuda"
              className="text-gray-700 hover:text-indigo-600 font-medium transition-colors relative group"
            >
              <AutoTranslate text="Central de Ajuda" />
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </Link>
            <Link
              href="/contactos"
              className="text-gray-700 hover:text-indigo-600 font-medium transition-colors relative group"
            >
              <AutoTranslate text="Contactos" />
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </Link>
            <Link
              href="#"
              className="text-gray-700 hover:text-indigo-600 font-medium transition-colors relative group"
            >
              <AutoTranslate text="Sobre Nós" />
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-indigo-600 transition-all group-hover:w-full"></span>
            </Link>
          </nav>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <LanguageSelector />

            {status === 'loading' ? (
              <div className="animate-pulse">
                <div className="h-10 w-24 bg-gray-200 rounded-full"></div>
              </div>
            ) : session ? (
              <div className="flex items-center space-x-4">
                {/* Cart Icon */}
                {showCart && (
                  <Link
                    href="/marketplace/cart"
                    className="relative p-2 text-gray-700 hover:text-indigo-600 transition-colors group"
                  >
                    <ShoppingCart className="w-6 h-6 group-hover:scale-110 transition-transform" />
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                        {cartCount}
                      </span>
                    )}
                  </Link>
                )}

                {/* User Dropdown */}
                <UserDropdown user={session.user} />
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => signIn()}
                  className="text-gray-700 hover:text-indigo-600 font-medium transition-colors"
                >
                  <AutoTranslate text="Entrar" />
                </button>
                <Link
                  href="/auth/signup"
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2.5 rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all font-medium flex items-center space-x-2 group"
                >
                  <span><AutoTranslate text="Registar" /></span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-gray-700 hover:text-indigo-600 transition-colors"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-xl">
            <nav className="px-4 py-6 space-y-4">
              <Link
                href="/marketplace"
                className="block text-gray-700 hover:text-indigo-600 font-medium py-2 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <AutoTranslate text="Marketplace" />
              </Link>
              <Link
                href="/ajuda"
                className="block text-gray-700 hover:text-indigo-600 font-medium py-2 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <AutoTranslate text="Central de Ajuda" />
              </Link>
              <Link
                href="/contactos"
                className="block text-gray-700 hover:text-indigo-600 font-medium py-2 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <AutoTranslate text="Contactos" />
              </Link>
              <Link
                href="#"
                className="block text-gray-700 hover:text-indigo-600 font-medium py-2 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <AutoTranslate text="Sobre Nós" />
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
