import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const addressId = params.id
    const data = await request.json()
    const { label, street, city, postalCode, country, isDefault, type } = data

    // Validações básicas
    if (!label || !street || !city || !postalCode) {
      return NextResponse.json(
        { message: 'Campos obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Buscar perfil atual
    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!profile) {
      return NextResponse.json(
        { message: 'Perfil não encontrado' },
        { status: 404 }
      )
    }

    let currentAddresses = profile.addresses as any[] || []

    // Encontrar endereço a editar
    const addressIndex = currentAddresses.findIndex(addr => addr.id === addressId)
    
    if (addressIndex === -1) {
      return NextResponse.json(
        { message: 'Endereço não encontrado' },
        { status: 404 }
      )
    }

    // Se for endereço padrão, remover padrão dos outros
    if (isDefault) {
      currentAddresses = currentAddresses.map(addr => ({ ...addr, isDefault: false }))
    }

    // Atualizar endereço
    currentAddresses[addressIndex] = {
      ...currentAddresses[addressIndex],
      label,
      street,
      city,
      postalCode,
      country: country || 'Portugal',
      isDefault,
      type: type || 'HOME',
      updatedAt: new Date().toISOString()
    }

    // Atualizar perfil
    await prisma.profile.update({
      where: {
        userId: session.user.id
      },
      data: {
        addresses: currentAddresses
      }
    })

    return NextResponse.json({
      message: 'Endereço atualizado com sucesso',
      address: currentAddresses[addressIndex]
    })

  } catch (error) {
    console.error('Erro ao atualizar endereço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const addressId = params.id

    // Buscar perfil atual
    const profile = await prisma.profile.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!profile) {
      return NextResponse.json(
        { message: 'Perfil não encontrado' },
        { status: 404 }
      )
    }

    let currentAddresses = profile.addresses as any[] || []

    // Encontrar endereço a eliminar
    const addressIndex = currentAddresses.findIndex(addr => addr.id === addressId)
    
    if (addressIndex === -1) {
      return NextResponse.json(
        { message: 'Endereço não encontrado' },
        { status: 404 }
      )
    }

    const addressToDelete = currentAddresses[addressIndex]

    // Remover endereço
    currentAddresses.splice(addressIndex, 1)

    // Se era o endereço padrão e ainda há endereços, definir o primeiro como padrão
    if (addressToDelete.isDefault && currentAddresses.length > 0) {
      currentAddresses[0].isDefault = true
    }

    // Atualizar perfil
    await prisma.profile.update({
      where: {
        userId: session.user.id
      },
      data: {
        addresses: currentAddresses
      }
    })

    return NextResponse.json({
      message: 'Endereço eliminado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao eliminar endereço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
