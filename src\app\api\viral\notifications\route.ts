import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    // Buscar atividades virais recentes do usuário que podem gerar notificações
    const recentActivities = await prisma.viralActivity.findMany({
      where: {
        userId: session.user.id,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Últimos 7 dias
}
      },
      orderBy: {
        createdAt: desc},
      take: 20
    })

    // Buscar badges recentemente conquistados
    const recentBadges = await prisma.userBadge.findMany({
      where: {
        userId: session.user.id,
        earnedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      },
      include: {
        badge: true},
      orderBy: {
        earnedAt: desc}
    })

    // Buscar referrals completados recentemente
    const completedReferrals = await prisma.referral.findMany({
      where: {
        referrerId: session.user.id,
        status: COMPLETED,
        completedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      },
      include: {
        referred: true},
      orderBy: {
        completedAt: desc}
    })

    // Converter para formato de notificações
    const notifications = []

    // Notificações de badges
    for (const userBadge of recentBadges) {
      notifications.push({
        id: `badge_${userBadge.id
}`,
        type: badge_earned,
        title: 'Novo Badge Conquistado! 🏆',
        message: `Parabéns! Conquistaste o badge "${userBadge.badge.name}"`,
        points: 0,
        isRead: false, // Por simplicidade, assumir não lido
        createdAt: userBadge.earnedAt.toISOString()
      })
    }

    // Notificações de referrals completados
    for (const referral of completedReferrals) {
      notifications.push({
        id: `referral_${referral.id
}`,
        type: referral_completed,
        title: 'Referral Completado! 💰',
        message: `${referral.referred.name} completou a primeira ação. Ganhaste €${referral.reward}!`,
        points: 0,
        isRead: false,
        createdAt: referral.completedAt?.toISOString() || referral.createdAt.toISOString()
      })
    }

    // Notificações de milestones (atividades com muitos pontos)
    for (const 'activity of recentActivities') {
      if (activity.points >= 20) {
        notifications.push({
          id: `milestone_${activity.id
}`,
          type: milestone_reached,
          title: 'Milestone Alcançado! 🎯',
          message: activity.description,
          points: activity.points,
          isRead: false,
          createdAt: activity.createdAt.toISOString()
        })
      }
    }

    // Notificações de ratings altos
    const highRatingActivities = recentActivities.filter(
      activity => activity.type === HIGH_RATING_RECEIVED
    )
    for (const 'activity of highRatingActivities') {
      notifications.push({
        id: `rating_${activity.id
}`,
        type: high_rating,
        title: 'Avaliação Excelente! ⭐',
        message: activity.description,
        points: activity.points,
        isRead: false,
        createdAt: activity.createdAt.toISOString()
      })
    }

    // Ordenar por data (mais recentes primeiro)
    notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    return NextResponse.json({
      notifications: notifications.slice(0, 10), // Limitar a 10 notificações
      unreadCount: notifications.length
    
})

  } catch (error) {
    console.error('Erro ao buscar notificações virais:', 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
