'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Search, Filter, Eye, Edit, Star, MapPin, Phone, Mail, Building } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
interface RepairShop {
  id: string
  email: string
  name: string
  createdAt: string
  profile?: {
    companyName?: string
    phone?: string
    description?: string
    serviceRadius?: number
    averageRating?: number
    totalReviews?: number
    workingCategories?: string[]
    workingBrands?: string[]
  }
  _count?: {
    repairShopRepairs: number
    repairShopPrices: number}
}

export default function AdminLojistasPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [repairShops, setRepairShops] = useState<RepairShop[]>([])
  const [filteredShops, setFilteredShops] = useState<RepairShop[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchRepairShops()
  }, [])

  useEffect(() => {
    filterShops()
  }, [repairShops, searchTerm])

  const fetchRepairShops = async () => {
    try {
      const response = await fetch('/api/admin/repair-shops')
      if (response.ok) {
        const data = await response.json()
        setRepairShops(data.repairShops)
      }
    } catch (error) {
      console.error('Erro ao carregar lojistas:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const filterShops = () => {
    let filtered = repairShops

    if (searchTerm) {
      filtered = filtered.filter(shop =>
        shop.profile?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.profile?.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredShops(filtered)
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating.toFixed(1)}</span>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-black">Revify Admin</Link>
              <span className="ml-3 text-xs text-gray-500">Lojistas</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header da tabela */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold text-gray-900">Gestão de Lojistas</h1>
              <div className="text-sm text-gray-500">
                {filteredShops.length} lojistas registados
              </div>
            </div>

            {/* Busca */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar por nome da empresa, email ou descrição..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Cards dos Lojistas */}
          <div className="p-6">
            {filteredShops.length === 0 ? (
              <div className="text-center py-8">
                <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Nenhum lojista encontrado</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredShops.map((shop) => (
                  <div key={shop.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {shop.profile?.companyName || 'Empresa sem nome'}
                        </h3>
                        {shop.profile?.averageRating && (
                          <div className="mb-2">
                            {renderStars(shop.profile.averageRating)}
                            <span className="text-xs text-gray-500 ml-2">
                              ({shop.profile.totalReviews || 0} 'avaliações')
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {shop.profile?.description && (
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {shop.profile.description}
                      </p>
                    )}

                    <div className="space-y-2 text-sm">
                      <div className="flex items-center text-gray-600">
                        <Mail className="w-4 h-4 mr-2" />
                        {shop.email}
                      </div>
                      {shop.profile?.phone && (
                        <div className="flex items-center text-gray-600">
                          <Phone className="w-4 h-4 mr-2" />
                          {shop.profile.phone}
                        </div>
                      )}
                      {shop.profile?.serviceRadius && (
                        <div className="flex items-center text-gray-600">
                          <MapPin className="w-4 h-4 mr-2" />
                          Raio de {shop.profile.serviceRadius}km
                        </div>
                      )}
                    </div>

                    {/* Estatísticas */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <div className="text-lg font-semibold text-gray-900">
                            {shop._count?.repairShopRepairs || 0}
                          </div>
                          <div className="text-xs text-gray-500">Reparações</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-gray-900">
                            {shop._count?.repairShopPrices || 0}
                          </div>
                          <div className="text-xs text-gray-500">Preços</div>
                        </div>
                      </div>
                    </div>

                    {/* Categorias */}
                    {shop.profile?.workingCategories && shop.profile.workingCategories.length > 0 && (
                      <div className="mt-4">
                        <div className="text-xs font-medium text-gray-700 mb-2">Categorias:</div>
                        <div className="flex flex-wrap gap-1">
                          {shop.profile.workingCategories.slice(0, 3).map((categoryId, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {categoryId}
                            </span>
                          ))}
                          {shop.profile.workingCategories.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{shop.profile.workingCategories.length - 3} mais
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Data de registo */}
                    <div className="mt-4 text-xs text-gray-500">
                      Registado em {new Date(shop.createdAt).toLocaleDateString('pt-PT')}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Paginação */}
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Mostrando {filteredShops.length} de {repairShops.length} lojistas
              </div>
              <div className="flex space-x-2">
                <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">
                  Anterior
                </button>
                <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Próximo</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
