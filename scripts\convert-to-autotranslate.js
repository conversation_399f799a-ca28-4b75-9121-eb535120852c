#!/usr/bin/env node

/**
 * Script para converter textos hardcoded em português para AutoTranslate
 * Identifica padrões comuns e sugere conversões
 */

const fs = require('fs');
const path = require('path');

// Textos comuns que devem ser traduzidos
const commonTexts = [
  // Navigation
  'Marketplace', 'Central de Ajuda', 'Contactos', 'Sobre Nós', 'Entrar', 'Registar',
  'Dashboard', 'Perfil', 'Configurações', 'Sair',
  
  // Actions
  'Salvar', 'Cancelar', 'Editar', 'Eliminar', 'Adicionar', 'Remover', 'Ver', 'Voltar',
  'Continuar', 'Confirmar', 'Fechar', 'Abrir', 'Enviar', 'Pesquisar', 'Filtrar',
  
  // Common words
  'Nome', 'Email', 'Telefone', 'Morada', 'Cidade', 'Código Postal', 'País',
  'Descrição', 'Preço', 'Quantidade', 'Total', 'Subtotal', 'Estado', 'Data',
  
  // Status
  'Ativo', 'Inativo', 'Pendente', 'Confirmado', 'Em Progresso', 'Concluído', 'Cancelado',
  
  // Business terms
  'Reparações', 'Dispositivos', 'Eletrónicos', 'Técnicos', 'Clientes', 'Produtos',
  'Serviços', 'Encomendas', 'Carrinho', 'Checkout', 'Pagamento', 'Entrega'
];

// Componentes principais para converter
const componentsToConvert = [
  'src/components/MainHeader.tsx',
  'src/components/ModernHeader.tsx', 
  'src/components/Footer.tsx',
  'src/components/ModernFooter.tsx',
  'src/app/page.tsx',
  'src/app/auth/signin/page.tsx',
  'src/app/auth/signup/page.tsx'
];

function findHardcodedTexts(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ Arquivo não encontrado: ${filePath}`);
    return [];
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const found = [];

  // Procurar por textos em português dentro de JSX
  const patterns = [
    // Texto entre aspas em JSX: "Texto"
    /"([^"]*[àáâãçéêíóôõú][^"]*)"/gi,
    // Texto entre aspas simples: 'Texto'
    /'([^']*[àáâãçéêíóôõú][^']*)'/gi,
    // Texto direto em JSX: >Texto<
    />([^<]*[àáâãçéêíóôõú][^<]*)</gi
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1].trim();
      if (text.length > 2 && !text.includes('{') && !text.includes('\\')) {
        found.push({
          text,
          line: content.substring(0, match.index).split('\n').length,
          context: content.substring(Math.max(0, match.index - 50), match.index + 50)
        });
      }
    }
  });

  return found;
}

function generateConversionSuggestions(filePath) {
  const hardcodedTexts = findHardcodedTexts(filePath);
  
  if (hardcodedTexts.length === 0) {
    console.log(`✅ ${filePath} - Nenhum texto hardcoded encontrado`);
    return;
  }

  console.log(`\n📄 ${filePath}`);
  console.log(`🔍 Encontrados ${hardcodedTexts.length} textos hardcoded:`);
  
  hardcodedTexts.forEach((item, index) => {
    console.log(`\n${index + 1}. Linha ${item.line}: "${item.text}"`);
    console.log(`   Sugestão: <AutoTranslate text="${item.text}" />`);
    
    // Se for um texto comum, destacar
    if (commonTexts.some(common => item.text.includes(common))) {
      console.log(`   ⭐ TEXTO COMUM - Prioridade alta para conversão`);
    }
  });
}

console.log('🔄 Analisando componentes para conversão AutoTranslate...\n');

componentsToConvert.forEach(generateConversionSuggestions);

console.log('\n📋 RESUMO:');
console.log('1. Adicionar import: import AutoTranslate from "@/components/ui/AutoTranslate"');
console.log('2. Converter textos hardcoded para: <AutoTranslate text="Texto" />');
console.log('3. Para elementos específicos: <AutoTranslate text="Texto" as="h1" className="..." />');
console.log('4. Testar mudança de idioma após conversão');
