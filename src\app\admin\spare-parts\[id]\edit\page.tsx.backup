'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { ArrowLeft, Upload, X, Plus } from 'lucide-react'
import Link from 'next/link'

export default function EditSparePartPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [deviceModels, setDeviceModels] = useState<any[]>([])
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sku: '',
    price: '',
    stock: '',
    categoryId: '',
    brandId: '',
    deviceModelId: '',
    deliveryTime: '3',
    images: [] as string[],
    compatibility: [] as string[],
    availability: true
  })
  const [newCompatibility, setNewCompatibility] = useState('')

  useEffect(() => {
    if (params.id) {
      fetchData()
      fetchPart()
    }
  }, [params.id])

  useEffect(() => {
    if (formData.brandId) {
      fetchDeviceModels(formData.brandId)
    } else {
      setDeviceModels([])
    }
  }, [formData.brandId])

  const fetchData = async () => {
    try {
      const [categoriesRes, brandsRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/brands')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories)
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData.brands)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    }
  }

  const fetchPart = async () => {
    try {
      const response = await fetch(`/api/admin/spare-parts/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        const part = data.part
        setFormData({
          name: part.name,
          description: part.description || '',
          sku: part.sku,
          price: part.price.toString(),
          stock: part.stock.toString(),
          categoryId: part.category.id,
          brandId: part.brand?.id || '',
          deviceModelId: part.deviceModel?.id || '',
          deliveryTime: part.deliveryTime.toString(),
          images: part.images || [],
          compatibility: part.compatibility || [],
          availability: part.availability
        })
      } else if (response.status === 404) {
        router.push('/admin/spare-parts')
      }
    } catch (error) {
      console.error('Erro ao buscar peça:', error)
    }
  }

  const fetchDeviceModels = async (brandId: string) => {
    try {
      const response = await fetch(`/api/device-models?brandId=${brandId}`)
      if (response.ok) {
        const data = await response.json()
        setDeviceModels(data.deviceModels)
      }
    } catch (error) {
      console.error('Erro ao buscar modelos:', error)
    }
  }

  const addCompatibility = () => {
    if (newCompatibility.trim() && !formData.compatibility.includes(newCompatibility.trim())) {
      setFormData({
        ...formData,
        compatibility: [...formData.compatibility, newCompatibility.trim()]
      })
      setNewCompatibility('')
    }
  }

  const removeCompatibility = (index: number) => {
    setFormData({
      ...formData,
      compatibility: formData.compatibility.filter((_, i) => i !== index)
    })
  }

  const addImage = () => {
    const url = prompt('URL da imagem:')
    if (url && url.trim()) {
      setFormData({
        ...formData,
        images: [...formData.images, url.trim()]
      })
    }
  }

  const removeImage = (index: number) => {
    setFormData({
      ...formData,
      images: formData.images.filter((_, i) => i !== index)
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/spare-parts/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          stock: parseInt(formData.stock),
          deliveryTime: parseInt(formData.deliveryTime),
          brandId: formData.brandId || null,
          deviceModelId: formData.deviceModelId || null
        })
      })

      if (response.ok) {
        alert('Peça atualizada com sucesso!')
        router.push(`/admin/spare-parts/${params.id}`)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar peça')
      }
    } catch (error) {
      console.error('Erro ao atualizar peça:', error)
      alert('Erro ao atualizar peça')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href={`/admin/spare-parts/${params.id}`}
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Editar Peça</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informações Básicas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome da Peça *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SKU *
                </label>
                <input
                  type="text"
                  required
                  value={formData.sku}
                  onChange={(e) => setFormData({...formData, sku: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>
          </div>

          {/* Categorização */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Categorização</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria *
                </label>
                <select
                  required
                  value={formData.categoryId}
                  onChange={(e) => setFormData({...formData, categoryId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                >
                  <option value="">Selecionar categoria</option>
                  {categories && categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Marca
                </label>
                <select
                  value={formData.brandId}
                  onChange={(e) => setFormData({...formData, brandId: e.target.value, deviceModelId: ''})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                >
                  <option value="">Selecionar marca</option>
                  {brands && brands.map(brand => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Modelo
                </label>
                <select
                  value={formData.deviceModelId}
                  onChange={(e) => setFormData({...formData, deviceModelId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  disabled={!formData.brandId}
                >
                  <option value="">Selecionar modelo</option>
                  {deviceModels && deviceModels.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Preço e Stock */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preço e Inventário</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preço (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stock *
                </label>
                <input
                  type="number"
                  min="0"
                  required
                  value={formData.stock}
                  onChange={(e) => setFormData({...formData, stock: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tempo de Entrega (dias)
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.deliveryTime}
                  onChange={(e) => setFormData({...formData, deliveryTime: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={formData.availability.toString()}
                  onChange={(e) => setFormData({...formData, availability: e.target.value === 'true'})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                >
                  <option value="true">Ativo</option>
                  <option value="false">Inativo</option>
                </select>
              </div>
            </div>
          </div>

          {/* Submit */}
          <div className="flex justify-end space-x-4">
            <Link
              href={`/admin/spare-parts/${params.id}`}
              className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isLoading ? 'Atualizando...' : 'Atualizar Peça'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
