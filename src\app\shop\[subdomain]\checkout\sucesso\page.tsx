'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import ShopLayout from '@/components/shop/ShopLayout'
import { CheckCircle, Package, Truck, MapPin, CreditCard, Mail, Phone } from 'lucide-react'

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string}

export default function CheckoutSuccessPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const subdomain = params.subdomain as string
  const orderId = searchParams.get('order')

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}`)
      if (response.ok) {
        const data = await response.json()
        setShop(data.shop)
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <Link 
            href="/"
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          >Voltar ao início</Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout 
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Encomenda Confirmada!</h1>
            
            <p className="text-lg text-gray-600 mb-8">Obrigado pela sua compra. A sua encomenda foi recebida e está a ser processada.</p>

            {orderId && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Package className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-500">Número da Encomenda</span>
                </div>
                <p className="text-2xl font-bold text-gray-900">#{orderId}</p>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">O que acontece a seguir?</h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100">
                      <Mail className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-gray-900">Confirmação por Email</h3>
                    <p className="text-sm text-gray-500">Receberá um email de confirmação com os detalhes da sua encomenda nos próximos minutos.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-8 w-8 rounded-full bg-yellow-100">
                      <Package className="h-4 w-4 text-yellow-600" />
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-gray-900">Preparação da Encomenda</h3>
                    <p className="text-sm text-gray-500">A sua encomenda será preparada e embalada com cuidado pela nossa equipa.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-8 w-8 rounded-full bg-green-100">
                      <Truck className="h-4 w-4 text-green-600" />
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-gray-900">Entrega ou Levantamento</h3>
                    <p className="text-sm text-gray-500">Será notificado quando a encomenda estiver pronta para entrega ou levantamento.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Precisa de Ajuda?</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <Phone className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900">Telefone</p>
                  <p className="text-sm text-gray-600">{shop.phone}</p>
                </div>
                
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <MapPin className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm font-medium text-gray-900">Loja Física</p>
                  <p className="text-sm text-gray-600">{shop.address}</p>
                  <p className="text-sm text-gray-600">{shop.postalCode} {shop.city}</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/shop/${subdomain}`}
                className="bg-gradient-to-r from-gray-900 to-black text-white px-8 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors font-medium"
              >
                Continuar a Comprar
              </Link>
              
              <Link
                href={`/shop/${subdomain}/conta`}
                className="bg-white text-gray-900 px-8 py-3 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors font-medium"
              >Ver Minhas Encomendas</Link>
            </div>
          </div>
        </div>
      </div>
    </ShopLayout>
  )
}
