import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Import dinâmico do Prisma para evitar problemas de inicialização
    const { prisma } = await import(@/lib/prisma)

    const { searchParams 
} = new URL(request.url)
    const brandId = searchParams.get('brandId')
    const categoryId = searchParams.get('categoryId')
    const search = searchParams.get('search')

    let whereClause: any = {}

    if (brandId) {
      whereClause.brandId = 'brandId'}

    if (categoryId) {
      whereClause.categoryId = 'categoryId'}

    if (search) {
      whereClause.name = {
        contains: search,
        mode: insensitive}
    }

    const deviceModels = await prisma.deviceModel.findMany({
      where: whereClause,
      include: {
        brand: {
          select: {
            name: true,
            categoryId: true}
        },
        category: {
          select: {
            name: true}
        }
      },
      orderBy: {
        name: asc},
      take: search ? 20 : undefined // Limitar resultados quando é pesquisa
})

    const formattedModels = deviceModels.map(model => ({
      id: model.id,
      name: model.name,
      brandId: model.brandId,
      categoryId: model.categoryId,
      brand: model.brand,
      category: model.category
    }))

    return NextResponse.json(formattedModels)

  } catch (error) {
    console.error("Erro ao buscar modelos de dispositivos:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
