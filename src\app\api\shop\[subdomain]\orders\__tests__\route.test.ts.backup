import { NextRequest } from 'next/server'
import { POST, GET } from '../route'

// Mock Prisma
const mockPrisma = {
  profile: {
    findFirst: jest.fn(),
  },
  customer: {
    findFirst: jest.fn(),
    create: jest.fn(),
  },
  product: {
    findMany: jest.fn(),
    update: jest.fn(),
  },
  order: {
    create: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn(),
  },
  $transaction: jest.fn(),
}

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma),
}))

describe('/api/shop/[subdomain]/orders', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST - Create Order', () => {
    const mockRequest = (body: any) => ({
      json: () => Promise.resolve(body),
    }) as NextRequest

    const mockParams = { params: { subdomain: 'test-shop' } }

    it('should create order successfully', async () => {
      const orderData = {
        customerName: '<PERSON>',
        customerEmail: '<EMAIL>',
        customerPhone: '123456789',
        deliveryMethod: 'home',
        paymentMethod: 'card',
        items: [
          { productId: 'prod1', quantity: 2, price: 29.99 }
        ],
        total: 59.98
      }

      // Mock shop exists
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      // Mock customer creation
      mockPrisma.customer.create.mockResolvedValue({
        id: 'customer1',
        email: '<EMAIL>'
      })

      // Mock products exist with stock
      mockPrisma.product.findMany.mockResolvedValue([
        {
          id: 'prod1',
          name: 'Test Product',
          price: 29.99,
          stock: 10
        }
      ])

      // Mock order creation
      mockPrisma.order.create.mockResolvedValue({
        id: 'order1',
        orderNumber: 'ORD-TEST-SHOP-0001',
        total: 59.98
      })

      // Mock transaction
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrisma)
      })

      const response = await POST(mockRequest(orderData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.orderNumber).toBe('ORD-TEST-SHOP-0001')
      expect(mockPrisma.order.create).toHaveBeenCalled()
    })

    it('should return error when shop not found', async () => {
      const orderData = {
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        items: []
      }

      mockPrisma.profile.findFirst.mockResolvedValue(null)

      const response = await POST(mockRequest(orderData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Loja não encontrada')
    })

    it('should return error when required fields missing', async () => {
      const orderData = {
        customerEmail: '<EMAIL>'
        // Missing required fields
      }

      const response = await POST(mockRequest(orderData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Campos obrigatórios em falta')
    })

    it('should return error when insufficient stock', async () => {
      const orderData = {
        customerName: 'João Silva',
        customerEmail: '<EMAIL>',
        customerPhone: '123456789',
        items: [
          { productId: 'prod1', quantity: 5, price: 29.99 }
        ]
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      // Mock product with insufficient stock
      mockPrisma.product.findMany.mockResolvedValue([
        {
          id: 'prod1',
          name: 'Test Product',
          price: 29.99,
          stock: 2 // Less than requested quantity
        }
      ])

      const response = await POST(mockRequest(orderData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toContain('Stock insuficiente')
    })
  })

  describe('GET - List Orders', () => {
    const mockRequest = (searchParams: string = '') => ({
      url: `http://localhost/api/shop/test-shop/orders${searchParams}`,
    }) as NextRequest

    const mockParams = { params: { subdomain: 'test-shop' } }

    it('should list orders successfully', async () => {
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      const mockOrders = [
        {
          id: 'order1',
          orderNumber: 'ORD-TEST-SHOP-0001',
          customerEmail: '<EMAIL>',
          total: 59.98,
          status: 'pending'
        }
      ]

      mockPrisma.order.findMany.mockResolvedValue(mockOrders)
      mockPrisma.order.count.mockResolvedValue(1)

      const response = await GET(mockRequest(), mockParams)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.orders).toEqual(mockOrders)
      expect(result.pagination.total).toBe(1)
    })

    it('should filter orders by customer email', async () => {
      mockPrisma.profile.findFirst.mockResolvedValue({
        id: 'shop1',
        customSubdomain: 'test-shop'
      })

      mockPrisma.order.findMany.mockResolvedValue([])
      mockPrisma.order.count.mockResolvedValue(0)

      const response = await GET(
        mockRequest('?customerEmail=<EMAIL>'),
        mockParams
      )

      expect(mockPrisma.order.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            customerEmail: '<EMAIL>'
          })
        })
      )
    })

    it('should return error when shop not found', async () => {
      mockPrisma.profile.findFirst.mockResolvedValue(null)

      const response = await GET(mockRequest(), mockParams)
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Loja não encontrada')
    })
  })
})
