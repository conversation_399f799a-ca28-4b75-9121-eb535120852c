import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const problemTypeId = searchParams.get('problemTypeId')
    const deviceId = searchParams.get('deviceId')
    const customerLocation = searchParams.get('location') // Para cálculo de distância

    if (!categoryId || !problemTypeId) {
      return NextResponse.json(
        { message: 'Categoria e tipo de problema são obrigatórios' },
        { status: 400 }
      )
    }

    console.log('Buscando lojas para:', { categoryId, problemTypeId, deviceId })

    // Buscar lojistas que têm preços para esta categoria e tipo de problema
    const repairShops = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP',
        repairShopPrices: {
          some: {
            OR: [
              {
                deviceModelId: deviceId,
                problemTypeId: problemTypeId,
                isActive: true
              },
              {
                categoryId: categoryId,
                problemTypeId: problemTypeId,
                deviceModelId: null,
                isActive: true
              }
            ]
          }
        }
      },
      include: {
        profile: true,
        repairShopPrices: {
          where: {
            OR: [
              {
                deviceModelId: deviceId,
                problemTypeId: problemTypeId,
                isActive: true
              },
              {
                categoryId: categoryId,
                problemTypeId: problemTypeId,
                deviceModelId: null,
                isActive: true
              }
            ]
          }
        },
        repairShopRepairs: {
          include: {
            review: true
          }
        }
      }
    })

    console.log('Lojas encontradas:', repairShops.length)

    // Processar dados das lojas
    const processedShops = repairShops.map(shop => {
      // Encontrar preço específico para este serviço
      let price = null
      let estimatedTime = null

      // Primeiro tentar preço específico para o modelo
      let specificPrice = shop.repairShopPrices.find(p => 
        p.deviceModelId === deviceId && p.problemTypeId === problemTypeId
      )

      // Se não houver, usar preço geral da categoria
      if (!specificPrice) {
        specificPrice = shop.repairShopPrices.find(p => 
          p.categoryId === categoryId && p.problemTypeId === problemTypeId && !p.deviceModelId
        )
      }

      if (specificPrice) {
        price = Number(specificPrice.price)
        estimatedTime = specificPrice.estimatedTime || 60
      } else {
        // Se não há preço específico, pular esta loja
        return null
      }

      // Calcular rating real baseado nas reviews
      const reviews = shop.repairShopRepairs
        .map(repair => repair.review)
        .filter(review => review !== null)

      let rating = 0
      let reviewCount = reviews.length

      if (reviewCount > 0) {
        const totalRating = reviews.reduce((sum, review) => sum + Number(review.rating), 0)
        rating = totalRating / reviewCount
      } else {
        // Se não há reviews, usar rating padrão baixo
        rating = 3.0
        reviewCount = 0
      }

      // Calcular distância (simulado por agora)
      const distance = Math.floor(Math.random() * 20) + 1 // Entre 1 e 20 km

      return {
        id: shop.id,
        name: shop.profile?.companyName || shop.name || 'Loja de Reparações',
        description: shop.profile?.description || 'Especialista em reparações',
        rating: Math.round(rating * 10) / 10,
        reviewCount: reviewCount,
        price: price,
        estimatedTime: estimatedTime || 120, // Retornar número em minutos
        estimatedTimeFormatted: estimatedTime && !isNaN(estimatedTime) ?
          (estimatedTime >= 60 ?
            `${Math.floor(estimatedTime / 60)}h ${estimatedTime % 60}min` :
            `${estimatedTime}min`) :
          'A consultar',
        distance: distance,
        serviceRadius: shop.profile?.serviceRadius || 10,
        phone: shop.profile?.phone || '',
        specialties: {
          categories: shop.profile?.workingCategories || [],
          brands: shop.profile?.workingBrands || [],
          problems: shop.profile?.workingProblems || []
        },
        availability: checkShopAvailability(shop.profile?.businessHours)
      }
    }).filter(shop => shop !== null) // Remover lojas sem preços

    // Ordenar por critérios de matching (distância, rating, preço)
    const sortedShops = processedShops.sort((a, b) => {
      // Primeiro por distância (mais próximo primeiro)
      if (a.distance !== b.distance) {
        return a.distance - b.distance
      }
      
      // Depois por rating (maior primeiro)
      if (a.rating !== b.rating) {
        return b.rating - a.rating
      }
      
      // Por último por preço (menor primeiro)
      return a.price - b.price
    })

    return NextResponse.json({
      shops: sortedShops,
      total: sortedShops.length,
      searchCriteria: {
        categoryId,
        problemTypeId,
        deviceId,
        customerLocation
      }
    })

  } catch (error) {
    console.error('Erro ao buscar lojas disponíveis:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function checkShopAvailability(businessHours: any) {
  const now = new Date()
  const currentDay = now.getDay() // 0 = domingo, 1 = segunda, etc.
  const currentTime = now.getHours() * 60 + now.getMinutes() // minutos desde meia-noite

  // Mapear dia da semana para chave do businessHours
  const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const todayKey = dayKeys[currentDay]

  // Horários padrão se não estiverem definidos
  const defaultHours = {
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '18:00', closed: false },
    friday: { open: '09:00', close: '18:00', closed: false },
    saturday: { open: '09:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: true }
  }

  const hours = businessHours || defaultHours
  const todayHours = hours[todayKey]

  if (!todayHours || todayHours.closed) {
    // Loja fechada hoje, encontrar próximo dia aberto
    const nextAvailable = findNextAvailableDay(hours, currentDay)
    return {
      isOpen: false,
      nextAvailable: nextAvailable
    }
  }

  // Converter horários para minutos
  const [openHour, openMin] = todayHours.open.split(':').map(Number)
  const [closeHour, closeMin] = todayHours.close.split(':').map(Number)
  const openTime = openHour * 60 + openMin
  const closeTime = closeHour * 60 + closeMin

  const isOpen = currentTime >= openTime && currentTime <= closeTime

  if (isOpen) {
    return {
      isOpen: true,
      nextAvailable: null
    }
  } else {
    // Loja fechada agora, calcular próxima abertura
    let nextAvailable
    if (currentTime < openTime) {
      // Ainda não abriu hoje
      const nextOpen = new Date()
      nextOpen.setHours(openHour, openMin, 0, 0)
      nextAvailable = nextOpen.toISOString()
    } else {
      // Já fechou hoje, próximo dia útil
      nextAvailable = findNextAvailableDay(hours, currentDay)
    }

    return {
      isOpen: false,
      nextAvailable: nextAvailable
    }
  }
}

function findNextAvailableDay(businessHours: any, currentDay: number) {
  const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']

  for (let i = 1; i <= 7; i++) {
    const nextDay = (currentDay + i) % 7
    const dayKey = dayKeys[nextDay]
    const dayHours = businessHours[dayKey]

    if (dayHours && !dayHours.closed) {
      const nextDate = new Date()
      nextDate.setDate(nextDate.getDate() + i)
      const [openHour, openMin] = dayHours.open.split(':').map(Number)
      nextDate.setHours(openHour, openMin, 0, 0)
      return nextDate.toISOString()
    }
  }

  // Se não encontrar nenhum dia aberto nos próximos 7 dias, retornar amanhã
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  tomorrow.setHours(9, 0, 0, 0)
  return tomorrow.toISOString()
}
