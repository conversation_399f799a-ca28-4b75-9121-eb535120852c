'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { ArrowLeft, Plus, X } from 'lucide-react'
import Link from 'next/link'

export default function EditPlanoPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    monthlyPrice: '',
    yearlyPrice: '',
    features: [] as string[],
    maxProducts: '',
    maxRepairs: '',
    marketplaceCommission: '5.0',
    repairCommission: '5.0',
    smsAccess: false,
    whatsappAccess: false,
    emailSupport: true,
    paymentDelayDays: '7',
    sparePartsDiscount: '0.0',
    recommendedProductsEnabled: false,
    recommendedProductsDays: '0',
    certifiedBadge: false,
    priority: '0',
    moloniIntegration: false,
    miniStore: false,
    individualRepairs: false,
    isPopular: false,
    isActive: true,
    availableApps: [] as string[]
  })
  const [newFeature, setNewFeature] = useState('')

  useEffect(() => {
    if (params.id) {
      fetchPlan()
    }
  }, [params.id])

  const fetchPlan = async () => {
    try {
      const response = await fetch(`/api/admin/subscription-plans/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        const plan = data.plan
        setFormData({
          name: plan.name,
          description: plan.description || '',
          monthlyPrice: plan.monthlyPrice.toString(),
          yearlyPrice: plan.yearlyPrice.toString(),
          features: plan.features || [],
          maxProducts: plan.maxProducts?.toString() || '',
          maxRepairs: plan.maxRepairs?.toString() || '',
          marketplaceCommission: plan.marketplaceCommission?.toString() || '5.0',
          repairCommission: plan.repairCommission?.toString() || '5.0',
          smsAccess: plan.smsAccess || false,
          whatsappAccess: plan.whatsappAccess || false,
          emailSupport: plan.emailSupport !== false,
          paymentDelayDays: plan.paymentDelayDays?.toString() || '7',
          sparePartsDiscount: plan.sparePartsDiscount?.toString() || '0.0',
          recommendedProductsEnabled: plan.recommendedProductsEnabled || false,
          recommendedProductsDays: plan.recommendedProductsDays?.toString() || '0',
          certifiedBadge: plan.certifiedBadge || false,
          priority: plan.priority?.toString() || '0',
          moloniIntegration: plan.moloniIntegration || false,
          miniStore: plan.miniStore || false,
          individualRepairs: plan.individualRepairs || false,
          isPopular: plan.isPopular || false,
          isActive: plan.isActive !== false,
          availableApps: plan.availableApps || []
        })
      } else if (response.status === 404) {
        router.push('/admin/planos')
      }
    } catch (error) {
      console.error('Erro ao buscar plano:', error)
    }
  }

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData({
        ...formData,
        features: [...formData.features, newFeature.trim()]
      })
      setNewFeature('')
    }
  }

  const removeFeature = (index: number) => {
    setFormData({
      ...formData,
      features: formData.features.filter((_, i) => i !== index)
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/subscription-plans/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          monthlyPrice: parseFloat(formData.monthlyPrice),
          yearlyPrice: parseFloat(formData.yearlyPrice),
          maxProducts: formData.maxProducts ? parseInt(formData.maxProducts) : null,
          maxRepairs: formData.maxRepairs ? parseInt(formData.maxRepairs) : null,
          marketplaceCommission: parseFloat(formData.marketplaceCommission),
          repairCommission: parseFloat(formData.repairCommission),
          paymentDelayDays: parseInt(formData.paymentDelayDays),
          sparePartsDiscount: parseFloat(formData.sparePartsDiscount),
          recommendedProductsEnabled: formData.recommendedProductsEnabled,
          recommendedProductsDays: parseInt(formData.recommendedProductsDays),
          priority: parseInt(formData.priority)
        })
      })

      if (response.ok) {
        alert('Plano atualizado com sucesso!')
        router.push('/admin/planos')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar plano')
      }
    } catch (error) {
      console.error('Erro ao atualizar plano:', error)
      alert('Erro ao atualizar plano')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/planos"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Editar Plano</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Informações Básicas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome do Plano *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isPopular}
                    onChange={(e) => setFormData({...formData, isPopular: e.target.checked})}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Plano Popular</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Ativo</span>
                </label>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>
          </div>

          {/* Preços */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preços</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preço Mensal (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.monthlyPrice}
                  onChange={(e) => setFormData({...formData, monthlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preço Anual (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.yearlyPrice}
                  onChange={(e) => setFormData({...formData, yearlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Funcionalidades</h3>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lista de Funcionalidades
              </label>
              <div className="flex mb-3">
                <input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Ex: Dashboard avançado"
                />
                <button
                  type="button"
                  onClick={addFeature}
                  className="px-4 py-2 bg-black text-white rounded-r-lg hover:bg-gray-800"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {formData.features.length > 0 && (
                <div className="space-y-2">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">{feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Checkboxes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.smsAccess}
                  onChange={(e) => setFormData({...formData, smsAccess: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Acesso a SMS</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.whatsappAccess}
                  onChange={(e) => setFormData({...formData, whatsappAccess: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Acesso a WhatsApp</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.certifiedBadge}
                  onChange={(e) => setFormData({...formData, certifiedBadge: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Badge Revify Certificada</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.moloniIntegration}
                  onChange={(e) => setFormData({...formData, moloniIntegration: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Integração Moloni</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.miniStore}
                  onChange={(e) => setFormData({...formData, miniStore: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Mini Store</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.individualRepairs}
                  onChange={(e) => setFormData({...formData, individualRepairs: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Reparações Individuais</span>
              </label>
            </div>
          </div>

          {/* Marketplace Features */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Funcionalidades do Marketplace</h3>
            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.recommendedProductsEnabled}
                  onChange={(e) => setFormData({...formData, recommendedProductsEnabled: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Produtos Recomendados Habilitados</span>
              </label>

              {formData.recommendedProductsEnabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dias de Destaque para Produtos Recomendados
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.recommendedProductsDays}
                    onChange={(e) => setFormData({...formData, recommendedProductsDays: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ex: 30 (dias)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Número de dias que os produtos ficam em destaque na seção de recomendados. 0 = sem limite.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Apps Disponíveis */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Apps Disponíveis</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { id: 'moloni', name: 'Moloni', description: 'Integração fiscal' },
                { id: 'crm-advanced', name: 'CRM Avançado', description: 'Gestão de clientes' },
                { id: 'newsletter-pro', name: 'Newsletter Pro', description: 'Email marketing' },
                { id: 'tax-calculator', name: 'Calculadora Impostos', description: 'Cálculos fiscais' },
                { id: 'inventory-manager', name: 'Gestor Inventário', description: 'Gestão de stock' },
                { id: 'whatsapp-business', name: 'WhatsApp Business', description: 'Comunicação' }
              ].map((app) => (
                <label key={app.id} className="flex items-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <input
                    type="checkbox"
                    checked={formData.availableApps.includes(app.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData({
                          ...formData,
                          availableApps: [...formData.availableApps, app.id]
                        })
                      } else {
                        setFormData({
                          ...formData,
                          availableApps: formData.availableApps.filter(id => id !== app.id)
                        })
                      }
                    }}
                    className="mr-3 mt-1"
                  />
                  <div>
                    <div className="font-medium text-gray-900">{app.name}</div>
                    <div className="text-sm text-gray-500">{app.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Submit */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/planos"
              className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isLoading ? 'Atualizando...' : 'Atualizar Plano'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
