'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Bell, Search, Menu, X, Package, Users, Wrench, ShoppingCart, FileText } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
export default function ModernDashboardHeader() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', 'handleScroll')
    return () => window.removeEventListener('scroll', 'handleScroll')
  }, [])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.length >= 2) {
        performSearch(searchQuery)
      } else {
        setSearchResults([])
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const performSearch = async (query: string) => {
    if (!query || query.length < 2) return

    console.log(🔍 Iniciando pesquisa:, 'query')
    setIsSearching(true)
    try {
      const response = await fetch(`/api/lojista/search?q=${encodeURIComponent(query)
}`)
      console.log('🔍 Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('🔍 Dados recebidos:', 'data')
        setSearchResults(data.results || [])
      } else {
        console.error('🔍 Erro na resposta:', response.status, response.statusText)
        const errorData = await response.text()
        console.error('🔍 Erro detalhes:', 'errorData')
        setSearchResults([])
      }
    } catch (error) {
      console.error('Erro na pesquisa:', 'error')
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <Package className="w-4 h-4" />
      case 'customer':
        return <Users className="w-4 h-4" />
      case 'repair':
        return <Wrench className="w-4 h-4" />
      default:
        return <Search className="w-4 h-4" />
    }
  }

  const getResultTypeLabel = (type: string) => {
    switch (type) {
      case 'product':
        return 'Produto'
      case 'customer':
        return 'Cliente'
      case 'repair':
        return 'Reparação'
      default:
        return 'Resultado'
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchResults.length > 0) {
      router.push(searchResults[0].url)
      setSearchQuery('')
      setShowSearchResults(false)
    }
  }

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-xl border-b border-gray-200/50 shadow-lg' 
        : 'bg-white/90 backdrop-blur-sm border-b border-gray-200/30'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/lojista" className="flex items-center space-x-2">
              <div className="hidden sm:block">
                <span className="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  Revify
                </span>
                <div className="text-xs text-gray-500 -mt-1">Dashboard</div>
              </div>
            </Link>
          </div>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md mx-8 relative">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Pesquisar produtos, clientes, reparações..."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value)
                    setShowSearchResults(e.target.value.length > 0)
                  }}
                  onFocus={() => setShowSearchResults(searchQuery.length > 0)}
                  onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                />
              </div>
            </form>

            {/* Search Results Dropdown */}
            {showSearchResults && searchQuery && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                {isSearching ? (
                  <div className="px-4 py-3 text-center text-gray-500 text-sm">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600 mx-auto mb-2"></div>
                    Pesquisando...
                  </div>
                ) : searchResults.length > 0 ? (
                  searchResults.map((result, index) => (
                    <button
                      key={`${result.type}-${result.id}`}
                      onClick={() => {
                        router.push(result.url)
                        setSearchQuery('')
                        setShowSearchResults(false)
                      }}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-start space-x-3 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="text-indigo-600 mt-0.5">
                        {getResultIcon(result.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900 truncate">{result.title}</span>
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                            {getResultTypeLabel(result.type)}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 truncate">{result.subtitle}</div>
                        {result.description && (
                          <div className="text-xs text-gray-500 truncate mt-1">{result.description}</div>
                        )}
                      </div>
                    </button>
                  ))
                ) : searchQuery.length >= 2 ? (
                  <div className="px-4 py-3 text-gray-500 text-sm">
                    Nenhum resultado encontrado para "{searchQuery}"
                  </div>
                ) : (
                  <div className="px-4 py-3 text-gray-500 text-sm">Digite pelo menos 2 caracteres para pesquisar</div>
                )}
              </div>
            )}
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5 text-gray-600" />
              ) : (
                <Menu className="w-5 h-5 text-gray-600" />
              )}
            </button>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <NotificationDropdown />
              <UserDropdown user={session?.user} />
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            {/* Mobile Search */}
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Pesquisar..."
                  className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>
            </div>
            
            {/* Mobile Navigation */}
            <div className="flex items-center justify-between">
              <NotificationDropdown />
              <UserDropdown user={session?.user} />
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
