const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testSubscriptionAPI() {
  try {
    console.log('🧪 Testando lógica da API de subscription...\n')

    const userId = 'cmdoeycm10000tbpshto54h0x' // ID do lojista teste

    console.log(`👤 Testando para usuário: ${userId}\n`)

    // Simular a mesma lógica da função checkSubscriptionAccess
    console.log('1️⃣ Executando query da API...')
    const subscriptions = await prisma.$queryRaw`
      SELECT 
        s.*,
        sp.name as plan_name,
        sp.features,
        sp."maxProducts",
        sp."maxRepairs"
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    console.log(`📊 Resultado da query: ${subscriptions.length} registros encontrados`)

    if (subscriptions.length === 0) {
      console.log('❌ Nenhuma subscription encontrada - retornaria hasActiveSubscription: false')
      return
    }

    const subscription = subscriptions[0]
    console.log('\n📋 Dados da subscription encontrada:')
    console.log(`   Status: ${subscription.status}`)
    console.log(`   Plano: ${subscription.plan_name}`)
    console.log(`   ID: ${subscription.id}`)
    console.log(`   Período: ${subscription.currentPeriodStart} - ${subscription.currentPeriodEnd}`)

    // Simular a lógica de verificação de status
    if (subscription.status === 'ACTIVE') {
      console.log('\n✅ Status é ACTIVE - retornaria:')
      console.log('   hasActiveSubscription: true')
      console.log('   hasPendingPayment: false')
      console.log('   subscriptionStatus: "ACTIVE"')
      console.log(`   planName: "${subscription.plan_name}"`)
    } else if (subscription.status === 'INCOMPLETE') {
      console.log('\n⚠️ Status é INCOMPLETE - verificando pagamentos pendentes...')
      
      const pendingPayment = await prisma.subscriptionPayment.findFirst({
        where: {
          subscriptionId: subscription.id,
          status: 'PENDING'
        }
      })

      console.log(`   Pagamento pendente: ${pendingPayment ? 'SIM' : 'NÃO'}`)
      console.log('   Retornaria:')
      console.log('   hasActiveSubscription: false')
      console.log(`   hasPendingPayment: ${!!pendingPayment}`)
      console.log('   subscriptionStatus: "INCOMPLETE"')
      console.log(`   planName: "${subscription.plan_name}"`)
    }

    // Testar também a API de subscription completa
    console.log('\n2️⃣ Testando API de subscription completa...')
    const fullSubscription = await prisma.$queryRaw`
      SELECT
        s.*,
        sp.name as plan_name,
        sp.features as plan_features,
        sp."monthlyPrice" as plan_monthly_price,
        sp."yearlyPrice" as plan_yearly_price
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    if (fullSubscription.length > 0) {
      console.log('✅ API de subscription completa também encontrou dados')
      const sub = fullSubscription[0]
      console.log(`   Plano: ${sub.plan_name}`)
      console.log(`   Preço mensal: €${sub.plan_monthly_price}`)
      console.log(`   Status: ${sub.status}`)
    } else {
      console.log('❌ API de subscription completa não encontrou dados')
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testSubscriptionAPI()
