'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Mail, Users, Send, Plus, Edit, Trash2, Eye, Upload } from 'lucide-react'
import Link from 'next/link'
import ImportContactsModal from '@/components/newsletter/ImportContactsModal'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  content: string
  createdAt: string
  isActive: boolean
}

interface EmailList {
  id: string
  name: string
  description: string
  subscriberCount: number
  createdAt: string
}

interface Campaign {
  id: string
  name: string
  subject: string
  templateId: string
  listId: string
  status: 'DRAFT' | 'SCHEDULED' | 'SENT'
  sentAt?: string
  openRate?: number
  clickRate?: number
  createdAt: string
}

export default function NewsletterProPage() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState<'campaigns' | 'templates' | 'lists' | 'analytics'>('campaigns')
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [lists, setLists] = useState<EmailList[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [modalType, setModalType] = useState<'campaign' | 'template' | 'list'>('campaign')
  const [showImportModal, setShowImportModal] = useState(false)
  const [selectedListId, setSelectedListId] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [campaignsRes, templatesRes, listsRes] = await Promise.all([
        fetch('/api/lojista/apps/newsletter-pro/campaigns'),
        fetch('/api/lojista/apps/newsletter-pro/templates'),
        fetch('/api/lojista/apps/newsletter-pro/lists')
      ])

      if (campaignsRes.ok) {
        const data = await campaignsRes.json()
        setCampaigns(data.campaigns)
      }

      if (templatesRes.ok) {
        const data = await templatesRes.json()
        setTemplates(data.templates)
      }

      if (listsRes.ok) {
        const data = await listsRes.json()
        setLists(data.lists)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const openCreateModal = (type: 'campaign' | 'template' | 'list') => {
    setModalType(type)
    setShowCreateModal(true)
  }

  const openImportModal = (listId: string) => {
    setSelectedListId(listId)
    setShowImportModal(true)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/appstore"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <div className="flex items-center">
                <Mail className="w-6 h-6 mr-2 text-blue-600" />
                <h1 className="text-2xl font-bold text-black">Newsletter Pro</h1>
              </div>
            </div>
            <button
              onClick={() => openCreateModal(activeTab as any)}
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            >
              <Plus className="w-4 h-4 mr-2" />
              {activeTab === 'campaigns' ? 'Nova Campanha' :
               activeTab === 'templates' ? 'Novo Template' :
               activeTab === 'lists' ? 'Nova Lista' : 'Criar'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('campaigns')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'campaigns'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Send className="w-4 h-4 inline mr-2" />
                Campanhas ({campaigns.length})
              </button>
              <button
                onClick={() => setActiveTab('templates')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'templates'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Edit className="w-4 h-4 inline mr-2" />
                Templates ({templates.length})
              </button>
              <button
                onClick={() => setActiveTab('lists')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'lists'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Users className="w-4 h-4 inline mr-2" />
                Listas ({lists.length})
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Analytics
              </button>
            </nav>
          </div>
        </div>

        {/* Campaigns Tab */}
        {activeTab === 'campaigns' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Campanhas de Email</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Assunto
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Taxa Abertura
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {campaigns.map((campaign) => (
                    <tr key={campaign.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {campaign.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {campaign.subject}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          campaign.status === 'SENT' ? 'bg-green-100 text-green-800' :
                          campaign.status === 'SCHEDULED' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {campaign.status === 'SENT' ? 'Enviada' :
                           campaign.status === 'SCHEDULED' ? 'Agendada' : 'Rascunho'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {campaign.openRate ? `${campaign.openRate}%` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {campaign.sentAt ? new Date(campaign.sentAt).toLocaleDateString('pt-PT') :
                         new Date(campaign.createdAt).toLocaleDateString('pt-PT')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <Edit className="w-4 h-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {campaigns.length === 0 && (
                <div className="text-center py-12">
                  <Send className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nenhuma campanha criada
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Crie sua primeira campanha de email marketing.
                  </p>
                  <button
                    onClick={() => openCreateModal('campaign')}
                    className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Nova Campanha
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <div key={template.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-4">{template.subject}</p>
                <div className="text-xs text-gray-500">
                  Criado em {template.createdAt ? new Date(template.createdAt).toLocaleDateString('pt-PT') : 'Data não disponível'}
                </div>
              </div>
            ))}
            
            {templates.length === 0 && (
              <div className="col-span-full text-center py-12">
                <Edit className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum template criado
                </h3>
                <p className="text-gray-500 mb-4">
                  Crie templates reutilizáveis para suas campanhas.
                </p>
                <button
                  onClick={() => openCreateModal('template')}
                  className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Novo Template
                </button>
              </div>
            )}
          </div>
        )}

        {/* Lists Tab */}
        {activeTab === 'lists' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Listas de Contactos</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Descrição
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Subscritores
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data Criação
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {lists.map((list) => (
                    <tr key={list.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {list.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {list.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {list.subscriberCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {list.createdAt ? new Date(list.createdAt).toLocaleDateString('pt-PT') : 'Data não disponível'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => openImportModal(list.id)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Importar contactos"
                        >
                          <Upload className="w-4 h-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900" title="Editar lista">
                          <Edit className="w-4 h-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900" title="Eliminar lista">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {lists.length === 0 && (
                <div className="text-center py-12">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nenhuma lista criada
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Crie listas para organizar seus contactos.
                  </p>
                  <button
                    onClick={() => openCreateModal('list')}
                    className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Nova Lista
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <Send className="w-8 h-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Campanhas Enviadas</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {campaigns.filter(c => c.status === 'SENT').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <Users className="w-8 h-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Subscritores</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {lists.reduce((sum, list) => sum + list.subscriberCount, 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <Eye className="w-8 h-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Taxa Abertura Média</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {campaigns.length > 0 
                      ? `${Math.round(campaigns.reduce((sum, c) => sum + (c.openRate || 0), 0) / campaigns.length)}%`
                      : '0%'
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <Edit className="w-8 h-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Templates Ativos</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {templates.filter(t => t.isActive).length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Modal */}
      {showCreateModal && (
        <CreateModal
          type={modalType}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
            // Refresh data based on type
            if (modalType === 'campaign') {
              fetchCampaigns()
            } else if (modalType === 'template') {
              fetchTemplates()
            } else if (modalType === 'list') {
              fetchLists()
            }
          }}
          templates={templates}
          lists={lists}
        />
      )}

      {/* Import Modal */}
      {showImportModal && selectedListId && (
        <ImportContactsModal
          listId={selectedListId}
          onClose={() => {
            setShowImportModal(false)
            setSelectedListId(null)
          }}
          onSuccess={() => {
            setShowImportModal(false)
            setSelectedListId(null)
            fetchLists()
          }}
        />
      )}
    </div>
  )
}

// Create Modal Component
function CreateModal({ type, onClose, onSuccess, templates, lists }: {
  type: 'campaign' | 'template' | 'list'
  onClose: () => void
  onSuccess: () => void
  templates: any[]
  lists: any[]
}) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    templateId: '',
    listId: '',
    description: '',
    recipientType: 'list',
    customerIds: [] as string[]
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const endpoint = `/api/lojista/apps/newsletter-pro/${type}s`
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        onSuccess()
      } else {
        console.error('Erro ao criar:', await response.text())
      }
    } catch (error) {
      console.error('Erro ao criar:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {type === 'campaign' ? 'Nova Campanha' :
           type === 'template' ? 'Novo Template' : 'Nova Lista'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              required
            />
          </div>

          {type === 'campaign' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assunto
                </label>
                <input
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template
                </label>
                <select
                  value={formData.templateId}
                  onChange={(e) => setFormData({ ...formData, templateId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  required
                >
                  <option value="">Selecionar template</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Destinatários
                </label>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="recipientType"
                      value="list"
                      checked={formData.recipientType === 'list'}
                      onChange={(e) => setFormData({ ...formData, recipientType: e.target.value, customerIds: [] })}
                      className="mr-2"
                    />
                    <span>Lista de subscritores</span>
                  </label>

                  {formData.recipientType === 'list' && (
                    <select
                      value={formData.listId}
                      onChange={(e) => setFormData({ ...formData, listId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black ml-6"
                      required
                    >
                      <option value="">Selecionar lista</option>
                      {lists.map((list) => (
                        <option key={list.id} value={list.id}>
                          {list.name} ({list.subscriberCount} subscritores)
                        </option>
                      ))}
                    </select>
                  )}

                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="recipientType"
                      value="customers"
                      checked={formData.recipientType === 'customers'}
                      onChange={(e) => setFormData({ ...formData, recipientType: e.target.value, listId: '' })}
                      className="mr-2"
                    />
                    <span>Clientes existentes</span>
                  </label>

                  {formData.recipientType === 'customers' && (
                    <CustomerSelector
                      selectedCustomers={formData.customerIds}
                      onSelectionChange={(customerIds) => setFormData({ ...formData, customerIds })}
                    />
                  )}
                </div>
              </div>
            </>
          )}

          {type === 'template' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Conteúdo HTML
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="<html>...</html>"
                required
              />
            </div>
          )}

          {type === 'list' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                placeholder="Descrição da lista..."
              />
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isLoading ? 'Criando...' : 'Criar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Customer Selector Component
function CustomerSelector({ selectedCustomers, onSelectionChange }: {
  selectedCustomers: string[]
  onSelectionChange: (customerIds: string[]) => void
}) {
  const [customers, setCustomers] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/lojista/apps/newsletter-pro/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data.customers)
      }
    } catch (error) {
      console.error('Erro ao carregar clientes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const toggleCustomer = (customerId: string) => {
    if (selectedCustomers.includes(customerId)) {
      onSelectionChange(selectedCustomers.filter(id => id !== customerId))
    } else {
      onSelectionChange([...selectedCustomers, customerId])
    }
  }

  const selectAll = () => {
    onSelectionChange(filteredCustomers.map(c => c.id))
  }

  const clearAll = () => {
    onSelectionChange([])
  }

  if (isLoading) {
    return <div className="text-center py-4">Carregando clientes...</div>
  }

  return (
    <div className="ml-6 border border-gray-300 rounded-lg p-4 max-h-64 overflow-y-auto">
      <div className="mb-3">
        <input
          type="text"
          placeholder="Buscar clientes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black text-sm"
        />
      </div>

      <div className="flex justify-between items-center mb-3 text-sm">
        <span>{selectedCustomers.length} de {filteredCustomers.length} selecionados</span>
        <div className="space-x-2">
          <button
            type="button"
            onClick={selectAll}
            className="text-blue-600 hover:underline"
          >
            Selecionar todos
          </button>
          <button
            type="button"
            onClick={clearAll}
            className="text-red-600 hover:underline"
          >
            Limpar
          </button>
        </div>
      </div>

      <div className="space-y-2">
        {filteredCustomers.map((customer) => (
          <label key={customer.id} className="flex items-center p-2 hover:bg-gray-50 rounded">
            <input
              type="checkbox"
              checked={selectedCustomers.includes(customer.id)}
              onChange={() => toggleCustomer(customer.id)}
              className="mr-3"
            />
            <div className="flex-1">
              <div className="font-medium text-sm">{customer.name}</div>
              <div className="text-xs text-gray-500">{customer.email}</div>
              <div className="text-xs text-gray-400">
                {customer.stats.orders} encomendas, {customer.stats.repairs} reparações
              </div>
            </div>
          </label>
        ))}
      </div>

      {filteredCustomers.length === 0 && (
        <div className="text-center py-4 text-gray-500">
          {searchTerm ? 'Nenhum cliente encontrado' : 'Nenhum cliente disponível'}
        </div>
      )}
    </div>
  )
}
