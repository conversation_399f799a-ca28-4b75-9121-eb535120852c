'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Calendar, Clock, ArrowLeft, Check } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface TimeSlot {
  time: string
  available: boolean
}

interface AvailableDay {
  date: string
  dayName: string
  slots: TimeSlot[]
}

export default function AgendamentoPage({ params }: { params: { id: string } }) {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [repair, setRepair] = useState<any>(null)
  const [availableDays, setAvailableDays] = useState<AvailableDay[]>([])
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')

  useEffect(() => {
    fetchRepairAndAvailability()
  }, [params.id])

  const fetchRepairAndAvailability = async () => {
    try {
      // Buscar dados da reparação
      const repairRes = await fetch(`/api/cliente/repairs/${params.id}`)
      if (repairRes.ok) {
        const repairData = await repairRes.json()
        setRepair(repairData)

        // Buscar disponibilidade da loja
        const availabilityRes = await fetch(`/api/cliente/availability/${repairData.repairShopId}`)
        if (availabilityRes.ok) {
          const availability = await availabilityRes.json()
          setAvailableDays(availability.days)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSchedule = async () => {
    if (!selectedDate || !selectedTime) {
      alert('Por favor selecione uma data e hora')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch(`/api/cliente/repairs/${params.id}/schedule`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scheduledDate: `${selectedDate}T${selectedTime}:00`,
          scheduledTime: selectedTime
        })
      })

      if (response.ok) {
        alert('Agendamento realizado com sucesso!')
        router.push(`/cliente/reparacoes/${params.id}`)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao agendar')
      }
    } catch (error) {
      console.error('Erro ao agendar:', error)
      alert('Erro ao agendar')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Agendamento</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href={`/cliente/reparacoes/${params.id}`} className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Voltar à Reparação
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Agendar Reparação</h1>

          {repair && (
            <div className="mb-8 p-4 bg-gray-50 rounded-lg">
              <h2 className="font-semibold text-gray-900 mb-2">Detalhes da Reparação</h2>
              <div className="text-sm text-gray-600 space-y-1">
                <p><span className="font-medium">Dispositivo:</span> {repair.deviceModel?.name}</p>
                <p><span className="font-medium">Problema:</span> {repair.problemType?.name}</p>
                <p><span className="font-medium">Loja:</span> {repair.repairShop?.profile?.companyName}</p>
                <p><span className="font-medium">Preço:</span> €{repair.estimatedPrice}</p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Seleção de Data */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Escolher Data
              </h3>
              <div className="space-y-2">
                {availableDays.map((day) => (
                  <button
                    key={day.date}
                    onClick={() => {
                      setSelectedDate(day.date)
                      setSelectedTime('') // Reset time when date changes
                    }}
                    className={`w-full p-3 text-left border rounded-lg transition-colors ${
                      selectedDate === day.date
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{day.dayName}</div>
                    <div className="text-sm text-gray-500">{new Date(day.date).toLocaleDateString('pt-PT')}</div>
                    <div className="text-xs text-green-600 mt-1">
                      {day.slots.filter(slot => slot.available).length} horários disponíveis
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Seleção de Hora */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Escolher Hora
              </h3>
              {selectedDate ? (
                <div className="grid grid-cols-3 gap-2">
                  {availableDays
                    .find(day => day.date === selectedDate)
                    ?.slots.map((slot) => (
                      <button
                        key={slot.time}
                        onClick={() => setSelectedTime(slot.time)}
                        disabled={!slot.available}
                        className={`p-2 text-sm border rounded-lg transition-colors ${
                          !slot.available
                            ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                            : selectedTime === slot.time
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {slot.time}
                      </button>
                    ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">Selecione uma data primeiro</p>
              )}
            </div>
          </div>

          {/* Resumo e Confirmação */}
          {selectedDate && selectedTime && (
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Resumo do Agendamento</h4>
              <div className="text-sm text-blue-700">
                <p><span className="font-medium">Data:</span> {new Date(selectedDate).toLocaleDateString('pt-PT', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</p>
                <p><span className="font-medium">Hora:</span> {selectedTime}</p>
              </div>
            </div>
          )}

          {/* Botão de Confirmação */}
          <div className="mt-8 flex justify-end">
            <button
              onClick={handleSchedule}
              disabled={!selectedDate || !selectedTime || isSaving}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Check className="w-5 h-5 mr-2" />
              {isSaving ? 'A agendar...' : 'Confirmar Agendamento'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
