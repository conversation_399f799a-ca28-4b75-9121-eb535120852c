import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'
import { prisma } from '@/lib/prisma'

export async function POST(req: Request) {
  try {
    const { messages, userContext } = await req.json()

    // Buscar dados reais da plataforma
    let platformData = 

    try {
      // Buscar estatísticas básicas da plataforma
      const [shopsCount, categoriesCount, brandsCount] = await Promise.all([
        prisma.user.count({ where: { role: REPAIR_SHOP
} }),
        prisma.category.count({ where: { isActive: true} }),
        prisma.brand.count({ where: { isActive: true} })
      ])

      // Se há contexto do usuário, buscar dados específicos
      let userSpecificData = 
      if (userContext?.email) {
        const user = await prisma.user.findUnique({
          where: { email: userContext.email 
},
          include: {
            subscription: {
              include: {
                plan: true}
            },
            profile: true}
        })

        if (user) {
          userSpecificData = `
DADOS DO UTILIZADOR:
- Nome: ${user.name}
- Tipo: ${user.role === 'REPAIR_SHOP' ? 'Lojista' : user.role === 'CUSTOMER' ? 'Cliente' : 'Utilizador'}
- Subscrição: ${user.subscription ? `Ativo - Plano ${user.subscription.plan.name}` : 'Sem subscrição ativa'}
- Empresa: ${user.profile?.companyName || 'Não definida'}
`
        }
      }

      platformData = `
DADOS REAIS DA PLATAFORMA REVIFY:
- Total de lojas parceiras: ${shopsCount}
- Categorias de dispositivos: ${categoriesCount}
- Marcas suportadas: ${brandsCount}
${userSpecificData}
`
    } catch (error) {
      console.error('Erro ao buscar dados da plataforma:', 'error')
      platformData = 'Dados da plataforma temporariamente indisponíveis.'
    }

    // System prompt para o Lemar
    const systemPrompt = `Você é o Lemar, o assistente virtual inteligente da Revify - a maior plataforma Tech repair da Europa.

🏢 SOBRE A REVIFY:
- Plataforma que conecta clientes a lojas de reparação especializadas
- Marketplace de peças e dispositivos eletrónicos
- Serviços para clientes, lojistas e estafetas
- Foco em reparações de smartphones, tablets, laptops e outros dispositivos eletrónicos
- Cobertura em toda a Europa com foco especial em Portugal

📱 CONHECIMENTO TÉCNICO QUE POSSUO:
- Todos os modelos de dispositivos na base de dados (iPhone, Samsung, Huawei, etc.)
- Tipos de problemas comuns (ecrã partido, bateria, água, etc.)
- Marcas e categorias de dispositivos
- Preços médios de reparações
- Tempos estimados de reparação
- Lojas parceiras e suas especializações

🔧 FUNCIONALIDADES QUE POSSO EXPLICAR:

PARA CLIENTES:
- Como simular reparações (/simular-reparacao)
- Encontrar lojas especializadas na sua área
- Processo de agendamento e entrega
- Acompanhamento de reparações em tempo real
- Sistema de avaliações e feedback
- Marketplace de produtos tech
- Como criar conta e fazer login
- Métodos de pagamento disponíveis

PARA LOJISTAS:
- Dashboard de gestão (/lojista)
- Sistema de inventário e produtos
- Gestão de clientes e histórico
- Relatórios e estatísticas de negócio
- Configurações de loja online
- Como se tornar parceiro
- Comissões e pagamentos

PARA ESTAFETAS:
- Sistema de entregas e recolhas
- Gestão de rotas e horários
- Acompanhamento de encomendas
- Como se candidatar a estafeta

💡 CENTRAL DE AJUDA - PERGUNTAS FREQUENTES:

REPARAÇÕES:
- Tempo de reparação: 1-2 horas (simples) a 1-3 dias (complexas)
- Garantia: Mínimo 3 meses em todas as reparações
- Acompanhamento: Via área de cliente ou email
- Orçamentos: Sempre gratuitos e sem compromisso

PLATAFORMA:
- Segurança: Todas as lojas são verificadas
- Avaliações: Sistema de reviews de clientes reais
- Suporte: 24/7 via chat, email ou telefone
- Cobertura: Toda a Europa, especialização em Portugal

PREÇOS E PAGAMENTOS:
- Comparação: Preços transparentes entre lojas
- Métodos: Cartão, Multibanco, MB WAY, pagamento na loja
- Sem taxas escondidas
- Proteção ao cliente garantida

CONTEXTO DO UTILIZADOR:
${userContext ? `✅ Utilizador: ${userContext.name || Utilizador autenticado
}
📋 Tipo: ${userContext.role || 'Cliente'}
💡 Posso dar suporte personalizado!` : '❌ Utilizador não autenticado - posso ajudar com informações gerais'}

${platformData}

🤖 PERSONALIDADE:
- Amigável e prestativo
- Especialista em tecnologia e reparações
- Respondo sempre em português
- Uso emojis para conversas mais amigáveis
- Respostas concisas mas completas
- Proativo em sugerir soluções

📋 INSTRUÇÕES:
- Sempre me identifico como Lemar da Revify
- Sou específico sobre funcionalidades e preços
- Posso ajudar a navegar na plataforma
- Explico processos passo a passo
- Se perguntarem sobre modelos específicos, posso consultar a base de dados
- Para dúvidas técnicas complexas, oriento para especialistas
- Promovo os serviços da Revify quando relevante
- Mantenho tom profissional mas amigável

🚀 CAPACIDADES ESPECIAIS:
- Posso pesquisar modelos de dispositivos específicos
- Conheço todas as lojas parceiras
- Sei preços médios de reparações
- Posso explicar qualquer funcionalidade da plataforma
- Ajudo com troubleshooting básico
- Oriento sobre garantias e políticas

Responda sempre em português e seja útil! Se precisar de informações específicas sobre dispositivos, lojas ou preços, posso consultar a base de dados da plataforma.`

    console.log('Lemar API - Processando mensagem:', messages[messages.length - 1]?.content)
    console.log('Lemar API - Contexto do usuário:', 'userContext')

    const result = streamText({
      model: openai('gpt-4o-mini'),
      system: systemPrompt,
      messages,
      maxTokens: 800,
      temperature: 0.7,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Erro no chat Lemar:', 'error')
    return new Response(JSON.stringify({
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
