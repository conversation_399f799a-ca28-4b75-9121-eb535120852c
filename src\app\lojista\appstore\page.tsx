'use client'

import { useState, useEffect } from 'react'
import { Download, Check, Star, Shield, Mail, Calculator, FileText, Users, Package, MessageCircle, Clock } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface App {
  id: string
  name: string
  description: string
  icon: string
  category: string
  price: number
  rating: number
  activations: number
  features: string[]
  screenshots: string[]
  isInstalled: boolean
  requiresPlan: string[]
  developer: string
  version: string
  hasTrialPeriod?: boolean
  trialDays?: number}

export default function AppStorePage() {
  const [apps, setApps] = useState<App[]>([])
  const [installedApps, setInstalledApps] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isLoading, setIsLoading] = useState(true)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [upgradeInfo, setUpgradeInfo] = useState<{
    appName: string
    currentPlan?: string
    requiredPlans: string[]
  } | null>(null)

  const [categories, setCategories] = useState([
    { id: all, name: Todas},
    { id: finance, name: Financeiro},
    { id: marketing, name: Marketing},
    { id: productivity, name: Produtividade},
    { id: integration, name: 'Integrações'}
  ])

  useEffect(() => {
    fetchApps()
  }, [])

  const fetchApps = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/lojista/apps/available')
      if (response.ok) {
        const data = await response.json()
        console.log('Apps data received:', 'data')
        setApps(data.apps)
        setCategories(data.categories)
        setInstalledApps(data.apps.filter((app: App) => app.isInstalled).map((app: App) => app.id))
      } else {
        console.error('Response not ok:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Erro ao buscar apps:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const installApp = async (appId: string) => {
    try {
      const response = await fetch('/api/lojista/apps/install', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 'appId'})
      })

      if (response.ok) {
        setInstalledApps(prev => [...prev, appId])
        alert('App instalada com sucesso!')
        fetchApps() // Refresh para atualizar status
} else if (response.status === 402) {
        const error = await response.json()
        const app = apps.find(a => a.id === 'appId')
        setUpgradeInfo({
          appName: app?.name || 'App',
          currentPlan: error.currentPlan,
          requiredPlans: error.requiredPlans || []
        })
        setShowUpgradeModal(true)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao instalar app')
      }
    } catch (error) {
      console.error('Erro ao instalar app:', 'error')
      alert('Erro ao instalar app')
    }
  }

  const uninstallApp = async (appId: string) => {
    if (!confirm('Tem certeza que deseja desinstalar esta app?')) return

    try {
      const response = await fetch('/api/lojista/apps/uninstall', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 'appId'})
      })

      if (response.ok) {
        setInstalledApps(prev => prev.filter(id => id !== 'appId'))
        alert('App desinstalada com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao desinstalar app')
      }
    } catch (error) {
      console.error('Erro ao desinstalar app:', 'error')
      alert('Erro ao desinstalar app')
    }
  }

  const filteredApps = selectedCategory === 'all'
    ? apps
    : apps.filter(app => app.category === 'selectedCategory')

  const getAppIcon = (iconName: string) => {
    const iconMap = {
      'FileText': FileText,
      'Users': Users,
      'Mail': Mail,
      'Calculator': Calculator,
      'Package': Package,
      'MessageCircle': 'MessageCircle'}
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || FileText
    return <IconComponent className="w-8 h-8 text-white" />
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            App Store
          </h1>
          <p className="text-muted-foreground">
            Expanda as funcionalidades da sua loja com aplicações especializadas
          </p>
        </div>
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="space-y-6">
        <TabsList className="flex w-full flex-wrap justify-center gap-1">
          {categories.map((category) => (
            <TabsTrigger key={category.id} value={category.id}>
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.id} value={category.id} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredApps.map((app) => {
                const isInstalled = installedApps.includes(app.id)

                return (
                  <Card key={app.id} className="overflow-hidden hover:shadow-lg transition-all duration-300">
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <div className="text-white">
                            {getAppIcon(app.icon)}
                          </div>
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-lg">{app.name}</CardTitle>
                          <CardDescription>{app.developer}</CardDescription>
                          <div className="flex items-center mt-1 space-x-2">
                            <div className="flex items-center text-sm">
                              <Star className="w-4 h-4 text-yellow-400 mr-1 fill-current" />
                              {app.rating}
                            </div>
                            <Badge variant="secondary" className="text-xs">
                              {app.activations} já usam esta app
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{app.description}</p>

                  {/* Features */}
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-2">
                      {app.features.slice(0, 2).map((feature, index) => (
                        <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                          <Check className="w-3 h-3 text-green-500 mr-1" />
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Plan Requirements */}
                  {app.requiresPlan.length > 0 && (
                    <div className="mb-4 p-3 bg-blue-50 rounded-xl border border-blue-200">
                      <div className="flex items-center text-sm text-blue-800">
                        <Shield className="w-4 h-4 mr-2" />
                        Requer plano: {app.requiresPlan.join(' ou ')}
                      </div>
                    </div>
                  )}

                  {/* Trial Notice */}
                  {app.hasTrialPeriod && app.price > 0 && (
                    <div className="mb-4 p-3 bg-green-50 rounded-xl border border-green-200">
                      <div className="flex items-center text-sm text-green-800">
                        <Clock className="w-4 h-4 mr-2" />
                        Trial gratuito de {app.trialDays} dias disponível
                      </div>
                    </div>
                  )}

                      {/* Price and Action */}
                      <div className="flex items-center justify-between pt-4 border-t">
                        <div>
                          {app.price === 0 ? (
                            <Badge variant="secondary" className="text-green-600">
                              Grátis
                            </Badge>
                          ) : (
                            <div>
                              <span className="text-lg font-bold">
                                €{app.price.toFixed(2)}
                              </span>
                              <span className="text-sm text-muted-foreground ml-1">
                                /mês
                              </span>
                            </div>
                          )}
                        </div>

                        {isInstalled ? (
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => uninstallApp(app.id)}
                              className="text-red-600 border-red-200 hover:bg-red-50"
                            >
                              Remover
                            </Button>
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <Check className="w-3 h-3 mr-1" />
                              Instalada
                            </Badge>
                          </div>
                        ) : (
                          <Button
                            onClick={() => installApp(app.id)}
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Obter
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {filteredApps.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📱</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhuma app encontrada
                </h3>
                <p className="text-gray-500">Não há apps disponíveis nesta categoria.</p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Modal de Upgrade */}
      {showUpgradeModal && upgradeInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-yellow-600" />
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">Upgrade Necessário</h3>

              <p className="text-gray-600 mb-4">
                A app <strong>{upgradeInfo.appName}</strong> requer um plano{' '}
                <strong>{upgradeInfo.requiredPlans.join(' ou ')}</strong> para ser instalada.
              </p>

              {upgradeInfo.currentPlan && (
                <p className="text-sm text-gray-500 mb-4">
                  Plano atual: <span className="font-medium">{upgradeInfo.currentPlan}</span>
                </p>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowUpgradeModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => {
                    setShowUpgradeModal(false)
                    window.location.href = '/lojista/upgrade'
                  }}
                  className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                >
                  Fazer Upgrade
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
