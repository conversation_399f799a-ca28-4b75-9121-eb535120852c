const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testSearch() {
  try {
    console.log('🔍 Testando pesquisa de produtos...')
    
    // Buscar o lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      }
    })
    
    if (!lojista) {
      console.log('❌ Lojista de teste não encontrado')
      return
    }
    
    console.log(`✅ Lojista encontrado: ${lojista.name} (${lojista.email})`)
    
    // Testar pesquisa por "iPhone"
    const products = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id,
        OR: [
          {
            name: {
              contains: 'iPhone',
              mode: 'insensitive'
            }
          },
          {
            description: {
              contains: 'iPhone',
              mode: 'insensitive'
            }
          }
        ]
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        }
      },
      take: 10,
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    console.log(`\n📱 Produtos encontrados para "iPhone": ${products.length}`)
    
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - €${Number(product.price).toFixed(2)}`)
      console.log(`   Categoria: ${product.category?.name || 'Sem categoria'}`)
      console.log(`   Marca: ${product.brand?.name || 'Sem marca'}`)
      console.log(`   Ativo: ${product.isActive ? 'Sim' : 'Não'}`)
      console.log(`   Stock: ${product.stock}`)
      console.log('')
    })
    
    // Testar pesquisa por "Samsung"
    const samsungProducts = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id,
        OR: [
          {
            name: {
              contains: 'Samsung',
              mode: 'insensitive'
            }
          },
          {
            description: {
              contains: 'Samsung',
              mode: 'insensitive'
            }
          }
        ]
      },
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } }
      }
    })
    
    console.log(`📱 Produtos encontrados para "Samsung": ${samsungProducts.length}`)
    samsungProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - €${Number(product.price).toFixed(2)}`)
    })
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testSearch()
