'use client'

import Link from 'next/link'
import { Users, TrendingUp, Award, Shield, 
  CheckCircle, Star, ArrowRight, Phone,
  Wrench, Euro, Clock, Target,
  Smartphone, Laptop, Tablet, Settings } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'
export default function BecomePartnerPage() {
  const { t } = useTranslation()

  const partnerTypes = [
    {
      icon: <Wrench className="w-8 h-8" />,
      title: 'Técnico de Reparações',
      description: 'Especialista em reparação de dispositivos eletrónicos',
      requirements: [
        Experiência mínima de 2 anos,
        Certificações técnicas,
        Espaço físico adequado,
        'Ferramentas profissionais'
      ],
      benefits: [
        Acesso a mais clientes,
        Sistema de gestão gratuito,
        'Pagamentos seguros',
        Suporte técnico
      ]
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Loja de Reparações',
      description: 'Estabelecimento comercial com equipa técnica',
      requirements: [
        Licença comercial válida,
        Equipa técnica qualificada,
        Instalações adequadas,
        'Seguro de responsabilidade'
      ],
      benefits: [
        'Maior visibilidade online',
        Gestão de agenda integrada,
        Marketing digital incluído,
        Relatórios detalhados
      ]
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: 'Centro de Assistência',
      description: 'Centro autorizado de marcas específicas',
      requirements: [
        Autorização das marcas,
        Certificações oficiais,
        Stock de peças originais,
        Formação contínua
      ],
      benefits: [
        Prioridade nas recomendações,
        Acesso a peças originais,
        Formação especializada,
        'Suporte premium'
      ]
    }
  ]

  const benefits = [
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: 'Aumente as Vendas',
      description: 'Acesso a milhares de clientes potenciais na sua área',
      stat: +150% 'vendas médias'},
    {
      icon: <Euro className="w-6 h-6" />,
      title: 'Pagamentos Seguros',
      description: 'Receba pagamentos automaticamente após cada reparação',
      stat: Pagamento em 24h
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: 'Gestão Simplificada',
      description: 'Sistema completo de gestão de clientes e reparações',
      stat: '70% menos tempo admin'
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: Credibilidade,
      description: 'Selo de qualidade Revify aumenta a confiança dos clientes',
      stat: 4.9/5 'avaliação média'}
  ]

  const process = [
    {
      step: 1,
      title: Candidatura,
      description: 'Preencha o formulário com os seus dados e experiência'},
    {
      step: 2,
      title: 'Verificação',
      description: 'Analisamos as suas qualificações e visitamos as instalações'},
    {
      step: 3,
      title: 'Formação',
      description: 'Formação gratuita sobre a plataforma e processos'},
    {
      step: 4,
      title: 'Ativação',
      description: 'Comece a receber clientes e a crescer o seu negócio'}
  ]

  const specializations = [
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: Smartphones,
      description: 'iPhone, Samsung, Huawei, Xiaomi',
      demand: 'Alta procura'
    },
    {
      icon: <Laptop className="w-6 h-6" />,
      title: Laptops,
      description: 'MacBook, Dell, HP, Lenovo',
      demand: 'Procura crescente'
    },
    {
      icon: <Tablet className="w-6 h-6" />,
      title: Tablets,
      description: 'iPad, Galaxy Tab, Surface',
      demand: 'Nicho especializado'
    }
  ]

  return (
    <ModernLayout>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-600 via-red-600 to-pink-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Users className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('becomePartnerTitle')}
            </h1>
            <p className="text-xl text-orange-100 max-w-3xl mx-auto mb-8">
              {t('becomePartnerSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#candidatura"
                className="bg-white text-orange-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Users className="w-5 h-5" />
                <span>Candidatar-me</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/contactos"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Falar Connosco</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Partner Types */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Tipos de Parceiros
            </h2>
            <p className="text-xl text-gray-600">Encontre a categoria que melhor se adequa ao seu negócio</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {partnerTypes.map((type, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all border border-gray-200">
                <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mb-6 text-orange-600">
                  {type.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{type.title}</h3>
                <p className="text-gray-600 mb-6">{type.description}</p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Requisitos:</h4>
                  <div className="space-y-2">
                    {type.requirements.map((req, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-gray-700 text-sm">{req}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Benefícios:</h4>
                  <div className="space-y-2">
                    {type.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Vantagens de Ser Parceiro Revify
            </h2>
            <p className="text-xl text-gray-600">Benefícios comprovados pelos nossos parceiros</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-6 text-orange-600">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{benefit.title}</h3>
                <p className="text-gray-600 mb-4">{benefit.description}</p>
                <div className="text-orange-600 font-bold text-lg">{benefit.stat}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Como Tornar-se Parceiro</h2>
            <p className="text-xl text-gray-600">
              Processo simples em 4 passos
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-orange-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Specializations */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Especializações em Demanda</h2>
            <p className="text-xl text-gray-600">Áreas com maior procura de técnicos especializados</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {specializations.map((spec, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-6 text-orange-600">
                  {spec.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{spec.title}</h3>
                <p className="text-gray-600 mb-4">{spec.description}</p>
                <div className="inline-flex items-center space-x-2 bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                  <Target className="w-4 h-4" />
                  <span>{spec.demand}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              O Que Dizem os Nossos Parceiros
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-2xl p-8">
              <div className="flex items-center mb-4">
                <div className="text-4xl mr-4">👨‍🔧</div>
                <div>
                  <div className="font-bold text-gray-900">Carlos Silva</div>
                  <div className="text-gray-600 text-sm">Técnico em Lisboa</div>
                </div>
              </div>
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600">
                Desde que me juntei à Revify, o meu negócio cresceu 200%.  A plataforma traz-me clientes constantemente.
              </p>
            </div>
            
            <div className="bg-gray-50 rounded-2xl p-8">
              <div className="flex items-center mb-4">
                <div className="text-4xl mr-4">👩‍💼</div>
                <div>
                  <div className="font-bold text-gray-900">Ana Costa</div>
                  <div className="text-gray-600 text-sm">Loja no Porto</div>
                </div>
              </div>
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600">
                O sistema de gestão é fantástico. Poupa-me horas de trabalho  administrativo todos os dias.
              </p>
            </div>
            
            <div className="bg-gray-50 rounded-2xl p-8">
              <div className="flex items-center mb-4">
                <div className="text-4xl mr-4">👨‍💻</div>
                <div>
                  <div className="font-bold text-gray-900">Miguel Santos</div>
                  <div className="text-gray-600 text-sm">Centro em Braga</div>
                </div>
              </div>
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-600">
                A Revify ajudou-me a profissionalizar o meu negócio.  Agora tenho uma agenda sempre cheia.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="candidatura" className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Pronto para Crescer o Seu Negócio?</h2>
          <p className="text-xl text-gray-300 mb-8">Junte-se a centenas de técnicos que já confiam na Revify</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup?role=repair_shop"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Users className="w-5 h-5" />
              <span>Candidatar-me Agora</span>
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
