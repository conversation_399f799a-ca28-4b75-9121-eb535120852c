-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "ActivityType" ADD VALUE 'USER_REGISTERED';
ALTER TYPE "ActivityType" ADD VALUE 'SHOP_CREATED';
ALTER TYPE "ActivityType" ADD VALUE 'REVIEW_LEFT';
ALTER TYPE "ActivityType" ADD VALUE 'HIGH_RATING_RECEIVED';
