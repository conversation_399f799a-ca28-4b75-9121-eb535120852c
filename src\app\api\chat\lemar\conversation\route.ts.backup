import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID é obrigatório' },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Buscar conversa existente
      const conversation = await prisma.lemarConversation.findUnique({
        where: {
          sessionId: sessionId
        }
      })

      if (!conversation) {
        return NextResponse.json({
          success: true,
          messages: []
        })
      }

      return NextResponse.json({
        success: true,
        messages: conversation.messages || []
      })

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error('Erro ao buscar conversa:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const body = await request.json()
    const { sessionId, messages } = body

    if (!sessionId || !messages) {
      return NextResponse.json(
        { error: 'Session ID e mensagens são obrigatórios' },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Salvar ou atualizar conversa
      const conversation = await prisma.lemarConversation.upsert({
        where: {
          sessionId: sessionId
        },
        update: {
          messages: messages,
          userId: session?.user?.id || null
        },
        create: {
          sessionId: sessionId,
          messages: messages,
          userId: session?.user?.id || null
        }
      })

      return NextResponse.json({
        success: true,
        conversation
      })

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error('Erro ao salvar conversa:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
