'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Check, Star, Crown, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
interface SubscriptionPlan {
  id: string
  name: string
  description: string | null
  monthlyPrice: number
  yearlyPrice: number
  features: string[]
  maxProducts: number | null
  maxRepairs: number | null
  marketplaceCommission: number
  repairCommission: number
  smsAccess: boolean
  whatsappAccess: boolean
  certifiedBadge: boolean
  priority: number
  isPopular: boolean}

interface UserSubscription {
  plan: {
    id: string
    name: string}
  status: string}

export default function UpgradePage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [plansRes, subscriptionRes] = await Promise.all([
        fetch('/api/subscription-plans'),
        fetch('/api/lojista/subscription')
      ])

      if (plansRes.ok) {
        const plansData = await plansRes.json()
        setPlans(plansData.plans)
      }

      if (subscriptionRes.ok) {
        const subscriptionData = await subscriptionRes.json()
        setCurrentSubscription(subscriptionData.subscription)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpgrade = async (planId: string) => {
    try {
      const response = await fetch('/api/lojista/upgrade', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ planId, billingCycle })
      })

      const data = await response.json()

      if (response.ok) {
        if (data.checkoutUrl) {
          window.location.href = data.checkoutUrl
        } else {
          alert('Upgrade realizado com sucesso!')
          router.push(/lojista/subscricao)
        }
      } else {
        alert(data.message || 'Erro ao processar upgrade')
      }
    } catch (error) {
      console.error('Erro no upgrade:', 'error')
      alert('Erro ao processar upgrade')
    }
  }

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.plan?.id === 'planId'}

  const getPrice = (plan: SubscriptionPlan) => {
    return billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice
  }

  const getSavings = (plan: SubscriptionPlan) => {
    const monthlyTotal = plan.monthlyPrice * 12
    const yearlyPrice = plan.yearlyPrice
    const savings = monthlyTotal - yearlyPrice
    return savings > 0 ? savings : 0
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Upgrade de Plano</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Billing Toggle */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Escolha o plano ideal para o seu negócio</h2>
          <p className="text-gray-600 mb-8">Desbloqueie funcionalidades avançadas e faça crescer a sua oficina</p>
          
          <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setBillingCycle('MONTHLY')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === 'MONTHLY'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Mensal
            </button>
            <button
              onClick={() => setBillingCycle('YEARLY')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === 'YEARLY'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Anual
              <span className="ml-1 text-xs text-green-600 font-semibold">(Poupe até 20%)</span>
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-2xl shadow-sm border-2 transition-all duration-200 ${
                plan.isPopular
                  ? 'border-blue-500 shadow-blue-100'
                  : isCurrentPlan(plan.id)
                  ? 'border-green-500 shadow-green-100'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              {/* Popular Badge */}
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center">
                    <Star className="w-4 h-4 mr-1" />
                    Mais Popular
                  </div>
                </div>
              )}

              {/* Current Plan Badge */}
              {isCurrentPlan(plan.id) && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center">
                    <Crown className="w-4 h-4 mr-1" />
                    Plano Atual
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  {plan.description && (
                    <p className="text-gray-600 text-sm">{plan.description}</p>
                  )}
                  
                  <div className="mt-6">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900">
                        €{Number(getPrice(plan)).toFixed(2)}
                      </span>
                      <span className="text-gray-500 ml-1">
                        /{billingCycle === 'MONTHLY' ? mês : 'ano'}
                      </span>
                    </div>
                    
                    {billingCycle === 'YEARLY' && getSavings(plan) > 0 && (
                      <div className="mt-2 text-sm text-green-600 font-semibold">
                        Poupe €{getSavings(plan).toFixed(2)} por ano
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </div>
                  ))}
                  
                  {/* Limits */}
                  <div className="pt-4 border-t border-gray-100">
                    <div className="flex items-start">
                      <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">
                        {plan.maxProducts ? `Até ${plan.maxProducts} produtos` : 'Produtos ilimitados'}
                      </span>
                    </div>
                    <div className="flex items-start mt-2">
                      <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">
                        {plan.maxRepairs ? `Até ${plan.maxRepairs} reparações/mês` : 'Reparações ilimitadas'}
                      </span>
                    </div>
                    
                    {/* Special Features */}
                    {plan.smsAccess && (
                      <div className="flex items-start mt-2">
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">Notificações SMS</span>
                      </div>
                    )}
                    
                    {plan.whatsappAccess && (
                      <div className="flex items-start mt-2">
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">Notificações WhatsApp</span>
                      </div>
                    )}
                    
                    {plan.certifiedBadge && (
                      <div className="flex items-start mt-2">
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">Badge "Revify Certificada"</span>
                      </div>
                    )}
                    
                    {plan.marketplaceCommission === 0 && (
                      <div className="flex items-start mt-2">
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">Sem comissão marketplace</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* CTA Button */}
                <div className="text-center">
                  {isCurrentPlan(plan.id) ? (
                    <button
                      disabled
                      className="w-full py-3 px-6 bg-green-100 text-green-800 rounded-lg font-semibold cursor-not-allowed"
                    >
                      Plano Atual
                    </button>
                  ) : (
                    <button
                      onClick={() => handleUpgrade(plan.id)}
                      className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                        plan.isPopular
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-black text-white hover:bg-gray-800'
                      }`}
                    >
                      Escolher {plan.name}
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Comparison Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Comparação Detalhada</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Funcionalidade
                  </th>
                  {plans.map(plan => (
                    <th key={plan.id} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {plan.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Comissão Marketplace</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                      {plan.marketplaceCommission}%
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Comissão Reparações</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                      {plan.repairCommission}%
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Prioridade nos Resultados
                  </td>
                  {plans.map(plan => (
                    <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                      {plan.priority > 0 ? (
                        <Check className="w-5 h-5 text-green-500 mx-auto" />
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Badge Certificada
                  </td>
                  {plans.map(plan => (
                    <td key={plan.id} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                      {plan.certifiedBadge ? (
                        <Check className="w-5 h-5 text-green-500 mx-auto" />
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
