import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Função para calcular próxima data de pagamento (considerando dias úteis)
function getNextPayoutDate(): string {
  const now = new Date()
  let nextPayout = new Date(now)

  // Pagamentos são feitos às sextas-feiras
  const daysUntilFriday = (5 - now.getDay() + 7) % 7
  nextPayout.setDate(now.getDate() + (daysUntilFriday === 0 ? 7 : daysUntilFriday))

  // Se for feriado, mover para próxima sexta
  // Aqui poderia haver lógica para feriados específicos

  return nextPayout.toISOString().split('T')[0]

}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    const transactions = await prisma.transaction.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        repair: {
          select: {
            id: true,
            description: true,
            status: true,
            deviceModel: {
              select: {
                name: true,
                brand: {
                  select: {
                    name: true}
                }
              }
            },
            problemType: {
              select: {
                name: true}
            },
            customer: {
              select: {
                name: true}
            }
          }
        },
        order: {
          select: {
            id: true,
            status: true}
        }
      },
      orderBy: {
        createdAt: desc}
    })

    // Calcular estatísticas
    const stats = {
      totalEarnings: transactions
        .filter(t => t.status === COMPLETED)
        .reduce((sum, t) => sum + Number(t.netAmount), 0),
      pendingAmount: transactions
        .filter(t => t.status === 'PENDING')
        .reduce((sum, t) => sum + Number(t.netAmount), 0),
      totalCommission: transactions
        .filter(t => t.status === 'COMPLETED')
        .reduce((sum, t) => sum + Number(t.commission || 0), 0),
      nextPayoutDate: getNextPayoutDate()
    
}

    return NextResponse.json({ transactions, stats })

  } catch (error) {
    console.error('Erro ao buscar transações:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
