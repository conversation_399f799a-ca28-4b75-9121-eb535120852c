'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import {
  Smartphone,
  Package,
  Clock,
  Star,
  Plus,
  Settings,
  User,
  MapPin
} from 'lucide-react'

interface DashboardStats {
  activeRepairs: number
  totalRepairs: number
  orders: number
  reviews: number
}

export default function ClienteDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats>({
    activeRepairs: 0,
    totalRepairs: 0,
    orders: 0,
    reviews: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (session?.user) {
      fetchStats()
    }
  }, [session])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/cliente/dashboard-stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const menuItems = [
    {
      title: 'Reparações',
      items: [
        { name: 'Nova Reparação', href: '/cliente/reparacoes/nova-v2', icon: Plus, description: 'Solicitar nova reparação' },
        { name: 'Minhas Reparações', href: '/cliente/reparacoes', icon: Smartphone, description: 'Acompanhar reparações' },
        { name: 'Histórico', href: '/cliente/reparacoes/historico', icon: Clock, description: 'Reparações anteriores' },
      ]
    },
    {
      title: 'Marketplace',
      items: [
        { name: 'Explorar Produtos', href: '/marketplace', icon: Package, description: 'Peças e acessórios' },
        { name: 'Minhas Encomendas', href: '/cliente/encomendas', icon: Package, description: 'Acompanhar encomendas' },
      ]
    },
    {
      title: 'Conta',
      items: [
        { name: 'Perfil', href: '/cliente/perfil', icon: User, description: 'Gerir dados pessoais' },
        { name: 'Endereços', href: '/cliente/enderecos', icon: MapPin, description: 'Gerir endereços' },
        { name: 'Configurações', href: '/cliente/configuracoes', icon: Settings, description: 'Preferências da conta' },
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Cliente</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white mb-8">
          <h1 className="text-2xl font-bold mb-2">Bem-vindo de volta!</h1>
          <p className="text-blue-100">
            Gerir as suas reparações e explorar produtos no marketplace.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Smartphone className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reparações Ativas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {isLoading ? '...' : stats.activeRepairs}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Package className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Reparações</p>
                <p className="text-2xl font-bold text-gray-900">
                  {isLoading ? '...' : stats.totalRepairs}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Encomendas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {isLoading ? '...' : stats.orders}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Sections */}
        <div className="space-y-8">
          {menuItems.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{section.title}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                      className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow group"
                    >
                      <div className="flex items-center mb-3">
                        <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-blue-600 group-hover:text-white transition-colors">
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <h3 className="ml-3 text-lg font-medium text-gray-900">{item.name}</h3>
                      </div>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
          <div className="flex flex-wrap gap-3">
            <Link
              href="/cliente/reparacoes/nova-v2"
              className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <Plus className="w-3 h-3 mr-2" />
              Nova Reparação
            </Link>
            <Link
              href="/marketplace"
              className="inline-flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
            >
              <Package className="w-3 h-3 mr-2" />
              Explorar Marketplace
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
