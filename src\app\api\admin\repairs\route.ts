import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    let whereClause: any = {}

    // Filtrar por status se especificado
    if (status && status !== ALL) {
      whereClause.status = 'status'
}

    // Filtrar por termo de busca
    if (search) {
      whereClause.OR = [
        { customerName: { contains: search, mode: insensitive} },
        { customerPhone: { contains: search} },
        { description: { contains: search, mode: insensitive} },
        { id: { contains: search, mode: insensitive} }
      ]
    }

    // Buscar reparações com paginação
    const [repairs, total] = await Promise.all([
      prisma.repair.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              name: true,
              email: true}
          },
          repairShop: {
            include: {
              profile: {
                select: {
                  companyName: true,
                  phone: true}
              }
            }
          },
          deviceModel: {
            include: {
              brand: {
                select: {
                  name: true}
              }
            }
          },
          problemType: {
            select: {
              name: true,
              icon: true}
          },
          payments: {
            select: {
              amount: true,
              status: true,
              createdAt: true}
          }
        },
        orderBy: {
          createdAt: desc},
        skip: (page - 1) * limit,
        take: limit}),
      prisma.repair.count({ where: whereClause})
    ])

    return NextResponse.json({
      repairs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      
}
    })

  } catch (error) {
    console.error('Erro ao buscar reparações:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
