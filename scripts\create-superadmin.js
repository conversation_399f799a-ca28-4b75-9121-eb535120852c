const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createSuperAdmin() {
  try {
    console.log('👤 Criando superadmin...')

    const hashedPassword = await bcrypt.hash('Teste123123_', 12)
    
    // Verificar se já existe
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('ℹ️  Usuário já existe, atualizando...')
      
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          role: 'ADMIN',
          isVerified: true,
          isSuperAdmin: true
        }
      })
      
      console.log('✅ Superadmin atualizado!')
    } else {
      await prisma.user.create({
        data: {
          email: 'carlos.te<PERSON><PERSON>@sellrocket.io',
          name: '<PERSON>',
          password: hashedPassword,
          role: 'ADMIN',
          isVerified: true,
          isSuperAdmin: true,
          profile: {
            create: {
              phone: '+351 912 000 000',
              address: 'Sede Revify',
              city: 'Lisboa',
              postalCode: '1000-001',
              country: 'PT'
            }
          }
        }
      })
      
      console.log('✅ Superadmin criado!')
    }

    console.log('\n🎉 Superadmin configurado com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: Teste123123_')

  } catch (error) {
    console.error('❌ Erro ao criar superadmin:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSuperAdmin()
