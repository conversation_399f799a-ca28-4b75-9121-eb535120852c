import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Modelo temporário para carrinho (em produção seria uma tabela separada)
interface CartItem {
  id: string
  productId: string
  quantity: number
  product: any
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        product: {
          include: {
            seller: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    const formattedItems = cartItems.map(item => ({
      id: item.id,
      productId: item.productId,
      quantity: item.quantity,
      product: {
        id: item.product.id,
        name: item.product.name,
        price: Number(item.product.price),
        images: item.product.images,
        condition: item.product.condition,
        seller: item.product.seller
      }
    }))

    const total = formattedItems.reduce((sum, item) =>
      sum + (item.product.price * item.quantity), 0
    )

    return NextResponse.json({
      items: formattedItems,
      total
    })

  } catch (error) {
    console.error('Erro ao buscar carrinho:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { productId, quantity = 1 } = await request.json()

    if (!productId) {
      return NextResponse.json(
        { message: 'ID do produto é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se o produto existe
    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: productId,
        isActive: true
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se já existe no carrinho
    const existingItem = await prisma.cartItem.findFirst({
      where: {
        userId: session.user.id,
        productId
      }
    })

    if (existingItem) {
      // Atualizar quantidade
      await prisma.cartItem.update({
        where: {
          id: existingItem.id
        },
        data: {
          quantity: existingItem.quantity + quantity
        }
      })
    } else {
      // Criar novo item
      await prisma.cartItem.create({
        data: {
          userId: session.user.id,
          productId,
          quantity
        }
      })
    }

    return NextResponse.json({
      message: 'Produto adicionado ao carrinho'
    })

  } catch (error) {
    console.error('Erro ao adicionar ao carrinho:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { itemId, quantity } = await request.json()

    if (!itemId || quantity < 1) {
      return NextResponse.json(
        { message: 'Dados inválidos' },
        { status: 400 }
      )
    }

    // Atualizar quantidade
    await prisma.cartItem.update({
      where: {
        id: itemId,
        userId: session.user.id
      },
      data: {
        quantity
      }
    })

    return NextResponse.json({
      message: 'Quantidade atualizada'
    })

  } catch (error) {
    console.error('Erro ao atualizar carrinho:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    const { itemId } = await request.json()

    if (!itemId) {
      return NextResponse.json(
        { message: 'ID do item é obrigatório' },
        { status: 400 }
      )
    }

    // Remover item do carrinho
    await prisma.cartItem.delete({
      where: {
        id: itemId,
        userId: session.user.id
      }
    })

    return NextResponse.json({
      message: 'Item removido do carrinho'
    })

  } catch (error) {
    console.error('Erro ao remover do carrinho:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
