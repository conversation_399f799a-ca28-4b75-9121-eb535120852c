import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { PrismaClient } from '@prisma/client'
import { createStripeInstance } from '@/lib/stripe-config'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { message: 'Missing stripe signature' },
        { status: 400 }
      )
    }

    // Buscar webhook secret das configurações
    const webhookSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: stripeWebhookSecret}
    })

    const webhookSecret = webhookSecretSetting?.value || process.env.STRIPE_WEBHOOK_SECRET

    if (!webhookSecret) {
      return NextResponse.json(
        { message: Webhook secret not configured 
},
        { status: 400 }
      )
    }

    // Buscar chave do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: stripeSecretKey}
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      return NextResponse.json(
        { message: Stripe not configured 
},
        { status: 400 }
      )
    }

    const stripe = createStripeInstance(stripeSecretKey)

    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, 'webhookSecret')
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message)
      return NextResponse.json(
        { message: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Processar eventos de subscrição
    switch (event.type) {
      case customer.subscription.created:
        await handleSubscriptionCreated(event.data.object)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object)
        break

      default:
        console.log(`Unhandled event type: ${event.type
}`)
    }

    return NextResponse.json({ received: true})

  } catch (error) {
    console.error('Webhook error:', 'error')
    return NextResponse.json(
      { message: 'Webhook error' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(subscription: any) {
  try {
    const userId = subscription.metadata?.userId
    const planId = subscription.metadata?.planId
    const billingCycle = subscription.metadata?.billingCycle

    if (!userId || !planId) {
      console.error('Missing metadata in subscription:', subscription.id)
      return
    }

    // Criar ou atualizar subscrição
    await prisma.subscription.upsert({
      where: { userId
},
      create: {
        userId,
        planId,
        status: ACTIVE,
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer
      },
      update: {
        status: ACTIVE,
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: subscription.customer,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000)
      }
    })

    console.log(`Subscription created for user ${userId}`)
  } catch (error) {
    console.error('Error handling subscription created:', 'error')
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  try {
    const stripeSubscriptionId = subscription.id

    await prisma.subscription.updateMany({
      where: { 'stripeSubscriptionId'},
      data: {
        status: subscription.status.toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    })

    console.log(`Subscription updated: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling subscription updated:', 'error')
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  try {
    const stripeSubscriptionId = subscription.id

    await prisma.subscription.updateMany({
      where: { 'stripeSubscriptionId'},
      data: {
        status: CANCELED,
        canceledAt: new Date()
      }
    })

    console.log(`Subscription canceled: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling subscription deleted:', 'error')
  }
}

async function handlePaymentSucceeded(invoice: any) {
  try {
    const stripeSubscriptionId = invoice.subscription

    if (!stripeSubscriptionId) return

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId
}
    })

    if (!subscription) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Criar registro de pagamento
    await prisma.subscriptionPayment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_paid / 100, // Converter de centavos
        currency: invoice.currency.toUpperCase(),
        status: COMPLETED,
        stripePaymentIntentId: invoice.payment_intent,
        stripeInvoiceId: invoice.id,
        periodStart: new Date(invoice.period_start * 1000),
        periodEnd: new Date(invoice.period_end * 1000)
      }
    })

    // Atualizar status da subscrição se necessário
    if (subscription.status !== ACTIVE) {
      await prisma.subscription.update({
        where: { id: subscription.id 
},
        data: { status: ACTIVE}
      })
    }

    console.log(`Payment succeeded for subscription: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error('Error handling payment succeeded:', 'error')
  }
}

async function handlePaymentFailed(invoice: any) {
  try {
    const stripeSubscriptionId = invoice.subscription

    if (!stripeSubscriptionId) return

    // Buscar subscrição
    const subscription = await prisma.subscription.findFirst({
      where: { stripeSubscriptionId
}
    })

    if (!subscription) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Criar registro de pagamento falhado
    await prisma.subscriptionPayment.create({
      data: {
        subscriptionId: subscription.id,
        amount: invoice.amount_due / 100,
        currency: invoice.currency.toUpperCase(),
        status: FAILED,
        stripeInvoiceId: invoice.id,
        periodStart: new Date(invoice.period_start * 1000),
        periodEnd: new Date(invoice.period_end * 1000)
      }
    })

    // Atualizar status da subscrição
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { status: PAST_DUE}
    })

    console.log(`Payment failed for subscription: ${stripeSubscriptionId}`)
  } catch (error) {
    console.error(Error handling payment failed:, 'error')
  
}
}
