import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendRepairCreatedEmail } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso a reparações independentes
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!user?.subscription?.plan?.individualRepairs) {
      return NextResponse.json(
        { message: 'Funcionalidade não disponível no seu plano' },
        { status: 403 }
      )
    }

    const {
      customerName,
      customerPhone,
      customerEmail,
      deviceBrand,
      deviceModel,
      deviceDescription,
      problemType,
      problemDescription,
      estimatedPrice,
      estimatedDays,
      deliveryDate,
      useConfiguredPrices = true
    } = await request.json()

    // Validações
    if (!customerName || !customerPhone || !deviceBrand || !deviceModel || !problemType || !problemDescription || !estimatedPrice || !estimatedDays) {
      return NextResponse.json(
        { message: 'Todos os campos obrigatórios devem ser preenchidos' },
        { status: 400 }
      )
    }

    // Gerar código único de tracking
    const trackingCode = `REP-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`

    // Buscar ou criar cliente
    let customer = await prisma.user.findUnique({
      where: { email: customerEmail || `${customerPhone}@temp.revify.pt` }
    })

    if (!customer && customerEmail) {
      // Criar cliente se email foi fornecido
      customer = await prisma.user.create({
        data: {
          email: customerEmail,
          name: customerName,
          role: 'CUSTOMER',
          isVerified: false,
          profile: {
            create: {
              phone: customerPhone,
              address: '',
              city: '',
              postalCode: '',
              country: 'PT'
            }
          }
        }
      })
    }

    // Buscar marca e tipo de problema
    const brand = await prisma.brand.findUnique({
      where: { id: deviceBrand }
    })

    const problemTypeData = await prisma.problemType.findUnique({
      where: { id: problemType }
    })

    if (!brand || !problemTypeData) {
      return NextResponse.json(
        { message: 'Marca ou tipo de problema inválido' },
        { status: 400 }
      )
    }

    // Buscar preço configurado se solicitado
    let finalPrice = parseFloat(estimatedPrice.toString())
    let finalDays = parseInt(estimatedDays.toString())

    if (useConfiguredPrices) {
      const priceConfig = await prisma.repairShopPrice.findFirst({
        where: {
          repairShopId: session.user.id,
          problemTypeId: problemType
        }
      })

      if (priceConfig) {
        finalPrice = Number(priceConfig.price)
        finalDays = priceConfig.estimatedDays || finalDays
      }
    }

    // Criar reparação independente
    const repair = await prisma.repair.create({
      data: {
        trackingCode,
        repairShopId: session.user.id,
        customerId: customer?.id || null,
        
        // Dados do cliente (para casos sem conta)
        customerName,
        customerPhone,
        customerEmail: customerEmail || null,
        
        // Dados do dispositivo
        deviceBrand: brand.name,
        deviceModel,
        deviceDescription: deviceDescription || '',
        
        // Problema
        problemType: problemTypeData.name,
        problemDescription,
        
        // Estimativas
        estimatedPrice: finalPrice,
        estimatedCompletionDate: new Date(Date.now() + (finalDays * 24 * 60 * 60 * 1000)),
        deliveryDate: deliveryDate ? new Date(deliveryDate) : null,

        // Status
        status: deliveryDate ? 'DELIVERED' : 'PENDING',
        isIndependent: true, // Marcar como reparação independente
        
        // Dados de criação
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })

    // Criar notificação para o lojista
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: 'REPAIR_CREATED',
        title: 'Nova Reparação Independente',
        message: `Reparação ${trackingCode} criada para ${customerName}`,
        data: {
          repairId: repair.id,
          trackingCode,
          customerName,
          deviceModel,
          problemType: problemTypeData.name
        }
      }
    })

    // Enviar email de notificação se o cliente tiver email
    if (customerEmail) {
      try {
        await sendRepairCreatedEmail({
          customerName: repair.customerName,
          customerEmail: customerEmail,
          trackingCode: repair.trackingCode,
          deviceBrand: brand.name,
          deviceModel: repair.deviceModel || 'N/A',
          problemType: problemTypeData.name,
          status: repair.status,
          shopName: user.name,
          shopPhone: user.profile?.phone || undefined,
          shopEmail: user.email,
          estimatedPrice: Number(repair.estimatedPrice),
          estimatedCompletionDate: repair.estimatedCompletionDate?.toISOString()
        })
      } catch (error) {
        console.error('Erro ao enviar email de notificação:', error)
        // Não falhar a criação da reparação por causa do email
      }
    }

    return NextResponse.json({
      success: true,
      repairId: repair.id,
      trackingCode,
      message: 'Reparação independente criada com sucesso',
      customerTrackingUrl: `${process.env.NEXTAUTH_URL}/track/${trackingCode}`,
      emailSent: !!customerEmail,
      data: {
        repair: {
          id: repair.id,
          trackingCode,
          customerName,
          deviceModel,
          problemType: problemTypeData.name,
          estimatedPrice: parseFloat(estimatedPrice.toString()),
          estimatedDays: parseInt(estimatedDays.toString()),
          status: 'PENDING'
        }
      }
    })

  } catch (error) {
    console.error('Erro ao criar reparação independente:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
