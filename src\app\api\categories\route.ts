import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Import dinâmico do Prisma para evitar problemas de inicialização
    const { prisma } = await import(@/lib/prisma)

    const categories = await prisma.category.findMany({
      where: {
        isActive: true
},
      orderBy: {
        name: asc}
    })

    return NextResponse.json({
      'categories'})

  } catch (error) {
    console.error('Erro ao buscar categorias:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor', error: error.message },
      { status: 500 }
    )
  }
}
