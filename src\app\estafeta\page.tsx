'use client'

import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Truck, 
  MapPin, 
  Clock, 
  Euro,
  Package,
  Settings,
  User,
  BarChart3 } from 'lucide-react'

export default function EstafetaDashboard() {
  const { data: session } = useSession()

  const menuItems = [
    {
      title: Entregas,
      items: [
        { name: 'Entregas Disponíveis', href: /estafeta/entregas/disponiveis, icon: Package, description: 'Ver entregas disponíveis'},
        { name: '<PERSON>as Entregas', href: /estafeta/entregas, icon: Truck, description: 'Entregas aceites' },
        { name: 'Hist<PERSON><PERSON><PERSON>', href: /estafeta/entregas/historico, icon: Clock, description: 'Entregas concluídas'},
      ]
    },
    {
      title: <PERSON><PERSON><PERSON><PERSON>,
      items: [
        { name: Disponibilidade, href: /estafeta/disponibilidade, icon: Clock, description: '<PERSON><PERSON><PERSON> hor<PERSON><PERSON><PERSON>'},
        { name: '<PERSON><PERSON> de Cobertura', href: /estafeta/zona, icon: MapPin, description: 'Definir área de trabalho'},
        { name: 'Estatísticas', href: /estafeta/estatisticas, icon: BarChart3, description: 'Performance e ganhos' },
      ]
    },
    {
      title: Conta,
      items: [
        { name: Perfil, href: /estafeta/perfil, icon: User, description: 'Gerir dados pessoais' },
        { name: Pagamentos, href: /estafeta/pagamentos, icon: Euro, description: 'Histórico de pagamentos'},
        { name: 'Configurações', href: /estafeta/configuracoes, icon: Settings, description: 'Configurações da conta'},
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-black">Revify</Link>
              <span className="ml-4 text-sm text-gray-500">Estafeta</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Olá, {session?.user?.name}</span>
              <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-xs font-medium">
                {session?.user?.name?.charAt(0).toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white mb-8">
          <h1 className="text-2xl font-bold mb-2">Dashboard Estafeta</h1>
          <p className="text-purple-100">
            Gerir entregas e maximizar os seus ganhos com flexibilidade.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Truck className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Entregas Hoje</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Euro className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Ganhos Hoje</p>
                <p className="text-2xl font-bold text-gray-900">€0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Entregas Disponíveis</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rating</p>
                <p className="text-2xl font-bold text-gray-900">5.0</p>
              </div>
            </div>
          </div>
        </div>

        {/* Status Card */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Estado Atual</h3>
              <p className="text-gray-600">Disponível para entregas</p>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-green-700">Online</span>
            </div>
          </div>
        </div>

        {/* Menu Sections */}
        <div className="space-y-8">
          {menuItems.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{section.title}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                      className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow group"
                    >
                      <div className="flex items-center mb-3">
                        <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-purple-600 group-hover:text-white transition-colors">
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <h3 className="ml-3 text-lg font-medium text-gray-900">{item.name}</h3>
                      </div>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
          <div className="flex flex-wrap gap-3">
            <Link
              href="/estafeta/entregas/disponiveis"
              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Package className="w-4 h-4 mr-2" />
              Ver Entregas
            </Link>
            <Link
              href="/estafeta/disponibilidade"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Clock className="w-4 h-4 mr-2" />Definir Horário</Link>
          </div>
        </div>
      </div>
    </div>
  )
}
