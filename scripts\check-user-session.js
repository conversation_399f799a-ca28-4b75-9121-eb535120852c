const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkUserSession() {
  try {
    console.log('🔍 Verificando dados do usuário e sessão...\n')

    // Buscar o lojista teste
    const user = await prisma.user.findFirst({
      where: { 
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      },
      include: {
        profile: true,
        accounts: true
      }
    })

    if (!user) {
      console.log('❌ Usuário lojista não encontrado!')
      return
    }

    console.log('👤 Dados do usuário:')
    console.log(`   ID: ${user.id}`)
    console.log(`   Nome: ${user.name}`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Role: ${user.role}`)
    console.log(`   Email verificado: ${user.emailVerified}`)
    console.log(`   Criado: ${user.createdAt}`)

    if (user.profile) {
      console.log('\n🏪 Dados do perfil:')
      console.log(`   Nome da empresa: ${user.profile.companyName}`)
      console.log(`   Subdomínio: ${user.profile.customSubdomain}`)
      console.log(`   Telefone: ${user.profile.phone}`)
    }

    console.log(`\n🔑 Contas vinculadas: ${user.accounts.length}`)
    user.accounts.forEach((account, index) => {
      console.log(`   ${index + 1}. Provider: ${account.provider}, Type: ${account.type}`)
    })

    // Verificar sessões ativas
    const sessions = await prisma.session.findMany({
      where: { userId: user.id },
      orderBy: { expires: 'desc' }
    })

    console.log(`\n📱 Sessões encontradas: ${sessions.length}`)
    sessions.forEach((session, index) => {
      const isExpired = new Date() > session.expires
      console.log(`   ${index + 1}. Token: ${session.sessionToken.substring(0, 20)}...`)
      console.log(`      Expira: ${session.expires}`)
      console.log(`      Status: ${isExpired ? '❌ Expirada' : '✅ Ativa'}`)
    })

    // Verificar subscription novamente
    console.log('\n💳 Verificando subscription:')
    const subscription = await prisma.subscription.findFirst({
      where: { userId: user.id },
      include: {
        plan: true
      },
      orderBy: { createdAt: 'desc' }
    })

    if (subscription) {
      console.log(`   ✅ Subscription encontrada:`)
      console.log(`      Status: ${subscription.status}`)
      console.log(`      Plano: ${subscription.plan.name}`)
      console.log(`      Período: ${subscription.currentPeriodStart} - ${subscription.currentPeriodEnd}`)
    } else {
      console.log('   ❌ Nenhuma subscription encontrada')
    }

    // Testar a função checkSubscriptionAccess diretamente
    console.log('\n🔍 Testando função checkSubscriptionAccess:')
    
    // Simular a mesma lógica
    const subscriptions = await prisma.$queryRaw`
      SELECT 
        s.*,
        sp.name as plan_name,
        sp.features,
        sp."maxProducts",
        sp."maxRepairs"
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${user.id}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    if (subscriptions.length === 0) {
      console.log('   ❌ checkSubscriptionAccess retornaria: hasActiveSubscription: false')
    } else {
      const sub = subscriptions[0]
      if (sub.status === 'ACTIVE') {
        console.log('   ✅ checkSubscriptionAccess retornaria:')
        console.log('      hasActiveSubscription: true')
        console.log('      hasPendingPayment: false')
        console.log('      subscriptionStatus: "ACTIVE"')
        console.log(`      planName: "${sub.plan_name}"`)
      }
    }

  } catch (error) {
    console.error('❌ Erro ao verificar usuário:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserSession()
