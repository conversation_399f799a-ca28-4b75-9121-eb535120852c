'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ShoppingCart, Search, User, Menu, X, ChevronDown } from 'lucide-react'

interface ShopHeaderProps {
  shopName: string
  subdomain: string
  cartItemsCount?: number
  logoUrl?: string}

interface Category {
  id: string
  name: string
  productCount: number
  brands: Brand[]
}

interface Brand {
  id: string
  name: string
  models: DeviceModel[]
}

interface DeviceModel {
  id: string
  name: string}

interface CustomPage {
  id: string
  slug: string
  title: string}

export default function ShopHeader({ shopName, subdomain, cartItemsCount = 0, logoUrl }: ShopHeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [headerPages, setHeaderPages] = useState<CustomPage[]>([])
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    fetchCategories()
    fetchHeaderPages()
  }, [subdomain])

  const fetchCategories = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/categories`)
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', 'error')
    }
  }

  const fetchHeaderPages = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/custom-pages?type=header`)
      if (response.ok) {
        const data = await response.json()
        setHeaderPages(data.pages || [])
      }
    } catch (error) {
      console.error('Erro ao carregar páginas do header:', 'error')
    }
  }

  const toggleDropdown = (categoryId: string) => {
    setActiveDropdown(activeDropdown === categoryId ? null : categoryId)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/shop/${subdomain}/produtos?search=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  return (
    <header className="bg-white shadow-lg">
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-10 text-sm">
            <div className="text-gray-200">Entregas grátis acima de 50€</div>
            <div className="flex items-center space-x-4">
              <Link href={`/shop/${subdomain}/login`} className="text-gray-200 hover:text-white transition-colors">
                Login
              </Link>
              <Link href={`/shop/${subdomain}/register`} className="text-gray-200 hover:text-white transition-colors">
                Criar Conta
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={`/shop/${subdomain}`} className="flex items-center">
              {logoUrl ? (
                <img src={logoUrl} alt={shopName} className="h-12 w-auto" />
              ) : (
                <>
                  <div className="h-10 w-10 bg-gradient-to-r from-gray-900 to-black rounded-lg flex items-center justify-center mr-3">
                    <span className="text-white font-bold text-lg">
                      {shopName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="text-2xl font-bold text-gray-900">{shopName}</span>
                </>
              )}
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex space-x-8">
            <Link href={`/shop/${subdomain}`} className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">Início</Link>
            <Link href={`/shop/${subdomain}/produtos`} className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">Produtos</Link>
            <Link href={`/shop/${subdomain}/nova-reparacao`} className="text-gray-900 hover:text-black px-3 py-2 text-sm font-medium">Reparações</Link>

            {/* Custom Pages */}
            {headerPages.map((page) => (
              <Link
                key={page.id}
                href={`/shop/${subdomain}/${page.slug}`}
                className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium"
              >
                {page.title}
              </Link>
            ))}
          </nav>

          {/* Search Bar */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="relative w-full">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Pesquisar produtos..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </form>
          </div>

          {/* Right Icons */}
          <div className="flex items-center space-x-4">
            <Link href={`/shop/${subdomain}/conta`} className="text-gray-700 hover:text-gray-900">
              <User className="h-6 w-6" />
            </Link>
            <Link href={`/shop/${subdomain}/carrinho`} className="relative text-gray-700 hover:text-gray-900">
              <ShoppingCart className="h-6 w-6" />
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-gradient-to-r from-gray-900 to-black text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>
            
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-gray-700 hover:text-gray-900"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Categories Bar */}
      <div className="bg-gray-50 border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-8 h-12 overflow-x-auto">
            {categories.map((category) => (
              <div key={category.id} className="relative">
                <button
                  onClick={() => toggleDropdown(category.id)}
                  className="flex items-center space-x-1 text-gray-700 hover:text-gray-900 whitespace-nowrap"
                >
                  <span>{category.name}</span>
                  <ChevronDown className="h-4 w-4" />
                </button>
                
                {/* Category Dropdown */}
                {activeDropdown === category.id && (
                  <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="p-4">
                      <div className="grid grid-cols-1 gap-4">
                        {category.brands.map((brand) => (
                          <div key={brand.id} className="border-b border-gray-100 last:border-b-0 pb-3 last:pb-0">
                            <h4 className="font-medium text-gray-900 mb-2">{brand.name}</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {brand.models.slice(0, 6).map((model) => (
                                <Link
                                  key={model.id}
                                  href={`/shop/${subdomain}/produtos?category=${category.id}&brand=${brand.id}&model=${model.id}`}
                                  className="text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 px-2 py-1 rounded"
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  {model.name}
                                </Link>
                              ))}
                              {brand.models.length > 6 && (
                                <Link
                                  href={`/shop/${subdomain}/produtos?category=${category.id}&brand=${brand.id}`}
                                  className="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded"
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  Ver todos ({brand.models.length})
                                </Link>
                              )}
                            </div>
                          </div>
                        ))}
                        <div className="pt-2 border-t border-gray-100">
                          <Link
                            href={`/shop/${subdomain}/produtos?category=${category.id}`}
                            className="block text-center text-sm font-medium text-gray-900 hover:text-black bg-gray-50 hover:bg-gray-100 px-4 py-2 rounded"
                            onClick={() => setActiveDropdown(null)}
                          >
                            Ver todos os produtos ({category.productCount})
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link href={`/shop/${subdomain}`} className="block px-3 py-2 text-gray-700 hover:text-gray-900">Início</Link>
            <Link href={`/shop/${subdomain}/produtos`} className="block px-3 py-2 text-gray-700 hover:text-gray-900">Produtos</Link>
            <Link href={`/shop/${subdomain}/nova-reparacao`} className="block px-3 py-2 text-gray-900 hover:text-black">Reparações</Link>
            <Link href={`/shop/${subdomain}/sobre`} className="block px-3 py-2 text-gray-700 hover:text-gray-900">Sobre</Link>
            <Link href={`/shop/${subdomain}/contacto`} className="block px-3 py-2 text-gray-700 hover:text-gray-900">Contacto</Link>
          </div>
        </div>
      )}
    </header>
  )
}
