'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Star, MapPin, Clock, Phone, Mail, ArrowLeft, Calendar, Award, Shield, Users, CheckCircle } from 'lucide-react'
import ModernLayout from '@/components/ModernLayout'
import { useTranslation } from '@/hooks/useTranslation'

interface RepairShop {
  id: string
  name: string
  email: string
  isVerified: boolean
  profile?: {
    companyName?: string
    description?: string
    phone?: string
    address?: string
    city?: string
    postalCode?: string
    averageRepairTime?: number
    businessHours?: any
  }
  rating: number
  reviewCount: number
  basePrice: number
  specialties: string[]
}

export default function LojaPage() {
  const params = useParams()
  const { t } = useTranslation()
  const [shop, setShop] = useState<RepairShop | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      fetchShop(params.id as string)
    }
  }, [params.id])

  const fetchShop = async (shopId: string) => {
    try {
      const response = await fetch(`/api/repair-shops/${shopId}`)
      if (response.ok) {
        const shopData = await response.json()
        setShop(shopData)
      } else {
        setError('Loja não encontrada')
      }
    } catch (error) {
      console.error('Erro ao carregar loja:', error)
      setError('Erro ao carregar dados da loja')
    } finally {
      setIsLoading(false)
    }
  }

  // Mock business hours
  const businessHours = {
    segunda: { open: '09:00', close: '18:00' },
    terça: { open: '09:00', close: '18:00' },
    quarta: { open: '09:00', close: '18:00' },
    quinta: { open: '09:00', close: '18:00' },
    sexta: { open: '09:00', close: '18:00' },
    sábado: { open: '09:00', close: '13:00' },
    domingo: null
  }

  const formatTime = (time: string) => {
    return time
  }

  if (isLoading) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">A carregar dados da loja...</p>
          </div>
        </div>
      </ModernLayout>
    )
  }

  if (error || !shop) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <Link
              href="/"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-semibold"
            >
              Voltar ao Início
            </Link>
          </div>
        </div>
      </ModernLayout>
    )
  }

  return (
    <ModernLayout>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex items-center mb-6">
            <Link href="/" className="mr-4 p-2 hover:bg-white/20 rounded-lg transition-colors">
              <ArrowLeft className="w-5 h-5 text-white" />
            </Link>
            <span className="text-white/80">{t('businessProfile')}</span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <div className="lg:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mr-6">
                  <Award className="w-10 h-10 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold mb-2">{shop.profile?.companyName || shop.name}</h1>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <span className="ml-1 text-lg font-semibold">{shop.rating.toFixed(1)}</span>
                      <span className="ml-1 text-white/80">({shop.reviewCount} avaliações)</span>
                    </div>
                    {shop.isVerified && (
                      <div className="flex items-center text-white/80">
                        <Shield className="w-4 h-4 mr-1" />
                        <span>Loja Verificada</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-white/90 text-lg leading-relaxed">
                {shop.profile?.description || 'Especialistas em reparação de dispositivos eletrónicos com anos de experiência e qualidade garantida.'}
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
              <h3 className="text-lg font-semibold mb-4">Contacto Rápido</h3>
              <div className="space-y-3">
                {shop.profile?.phone && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-3" />
                    <span>{shop.profile.phone}</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-3" />
                  <span>{shop.email}</span>
                </div>
                {shop.profile?.address && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-3" />
                    <span>{shop.profile.address}, {shop.profile.city}</span>
                  </div>
                )}
              </div>
              <Link
                href="/cliente/reparacoes/nova-v2"
                className="block w-full mt-4 bg-white text-blue-600 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors text-center"
              >
                Solicitar Reparação
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Services & Info Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Services */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('services')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {shop.specialties.map((specialty, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 rounded-xl">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    <span className="font-medium text-gray-900">{specialty}</span>
                  </div>
                ))}
              </div>

              <div className="mt-8 p-6 bg-blue-50 rounded-xl">
                <div className="flex items-center mb-4">
                  <Clock className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Tempo Médio de Reparação</h3>
                </div>
                <p className="text-3xl font-bold text-blue-600">
                  {shop.profile?.averageRepairTime
                    ? (shop.profile.averageRepairTime < 60
                        ? `${shop.profile.averageRepairTime}min`
                        : `${Math.round(shop.profile.averageRepairTime / 60)}h`)
                    : 'Consultar'
                  }
                </p>
                <p className="text-gray-600">
                  {shop.profile?.averageRepairTime
                    ? (shop.reviewCount > 0
                        ? 'Baseado em reparações anteriores'
                        : 'Tempo estimado pela loja')
                    : 'Contacte para mais informações'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Contact & Hours */}
          <div className="space-y-6">
            {/* Contact Info */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('contactInfo')}</h3>
              <div className="space-y-4">
                {shop.profile?.phone && (
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-gray-400 mr-3" />
                    <span className="text-gray-900">{shop.profile.phone}</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Mail className="w-5 h-5 text-gray-400 mr-3" />
                  <span className="text-gray-900">{shop.email}</span>
                </div>
                {shop.profile?.address && (
                  <div className="flex items-start">
                    <MapPin className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-gray-900">{shop.profile.address}</p>
                      <p className="text-gray-600">{shop.profile.city} {shop.profile.postalCode}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('openingHours')}</h3>
              <div className="space-y-2">
                {Object.entries(businessHours).map(([day, hours]: [string, any]) => (
                  <div key={day} className="flex justify-between">
                    <span className="text-gray-600 capitalize">{day}</span>
                    <span className="text-gray-900">
                      {hours?.open && hours?.close
                        ? `${formatTime(hours.open)} - ${formatTime(hours.close)}`
                        : 'Fechado'
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Location Map */}
        {shop.profile?.address && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('location')}</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Morada</h3>
                  <p className="text-gray-600">{shop.profile.address}</p>
                  <p className="text-gray-600">{shop.profile.city} {shop.profile.postalCode}</p>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span>Fácil acesso de transportes públicos</span>
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span>Estacionamento disponível</span>
                  </div>
                  <div className="flex items-center text-green-600">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    <span>Acessível a pessoas com mobilidade reduzida</span>
                  </div>
                </div>
              </div>
              <div className="h-64 bg-gray-200 rounded-xl overflow-hidden">
                <iframe
                  src={`https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${encodeURIComponent(shop.profile.address + ', ' + shop.profile.city)}`}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="rounded-xl"
                ></iframe>
              </div>
            </div>
          </div>
        )}

        {/* Reviews Section */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">{t('reviews')}</h2>
          <div className="text-center py-12">
            <Star className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Ainda sem avaliações</h3>
            <p className="text-gray-600">Seja o primeiro a avaliar esta loja após uma reparação.</p>
          </div>
        </div>
      </div>
    </ModernLayout>
  )
}
