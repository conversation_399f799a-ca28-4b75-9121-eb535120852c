import { NextRequest, NextResponse } from 'next/server'
import { getCustomPage } from '@/lib/customPages'
import { prisma } from '@/lib/prisma'

// GET - Obter página customizada específica por slug
export async function GET(
  request: NextRequest,
  { params }: { params: { subdomain: string; slug: string } }
) {
  try {
    const { subdomain, slug } = params

    // Buscar o usuário pela subdomain
    const profile = await prisma.profile.findFirst({
      where: { customSubdomain: subdomain },
      include: { user: true }
    })

    if (!profile) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    const page = await getCustomPage(profile.userId, slug)

    if (!page) {
      return NextResponse.json(
        { message: 'Página não encontrada' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      page
    })

  } catch (error) {
    console.error('Erro ao buscar página customizada:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
