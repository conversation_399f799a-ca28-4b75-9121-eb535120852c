'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Download, Printer, QrCode, Store, Wrench, Copy, Check, AlertTriangle } from 'lucide-react'
import Link from 'next/link'
import AutoTranslate from '@/components/ui/AutoTranslate'
import QRCodeLib from 'qrcode'

interface QRCodeData {
  type: 'loja' | 'reparacoes' | 'custom'
  url: string
  title: string
  description: string
}

export default function QRCodePage() {
  const { data: session } = useSession()
  const [selectedType, setSelectedType] = useState<'loja' | 'reparacoes' | 'custom'>('loja')
  const [customUrl, setCustomUrl] = useState('')
  const [customTitle, setCustomTitle] = useState('')
  const [customDescription, setCustomDescription] = useState('')
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [copied, setCopied] = useState(false)
  const [userSubdomain, setUserSubdomain] = useState<string | null>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Buscar subdomínio do usuário
  useEffect(() => {
    const fetchUserSubdomain = async () => {
      if (session?.user?.id) {
        try {
          const response = await fetch('/api/lojista/profile')
          if (response.ok) {
            const data = await response.json()
            console.log('Profile data:', data.profile) // Debug
            const subdomain = data.profile?.customSubdomain
            console.log('Subdomain found:', subdomain) // Debug
            setUserSubdomain(subdomain || null)
          } else {
            console.error('Failed to fetch profile:', response.status)
          }
        } catch (error) {
          console.error('Erro ao buscar subdomínio:', error)
        }
      }
    }

    fetchUserSubdomain()
  }, [session?.user?.id])

  // URLs base - usar localhost para desenvolvimento
  const baseUrl = process.env.NODE_ENV === 'development'
    ? 'http://localhost:3001'
    : 'https://revify.pt'

  // Garantir que sempre temos um subdomínio válido
  const effectiveSubdomain = userSubdomain || 'loja-demo'

  const shopUrl = process.env.NODE_ENV === 'development'
    ? `${baseUrl}/shop/${effectiveSubdomain}`
    : `https://${effectiveSubdomain}.revify.pt`

  const repairsUrl = `${shopUrl}/reparacoes`

  console.log('QR Code URLs:', { userSubdomain, effectiveSubdomain, shopUrl, repairsUrl }) // Debug

  const qrCodeOptions = [
    {
      id: 'loja',
      name: 'Loja Online',
      icon: Store,
      description: 'QR Code para acesso direto à sua loja online',
      url: shopUrl,
      title: 'Visite a nossa loja online',
      subtitle: 'Escaneie para ver produtos e fazer encomendas'
    },
    {
      id: 'reparacoes',
      name: 'Reparações',
      icon: Wrench,
      description: 'QR Code para serviços de reparação',
      url: repairsUrl,
      title: 'Solicite uma reparação',
      subtitle: 'Escaneie para agendar reparações'
    },
    {
      id: 'custom',
      name: 'Personalizado',
      icon: QrCode,
      description: 'QR Code personalizado com URL à sua escolha',
      url: customUrl,
      title: customTitle || 'QR Code Personalizado',
      subtitle: customDescription || 'Escaneie para aceder'
    }
  ]

  const currentQRData = qrCodeOptions.find(option => option.id === selectedType)

  useEffect(() => {
    if (currentQRData?.url) {
      generateQRCode(currentQRData.url)
    }
  }, [selectedType, customUrl, customTitle, customDescription])

  const generateQRCode = async (url: string) => {
    if (!url) return

    setIsGenerating(true)
    try {
      const dataUrl = await QRCodeLib.toDataURL(url, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
      setQrCodeDataUrl(dataUrl)
    } catch (error) {
      console.error('Erro ao gerar QR Code:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const downloadQRCode = () => {
    if (!qrCodeDataUrl) return

    const link = document.createElement('a')
    link.download = `qrcode-${selectedType}-${Date.now()}.png`
    link.href = qrCodeDataUrl
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const printQRCode = () => {
    if (!currentQRData) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>QR Code - ${currentQRData.title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 20px;
              margin: 0;
            }
            .qr-container {
              max-width: 400px;
              margin: 0 auto;
              border: 2px solid #000;
              padding: 20px;
              border-radius: 10px;
            }
            .qr-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
              color: #000;
            }
            .qr-subtitle {
              font-size: 16px;
              color: #666;
              margin-bottom: 20px;
            }
            .qr-code {
              margin: 20px 0;
            }
            .qr-url {
              font-size: 14px;
              color: #333;
              word-break: break-all;
              margin-top: 15px;
              padding: 10px;
              background: #f5f5f5;
              border-radius: 5px;
            }
            @media print {
              body { margin: 0; }
              .qr-container { border: 2px solid #000; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div class="qr-title">${currentQRData.title}</div>
            <div class="qr-subtitle">${currentQRData.subtitle}</div>
            <div class="qr-code">
              <img src="${qrCodeDataUrl}" alt="QR Code" style="max-width: 250px;" />
            </div>
            <div class="qr-url">${currentQRData.url}</div>
          </div>
        </body>
      </html>
    `

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 250)
  }

  const copyUrl = async () => {
    if (!currentQRData?.url) return

    try {
      await navigator.clipboard.writeText(currentQRData.url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Erro ao copiar URL:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Gerador de QR Code</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Warning when subdomain is not configured */}
        {!userSubdomain && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-yellow-800"><AutoTranslate text="Subdomínio não configurado" /></h3>
                <p className="text-sm text-yellow-700 mt-1"><AutoTranslate text="Os QR codes estão a usar um subdomínio temporário. Para personalizar o seu subdomínio, vá às" /><Link href="/lojista/configuracoes" className="underline font-medium"><AutoTranslate text="configurações da loja" /></Link>.
                </p>
                <p className="text-xs text-yellow-600 mt-2 font-mono">
                  URL atual: {shopUrl}
                </p>
              </div>
            </div>
          </div>
        )}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Options */}
          <div className="space-y-6">
            {/* Type Selection */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Tipo de QR Code</h2>
              
              <div className="space-y-3">
                {qrCodeOptions.slice(0, 2).map((option) => (
                  <button
                    key={option.id}
                    onClick={() => setSelectedType(option.id as any)}
                    className={`w-full p-4 border-2 rounded-lg text-left transition-colors ${
                      selectedType === option.id
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start">
                      <option.icon className="w-6 h-6 text-indigo-600 mt-1 mr-3" />
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{option.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{option.description}</p>
                        <p className="text-xs text-gray-400 mt-2 font-mono">{option.url}</p>
                      </div>
                    </div>
                  </button>
                ))}

                {/* Custom Option */}
                <button
                  onClick={() => setSelectedType('custom')}
                  className={`w-full p-4 border-2 rounded-lg text-left transition-colors ${
                    selectedType === 'custom'
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start">
                    <QrCode className="w-6 h-6 text-indigo-600 mt-1 mr-3" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">Personalizado</h3>
                      <p className="text-sm text-gray-500 mt-1"><AutoTranslate text="QR Code personalizado com URL à sua escolha" /></p>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            {/* Custom URL Configuration */}
            {selectedType === 'custom' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4"><AutoTranslate text="Configuração Personalizada" /></h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      URL de Destino *
                    </label>
                    <input
                      type="url"
                      value={customUrl}
                      onChange={(e) => setCustomUrl(e.target.value)}
                      placeholder="https://exemplo.com"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2"><AutoTranslate text="Título" /></label>
                    <input
                      type="text"
                      value={customTitle}
                      onChange={(e) => setCustomTitle(e.target.value)}
                      placeholder="Título do QR Code"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2"><AutoTranslate text="Descrição" /></label>
                    <input
                      type="text"
                      value={customDescription}
                      onChange={(e) => setCustomDescription(e.target.value)}
                      placeholder="Descrição do QR Code"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Preview and Actions */}
          <div className="space-y-6">
            {/* QR Code Preview */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Preview do QR Code</h2>
              
              <div className="text-center">
                {currentQRData && (
                  <>
                    <div className="mb-4">
                      <h3 className="text-xl font-semibold text-gray-900">{currentQRData.title}</h3>
                      <p className="text-gray-600 mt-1">{currentQRData.subtitle}</p>
                    </div>

                    <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                      {isGenerating ? (
                        <div className="w-64 h-64 flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                        </div>
                      ) : qrCodeDataUrl ? (
                        <img 
                          src={qrCodeDataUrl} 
                          alt="QR Code" 
                          className="w-64 h-64"
                        />
                      ) : (
                        <div className="w-64 h-64 flex items-center justify-center text-gray-400">
                          <QrCode className="w-16 h-16" />
                        </div>
                      )}
                    </div>

                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 font-mono break-all">
                          {currentQRData.url}
                        </span>
                        <button
                          onClick={copyUrl}
                          className="ml-2 p-1 text-gray-400 hover:text-gray-600"
                          title="Copiar URL"
                        >
                          {copied ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Actions */}
            {qrCodeDataUrl && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4"><AutoTranslate text="Ações" /></h2>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button
                    onClick={downloadQRCode}
                    className="inline-flex items-center justify-center px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  >
                    <Download className="w-5 h-5 mr-2" />
                    Baixar PNG
                  </button>

                  <button
                    onClick={printQRCode}
                    className="inline-flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Printer className="w-5 h-5 mr-2" />
                    Imprimir
                  </button>
                </div>

                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="text-sm font-medium text-blue-900 mb-2">💡 Dica de Uso</h3>
                  <p className="text-sm text-blue-800"><AutoTranslate text="Cole este QR Code na montra da sua loja, em cartões de visita, ou em materiais promocionais  para que os clientes possam aceder facilmente à sua loja online ou serviços de reparação." /></p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
