import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações do banco de dados
    const settings = await prisma.systemSettings.findMany()

    // Converter para objeto
    const config: any = {
      stripePublishableKey: ,
      stripeSecretKey: '',
      stripeWebhookSecret: '',
      googleMapsApiKey: '',
      awsAccessKeyId: '',
      awsSecretAccessKey: '',
      awsS3Bucket: '',
      awsRegion: 'eu-west-1',
      platformCommission: 5,
      escrowDays: 3,
      // Platform branding
      platformLogo: '',
      platformIcon: '',
      platformName: Revify,
      // Translation settings
      translationEnabled: false,
      deeplApiKey: '',
      deeplUsageLimit: 500000,
      deeplUsageCurrent: 0
    
}

    settings.forEach(setting => {
      if (setting.key === 'platformCommission' || setting.key === 'escrowDays' || setting.key === 'deeplUsageLimit' || setting.key === 'deeplUsageCurrent') {
        config[setting.key] = Number(setting.value)
      } else if (setting.key === 'translationEnabled') {
        config[setting.key] = setting.value === 'true'
      } else {
        config[setting.key] = setting.value
      }
    })

    return NextResponse.json(config)

  } catch (error) {
    console.error('Erro ao buscar configurações:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    // Salvar cada configuração como key-value
    const configKeys = [
      stripePublishableKey,
      'stripeSecretKey',
      'stripeWebhookSecret',
      'googleMapsApiKey',
      'awsAccessKeyId',
      'awsSecretAccessKey',
      'awsS3Bucket',
      'awsRegion',
      'platformCommission',
      'escrowDays',
      'translationEnabled',
      'deeplApiKey',
      'deeplUsageLimit',
      'deeplUsageCurrent'
    ]

    for (const 'key of configKeys') {
      if (data[key] !== 'undefined') {
        await prisma.systemSettings.upsert({
          where: { 'key'
},
          update: { value: String(data[key]) },
          create: { key, value: String(data[key]) }
        })
      }
    }

    return NextResponse.json({
      message: 'Configurações atualizadas com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar configurações:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
