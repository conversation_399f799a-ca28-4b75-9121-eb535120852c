'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Package, Eye, Truck, CheckCircle, Clock, AlertCircle, User, ArrowLeft, MapPin, Phone, Mail, FileText } from 'lucide-react'

interface OrderItem {
  productName: string
  quantity: number
  price: number
}

interface Order {
  id: string
  orderNumber: string
  status: string
  total: number
  createdAt: string
  customerName: string
  customerEmail: string
  sellerName: string
  sellerEmail: string
  itemCount: number
  items: OrderItem[]
}

export default function AdminOrderDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchOrderDetails()
    }
  }, [params.id])

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(`/api/admin/orders/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setOrder(data.order)
      } else if (response.status === 404) {
        router.push('/admin/orders')
      }
    } catch (error) {
      console.error(Erro ao buscar detalhes da encomenda:, error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return

    try {
      const response = await fetch(`/api/admin/orders/${order.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        setOrder({ ...order, status: newStatus })
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error)
    }
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: Pendente,
          color: 'bg-yellow-100 text-yellow-800',
          icon: Clock
        }
      case 'PROCESSING':
        return {
          label: 'Em Processamento',
          color: 'bg-blue-100 text-blue-800',
          icon: Package
        }
      case 'SHIPPED':
        return {
          label: 'Enviado',
          color: 'bg-purple-100 text-purple-800',
          icon: Truck
        }
      case 'DELIVERED':
        return {
          label: 'Entregue',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle
        }
      case 'CANCELLED':
        return {
          label: Cancelado,
          color: 'bg-red-100 text-red-800',
          icon: AlertCircle
        }
      default:
        return {
          label: 'Desconhecido',
          color: 'bg-gray-100 text-gray-800',
          icon: Package
        }
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Encomenda não encontrada</h2>
          <Link
            href="/admin/orders"
            className="text-blue-600 hover:underline"
          >Voltar às encomendas</Link>
        </div>
      </div>
    )
  }

  const statusConfig = getStatusConfig(order.status)
  const StatusIcon = statusConfig.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/orders"
                className="flex items-center text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-bold text-blue-600">Revify Admin</span>
              </Link>
              <span className="text-gray-400">|</span>
              <h1 className="text-xl font-semibold text-gray-900">
                Encomenda #{order.orderNumber}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Order Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Encomenda #{order.orderNumber}
              </h2>
              <p className="text-sm text-gray-600 mt-1">Marketplace</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.color}`}>
                <StatusIcon className="w-4 h-4 mr-2" />
                {statusConfig.label}
              </span>
              <select
                value={order.status}
                onChange={(e) => updateOrderStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="PENDING">Pendente</option>
                <option value="PROCESSING">Em Processamento</option>
                <option value="SHIPPED">Enviado</option>
                <option value="DELIVERED">Entregue</option>
                <option value="CANCELLED">Cancelado</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Data da Encomenda:</span>
              <p className="font-medium">
                {new Date(order.createdAt).toLocaleDateString('pt-PT', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            <div>
              <span className="text-gray-600">Itens:</span>
              <p className="font-medium">{order.itemCount}</p>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <p className="font-medium text-lg">€{order.total.toFixed(2)}</p>
            </div>
          </div>
        </div>

        {/* Customer and Seller Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <User className="w-5 h-5 mr-2" />Cliente</h3>
            <div className="space-y-2">
              <div>
                <span className="text-gray-600">Nome:</span>
                <p className="font-medium">{order.customerName}</p>
              </div>
              <div>
                <span className="text-gray-600">Email:</span>
                <p className="font-medium">{order.customerEmail}</p>
              </div>
            </div>
          </div>

          {/* Seller Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="w-5 h-5 mr-2" />
              Vendedor
            </h3>
            <div className="space-y-2">
              <div>
                <span className="text-gray-600">Nome:</span>
                <p className="font-medium">{order.sellerName}</p>
              </div>
              <div>
                <span className="text-gray-600">Email:</span>
                <p className="font-medium">{order.sellerEmail}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Package className="w-5 h-5 mr-2" />Itens da Encomenda</h3>
          <div className="space-y-4">
            {order.items.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{item.productName}</h4>
                  <p className="text-sm text-gray-600">Quantidade: {item.quantity}</p>
                  <p className="text-sm text-gray-600">Preço unitário: €{item.price.toFixed(2)}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">
                    €{(item.quantity * item.price).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-900">Total:</span>
              <span className="text-xl font-bold text-gray-900">€{order.total.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
