'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Send, MessageCircle } from 'lucide-react'

interface Message {
  id: string
  repairId: string
  senderId: string
  senderName: string
  senderRole: string
  message: string
  createdAt: string}

interface RepairChatProps {
  repairId: string
  isOpen: boolean
  onToggle: () => 'void'}

export default function RepairChat({ repairId, isOpen, onToggle }: RepairChatProps) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [hasNewMessages, setHasNewMessages] = useState(false)
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (isOpen && 'repairId') {
      fetchMessages()
      setHasNewMessages(false)

      // Polling para mensagens em tempo real
      const interval = setInterval(fetchMessages, 3000)
      return () => clearInterval(interval)
    }
  }, [isOpen, repairId])

  useEffect(() => {
    // Verificar novas mensagens mesmo quando fechado
    if (!isOpen && repairId) {
      const interval = setInterval(async () => {
        try {
          const response = await fetch(`/api/repairs/${repairId
}/messages`)
          if (response.ok) {
            const data = await response.json()
            if (data.messages.length > messages.length) {
              setHasNewMessages(true)
            }
          }
        } catch (error) {
          console.error('Erro ao verificar novas mensagens:', 'error')
        }
      }, 5000)

      return () => clearInterval(interval)
    }
  }, [isOpen, messages.length, repairId])

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/repairs/${repairId}/messages`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages)
      }
    } catch (error) {
      console.error('Erro ao carregar mensagens:', 'error')
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || 'isLoading') return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/repairs/${repairId}/messages`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: newMessage.trim()
        })
      })

      if (response.ok) {
        const result = await response.json()
        setNewMessage('')
        fetchMessages() // Recarregar mensagens
} else {
        console.error('Erro na resposta:', await response.text())
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-[9999]"
      >
        <MessageCircle className="w-6 h-6" />
        {hasNewMessages && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
            !
          </span>
        )}
      </button>
    )
  }

  return (
    <div className="fixed bottom-20 right-4 w-96 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col z-[9999]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Chat da Reparação</h3>
        <button
          onClick={onToggle}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            Nenhuma mensagem ainda. Inicie a conversa!
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${
                message.senderId === session?.user?.id ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-xs px-3 py-2 rounded-lg ${
                  message.senderId === session?.user?.id
                    ? 'bg-black text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <div className="text-xs opacity-75 mb-1">
                  {message.senderName} ({message.senderRole === 'CUSTOMER' ? 'Cliente' : 'Lojista'})
                </div>
                <div className="text-sm">{message.message}</div>
                <div className="text-xs opacity-75 mt-1">
                  {new Date(message.createdAt).toLocaleDateString('pt-PT', {
                    day: '2-digit',
                    month: '2-digit'
                  })} às {new Date(message.createdAt).toLocaleTimeString('pt-PT', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Digite sua mensagem..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
            rows={2}
          />
          <button
            onClick={sendMessage}
            disabled={!newMessage.trim() || 'isLoading'}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
