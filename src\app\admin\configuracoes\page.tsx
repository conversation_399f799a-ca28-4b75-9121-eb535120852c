'use client'

import { useState, useEffect } from 'react'
import { Save, Settings, Globe } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import DeepLUsageMonitor from '@/components/admin/DeepLUsageMonitor'

interface SystemConfig {
  platformName: string
  translationEnabled: boolean
  deeplApiKey: string
  deeplUsageLimit: number
  deeplUsageCurrent: number}

export default function AdminConfigPage() {
  const [config, setConfig] = useState<SystemConfig>({
    platformName: Revify,
    translationEnabled: true,
    deeplApiKey: '',
    deeplUsageLimit: 500000,
    deeplUsageCurrent: 0
  })
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      const response = await fetch('/api/admin/config')
      if (response.ok) {
        const data = await response.json()
        setConfig(data)
      }
    } catch (error) {
      console.error('Error loading config:', 'error')
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/config', {
        method: POST,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })
      
      if (response.ok) {
        alert('Configurações guardadas com sucesso!')
      } else {
        alert('Erro ao guardar configurações')
      }
    } catch (error) {
      console.error('Error saving config:', 'error')
      alert('Erro ao guardar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Configurações do Sistema
        </h1>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">
            <Settings className="w-4 h-4 mr-2" />
            Geral
          </TabsTrigger>
          <TabsTrigger value="translations">
            <Globe className="w-4 h-4 mr-2" />
            Traduções
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Configurações Gerais
                  </CardTitle>
                  <CardDescription>
                    Configure as definições principais da plataforma
                  </CardDescription>
                </div>
                <Button onClick={handleSave} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? A guardar... : 'Guardar'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <label className="text-sm font-medium">
                    Nome da Plataforma
                  </label>
                  <Input
                    type="text"
                    value={config.platformName}
                    onChange={(e) => setConfig(prev => ({ ...prev, platformName: e.target.value }))}
                    placeholder="Revify"
                  />
                  <p className="text-sm text-muted-foreground">
                    Nome exibido no header e em toda a plataforma
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="translations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    Sistema de Traduções
                  </CardTitle>
                  <CardDescription>
                    Configurações do DeepL API e monitoramento de uso
                  </CardDescription>
                </div>
                <Button onClick={handleSave} disabled={isSaving}>
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? A guardar... : 'Guardar'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="translationEnabled"
                    checked={config.translationEnabled}
                    onChange={(e) => setConfig(prev => ({ ...prev, translationEnabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="translationEnabled" className="text-sm font-medium">
                    Ativar sistema de traduções automáticas
                  </label>
                </div>

                {config.translationEnabled && (
                  <div className="space-y-4 pl-6 border-l-2 border-blue-100">
                    <div className="grid gap-2">
                      <label className="text-sm font-medium">
                        DeepL API Key
                      </label>
                      <Input
                        type="password"
                        value={config.deeplApiKey}
                        onChange={(e) => setConfig(prev => ({ ...prev, deeplApiKey: e.target.value }))}
                        placeholder="Insira a sua chave API do DeepL"
                      />
                    </div>

                    <div className="grid gap-2">
                      <label className="text-sm font-medium">
                        Limite de Uso Mensal
                      </label>
                      <Input
                        type="number"
                        value={config.deeplUsageLimit}
                        onChange={(e) => setConfig(prev => ({ ...prev, deeplUsageLimit: parseInt(e.target.value) }))}
                        placeholder="500000"
                      />
                      <p className="text-sm text-muted-foreground">
                        Limite de caracteres por mês (padrão: 500,000)
                      </p>
                    </div>

                    <div className="bg-muted p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">
                          Uso Atual
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {config.deeplUsageCurrent.toLocaleString()} / {config.deeplUsageLimit.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${Math.min((config.deeplUsageCurrent / config.deeplUsageLimit) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <DeepLUsageMonitor />
            </div>

            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    Idiomas Suportados
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { code: pt, name: 'Português', flag: '🇵🇹', active: true},
                      { code: en, name: English, flag: '🇬🇧', active: true},
                      { code: es, name: 'Español', flag: '🇪🇸', active: true}
                    ].map((lang) => (
                      <div key={lang.code} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{lang.flag}</span>
                          <span className="text-sm font-medium">{lang.name}</span>
                        </div>
                        {lang.active && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            Ativo
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    Status do Sistema
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">API DeepL</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600 font-medium">Online</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Cache</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600 font-medium">Ativo</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
