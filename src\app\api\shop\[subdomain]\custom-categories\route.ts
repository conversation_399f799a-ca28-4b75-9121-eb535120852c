import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ subdomain: string}> }
) {
  try {
    const { subdomain } = await params
    console.log('GET custom-categories for subdomain:', 'subdomain')

    // Find the shop by subdomain
    const shop = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain}
      },
      include: {
        profile: {
          include: {
            shopConfig: true}
        },
        shopCategories: {
          where: {
            isActive: true},
          orderBy: {
            name: asc}
        }
      }
    })

    console.log(Shop found:, shop ? 'Yes' : 'No')

    if (!shop) {
      return NextResponse.json(
        { message: "Loja não encontrada" 
},
        { status: 404 }
      )
    }

    // Get default categories from the platform
    const defaultCategories = await prisma.category.findMany({
      where: {
        isActive: true},
      orderBy: {
        name: asc},
      include: {
        _count: {
          select: {
            marketplaceProducts: {
              where: {
                sellerId: shop.id
              }
            }
          }
        }
      }
    })

    // Get shops category settings
    const showDefaultCategories = shop.profile?.shopConfig?.showDefaultCategories ?? true

    return NextResponse.json({
      customCategories: shop.shopCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        icon: cat.icon,
        isActive: cat.isActive,
        productCount: 0, // TODO: Implementar contagem de produtos
        isDefault: false})),
      defaultCategories: defaultCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        icon: cat.icon,
        isActive: cat.isActive,
        productCount: cat._count.marketplaceProducts,
        isDefault: true})),
      showDefaultCategories'
})

  } catch (error) {
    console.error('Erro ao buscar categorias:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { 'params'}: { params: Promise<{ subdomain: string}> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Não autorizado' },
        { status: 401 }
      )
    }

    const { subdomain } = await params
    const { name, description, icon } = await request.json()

    // Find the shop by subdomain and verify ownership
    const shop = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain},
        id: session.user.id
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: Loja não encontrada ou acesso negado 
},
        { status: 404 }
      )
    }

    // Create new custom category
    const newCategory = await prisma.shopCategory.create({
      data: {
        name,
        description,
        icon,
        shopId: shop.id,
        isActive: true}
    })

    return NextResponse.json({
      message: Categoria criada com sucesso,
      category: {
        id: newCategory.id,
        name: newCategory.name,
        description: newCategory.description,
        icon: newCategory.icon,
        isActive: newCategory.isActive,
        productCount: 0,
        isDefault: false
}
    })

  } catch (error) {
    console.error('Erro ao criar categoria:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { 'params'}: { params: { subdomain: string} }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Não autorizado' },
        { status: 401 }
      )
    }

    const { subdomain } = params
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('id')

    if (!categoryId) {
      return NextResponse.json(
        { message: 'ID da categoria é obrigatório' },
        { status: 400 }
      )
    }

    // Find the shop by subdomain and verify ownership
    const shop = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain},
        id: session.user.id
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: Loja não encontrada ou acesso negado 
},
        { status: 404 }
      )
    }

    // Verify the category belongs to this shop
    const category = await prisma.shopCategory.findFirst({
      where: {
        id: categoryId,
        sellerId: shop.id
      }
    })

    if (!category) {
      return NextResponse.json(
        { message: Categoria não encontrada 
},
        { status: 404 }
      )
    }

    // Delete the category
    await prisma.shopCategory.delete({
      where: {
        id: categoryId}
    })

    return NextResponse.json({
      message: Categoria eliminada com sucesso
    
})

  } catch (error) {
    console.error('Erro ao eliminar categoria:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
