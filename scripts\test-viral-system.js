const { PrismaClient } = require('@prisma/client')

async function testViralSystem() {
  const prisma = new PrismaClient()

  try {
    console.log('🧪 Testando Sistema Viral...\n')

    // 1. Verificar se existem atividades virais
    const activities = await prisma.viralActivity.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { name: true, role: true }
        }
      }
    })

    console.log('📊 Últimas 5 Atividades Virais:')
    activities.forEach((activity, index) => {
      console.log(`${index + 1}. ${activity.description}`)
      console.log(`   Tipo: ${activity.type} | Pontos: ${activity.points}`)
      console.log(`   Usuário: ${activity.user.name} (${activity.user.role})`)
      console.log(`   Data: ${activity.createdAt.toLocaleString('pt-PT')}\n`)
    })

    // 2. Verificar badges dos usuários
    const userBadges = await prisma.userBadge.findMany({
      take: 5,
      orderBy: { earnedAt: 'desc' },
      include: {
        user: {
          select: { name: true, role: true }
        },
        badge: {
          select: { name: true, description: true, category: true }
        }
      }
    })

    console.log('🏆 Badges Conquistados:')
    userBadges.forEach((userBadge, index) => {
      console.log(`${index + 1}. ${userBadge.badge.name} - ${userBadge.user.name}`)
      console.log(`   Descrição: ${userBadge.badge.description}`)
      console.log(`   Categoria: ${userBadge.badge.category}`)
      console.log(`   Data: ${userBadge.earnedAt.toLocaleString('pt-PT')}\n`)
    })

    // 3. Verificar referrals ativos
    const referrals = await prisma.referral.findMany({
      take: 5,
      include: {
        referrer: {
          select: { name: true, role: true }
        },
        referred: {
          select: { name: true, role: true }
        }
      }
    })

    console.log('🔗 Referrals:')
    referrals.forEach((referral, index) => {
      console.log(`${index + 1}. ${referral.referrer.name} → ${referral.referred?.name || 'Pendente'}`)
      console.log(`   Código: ${referral.code}`)
      console.log(`   Status: ${referral.status}`)
      console.log(`   Recompensa: €${referral.rewardAmount}\n`)
    })

    // 4. Verificar estatísticas gerais
    const totalActivities = await prisma.viralActivity.count()
    const totalUserBadges = await prisma.userBadge.count()
    const totalReferrals = await prisma.referral.count()
    const totalPoints = await prisma.user.aggregate({
      _sum: { viralPoints: true }
    })

    console.log('📈 Estatísticas Gerais:')
    console.log(`Total de Atividades Virais: ${totalActivities}`)
    console.log(`Total de Badges Conquistados: ${totalUserBadges}`)
    console.log(`Total de Referrals: ${totalReferrals}`)
    console.log(`Total de Pontos Virais: ${totalPoints._sum.viralPoints || 0}`)

    // 5. Verificar usuários com mais pontos virais
    const topUsers = await prisma.user.findMany({
      where: {
        viralPoints: { gt: 0 }
      },
      orderBy: { viralPoints: 'desc' },
      take: 5,
      select: {
        name: true,
        role: true,
        viralPoints: true,
        email: true
      }
    })

    console.log('\n🏅 Top 5 Usuários com Mais Pontos Virais:')
    topUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.role})`)
      console.log(`   Email: ${user.email}`)
      console.log(`   Pontos: ${user.viralPoints}\n`)
    })

    console.log('✅ Teste do Sistema Viral Concluído!')

  } catch (error) {
    console.error('❌ Erro ao testar sistema viral:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Executar teste
testViralSystem()
