import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 Criando apps do sistema...')

  // Verificar se já existem apps
  const existingApps = await prisma.$queryRaw`SELECT COUNT(*) as count FROM app_definitions` as any[]
  const appCount = Number(existingApps[0]?.count || 0)

  if (appCount > 0) {
    console.log(`✅ Já existem ${appCount} apps na base de dados`)
    return
  }

  // Criar apps do sistema
  const apps = [
    {
      appId: 'moloni',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Integração completa com Moloni para emissão automática de faturas e gestão fiscal.',
      category: 'finance',
      isPaid: false,
      monthlyPrice: 0,
      hasTrialPeriod: false,
      trialDays: 0,
      features: ['Emissão automática de faturas', 'Sincronização de produtos', 'Relatórios fiscais', 'Gestão de clientes', 'Backup automático'],
      requiredPlans: ['REVY BASIC', 'REVY PRO'],
      isActive: true,
      isPopular: false
    },
    {
      appId: 'newsletter-pro',
      name: 'Newsletter Pro',
      description: 'Sistema avançado de email marketing com templates profissionais e automação.',
      category: 'marketing',
      isPaid: true,
      monthlyPrice: 14.99,
      hasTrialPeriod: true,
      trialDays: 30,
      features: ['Templates profissionais', 'Automação de campanhas', 'Segmentação avançada', 'Analytics detalhados', 'A/B Testing'],
      requiredPlans: ['REVY PRO'],
      isActive: true,
      isPopular: true
    },
    {
      appId: 'customer-manager',
      name: 'Customer Manager',
      description: 'CRM completo para gestão de clientes, histórico de reparações e follow-up automático.',
      category: 'crm',
      isPaid: true,
      monthlyPrice: 9.99,
      hasTrialPeriod: true,
      trialDays: 14,
      features: ['Base de dados de clientes', 'Histórico completo', 'Follow-up automático', 'Segmentação', 'Relatórios'],
      requiredPlans: ['REVY BASIC', 'REVY PRO'],
      isActive: true,
      isPopular: false
    },
    {
      appId: 'inventory-tracker',
      name: 'Inventory Tracker',
      description: 'Gestão avançada de stock com alertas automáticos e previsão de necessidades.',
      category: 'productivity',
      isPaid: true,
      monthlyPrice: 7.99,
      hasTrialPeriod: true,
      trialDays: 21,
      features: ['Controle de stock', 'Alertas automáticos', 'Previsão de compras', 'Códigos de barras', 'Relatórios'],
      requiredPlans: ['REVY PRO'],
      isActive: true,
      isPopular: false
    },
    {
      appId: 'sms-notifications',
      name: 'SMS Notifications',
      description: 'Envio automático de SMS para clientes sobre status das reparações.',
      category: 'communication',
      isPaid: true,
      monthlyPrice: 4.99,
      hasTrialPeriod: true,
      trialDays: 7,
      features: ['SMS automáticos', 'Templates personalizáveis', 'Agendamento', 'Relatórios de entrega', 'Integração WhatsApp'],
      requiredPlans: ['REVY BASIC', 'REVY PRO'],
      isActive: true,
      isPopular: true
    },
    {
      appId: 'analytics-pro',
      name: 'Analytics Pro',
      description: 'Relatórios avançados e dashboards personalizáveis para análise de negócio.',
      category: 'analytics',
      isPaid: true,
      monthlyPrice: 5.99,
      hasTrialPeriod: true,
      trialDays: 30,
      features: ['Dashboards personalizáveis', 'Relatórios automáticos', 'Métricas de performance', 'Exportação de dados', 'Alertas inteligentes'],
      requiredPlans: ['REVY PRO'],
      isActive: true,
      isPopular: false
    }
  ]

  for (const app of apps) {
    await prisma.$queryRaw`
      INSERT INTO app_definitions (
        "id", "appId", name, description, category,
        "isPaid", "monthlyPrice", "hasTrialPeriod", "trialDays",
        features, "requiredPlans", "isActive", "isPopular",
        "createdAt", "updatedAt"
      )
      VALUES (
        gen_random_uuid(), ${app.appId}, ${app.name}, ${app.description}, ${app.category},
        ${app.isPaid}, ${app.monthlyPrice}, ${app.hasTrialPeriod}, ${app.trialDays},
        ${JSON.stringify(app.features)}::jsonb, ${JSON.stringify(app.requiredPlans)}::jsonb,
        ${app.isActive}, ${app.isPopular},
        NOW(), NOW()
      )
    `
  }

  console.log(`✅ ${apps.length} apps criadas com sucesso!`)
}

main()
  .catch((e) => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
