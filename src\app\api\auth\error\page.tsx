'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import AutoTranslate from '@/components/ui/AutoTranslate'
import { Suspense } from 'react'

function AuthErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return 'Erro de configuração do servidor'
      case 'AccessDenied':
        return 'Acesso negado'
      case 'Verification':
        return 'Token de verificação inválido'
      case 'Default':
      default:
        return 'Ocorreu um erro durante a autenticação'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="mt-6 text-2xl font-bold text-gray-900">
              <AutoTranslate text="Erro de Autenticação" />
            </CardTitle>
            <CardDescription>
              <AutoTranslate text={getErrorMessage(error)} />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                <AutoTranslate text="Por favor, tente novamente ou contacte o suporte se o problema persistir." />
              </p>
              <div className="space-y-2">
                <Button asChild className="w-full">
                  <Link href="/auth/signin">
                    <AutoTranslate text="Tentar Novamente" />
                  </Link>
                </Button>
                <Button variant="outline" asChild className="w-full">
                  <Link href="/">
                    <AutoTranslate text="Voltar ao Início" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function AuthError() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AuthErrorContent />
    </Suspense>
  )
}
