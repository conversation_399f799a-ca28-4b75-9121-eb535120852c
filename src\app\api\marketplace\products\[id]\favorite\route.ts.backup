import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 401 }
      )
    }

    // Verificar se o produto existe
    const product = await prisma.marketplaceProduct.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se já está nos favoritos
    const existingFavorite = await prisma.productFavorite.findUnique({
      where: {
        customerId_productId: {
          customerId: session.user.id,
          productId: resolvedParams.id
        }
      }
    })

    if (existingFavorite) {
      return NextResponse.json(
        { message: 'Produto já está nos favoritos' },
        { status: 400 }
      )
    }

    // Adicionar aos favoritos
    await prisma.productFavorite.create({
      data: {
        customerId: session.user.id,
        productId: resolvedParams.id
      }
    })

    return NextResponse.json({ message: 'Produto adicionado aos favoritos' })

  } catch (error) {
    console.error('Erro ao adicionar favorito:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 401 }
      )
    }

    // Remover dos favoritos
    await prisma.productFavorite.deleteMany({
      where: {
        customerId: session.user.id,
        productId: resolvedParams.id
      }
    })

    return NextResponse.json({ message: 'Produto removido dos favoritos' })

  } catch (error) {
    console.error('Erro ao remover favorito:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
