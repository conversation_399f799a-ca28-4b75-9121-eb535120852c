import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Buscar perfil do usuário
      const profile = await prisma.profile.findUnique({
        where: { userId: session.user.id },
        include: {
          shopConfig: true}
      })

      if (!profile) {
        return NextResponse.json({ error: "Perfil não encontrado" }, { status: 404 })
      }

      // Se não existe configuração da loja, criar uma padrão
      let shopConfig = profile.shopConfig
      if (!shopConfig) {
        shopConfig = await prisma.shopConfig.create({
          data: {
            profileId: profile.id,
            aboutUs: ,
            contactInfo: '',
            homePageText: '',
            stripePublicKey: '',
            stripeSecretKey: '',
            shippingRates: [],
            marketingText: '',
            customCss: '',
            logoUrl: '',
            bannerUrl: '',
            primaryColor: '#000000',
            secondaryColor: '#666666'
          
}
        })
      }

      return NextResponse.json({ 'shopConfig'})

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error('Erro ao buscar configuração da loja:', 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }

    const body = await request.json()
    const { aboutUs,
      contactInfo,
      homePageText,
      stripePublicKey,
      stripeSecretKey,
      shippingRates,
      marketingText,
      customCss,
      logoUrl,
      bannerUrl,
      primaryColor,
      secondaryColor,
      freeShippingThreshold,
      defaultShippingRate,
      shippingByCountry,
      taxEnabled,
      taxIncluded,
      defaultTaxRate,
      taxByCountry } = body

    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Buscar perfil do usuário
      const profile = await prisma.profile.findUnique({
        where: { userId: session.user.id }
      })

      if (!profile) {
        return NextResponse.json({ error: Perfil não encontrado 
}, { status: 404 })
      }

      // Atualizar ou criar configuração da loja
      const shopConfig = await prisma.shopConfig.upsert({
        where: { profileId: profile.id },
        update: {
          aboutUs,
          contactInfo,
          homePageText,
          stripePublicKey,
          stripeSecretKey,
          shippingRates,
          marketingText,
          customCss,
          logoUrl,
          bannerUrl,
          primaryColor,
          secondaryColor,
          freeShippingThreshold,
          defaultShippingRate,
          shippingByCountry,
          taxEnabled,
          taxIncluded,
          defaultTaxRate, taxByCountry },
        create: {
          profileId: profile.id,
          aboutUs: aboutUs || ,
          contactInfo: contactInfo || '',
          homePageText: homePageText || '',
          stripePublicKey: stripePublicKey || '',
          stripeSecretKey: stripeSecretKey || '',
          shippingRates: shippingRates || [],
          marketingText: marketingText || '',
          customCss: customCss || '',
          logoUrl: logoUrl || '',
          bannerUrl: bannerUrl || '',
          primaryColor: primaryColor || '#000000',
          secondaryColor: secondaryColor || '#666666',
          freeShippingThreshold: freeShippingThreshold || 50,
          defaultShippingRate: defaultShippingRate || 5,
          shippingByCountry: shippingByCountry || [],
          taxEnabled: taxEnabled || false,
          taxIncluded: taxIncluded !== false,
          defaultTaxRate: defaultTaxRate || 23,
          taxByCountry: taxByCountry || []
        
}
      })

      // Se o logoUrl foi atualizado, também atualizar no perfil para manter consistência
      if (logoUrl && logoUrl !== profile.logo) {
        await prisma.profile.update({
          where: { id: profile.id },
          data: { logo: logoUrl}
        })
      }

      return NextResponse.json({
        message: Configuração atualizada com sucesso,
        'shopConfig'
})

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error('Erro ao atualizar configuração da loja:', 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
