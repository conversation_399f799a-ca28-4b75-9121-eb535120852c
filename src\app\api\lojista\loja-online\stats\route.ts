import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso à loja online
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true}
        }
      }
    })

    if (!user?.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: "Funcionalidade não disponível no seu plano" },
        { status: 403 }
      )
    }

    // Calcular estatísticas
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    // Produtos ativos no marketplace (que podem ser usados na loja online)
    const totalProducts = await prisma.marketplaceProduct.count({
      where: {
        sellerId: session.user.id,
        isActive: true
}
    })

    // Encomendas da loja online (orders com source = ONLINE_STORE)
    const totalOrders = await prisma.order.count({
      where: {
        sellerId: session.user.id,
        source: ONLINE_STORE
}
    })

    // Clientes únicos que fizeram encomendas na loja online
    const uniqueCustomers = await prisma.order.findMany({
      where: {
        sellerId: session.user.id,
        source: ONLINE_STORE},
      select: {
        customerId: true},
      distinct: [customerId]
    
})

    // Receita mensal da loja online
    const monthlyOrders = await prisma.order.findMany({
      where: {
        sellerId: session.user.id,
        source: ONLINE_STORE,
        createdAt: {
          gte: startOfMonth},
        status: {
          in: [COMPLETED, 'DELIVERED']
        
}
      },
      select: {
        total: true}
    })

    const monthlyRevenue = monthlyOrders.reduce((sum, 'order') => {
      return sum + Number(order.total)
    }, 0)

    const stats = {
      totalProducts,
      totalOrders,
      totalCustomers: uniqueCustomers.length, monthlyRevenue }

    return NextResponse.json({
      success: true, stats })

  } catch (error) {
    console.error("Erro ao buscar estatísticas da loja online:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
