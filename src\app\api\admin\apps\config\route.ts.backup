import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações personalizadas das apps (globais)
    const configs = await prisma.appConfig.findMany()

    return NextResponse.json({
      configs: configs.map(config => ({
        appId: config.appId,
        ...config.settings
      }))
    })

  } catch (error) {
    console.error('Erro ao buscar configurações:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { appId, name, description, monthlyPrice, isActive, isPopular, features } = await request.json()

    if (!appId) {
      return NextResponse.json(
        { message: 'App ID é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se já existe configuração para esta app
    const existingConfig = await prisma.appConfig.findFirst({
      where: { appId: appId }
    })

    if (existingConfig) {
      // Atualizar configuração existente
      await prisma.appConfig.update({
        where: { id: existingConfig.id },
        data: {
          settings: {
            name,
            description,
            monthlyPrice,
            isActive,
            isPopular,
            features
          }
        }
      })
    } else {
      // Criar nova configuração usando um userId padrão ou o primeiro admin encontrado
      let adminUserId = session.user.id

      try {
        // Verificar se o usuário atual existe
        const currentUser = await prisma.user.findUnique({
          where: { id: session.user.id }
        })

        if (!currentUser) {
          // Se não existe, buscar qualquer admin
          const anyAdmin = await prisma.user.findFirst({
            where: { role: 'ADMIN' }
          })

          if (anyAdmin) {
            adminUserId = anyAdmin.id
          } else {
            // Se não há admin, criar um temporário
            const tempAdmin = await prisma.user.create({
              data: {
                email: '<EMAIL>',
                name: 'Admin',
                role: 'ADMIN',
                emailVerified: new Date()
              }
            })
            adminUserId = tempAdmin.id
          }
        }
      } catch (error) {
        console.error('Erro ao verificar usuário:', error)
        // Usar o ID da sessão mesmo assim
      }

      await prisma.appConfig.create({
        data: {
          userId: adminUserId,
          appId: appId,
          settings: {
            name,
            description,
            monthlyPrice,
            isActive,
            isPopular,
            features
          }
        }
      })
    }

    return NextResponse.json({
      message: 'Configuração salva com sucesso'
    })

  } catch (error) {
    console.error('Erro ao salvar configuração:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
