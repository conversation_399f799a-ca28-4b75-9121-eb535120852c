'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { MessageSquare, Truck, FileText, Settings, Save, AlertCircle, CheckCircle } from 'lucide-react'

interface WhatsAppConfig {
  accessToken: string
  phoneNumberId: string
  businessAccountId: string
  webhookVerifyToken: string
  enabled: boolean
  webhookUrl: string
}

interface CTTConfig {
  apiKey: string
  clientId: string
  clientSecret: string
  environment: 'sandbox' | 'production'
  enabled: boolean
}

interface ElectronicInvoiceConfig {
  certificatePath: string
  certificatePassword: string
  companyTaxNumber: string
  companyName: string
  softwareCertificate: string
  environment: 'test' | 'production'
  enabled: boolean
}

export default function IntegrationsPage() {
  const [whatsappConfig, setWhatsappConfig] = useState<WhatsAppConfig>({
    accessToken: '',
    phoneNumberId: '',
    businessAccountId: '',
    webhookVerifyToken: '',
    enabled: false,
    webhookUrl: ''
  })

  const [cttConfig, setCttConfig] = useState<CTTConfig>({
    apiKey: '',
    clientId: '',
    clientSecret: '',
    environment: 'sandbox',
    enabled: false
  })

  const [invoiceConfig, setInvoiceConfig] = useState<ElectronicInvoiceConfig>({
    certificatePath: '',
    certificatePassword: '',
    companyTaxNumber: '',
    companyName: '',
    softwareCertificate: '',
    environment: 'test',
    enabled: false
  })

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('whatsapp')

  useEffect(() => {
    loadConfigurations()
  }, [])

  const loadConfigurations = async () => {
    try {
      const [whatsappRes, cttRes, invoiceRes] = await Promise.all([
        fetch('/api/admin/integrations/whatsapp'),
        fetch('/api/admin/integrations/ctt'),
        fetch('/api/admin/integrations/electronic-invoices')
      ])

      if (whatsappRes.ok) {
        const whatsappData = await whatsappRes.json()
        setWhatsappConfig(whatsappData)
      }

      if (cttRes.ok) {
        const cttData = await cttRes.json()
        setCttConfig(cttData)
      }

      if (invoiceRes.ok) {
        const invoiceData = await invoiceRes.json()
        setInvoiceConfig(invoiceData)
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error)
      toast.error('Erro ao carregar configurações')
    } finally {
      setLoading(false)
    }
  }

  const saveWhatsAppConfig = async () => {
    setSaving('whatsapp')
    try {
      const response = await fetch('/api/admin/integrations/whatsapp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(whatsappConfig)
      })

      if (response.ok) {
        alert('Configuração WhatsApp salva com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar configuração')
      }
    } catch (error) {
      alert('Erro ao salvar configuração WhatsApp')
    } finally {
      setSaving(null)
    }
  }

  const saveCTTConfig = async () => {
    setSaving('ctt')
    try {
      const response = await fetch('/api/admin/integrations/ctt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cttConfig)
      })

      if (response.ok) {
        alert('Configuração CTT salva com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar configuração')
      }
    } catch (error) {
      alert('Erro ao salvar configuração CTT')
    } finally {
      setSaving(null)
    }
  }

  const saveInvoiceConfig = async () => {
    setSaving('invoice')
    try {
      const response = await fetch('/api/admin/integrations/electronic-invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceConfig)
      })

      if (response.ok) {
        alert('Configuração de faturas eletrónicas salva com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar configuração')
      }
    } catch (error) {
      alert('Erro ao salvar configuração de faturas eletrónicas')
    } finally {
      setSaving(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-black">Revify Admin</Link>
              <span className="ml-3 text-xs text-gray-500">Integrações</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Integrações</h1>
          <p className="text-gray-600">Configure as integrações externas da plataforma</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* WhatsApp Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <MessageSquare className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">WhatsApp Business</h3>
                <p className="text-sm text-gray-600">Notificações automáticas</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Access Token</label>
                <input
                  type="password"
                  value={whatsappConfig.accessToken}
                  onChange={(e) => setWhatsappConfig(prev => ({ ...prev, accessToken: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Token de acesso"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number ID</label>
                <input
                  type="text"
                  value={whatsappConfig.phoneNumberId}
                  onChange={(e) => setWhatsappConfig(prev => ({ ...prev, phoneNumberId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="ID do número"
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={whatsappConfig.enabled}
                  onChange={(e) => setWhatsappConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  className="mr-2"
                />
                <label className="text-sm text-gray-700">Ativar WhatsApp</label>
              </div>
              <button
                onClick={saveWhatsAppConfig}
                disabled={saving === 'whatsapp'}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {saving === 'whatsapp' ? 'A guardar...' : 'Guardar'}
              </button>
            </div>
          </div>

          {/* CTT Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Truck className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">CTT Tracking</h3>
                <p className="text-sm text-gray-600">Tracking de envios</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                <input
                  type="password"
                  value={cttConfig.apiKey}
                  onChange={(e) => setCttConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Chave da API"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Client ID</label>
                <input
                  type="text"
                  value={cttConfig.clientId}
                  onChange={(e) => setCttConfig(prev => ({ ...prev, clientId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="ID do cliente"
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={cttConfig.enabled}
                  onChange={(e) => setCttConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  className="mr-2"
                />
                <label className="text-sm text-gray-700">Ativar CTT</label>
              </div>
              <button
                onClick={saveCTTConfig}
                disabled={saving === 'ctt'}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {saving === 'ctt' ? 'A guardar...' : 'Guardar'}
              </button>
            </div>
          </div>

          {/* Electronic Invoices Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <FileText className="w-8 h-8 text-purple-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">Faturas Eletrónicas</h3>
                <p className="text-sm text-gray-600">Conformidade AT</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Empresa</label>
                <input
                  type="text"
                  value={invoiceConfig.companyName}
                  onChange={(e) => setInvoiceConfig(prev => ({ ...prev, companyName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Nome da empresa"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">NIF</label>
                <input
                  type="text"
                  value={invoiceConfig.companyTaxNumber}
                  onChange={(e) => setInvoiceConfig(prev => ({ ...prev, companyTaxNumber: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="*********"
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={invoiceConfig.enabled}
                  onChange={(e) => setInvoiceConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  className="mr-2"
                />
                <label className="text-sm text-gray-700">Ativar Faturas</label>
              </div>
              <button
                onClick={saveInvoiceConfig}
                disabled={saving === 'invoice'}
                className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                {saving === 'invoice' ? 'A guardar...' : 'Guardar'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
