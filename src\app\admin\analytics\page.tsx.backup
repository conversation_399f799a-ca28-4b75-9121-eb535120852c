'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { 
  TrendingUp, 
  Users, 
  Package, 
  DollarSign, 
  ShoppingCart, 
  Wrench,
  Calendar,
  BarChart3
} from 'lucide-react'

interface Analytics {
  users: {
    total: number
    customers: number
    repairShops: number
    couriers: number
    newThisMonth: number
  }
  orders: {
    total: number
    pending: number
    completed: number
    revenue: number
    averageValue: number
  }
  repairs: {
    total: number
    pending: number
    completed: number
    revenue: number
    averageValue: number
  }
  marketplace: {
    products: number
    orders: number
    revenue: number
    topSellers: Array<{
      name: string
      sales: number
      revenue: number
    }>
  }
}

export default function AdminAnalyticsPage() {
  const { data: session } = useSession()
  const [analytics, setAnalytics] = useState<Analytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/analytics?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data)
      }
    } catch (error) {
      console.error('Erro ao carregar analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (session?.user?.role !== 'ADMIN') {
    return <div>Acesso negado</div>
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
              <p className="mt-2 text-gray-600">Visão geral da plataforma</p>
            </div>
            
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Últimos 7 dias</option>
              <option value="30d">Últimos 30 dias</option>
              <option value="90d">Últimos 90 dias</option>
              <option value="1y">Último ano</option>
            </select>
          </div>
        </div>

        {analytics && (
          <>
            {/* Users Stats */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Usuários
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Users className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total</p>
                      <p className="text-2xl font-bold text-gray-900">{analytics.users.total}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Users className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Clientes</p>
                      <p className="text-2xl font-bold text-gray-900">{analytics.users.customers}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Wrench className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Lojistas</p>
                      <p className="text-2xl font-bold text-gray-900">{analytics.users.repairShops}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Package className="h-8 w-8 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Estafetas</p>
                      <p className="text-2xl font-bold text-gray-900">{analytics.users.couriers}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <TrendingUp className="h-8 w-8 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Novos</p>
                      <p className="text-2xl font-bold text-gray-900">{analytics.users.newThisMonth}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Marketplace */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  Marketplace
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Produtos</span>
                    <span className="font-semibold">{analytics.marketplace.products}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Encomendas</span>
                    <span className="font-semibold">{analytics.marketplace.orders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Receita</span>
                    <span className="font-semibold text-green-600">€{analytics.marketplace.revenue.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Repairs */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Wrench className="w-5 h-5 mr-2" />
                  Reparações
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total</span>
                    <span className="font-semibold">{analytics.repairs.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pendentes</span>
                    <span className="font-semibold text-yellow-600">{analytics.repairs.pending}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Concluídas</span>
                    <span className="font-semibold text-green-600">{analytics.repairs.completed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Receita</span>
                    <span className="font-semibold text-green-600">€{analytics.repairs.revenue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Valor Médio</span>
                    <span className="font-semibold">€{analytics.repairs.averageValue.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Top Sellers */}
            {analytics.marketplace.topSellers.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Top Vendedores
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-2 text-sm font-medium text-gray-600">Vendedor</th>
                        <th className="text-right py-2 text-sm font-medium text-gray-600">Vendas</th>
                        <th className="text-right py-2 text-sm font-medium text-gray-600">Receita</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analytics.marketplace.topSellers.map((seller, index) => (
                        <tr key={index} className="border-b border-gray-100">
                          <td className="py-3 text-sm text-gray-900">{seller.name}</td>
                          <td className="py-3 text-sm text-gray-900 text-right">{seller.sales}</td>
                          <td className="py-3 text-sm text-green-600 text-right font-semibold">
                            €{seller.revenue.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
