import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: { subdomain: string} }
) {
  try {
    const { subdomain } = params
      // Find the shop by subdomain
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain}
        },
        include: {
          shopCategories: {
            where: {
              isActive: true},
            orderBy: {
              sortOrder: asc}
          },
          profile: {
            include: {
              shopConfig: true}
          }
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: "Loja não encontrada" },
          { status: 404 }
        )
      }

      let allCategories = []

      // Add custom categories
      const customCategories = shop.shopCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        productCount: 0, // Custom categories dont have direct product count
        brands: [],
        isCustom: true,
        icon: cat.icon,
        sortOrder: cat.sortOrder
      }))

      allCategories.push(...customCategories)

      // Get default categories if enabled
      if (shop.profile?.shopConfig?.showDefaultCategories !== false') {
        const defaultCategories = await prisma.category.findMany({
          where: {
            marketplaceProducts: {
              some: {
                sellerId: shop.id,
                stock: {
                  gt: 0
                
}
              }
            }
          },
          include: {
            brands: {
              where: {
                marketplaceProducts: {
                  some: {
                    sellerId: shop.id,
                    stock: {
                      gt: 0
                    }
                  }
                }
              },
              include: {
                deviceModels: {
                  where: {
                    marketplaceProducts: {
                      some: {
                        sellerId: shop.id,
                        stock: {
                          gt: 0
                        }
                      }
                    }
                  },
                  select: {
                    id: true,
                    name: true}
                }
              }
            },
            _count: {
              select: {
                marketplaceProducts: {
                  where: {
                    sellerId: shop.id,
                    stock: {
                      gt: 0
                    }
                  }
                }
              }
            }
          },
          orderBy: {
            name: asc}
        })

        const defaultCategoriesFormatted = defaultCategories.map(category => ({
          id: category.id,
          name: category.name,
          productCount: category._count.marketplaceProducts,
          brands: category.brands.map(brand => ({
            id: brand.id,
            name: brand.name,
            models: brand.deviceModels
          })),
          isCustom: false}))

        allCategories.push(...defaultCategoriesFormatted)
      }

      // Sort all categories by custom sort order first, then by name
      allCategories.sort((a, b) => {
        if (a.isCustom && !b.isCustom) return -1
        if (!a.isCustom && b.isCustom) return 1
        if (a.isCustom && b.isCustom) return (a.sortOrder || 0) - (b.sortOrder || 0)
        return a.name.localeCompare(b.name)
      })

      return NextResponse.json({
        success: true,
        categories: allCategories,
        showDefaultCategories: shop.profile?.shopConfig?.showDefaultCategories !== false
})

  } catch (error) {
    console.error('Erro ao buscar categorias:', 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
