'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Plus, X } from 'lucide-react'
import Link from 'next/link'

export default function NovoPlanoPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    monthlyPrice: '',
    yearlyPrice: '',
    features: [] as string[],
    maxProducts: '',
    maxRepairs: '',
    marketplaceCommission: '5.0',
    repairCommission: '5.0',
    smsAccess: false,
    whatsappAccess: false,
    emailSupport: true,
    paymentDelayDays: '7',
    sparePartsDiscount: '0.0',
    recommendedProductsEnabled: false,
    recommendedProductsDays: '0',
    certifiedBadge: false,
    priority: '0',
    moloniIntegration: false,
    miniStore: false,
    individualRepairs: false,
    isPopular: false
  })
  const [newFeature, setNewFeature] = useState('')

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData({
        ...formData,
        features: [...formData.features, newFeature.trim()]
      })
      setNewFeature('')
    }
  }

  const removeFeature = (index: number) => {
    setFormData({
      ...formData,
      features: formData.features.filter((_, i) => i !== index)
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch('/api/admin/subscription-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          monthlyPrice: parseFloat(formData.monthlyPrice),
          yearlyPrice: parseFloat(formData.yearlyPrice),
          maxProducts: formData.maxProducts ? parseInt(formData.maxProducts) : null,
          maxRepairs: formData.maxRepairs ? parseInt(formData.maxRepairs) : null,
          marketplaceCommission: parseFloat(formData.marketplaceCommission),
          repairCommission: parseFloat(formData.repairCommission),
          paymentDelayDays: parseInt(formData.paymentDelayDays),
          sparePartsDiscount: parseFloat(formData.sparePartsDiscount),
          recommendedProductsEnabled: formData.recommendedProductsEnabled,
          recommendedProductsDays: parseInt(formData.recommendedProductsDays),
          priority: parseInt(formData.priority)
        })
      })

      if (response.ok) {
        alert('Plano criado com sucesso!')
        router.push('/admin/planos')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao criar plano')
      }
    } catch (error) {
      console.error('Erro ao criar plano:', error)
      alert('Erro ao criar plano')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/planos"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Novo Plano</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Informações Básicas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome do Plano *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Ex: Revy PRO"
                />
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isPopular}
                    onChange={(e) => setFormData({...formData, isPopular: e.target.checked})}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">Plano Popular</span>
                </label>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Descrição
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Descrição do plano..."
                />
              </div>
            </div>
          </div>

          {/* Preços */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preços</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preço Mensal (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.monthlyPrice}
                  onChange={(e) => setFormData({...formData, monthlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="9.90"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preço Anual (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.yearlyPrice}
                  onChange={(e) => setFormData({...formData, yearlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="99.00"
                />
              </div>
            </div>
          </div>

          {/* Limites */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Limites</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Máximo de Produtos
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.maxProducts}
                  onChange={(e) => setFormData({...formData, maxProducts: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Deixe vazio para ilimitado"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Máximo de Reparações/mês
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.maxRepairs}
                  onChange={(e) => setFormData({...formData, maxRepairs: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Deixe vazio para ilimitado"
                />
              </div>
            </div>
          </div>

          {/* Comissões */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Comissões (%)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Comissão Marketplace
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={formData.marketplaceCommission}
                  onChange={(e) => setFormData({...formData, marketplaceCommission: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
                <p className="text-xs text-gray-500 mt-1">0 = sem comissão</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Comissão Reparações
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={formData.repairCommission}
                  onChange={(e) => setFormData({...formData, repairCommission: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
                <p className="text-xs text-gray-500 mt-1">0 = sem comissão</p>
              </div>
            </div>
          </div>

          {/* Funcionalidades */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Funcionalidades</h3>
            
            {/* Features List */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lista de Funcionalidades
              </label>
              <div className="flex mb-3">
                <input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Ex: Dashboard avançado"
                />
                <button
                  type="button"
                  onClick={addFeature}
                  className="px-4 py-2 bg-black text-white rounded-r-lg hover:bg-gray-800"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {formData.features.length > 0 && (
                <div className="space-y-2">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">{feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Checkboxes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.smsAccess}
                  onChange={(e) => setFormData({...formData, smsAccess: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Acesso a SMS</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.whatsappAccess}
                  onChange={(e) => setFormData({...formData, whatsappAccess: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Acesso a WhatsApp</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.certifiedBadge}
                  onChange={(e) => setFormData({...formData, certifiedBadge: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Badge Revify Certificada</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.moloniIntegration}
                  onChange={(e) => setFormData({...formData, moloniIntegration: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Integração Moloni</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.miniStore}
                  onChange={(e) => setFormData({...formData, miniStore: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Mini Store</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.individualRepairs}
                  onChange={(e) => setFormData({...formData, individualRepairs: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Reparações Individuais</span>
              </label>
            </div>
          </div>

          {/* Marketplace Features */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Funcionalidades do Marketplace</h3>
            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.recommendedProductsEnabled}
                  onChange={(e) => setFormData({...formData, recommendedProductsEnabled: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm">Produtos Recomendados Habilitados</span>
              </label>

              {formData.recommendedProductsEnabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dias de Destaque para Produtos Recomendados
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.recommendedProductsDays}
                    onChange={(e) => setFormData({...formData, recommendedProductsDays: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="Ex: 30 (dias)"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Número de dias que os produtos ficam em destaque na seção de recomendados. 0 = sem limite.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Configurações Avançadas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Configurações Avançadas</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dias para Receber Pagamentos
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.paymentDelayDays}
                  onChange={(e) => setFormData({...formData, paymentDelayDays: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Desconto Loja Peças (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={formData.sparePartsDiscount}
                  onChange={(e) => setFormData({...formData, sparePartsDiscount: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prioridade
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.priority}
                  onChange={(e) => setFormData({...formData, priority: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
                <p className="text-xs text-gray-500 mt-1">Maior = mais destaque</p>
              </div>
            </div>
          </div>

          {/* Submit */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/planos"
              className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isLoading ? 'Criando...' : 'Criar Plano'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
