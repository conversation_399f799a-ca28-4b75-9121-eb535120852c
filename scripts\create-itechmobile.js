const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createItechMobile() {
  try {
    console.log('🏪 Criando loja iTech Mobile...')

    // Verificar se já existe
    const existing = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: 'itechmobile'
        }
      }
    })

    if (existing) {
      console.log('✅ Loja iTech Mobile já existe!')
      console.log('📧 Email:', existing.email)
      console.log('🌐 URL: http://localhost:3000/shop/itechmobile')
      return
    }

    // Criar usuário
    const hashedPassword = await bcrypt.hash('123456', 10)
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'iTech Mobile',
        password: hashedPassword,
        role: 'REPAIR_SHOP',
        isVerified: true,
        profile: {
          create: {
            companyName: 'iTech Mobile - Reparações',
            description: 'Especialistas em reparação de dispositivos móveis',
            phone: '+*********** 678',
            address: 'Rua da Tecnologia, 123',
            city: 'Porto',
            postalCode: '4000-001',
            country: 'Portugal',
            customSubdomain: 'itechmobile',
            workingBrands: ['1', '2', '3'],
            workingCategories: ['1', '2'],
            workingProblems: ['1', '2', '3', '4', '5'],
            serviceRadius: 50,
            averageRepairTime: 24,
            shippingEnabled: true,
            freeShippingThreshold: 50.00
          }
        }
      }
    })

    console.log('✅ Loja iTech Mobile criada com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: 123456')
    console.log('🌐 URL: http://localhost:3000/shop/itechmobile')

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createItechMobile()
