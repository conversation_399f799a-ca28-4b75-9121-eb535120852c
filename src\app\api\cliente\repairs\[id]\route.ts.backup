import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id

    // Buscar reparação com todos os dados necessários
    const repair = await prisma.repair.findUnique({
      where: { id: repairId },
      include: {
        customer: true,
        repairShop: {
          include: {
            profile: true
          }
        },
        deviceModel: {
          include: {
            brand: true,
            category: true
          }
        },
        problemType: true,
        payments: true
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se o usuário tem acesso
    if (repair.customerId !== session.user.id && repair.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar avaliação se existir
    const review = await prisma.review.findFirst({
      where: {
        repairId: repairId,
        customerId: repair.customerId
      }
    })

    return NextResponse.json({
      ...repair,
      review: review
    })

  } catch (error) {
    console.error('Erro ao buscar reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
