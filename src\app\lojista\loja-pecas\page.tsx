'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Search, Filter, ShoppingCart, Package, Plus, Minus, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
interface SparePart {
  id: string
  name: string
  description: string
  sku: string
  price: number
  stock: number
  images: string[]
  category: {
    id: string
    name: string}
  brand?: {
    id: string
    name: string}
  deviceModel?: {
    id: string
    name: string}
  availability: boolean
  deliveryTime: number
  compatibility: string[]
}

interface CartItem {
  partId: string
  part: SparePart
  quantity: number}

export default function LojaPecasPage() {
  const { data: session } = useSession()
  const [parts, setParts] = useState<SparePart[]>([])
  const [filteredParts, setFilteredParts] = useState<SparePart[]>([])
  const [cart, setCart] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('ALL')
  const [selectedBrand, setSelectedBrand] = useState('ALL')
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [showCart, setShowCart] = useState(false)
  const [showCheckout, setShowCheckout] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('card')
  const [showToast, setShowToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')

  useEffect(() => {
    fetchData()
    loadCartFromStorage()
  }, [])

  useEffect(() => {
    filterParts()
  }, [parts, searchTerm, selectedCategory, selectedBrand])

  useEffect(() => {
    saveCartToStorage()
  }, [cart])

  const fetchData = async () => {
    try {
      const [partsRes, categoriesRes, brandsRes] = await Promise.all([
        fetch('/api/lojista/spare-parts'),
        fetch('/api/categories'),
        fetch('/api/brands')
      ])

      if (partsRes.ok) {
        const partsData = await partsRes.json()
        setParts(partsData.parts)
      }

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories)
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData.brands)
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const filterParts = () => {
    let filtered = parts

    if (searchTerm) {
      filtered = filtered.filter(part =>
        part.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== 'ALL') {
      filtered = filtered.filter(part => part.category.id === 'selectedCategory')
    }

    if (selectedBrand !== 'ALL') {
      filtered = filtered.filter(part => part.brand?.id === 'selectedBrand')
    }

    setFilteredParts(filtered)
  }

  const addToCart = (part: SparePart) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.partId === part.id)
      if (existingItem) {
        if (existingItem.quantity < part.stock) {
          showToastMessage(`${part.name} adicionado ao carrinho`)
          return prevCart.map(item =>
            item.partId === part.id
              ? { ...item, quantity: Math.min(item.quantity + 1, part.stock) }
              : item)
        } else {
          showToastMessage('Stock insuficiente')
          'return prevCart'}
      } else {
        showToastMessage(`${part.name} adicionado ao carrinho`)
        return [...prevCart, { partId: part.id, part, quantity: 1 }]
      }
    })
  }

  const showToastMessage = (message: string) => {
    setToastMessage(message)
    setShowToast(true)
    setTimeout(() => setShowToast(false), 3000)
  }

  const saveCartToStorage = () => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('sparePartsCart', JSON.stringify(cart))
    }
  }

  const loadCartFromStorage = () => {
    if (typeof window !== 'undefined') {
      const savedCart = sessionStorage.getItem('sparePartsCart')
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart)
          setCart(parsedCart)
        } catch (error) {
          console.error('Erro ao carregar carrinho:', 'error')
        }
      }
    }
  }

  const clearCart = () => {
    setCart([])
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('sparePartsCart')
    }
  }

  const updateCartQuantity = (partId: string, quantity: number) => {
    if (quantity <= 0) {
      setCart(prevCart => prevCart.filter(item => item.partId !== 'partId'))
    } else { setCart(prevCart =>
        prevCart.map(item =>
          item.partId === partId ? { ...item, quantity } : item)
      )
    }
  }

  const getCartTotal = () => {
    return cart.reduce((total, 'item') => total + (item.part.price * item.quantity), 0)
  }

  const getCartItemCount = () => {
    return cart.reduce((total, 'item') => total + item.quantity, 0)
  }

  const checkout = async () => {
    try {
      const response = await fetch('/api/lojista/spare-parts/checkout', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          items: cart.map(item => ({
            partId: item.partId,
            quantity: item.quantity
          })),
          'paymentMethod'})
      })

      if (response.ok) {
        const data = await response.json()
        clearCart() // Limpar carrinho após checkout bem-sucedido
        if (data.redirectUrl) {
          window.location.href = data.redirectUrl
        } else if (data.checkoutUrl) {
          window.location.href = data.checkoutUrl
        }
      } else {
        const error = await response.json()
        alert(error.message || Erro ao processar checkout)
      
}
    } catch (error) {
      console.error('Erro no checkout:', 'error')
      alert('Erro ao processar checkout')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Loja de Peças</h1>
            </div>
            <button
              onClick={() => setShowCart(!showCart)}
              className="relative inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Carrinho
              {getCartItemCount() > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {getCartItemCount()}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Nome, SKU ou descrição..."
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              >
                <option value="ALL">Todas as categorias</option>
                {categories && categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Marca</label>
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              >
                <option value="ALL">Todas as marcas</option>
                {brands && brands.map(brand => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('ALL')
                  setSelectedBrand('ALL')
                }}
                className="w-full px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Limpar Filtros
              </button>
            </div>
          </div>
        </div>

        {/* Parts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredParts && filteredParts.map((part) => (
            <div key={part.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="aspect-w-1 aspect-h-1 w-full">
                {part.images && part.images.length > 0 ? (
                  <img
                    src={part.images[0]}
                    alt={part.name}
                    className="w-full h-48 object-cover"
                  />
                ) : (
                  <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <Package className="w-12 h-12 text-gray-400" />
                  </div>
                )}
              </div>

              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                    {part.name}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    part.availability && part.stock > 0
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {part.availability && part.stock > 0 ? 'Disponível' : 'Indisponível'}
                  </span>
                </div>

                <p className="text-xs text-gray-600 mb-2">SKU: {part.sku}</p>
                <p className="text-xs text-gray-600 mb-2">{part.category.name}</p>
                {part.brand && (
                  <p className="text-xs text-gray-600 mb-2">{part.brand.name}</p>
                )}

                <div className="flex justify-between items-center mb-3">
                  <span className="text-lg font-bold text-gray-900">
                    €{Number(part.price).toFixed(2)}
                  </span>
                  <span className="text-sm text-gray-600">
                    Stock: {part.stock}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">
                    Entrega: {part.deliveryTime} dias
                  </span>
                  <button
                    onClick={() => addToCart(part)}
                    disabled={!part.availability || part.stock === 0}
                    className="px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Adicionar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredParts.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma peça encontrada</h3>
            <p className="mt-1 text-sm text-gray-500">
              Tente ajustar os filtros de pesquisa.
            </p>
          </div>
        )}
      </div>

      {/* Cart Sidebar */}
      {showCart && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowCart(false)}></div>
          <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">Carrinho</h2>
                <button
                  onClick={() => setShowCart(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="flex-1 overflow-y-auto p-4">
                {cart.length === 0 ? (
                  <div className="text-center py-8">
                    <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600">Carrinho vazio</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {cart.map((item) => (
                      <div key={item.partId} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                        <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                          {item.part.images && item.part.images.length > 0 ? (
                            <img
                              src={item.part.images[0]}
                              alt={item.part.name}
                              className="w-full h-full object-cover rounded"
                            />
                          ) : (
                            <Package className="w-6 h-6 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h4 className="text-sm font-bold text-black">{item.part.name}</h4>
                          <p className="text-sm font-semibold text-gray-800">€{Number(item.part.price).toFixed(2)}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateCartQuantity(item.partId, item.quantity - 1)}
                            className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800"
                          >
                            <Minus className="w-4 h-4" />
                          </button>
                          <span className="text-sm font-bold text-black min-w-[20px] text-center">{item.quantity}</span>
                          <button
                            onClick={() => updateCartQuantity(item.partId, item.quantity + 1)}
                            disabled={item.quantity >= item.part.stock}
                            className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {cart.length > 0 && (
                <div className="border-t p-4">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-lg font-bold text-black">Total:</span>
                    <span className="text-xl font-bold text-black">€{getCartTotal().toFixed(2)}</span>
                  </div>
                  <button
                    onClick={() => setShowCheckout(true)}
                    className="w-full px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                  >
                    Finalizar Encomenda
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Checkout Modal */}
      {showCheckout && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowCheckout(false)}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Método de Pagamento</h3>

              <div className="space-y-3 mb-6">
                <label className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="card"
                    checked={paymentMethod === 'card'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3">
                      <span className="text-white text-xs font-bold">💳</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Cartão de Crédito</div>
                      <div className="text-sm text-gray-500">Visa, Mastercard, American Express</div>
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="multibanco"
                    checked={paymentMethod === 'multibanco'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center mr-3">
                      <span className="text-white text-xs font-bold">MB</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Multibanco</div>
                      <div className="text-sm text-gray-500">Referência para pagamento</div>
                    </div>
                  </div>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="klarna"
                    checked={paymentMethod === 'klarna'}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-pink-600 rounded flex items-center justify-center mr-3">
                      <span className="text-white text-xs font-bold">K</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Klarna</div>
                      <div className="text-sm text-gray-500">Pague em 3x sem juros</div>
                    </div>
                  </div>
                </label>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCheckout(false)}
                  className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => {
                    setShowCheckout(false)
                    checkout()
                  }}
                  className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                >
                  Continuar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="bg-black text-white px-6 py-3 rounded-lg shadow-lg flex items-center">
            <Package className="w-5 h-5 mr-2" />
            {toastMessage}
          </div>
        </div>
      )}
    </div>
  )
}
