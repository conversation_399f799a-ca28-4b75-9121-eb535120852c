import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const subscriptions = await prisma.subscription.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                companyName: true}
            }
          }
        },
        plan: {
          select: {
            id: true,
            name: true,
            monthlyPrice: true,
            yearlyPrice: true}
        },
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            createdAt: true},
          orderBy: {
            createdAt: desc},
          take: 10
        }
      },
      orderBy: {
        createdAt: desc}
    })

    return NextResponse.json({
      'subscriptions'})

  } catch (error) {
    console.error("Erro ao buscar subscrições:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
