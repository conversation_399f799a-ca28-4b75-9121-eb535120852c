'use client'

import Link from 'next/link'
import { 
  Shield, Lock, Eye, FileText, 
  CheckCircle, Phone, Mail, MapPin
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'

export default function PrivacyPolicyPage() {
  const { t } = useTranslation()

  const sections = [
    {
      id: 'informacoes-coletadas',
      title: 'Informações que Coletamos',
      icon: <FileText className="w-6 h-6" />,
      content: [
        'Dados pessoais fornecidos voluntariamente (nome, email, telefone)',
        'Informações sobre o dispositivo a reparar',
        'Dados de localização para encontrar técnicos próximos',
        'Informações de pagamento (processadas por terceiros seguros)',
        'Dados de utilização da plataforma para melhorar o serviço'
      ]
    },
    {
      id: 'como-usamos',
      title: '<PERSON> Usamos as Suas Informações',
      icon: <Eye className="w-6 h-6" />,
      content: [
        'Conectar clientes a técnicos especializados',
        'Processar pedidos de reparação e pagamentos',
        'Comunicar sobre o estado das reparações',
        'Melhorar a qualidade dos nossos serviços',
        'Enviar notificações importantes sobre a conta',
        'Cumprir obrigações legais e regulamentares'
      ]
    },
    {
      id: 'partilha-dados',
      title: 'Partilha de Dados',
      icon: <Lock className="w-6 h-6" />,
      content: [
        'Com técnicos parceiros (apenas dados necessários para a reparação)',
        'Com processadores de pagamento seguros',
        'Com autoridades legais quando exigido por lei',
        'Nunca vendemos os seus dados a terceiros',
        'Nunca partilhamos dados para fins de marketing externo'
      ]
    },
    {
      id: 'seguranca',
      title: 'Segurança dos Dados',
      icon: <Shield className="w-6 h-6" />,
      content: [
        'Encriptação SSL/TLS em todas as comunicações',
        'Servidores seguros com acesso restrito',
        'Auditorias regulares de segurança',
        'Formação da equipa em proteção de dados',
        'Backup seguro e recuperação de dados',
        'Conformidade com RGPD e legislação portuguesa'
      ]
    }
  ]

  const rights = [
    {
      title: 'Direito de Acesso',
      description: 'Pode solicitar uma cópia de todos os dados pessoais que temos sobre si.'
    },
    {
      title: 'Direito de Retificação',
      description: 'Pode corrigir dados pessoais incorretos ou incompletos.'
    },
    {
      title: 'Direito de Eliminação',
      description: 'Pode solicitar a eliminação dos seus dados pessoais.'
    },
    {
      title: 'Direito de Portabilidade',
      description: 'Pode solicitar os seus dados num formato estruturado e legível.'
    },
    {
      title: 'Direito de Oposição',
      description: 'Pode opor-se ao processamento dos seus dados para fins específicos.'
    },
    {
      title: 'Direito de Limitação',
      description: 'Pode solicitar a limitação do processamento dos seus dados.'
    }
  ]

  return (
    <ModernLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-slate-600 via-gray-600 to-zinc-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Shield className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Política de Privacidade
            </h1>
            <p className="text-xl text-slate-100 max-w-3xl mx-auto mb-8">
              A sua privacidade é fundamental para nós. Saiba como protegemos e utilizamos os seus dados.
            </p>
            <div className="text-slate-200">
              <p>Última atualização: 1 de Janeiro de 2024</p>
            </div>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-600 leading-relaxed">
              A Revify compromete-se a proteger a sua privacidade e os seus dados pessoais. 
              Esta Política de Privacidade explica como coletamos, usamos, armazenamos e 
              protegemos as suas informações quando utiliza a nossa plataforma de reparações.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              Ao utilizar os nossos serviços, concorda com as práticas descritas nesta política. 
              Se não concordar com algum aspeto, por favor não utilize a nossa plataforma.
            </p>
          </div>
        </div>
      </section>

      {/* Main Sections */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
            {sections.map((section, index) => (
              <div key={index} id={section.id} className="bg-white rounded-2xl p-8 shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center mr-4 text-slate-600">
                    {section.icon}
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                </div>
                <div className="space-y-4">
                  {section.content.map((item, idx) => (
                    <div key={idx} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Rights Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Os Seus Direitos
            </h2>
            <p className="text-xl text-gray-600">
              De acordo com o RGPD, tem os seguintes direitos sobre os seus dados
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {rights.map((right, index) => (
              <div key={index} className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-3">{right.title}</h3>
                <p className="text-gray-600">{right.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Cookies Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Cookies e Tecnologias Similares</h2>
            <div className="space-y-4 text-gray-700">
              <p>
                Utilizamos cookies e tecnologias similares para melhorar a sua experiência na nossa plataforma:
              </p>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span><strong>Cookies Essenciais:</strong> Necessários para o funcionamento básico da plataforma</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span><strong>Cookies de Performance:</strong> Ajudam-nos a entender como utiliza o site</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span><strong>Cookies de Funcionalidade:</strong> Lembram as suas preferências</span>
                </div>
              </div>
              <p>
                Pode gerir as suas preferências de cookies nas definições do seu navegador.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Retention Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Retenção de Dados</h2>
            <div className="space-y-4 text-gray-700">
              <p>
                Mantemos os seus dados pessoais apenas pelo tempo necessário para:
              </p>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Fornecer os nossos serviços</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Cumprir obrigações legais</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Resolver disputas</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Prevenir fraudes</span>
                </div>
              </div>
              <p>
                Quando os dados já não são necessários, são eliminados de forma segura.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Contacte-nos</h2>
            <p className="text-gray-700 mb-6">
              Se tiver questões sobre esta Política de Privacidade ou quiser exercer os seus direitos, 
              pode contactar-nos através de:
            </p>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-slate-600" />
                <span className="text-gray-700"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-slate-600" />
                <span className="text-gray-700">+351 123 456 789</span>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-slate-600 mt-0.5" />
                <span className="text-gray-700">
                  Revify, Lda.<br />
                  Rua da Tecnologia, 123<br />
                  1000-001 Lisboa, Portugal
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Updates Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-slate-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Atualizações desta Política</h2>
            <div className="space-y-4 text-gray-700">
              <p>
                Podemos atualizar esta Política de Privacidade periodicamente para refletir 
                mudanças nos nossos serviços ou na legislação aplicável.
              </p>
              <p>
                Quando fizermos alterações significativas, notificaremos através de:
              </p>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Email para utilizadores registados</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Aviso na nossa plataforma</span>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                  <span>Atualização da data no topo desta página</span>
                </div>
              </div>
              <p>
                Recomendamos que reveja esta política regularmente para se manter informado 
                sobre como protegemos os seus dados.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Tem Questões sobre Privacidade?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            A nossa equipa está disponível para esclarecer qualquer dúvida
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contactos"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Contactar-nos</span>
            </Link>
            <Link
              href="/"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Shield className="w-5 h-5" />
              <span>Voltar ao Início</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
