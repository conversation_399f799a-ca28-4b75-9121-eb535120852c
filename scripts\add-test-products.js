const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addTestProducts() {
  try {
    console.log('🔍 Procurando lojista de teste...')
    
    // Encontrar o lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      }
    })

    if (!lojista) {
      console.log('❌ Lojista de teste não encontrado!')
      return
    }

    console.log(`✅ Lojista encontrado: ${lojista.name} (${lojista.email})`)

    // Verificar se já tem produtos
    const existingProducts = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id
      }
    })

    console.log(`📦 Produtos existentes: ${existingProducts.length}`)

    if (existingProducts.length > 0) {
      console.log('ℹ️ Lojista já tem produtos. Listando produtos existentes:')
      existingProducts.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.name} - €${product.price}`)
      })
      return
    }

    // Buscar categorias, marcas e modelos
    const categories = await prisma.category.findMany()
    const brands = await prisma.brand.findMany()
    const models = await prisma.deviceModel.findMany()

    if (categories.length === 0 || brands.length === 0) {
      console.log('❌ Não há categorias ou marcas no sistema!')
      return
    }

    console.log(`📱 Categorias disponíveis: ${categories.length}`)
    console.log(`🏷️ Marcas disponíveis: ${brands.length}`)
    console.log(`📋 Modelos disponíveis: ${models.length}`)

    // Produtos de teste para adicionar
    const testProducts = [
      {
        name: 'iPhone 13 Pro Max 256GB',
        description: 'iPhone 13 Pro Max em excelente estado, 256GB de armazenamento, cor Azul Sierra. Inclui carregador original.',
        price: 899.99,
        originalPrice: 1199.99,
        condition: 'USED_LIKE_NEW',
        stock: 2,
        images: [
          'https://images.unsplash.com/photo-1632661674596-df8be070a5c5?w=500',
          'https://images.unsplash.com/photo-1632661674596-df8be070a5c5?w=500'
        ]
      },
      {
        name: 'Samsung Galaxy S23 Ultra',
        description: 'Samsung Galaxy S23 Ultra 512GB, cor Phantom Black. Estado impecável, com S Pen incluída.',
        price: 799.99,
        originalPrice: 1099.99,
        condition: 'USED_EXCELLENT',
        stock: 1,
        images: [
          'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500'
        ]
      },
      {
        name: 'MacBook Air M2 2022',
        description: 'MacBook Air com chip M2, 8GB RAM, 256GB SSD. Cor Midnight. Perfeito para trabalho e estudos.',
        price: 1199.99,
        originalPrice: 1399.99,
        condition: 'USED_LIKE_NEW',
        stock: 1,
        images: [
          'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500'
        ]
      },
      {
        name: 'iPad Pro 11" 2023',
        description: 'iPad Pro 11 polegadas com chip M2, 128GB, Wi-Fi. Inclui Apple Pencil de 2ª geração.',
        price: 699.99,
        originalPrice: 899.99,
        condition: 'USED_EXCELLENT',
        stock: 3,
        images: [
          'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500'
        ]
      },
      {
        name: 'AirPods Pro 2ª Geração',
        description: 'AirPods Pro com cancelamento ativo de ruído, estojo de carregamento MagSafe. Como novos.',
        price: 199.99,
        originalPrice: 279.99,
        condition: 'USED_LIKE_NEW',
        stock: 5,
        images: [
          'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=500'
        ]
      }
    ]

    console.log('🚀 Criando produtos de teste...')

    for (const productData of testProducts) {
      // Encontrar categoria apropriada
      let category = categories.find(c => 
        c.name.toLowerCase().includes('smartphone') || 
        c.name.toLowerCase().includes('telefone') ||
        c.name.toLowerCase().includes('telemóvel')
      )
      
      if (!category && productData.name.toLowerCase().includes('macbook')) {
        category = categories.find(c => 
          c.name.toLowerCase().includes('laptop') || 
          c.name.toLowerCase().includes('computador')
        )
      }
      
      if (!category && productData.name.toLowerCase().includes('ipad')) {
        category = categories.find(c => 
          c.name.toLowerCase().includes('tablet')
        )
      }
      
      if (!category) {
        category = categories[0] // Usar primeira categoria como fallback
      }

      // Encontrar marca apropriada
      let brand = brands.find(b => {
        const productName = productData.name.toLowerCase()
        const brandName = b.name.toLowerCase()
        return productName.includes(brandName)
      })
      
      if (!brand) {
        brand = brands[0] // Usar primeira marca como fallback
      }

      // Encontrar modelo se disponível
      let deviceModel = null
      if (models.length > 0) {
        deviceModel = models.find(m => {
          const productName = productData.name.toLowerCase()
          const modelName = m.name.toLowerCase()
          return productName.includes(modelName)
        })
      }

      const product = await prisma.marketplaceProduct.create({
        data: {
          sellerId: lojista.id,
          name: productData.name,
          description: productData.description,
          price: productData.price,
          originalPrice: productData.originalPrice,
          condition: productData.condition,
          stock: productData.stock,
          categoryId: category.id,
          brandId: brand.id,
          deviceModelId: deviceModel?.id || null,
          images: productData.images,
          isActive: true
        }
      })

      console.log(`✅ Produto criado: ${product.name} - €${product.price}`)
    }

    console.log('🎉 Todos os produtos de teste foram criados com sucesso!')
    
    // Verificar produtos criados
    const finalProducts = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id
      },
      include: {
        category: true,
        brand: true,
        deviceModel: true
      }
    })

    console.log(`\n📊 Resumo final:`)
    console.log(`   Total de produtos: ${finalProducts.length}`)
    finalProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name} - €${product.price} (${product.category.name} - ${product.brand.name})`)
    })

  } catch (error) {
    console.error('❌ Erro ao criar produtos de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addTestProducts()
