import Link from 'next/link'

interface HelpArticleLayoutProps {
  title: string
  description: string
  breadcrumb: {
    category: string
    categoryHref: string
    current: string
  }
  children: React.ReactNode
}

export default function HelpArticleLayout({
  title,
  description,
  breadcrumb,
  children
}: HelpArticleLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="mr-8">
                <Link href="/" className="font-bold text-black text-xl">
                  Revify
                </Link>
                <div className="text-xs text-gray-600 font-light">Central de Ajuda</div>
              </div>
              <Link
                href="/ajuda"
                className="text-gray-600 hover:text-gray-900 font-medium"
              >
                Central de Ajuda
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="help-article">
          {/* Breadcrumb */}
          <div className="text-sm text-gray-500 mb-4">
            <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
            <span className="mx-2">›</span>
            <Link href={breadcrumb.categoryHref} className="hover:text-gray-700">{breadcrumb.category}</Link>
            <span className="mx-2">›</span>
            <span>{breadcrumb.current}</span>
          </div>
          
          {/* Title and Description */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="help-description mb-8">
            {description}
          </p>

          {/* Article Content */}
          <div className="prose max-w-none">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
