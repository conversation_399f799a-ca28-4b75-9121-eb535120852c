import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: { code: string} }
) {
  try {
    const trackingCode = params.code

    if (!trackingCode) {
      return NextResponse.json(
        { message: "Código de tracking é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar reparação pelo código de tracking
    const repair = await prisma.repair.findUnique({
      where: { trackingCode
},
      include: {
        repairShop: {
          include: {
            profile: true}
        },
        customer: {
          include: {
            profile: true}
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    // Preparar dados para retorno (sem informações sensíveis)
    const trackingData = {
      id: repair.id,
      trackingCode: repair.trackingCode,
      status: repair.status,
      customerName: repair.customerName,
      customerPhone: repair.customerPhone,
      customerEmail: repair.customerEmail,
      deviceBrand: repair.deviceBrand,
      deviceModel: repair.deviceModel,
      deviceDescription: repair.deviceDescription,
      problemType: repair.problemType,
      problemDescription: repair.problemDescription,
      estimatedPrice: Number(repair.estimatedPrice),
      estimatedCompletionDate: repair.estimatedCompletionDate,
      createdAt: repair.createdAt,
      updatedAt: repair.updatedAt,
      isIndependent: repair.isIndependent || false,
      repairShop: {
        name: repair.repairShop.name,
        profile: repair.repairShop.profile ? {
          phone: repair.repairShop.profile.phone,
          companyName: repair.repairShop.profile.companyName,
          address: repair.repairShop.profile.address,
          city: repair.repairShop.profile.city
        
} : null}
    }

    return NextResponse.json({
      success: true,
      repair: trackingData})

  } catch (error) {
    console.error("Erro ao buscar reparação por tracking:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
