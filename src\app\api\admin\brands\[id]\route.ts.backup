import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const brand = await prisma.brand.findUnique({
      where: { id: resolvedParams.id },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    if (!brand) {
      return NextResponse.json(
        { message: 'Marca não encontrada' },
        { status: 404 }
      )
    }

    return NextResponse.json(brand)

  } catch (error) {
    console.error('Erro ao buscar marca:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, logo, isActive } = await request.json()

    if (!name) {
      return NextResponse.json(
        { message: 'Nome da marca é obrigatório' },
        { status: 400 }
      )
    }

    // Verificar se a marca existe
    const existingBrand = await prisma.brand.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!existingBrand) {
      return NextResponse.json(
        { message: 'Marca não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se outro marca já tem este nome
    const duplicateBrand = await prisma.brand.findFirst({
      where: {
        name,
        id: { not: resolvedParams.id }
      }
    })

    if (duplicateBrand) {
      return NextResponse.json(
        { message: 'Já existe uma marca com este nome' },
        { status: 400 }
      )
    }

    const brand = await prisma.brand.update({
      where: { id: resolvedParams.id },
      data: {
        name,
        logo: logo || null,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    return NextResponse.json(brand)

  } catch (error) {
    console.error('Erro ao atualizar marca:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a marca existe
    const brand = await prisma.brand.findUnique({
      where: { id: resolvedParams.id },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    if (!brand) {
      return NextResponse.json(
        { message: 'Marca não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se há modelos associados
    if (brand._count.deviceModels > 0) {
      return NextResponse.json(
        { message: 'Não é possível eliminar uma marca que tem modelos associados' },
        { status: 400 }
      )
    }

    await prisma.brand.delete({
      where: { id: resolvedParams.id }
    })

    return NextResponse.json({ message: 'Marca eliminada com sucesso' })

  } catch (error) {
    console.error('Erro ao eliminar marca:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
