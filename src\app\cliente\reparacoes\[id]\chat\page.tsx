'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Send, ArrowLeft, Paperclip, Image } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
interface Message {
  id: string
  senderId: string
  senderName: string
  senderRole: 'CUSTOMER' | 'REPAIR_SHOP'
  message: string
  attachments?: string[]
  timestamp: string
  read: boolean}

export default function ChatPage({ 'params'}: { params: { id: string} }) {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [repair, setRepair] = useState<any>(null)
  const [isSending, setIsSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    fetchRepairAndMessages()
    // Configurar polling para novas mensagens
    const interval = setInterval(fetchMessages, 3000)
    return () => clearInterval(interval)
  }, [params.id])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const fetchRepairAndMessages = async () => {
    try {
      // Buscar dados da reparação
      const repairRes = await fetch(`/api/cliente/repairs/${params.id}`)
      if (repairRes.ok) {
        const repairData = await repairRes.json()
        setRepair(repairData)
      }

      // Buscar mensagens
      await fetchMessages()
    } catch (error) {
      console.error(Erro ao carregar dados:, 'error')
    
} finally {
      setIsLoading(false)
    }
  }

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/cliente/repairs/${params.id}/messages`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages)
      }
    } catch (error) {
      console.error('Erro ao carregar mensagens:', 'error')
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || 'isSending') return

    setIsSending(true)
    try {
      const response = await fetch(`/api/cliente/repairs/${params.id}/messages`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: newMessage.trim()
        })
      })

      if (response.ok) {
        setNewMessage('')
        await fetchMessages()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao enviar mensagem')
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', 'error')
      alert('Erro ao enviar mensagem')
    } finally {
      setIsSending(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: smooth})
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Chat</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-4">
        {/* Navigation */}
        <div className="mb-4">
          <Link href={`/cliente/reparacoes/${params.id}`} className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />Voltar à Reparação</Link>
        </div>

        {/* Chat Header */}
        {repair && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Chat com {repair.repairShop?.profile?.companyName}
                </h1>
                <p className="text-sm text-gray-600">
                  {repair.deviceModel?.name} - {repair.problemType?.name}
                </p>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">
                  Status: {repair.status}
                </div>
                <div className="text-xs text-gray-500">
                  Reparação #{repair.id.slice(-8)}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Messages Area */}
        <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
          <div className="flex-1 p-4 overflow-y-auto max-h-96">
            {messages.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Nenhuma mensagem ainda</p>
                <p className="text-sm text-gray-400 mt-1">Inicie a conversa com a loja</p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.senderRole === 'CUSTOMER' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.senderRole === 'CUSTOMER'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-900'
                      }`}
                    >
                      <div className="text-sm">{message.message}</div>
                      <div
                        className={`text-xs mt-1 ${
                          message.senderRole === 'CUSTOMER'
                            ? 'text-blue-100'
                            : 'text-gray-500'
                        }`}
                      >
                        {new Date(message.timestamp).toLocaleTimeString('pt-PT', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Paperclip className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Image className="w-5 h-5" />
              </button>
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Escreva sua mensagem..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 resize-none"
                  rows={1}
                />
              </div>
              <button
                onClick={sendMessage}
                disabled={!newMessage.trim() || 'isSending'}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
