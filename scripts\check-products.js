const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkProducts() {
  try {
    // Verificar lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    })

    if (!lojista) {
      console.log('Lojista de teste não encontrado')
      return
    }

    console.log(`Lojista: ${lojista.name} (${lojista.email})`)

    // Verificar produtos
    const products = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id
      },
      include: {
        category: true,
        brand: true
      }
    })

    console.log(`Total de produtos: ${products.length}`)
    
    if (products.length === 0) {
      console.log('Nenhum produto encontrado para este lojista')
      
      // Verificar se há categorias e marcas
      const categories = await prisma.category.findMany()
      const brands = await prisma.brand.findMany()
      
      console.log(`Categorias disponíveis: ${categories.length}`)
      console.log(`Marcas disponíveis: ${brands.length}`)
      
      if (categories.length > 0 && brands.length > 0) {
        console.log('Criando produto de teste...')
        
        const testProduct = await prisma.marketplaceProduct.create({
          data: {
            sellerId: lojista.id,
            name: 'iPhone 13 Pro Max',
            description: 'iPhone 13 Pro Max em excelente estado',
            price: 899.99,
            condition: 'USED_LIKE_NEW',
            stock: 1,
            categoryId: categories[0].id,
            brandId: brands[0].id,
            isActive: true,
            images: []
          }
        })
        
        console.log(`Produto criado: ${testProduct.name}`)
      }
    } else {
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - €${product.price} (${product.category?.name} - ${product.brand?.name})`)
      })
    }

  } catch (error) {
    console.error('Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkProducts()
