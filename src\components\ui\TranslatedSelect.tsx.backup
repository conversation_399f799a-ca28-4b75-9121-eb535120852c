'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'

interface Option {
  value: string
  label: string
}

interface TranslatedSelectProps {
  value: string
  onChange: (value: string) => void
  options: Option[]
  placeholder?: string
  disabled?: boolean
  className?: string
  required?: boolean
}

/**
 * Componente Select que traduz automaticamente as opções
 * Mais eficiente que usar AutoTranslate dentro de cada option
 */
export default function TranslatedSelect({
  value,
  onChange,
  options,
  placeholder = "Selecione uma opção",
  disabled = false,
  className = "",
  required = false
}: TranslatedSelectProps) {
  const { tBatch, currentLanguage } = useTranslation()
  const [translatedOptions, setTranslatedOptions] = useState<Option[]>(options)
  const [translatedPlaceholder, setTranslatedPlaceholder] = useState(placeholder)

  useEffect(() => {
    const translateOptions = async () => {
      if (currentLanguage === 'pt') {
        setTranslatedOptions(options)
        setTranslatedPlaceholder(placeholder)
        return
      }

      try {
        // Traduzir todas as labels das opções + placeholder de uma vez
        const textsToTranslate = [placeholder, ...options.map(opt => opt.label)]
        const translatedTexts = await tBatch(textsToTranslate)
        
        setTranslatedPlaceholder(translatedTexts[0])
        
        const newTranslatedOptions = options.map((option, index) => ({
          ...option,
          label: translatedTexts[index + 1] || option.label
        }))
        
        setTranslatedOptions(newTranslatedOptions)
      } catch (error) {
        console.error('Erro ao traduzir opções do select:', error)
        setTranslatedOptions(options)
        setTranslatedPlaceholder(placeholder)
      }
    }

    translateOptions()
  }, [options, placeholder, currentLanguage, tBatch])

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      required={required}
      className={`w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 ${className}`}
    >
      <option value="">{translatedPlaceholder}</option>
      {translatedOptions.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )
}
