'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, Plus, X } from 'lucide-react'
import Link from 'next/link'
export default function EditPlanPage() {
  const params = useParams()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    monthlyPrice: '',
    yearlyPrice: '',
    features: [''],
    maxProducts: '',
    maxRepairs: '',
    marketplaceCommission: '',
    repairCommission: '',
    smsAccess: false,
    whatsappAccess: false,
    emailSupport: true,
    paymentDelayDays: '7',
    sparePartsDiscount: '',
    certifiedBadge: false,
    priority: '0',
    moloniIntegration: false,
    miniStore: false,
    individualRepairs: false,
    isPopular: false,
    isActive: true})

  useEffect(() => {
    fetchPlan()
  }, [params.id])

  const fetchPlan = async () => {
    try {
      const response = await fetch(`/api/admin/subscription-plans/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        const plan = data.plan
        
        setFormData({
          name: plan.name || '',
          description: plan.description || '',
          monthlyPrice: plan.monthlyPrice?.toString() || '',
          yearlyPrice: plan.yearlyPrice?.toString() || '',
          features: plan.features || [''],
          maxProducts: plan.maxProducts?.toString() || '',
          maxRepairs: plan.maxRepairs?.toString() || '',
          marketplaceCommission: plan.marketplaceCommission?.toString() || '',
          repairCommission: plan.repairCommission?.toString() || '',
          smsAccess: plan.smsAccess || false,
          whatsappAccess: plan.whatsappAccess || false,
          emailSupport: plan.emailSupport !== false,
          paymentDelayDays: plan.paymentDelayDays?.toString() || '7',
          sparePartsDiscount: plan.sparePartsDiscount?.toString() || '',
          certifiedBadge: plan.certifiedBadge || false,
          priority: plan.priority?.toString() || '0',
          moloniIntegration: plan.moloniIntegration || false,
          miniStore: plan.miniStore || false,
          individualRepairs: plan.individualRepairs || false,
          isPopular: plan.isPopular || false,
          isActive: plan.isActive !== 'false'})
      }
    } catch (error) {
      console.error('Erro ao buscar plano:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      const response = await fetch(`/api/admin/subscription-plans/${params.id}`, {
        method: PUT,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          monthlyPrice: parseFloat(formData.monthlyPrice) || 0,
          yearlyPrice: parseFloat(formData.yearlyPrice) || 0,
          maxProducts: formData.maxProducts ? parseInt(formData.maxProducts) : null,
          maxRepairs: formData.maxRepairs ? parseInt(formData.maxRepairs) : null,
          marketplaceCommission: parseFloat(formData.marketplaceCommission) || 5.0,
          repairCommission: parseFloat(formData.repairCommission) || 5.0,
          paymentDelayDays: parseInt(formData.paymentDelayDays) || 7,
          sparePartsDiscount: parseFloat(formData.sparePartsDiscount) || 0,
          priority: parseInt(formData.priority) || 0,
          features: formData.features.filter(f => f.trim() !== '')
        })
      })

      if (response.ok) {
        alert('Plano atualizado com sucesso!')
        router.push('/admin/planos')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar plano')
      }
    } catch (error) {
      console.error('Erro ao salvar plano:', 'error')
      alert('Erro ao salvar plano')
    } finally {
      setIsSaving(false)
    }
  }

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, '']
    }))
  }

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== 'index')
    }))
  }

  const updateFeature = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((f, i) => i === index ? value : f)
    }))
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/planos"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Editar Plano</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informações Básicas */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nome do Plano *</label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prioridade
                </label>
                <input
                  type="number"
                  value={formData.priority}
                  onChange={(e) => setFormData({...formData, priority: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              />
            </div>
          </div>

          {/* Preços */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Preços</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Preço Mensal (€) *</label>
                <input
                  type="number"
                  step="0.01"
                  required
                  value={formData.monthlyPrice}
                  onChange={(e) => setFormData({...formData, monthlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Preço Anual (€) *</label>
                <input
                  type="number"
                  step="0.01"
                  required
                  value={formData.yearlyPrice}
                  onChange={(e) => setFormData({...formData, yearlyPrice: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                />
              </div>
            </div>
          </div>

          {/* Comissões */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Comissões</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Comissão Marketplace (%)</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.marketplaceCommission}
                  onChange={(e) => setFormData({...formData, marketplaceCommission: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="5.0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Comissão Reparações (%)</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.repairCommission}
                  onChange={(e) => setFormData({...formData, repairCommission: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="5.0"
                />
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Funcionalidades</h2>
              <button
                type="button"
                onClick={addFeature}
                className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm bg-white hover:bg-gray-50"
              >
                <Plus className="w-4 h-4 mr-1" />Adicionar</button>
            </div>
            
            <div className="space-y-2">
              {formData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={feature}
                    onChange={(e) => updateFeature(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="Funcionalidade..."
                  />
                  {formData.features.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeFeature(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/planos"
              className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </Link>
            <button
              type="submit"
              disabled={isSaving}
              className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isSaving ? 'Salvando...' : 'Salvar Alterações'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
