import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '8')

    let recommendedProducts = []
    let hasRecommendedAccess = false

    if (session) {
      // Verificar se o utilizador tem acesso a produtos recomendados baseado no plano
      const userSubscription = await prisma.subscription.findFirst({
        where: {
          userId: session.user.id,
          status: ACTIVE},
        include: {
          plan: true}
      })

      // Verificar se tem acesso a produtos recomendados
      if (userSubscription?.plan?.recommendedProductsEnabled) {
        hasRecommendedAccess = true

        // Verificar se ainda está dentro do período permitido
        if (userSubscription.plan.recommendedProductsDays > 0) {
          const daysSinceStart = Math.floor(
            (new Date().getTime() - userSubscription.currentPeriodStart.getTime()) / (1000 * 60 * 60 * 24)
          )

          if (daysSinceStart > userSubscription.plan.recommendedProductsDays) {
            hasRecommendedAccess = false
}
        }
      }
    }

    if (session && 'hasRecommendedAccess') {
      // Para utilizadores logados, recomendar baseado no histórico
      // 1. Buscar categorias e marcas dos produtos já comprados/visualizados
      const userOrders = await prisma.order.findMany({
        where: {
          customerId: session.user.id
        },
        include: {
          marketplaceOrderItems: {
            include: {
              product: {
                include: {
                  category: true,
                  brand: true}
              }
            }
          }
        },
        take: 10
      })

      // Extrair categorias e marcas preferidas
      const preferredCategories = new Set()
      const preferredBrands = new Set()

      userOrders.forEach(order => {
        order.marketplaceOrderItems.forEach(item => {
          if (item.product.category) {
            preferredCategories.add(item.product.category.id)
          }
          if (item.product.brand) {
            preferredBrands.add(item.product.brand.id)
          }
        })
      })

      // Buscar produtos similares
      if (preferredCategories.size > 0 || preferredBrands.size > 0) {
        recommendedProducts = await prisma.marketplaceProduct.findMany({
          where: {
            isActive: true,
            stock: { gt: 0 },
            OR: [
              { categoryId: { in: Array.from(preferredCategories) } },
              { brandId: { in: Array.from(preferredBrands) } }
            ]
          },
          include: {
            category: { select: { name: true} },
            brand: { select: { name: true} },
            deviceModel: { select: { name: true} },
            seller: { 
              select: { 
                name: true,
                isVerified: true,
                isFeatured: true} 
            }
          },
          orderBy: [
            { seller: { isFeatured: desc} }, // Priorizar lojas em destaque
            { seller: { isVerified: desc} }, // Depois lojas verificadas
            { createdAt: desc}
          ],
          take: limit})
      }
    }

    // Se não há produtos recomendados baseados no histórico, usar produtos populares
    if (recommendedProducts.length < limit) {
      const popularProducts = await prisma.marketplaceProduct.findMany({
        where: {
          isActive: true,
          stock: { gt: 0 
}
        },
        include: {
          category: { select: { name: true} },
          brand: { select: { name: true} },
          deviceModel: { select: { name: true} },
          seller: { 
            select: { 
              name: true,
              isVerified: true,
              isFeatured: true} 
          }
        },
        orderBy: [
          { seller: { isFeatured: desc} }, // Priorizar lojas em destaque
          { seller: { isVerified: desc} }, // Depois lojas verificadas
          { price: asc} // Produtos mais baratos primeiro
        ],
        take: limit - recommendedProducts.length
      })

      recommendedProducts = [...recommendedProducts, ...popularProducts]
    }

    // Formatar produtos para resposta
    const formattedProducts = recommendedProducts.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      images: product.images,
      condition: product.condition,
      stock: product.stock,
      category: product.category,
      brand: product.brand,
      deviceModel: product.deviceModel,
      seller: {
        name: product.seller.name,
        isVerified: product.seller.isVerified,
        isFeatured: product.seller.isFeatured
      }
    }))

    return NextResponse.json({
      products: formattedProducts,
      total: formattedProducts.length,
      isPersonalized: session && hasRecommendedAccess,
      hasAccess: hasRecommendedAccess,
      planRequired: !hasRecommendedAccess && session
})

  } catch (error) {
    console.error('Erro ao buscar produtos recomendados:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
