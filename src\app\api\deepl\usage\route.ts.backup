import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Verificar se o usuário está autenticado e é admin
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configuração do sistema
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: ['translationEnabled', 'deeplApiKey']
        }
      }
    })

    const settingsMap = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value
      return acc
    }, {} as Record<string, string>)

    if (settingsMap.translationEnabled !== 'true') {
      return NextResponse.json(
        { message: 'Sistema de traduções não está ativo' },
        { status: 400 }
      )
    }

    const apiKey = settingsMap.deeplApiKey
    if (!apiKey) {
      return NextResponse.json(
        { message: 'Chave API DeepL não configurada no sistema' },
        { status: 500 }
      )
    }

    // Chamar API DeepL para obter informações de uso
    const response = await fetch('https://api-free.deepl.com/v2/usage', {
      method: 'GET',
      headers: {
        'Authorization': `DeepL-Auth-Key ${apiKey}`,
      }
    })

    if (!response.ok) {
      console.error('Erro na API DeepL Usage:', response.status, await response.text())
      return NextResponse.json(
        { message: 'Erro ao obter informações de uso' },
        { status: 500 }
      )
    }

    const data = await response.json()

    // Calcular percentual de uso
    const usagePercentage = (data.character_count / data.character_limit) * 100

    // Atualizar uso atual no sistema
    await prisma.systemSettings.upsert({
      where: { key: 'deeplUsageCurrent' },
      update: { value: String(data.character_count) },
      create: { key: 'deeplUsageCurrent', value: String(data.character_count) }
    })

    return NextResponse.json({
      characterCount: data.character_count,
      characterLimit: data.character_limit,
      usagePercentage: Math.round(usagePercentage * 100) / 100,
      remainingCharacters: data.character_limit - data.character_count,
      isNearLimit: usagePercentage > 80,
      isCritical: usagePercentage > 95
    })

  } catch (error) {
    console.error('Erro ao obter uso da API DeepL:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
