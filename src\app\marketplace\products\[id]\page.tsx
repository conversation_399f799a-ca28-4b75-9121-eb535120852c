'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import MainHeader from '@/components/MainHeader'
import AutoTranslate from '@/components/ui/AutoTranslate'
import {
  ArrowLeft,
  Heart,
  ShoppingCart,
  Star,
  Share2,
  Shield,
  Truck,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
  Plus,
  Minus
} from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  condition: string
  images: string[]
  category: {
    name: string
  }
  brand: {
    name: string
  }
  deviceModel?: {
    name: string
  }
  seller: {
    id: string
    name: string
    createdAt: string
    isVerified: boolean
    rating: number
    reviewCount: number
    profile: {
      companyName: string
    }
  }
  specifications: any
  warranty: number
  stock: number
  isActive: boolean
  createdAt: string
}

export default function ProductDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [isFavorite, setIsFavorite] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [quantity, setQuantity] = useState(1)
  const [relatedProducts, setRelatedProducts] = useState([])
  const [cartCount, setCartCount] = useState(0)

  useEffect(() => {
    if (params?.id) {
      fetchProduct()
      fetchCartCount()
    }
  }, [params?.id])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/marketplace/products/${params?.id}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
        // Buscar produtos relacionados
        fetchRelatedProducts()
      } else {
        router.push(<AutoTranslate text="/marketplace" />)
      }
    } catch (error) {
      console.error('Erro ao carregar produto:', error)
      router.push(<AutoTranslate text="/marketplace" />)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchRelatedProducts = async () => {
    try {
      const response = await fetch(`/api/marketplace/products/${params?.id}/related?limit=8`)
      if (response.ok) {
        const data = await response.json()
        setRelatedProducts(data.products)
      }
    } catch (error) {
      console.error('Erro ao carregar produtos relacionados:', error)
    }
  }

  const fetchCartCount = async () => {
    try {
      const response = await fetch('/api/marketplace/cart')
      if (response.ok) {
        const cartData = await response.json()
        setCartCount(cartData.items?.length || 0)
      }
    } catch (error) {
      console.error('Erro ao buscar carrinho:', error)
    }
  }

  const toggleFavorite = async () => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    try {
      const response = await fetch(`/api/marketplace/products/${params?.id}/favorite`, {
        method: isFavorite ? 'DELETE' : 'POST'
      })

      if (response.ok) {
        setIsFavorite(!isFavorite)
      }
    } catch (error) {
      console.error('Erro ao alterar favorito:', error)
    }
  }

  const addToCart = async () => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (product.stock <= 0) {
      alert('Produto esgotado')
      return
    }

    if (quantity > product.stock) {
      alert(`Apenas ${product.stock} unidade${product.stock > 1 ? 's' : ''} em stock`)
      return
    }

    setIsAddingToCart(true)
    try {
      const response = await fetch('/api/marketplace/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId: params?.id,
          quantity: quantity
        })
      })

      if (response.ok) {
        alert('Produto adicionado ao carrinho!')
        fetchCartCount() // Atualizar contador do carrinho
        // Atualizar contador no header se disponível
        if (window.updateCartCount) {
          window.updateCartCount()
        }
      } else {
        alert('Erro ao adicionar ao carrinho')
      }
    } catch (error) {
      console.error('Erro ao adicionar ao carrinho:', error)
      alert('Erro ao adicionar ao carrinho')
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleShare = () => {
    const url = window.location.href
    const text = `Confira este produto: ${product.name} - €${Number(product.price).toFixed(2)}`

    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: text,
        url: url
      })
    } else {
      // Fallback para WhatsApp
      const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`
      window.open(whatsappUrl, '_blank')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Produto não encontrado" /></h1>
          <Link href="/marketplace" className="text-blue-600 hover:text-blue-700"><AutoTranslate text="Voltar ao marketplace" /></Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader showCart={true} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-gray-900"><AutoTranslate text="Início" /></Link>
          <span>/</span>
          <Link href="/marketplace" className="hover:text-gray-900"><AutoTranslate text="Marketplace" /></Link>
          <span>/</span>
          <span className="text-gray-900">{product.category.name}</span>
          <span>/</span>
          <span className="text-gray-900 font-medium">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Galeria de Imagens */}
          <div className="space-y-4">
            {/* Imagem Principal */}
            <div className="relative bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100">
              <div className="aspect-square relative group">
                {product.images.length > 0 ? (
                  <img
                    src={product.images[selectedImage]}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <span className="text-gray-400">Sem imagem</span>
                  </div>
                )}

                {/* Navegação de imagens */}
                {product.images.length > 1 && (
                  <>
                    <button
                      onClick={() => setSelectedImage(prev => prev > 0 ? prev - 1 : product.images.length - 1)}
                      className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <ChevronLeft className="w-5 h-5 text-gray-700" />
                    </button>
                    <button
                      onClick={() => setSelectedImage(prev => prev < product.images.length - 1 ? prev + 1 : 0)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <ChevronRight className="w-5 h-5 text-gray-700" />
                    </button>
                  </>
                )}

                {/* Botões de ação */}
                <div className="absolute top-4 right-4 flex flex-col space-y-2">
                  <button
                    onClick={toggleFavorite}
                    className={`w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-colors ${
                      isFavorite ? 'bg-red-500 text-white' : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-white'
                    }`}
                  >
                    <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
                  </button>
                  <button
                    onClick={handleShare}
                    className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg text-gray-700 hover:bg-white transition-colors"
                  >
                    <Share2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Miniaturas */}
            {product.images.length > 1 && (
              <div className="flex space-x-3 overflow-x-auto pb-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                      selectedImage === index
                        ? 'border-gray-900 shadow-md'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Detalhes do Produto */}
          <div className="space-y-6">
            {/* Cabeçalho */}
            <div>
              <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                <span className="bg-gray-100 px-2 py-1 rounded-md">{product.category.name}</span>
                <span>•</span>
                <span>{product.brand.name}</span>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>

              {/* Avaliações */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`w-5 h-5 ${i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">4.2</span>
                </div>
                <span className="text-sm text-gray-500"><AutoTranslate text="12 avaliações" /></span>
              </div>
            </div>

            {/* Preço */}
            <div>
              <div className="flex items-baseline space-x-3 mb-2">
                <span className="text-4xl font-bold text-gray-900">€{Number(product.price).toFixed(2)}</span>
                {product.originalPrice && Number(product.originalPrice) > Number(product.price) && (
                  <span className="text-xl text-gray-500 line-through">
                    €{Number(product.originalPrice).toFixed(2)}
                  </span>
                )}
              </div>
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                product.condition === 'NEW' ? 'bg-green-100 text-green-800' :
                product.condition === 'LIKE_NEW' ? 'bg-blue-100 text-blue-800' :
                product.condition === 'GOOD' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {product.condition === 'NEW' ? 'Novo' :
                 product.condition === 'LIKE_NEW' ? 'Como Novo' :
                 product.condition === 'GOOD' ? 'Bom Estado' :
                 product.condition === 'FAIR' ? 'Estado Razoável' : 'Usado'}
              </span>
            </div>

            {/* Descrição */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-2"><AutoTranslate text="Descrição:" /></h3>
              <p className="text-gray-700 leading-relaxed">{product.description}</p>
            </div>

            {/* Botões de Ação */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-3 hover:bg-gray-50 transition-colors"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="px-4 py-3 border-x border-gray-300 min-w-[60px] text-center font-medium">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-3 hover:bg-gray-50 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                <span className={`text-sm ${
                  product.stock > 0 ? (
                    product.stock <= 5 ? 'text-orange-600' : 'text-green-600'
                  ) : 'text-red-600'
                }`}>
                  {product.stock > 0 ? (
                    product.stock <= 5 ? `Apenas ${product.stock} em stock` : 'Em stock'
                  ) : 'Esgotado'}
                </span>
              </div>

              <button
                onClick={addToCart}
                disabled={isAddingToCart || product.stock <= 0}
                className={`w-full py-4 px-6 rounded-xl transition-colors font-semibold disabled:opacity-50 flex items-center justify-center text-lg ${
                  product.stock <= 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-black text-white hover:bg-gray-800'
                }`}
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                {isAddingToCart ? 'Adicionando...' : (
                  product.stock <= 0 ? 'Indisponível' : 'Adicionar ao Carrinho'
                )}
              </button>
            </div>

            {/* Informações de Entrega e Termos */}
            <div className="bg-gray-50 rounded-xl p-6 space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2"><AutoTranslate text="Condições de Entrega" /></h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-green-600" />
                    <span>Garantia de {product.warranty} meses</span>
                  </div>
                  <div className="flex items-center">
                    <Truck className="w-4 h-4 mr-2 text-blue-600" />
                    <span><AutoTranslate text="Envio grátis para encomendas acima de €50" /></span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2"><AutoTranslate text="Métodos de Pagamento Aceites" /></h4>
                <div className="flex space-x-3">
                  <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">VISA</span>
                  </div>
                  <div className="w-12 h-8 bg-red-500 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">MC</span>
                  </div>
                  <div className="w-12 h-8 bg-blue-400 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">PP</span>
                  </div>
                  <div className="w-12 h-8 bg-green-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">MB</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Vendido por */}
            <div className="border-t pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Vendido por</h3>
              <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-xl">
                    {(product.seller.profile?.companyName || product.seller.name).charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      {product.seller.profile?.companyName || product.seller.name}
                    </h4>
                    <div className="flex items-center space-x-4 mb-3">
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className={`w-4 h-4 ${i < Math.floor(product.seller.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                        ))}
                        <span className="text-sm text-gray-600 ml-1">
                          {product.seller.rating.toFixed(1)}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-600">
                        Membro há {(() => {
                          const createdDate = new Date(product.seller.createdAt)
                          const now = new Date()
                          const diffTime = Math.abs(now.getTime() - createdDate.getTime())
                          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                          const months = Math.floor(diffDays / 30)
                          const days = diffDays % 30

                          if (months > 0) {
                            return `${months} ${months === 1 ? 'mês' : 'meses'}${days > 0 ? ` e ${days} ${days === 1 ? 'dia' : 'dias'}` : ''}`
                          } else {
                            return `${days} ${days === 1 ? 'dia' : 'dias'}`
                          }
                        })()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      {product.seller.isVerified && (
                        <div className="flex items-center space-x-1">
                          <Shield className="w-4 h-4 text-green-500" />
                          <span>Vendedor Verificado</span>
                        </div>
                      )}
                      {product.seller.reviewCount > 0 && (
                        <>
                          {product.seller.isVerified && <span>•</span>}
                          <span>
                            {Math.round((product.seller.rating / 5) * 100)}% de avaliações positivas
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <Link
                    href={`/loja/${product.seller.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm"
                  ><AutoTranslate text="Ver perfil da loja" /><ChevronRight className="w-4 h-4 ml-1" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Produtos Semelhantes */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900"><AutoTranslate text="Produtos Semelhantes" /></h2>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct: any) => (
                <Link
                  key={relatedProduct.id}
                  href={`/marketplace/products/${relatedProduct.id}`}
                  className="bg-white rounded-xl border border-gray-100 overflow-hidden hover:shadow-lg transition-shadow group"
                >
                  <div className="aspect-square bg-gray-100 relative">
                    {relatedProduct.images.length > 0 ? (
                      <img
                        src={relatedProduct.images[0]}
                        alt={relatedProduct.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <span className="text-sm">Sem Imagem</span>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">{relatedProduct.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{relatedProduct.brand.name}</p>
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-gray-900">€{Number(relatedProduct.price).toFixed(2)}</span>
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          // Adicionar funcionalidade de favorito
                        }}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Heart className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-black mb-4">Revify</h3>
              <p className="text-gray-600 mb-6 max-w-md"><AutoTranslate text="A plataforma líder em reparações de dispositivos eletrónicos em Portugal. Conectamos clientes a técnicos especializados com garantia e transparência total." /></p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                  <span className="text-gray-600 font-semibold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                  <span className="text-gray-600 font-semibold">in</span>
                </div>
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors cursor-pointer">
                  <span className="text-gray-600 font-semibold">@</span>
                </div>
              </div>
            </div>

            {/* Services */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4"><AutoTranslate text="Serviços" /></h4>
              <ul className="space-y-3">
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Reparação de Smartphones" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Reparação de Laptops" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Reparação de Tablets" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Diagnóstico Gratuito" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Garantia Estendida" /></Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Suporte</h4>
              <ul className="space-y-3">
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Centro de Ajuda" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Como Funciona" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors">Contactar Suporte</Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Tornar-se Parceiro" /></Link></li>
                <li><Link href="#" className="text-gray-600 hover:text-gray-900 transition-colors"><AutoTranslate text="Política de Privacidade" /></Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
              <p className="text-gray-600 text-sm"><AutoTranslate text="© 2024 Revify. Todos os direitos reservados." /></p>
            </div>

            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="#" className="text-gray-600 hover:text-gray-900 text-sm transition-colors"><AutoTranslate text="Termos de Serviço" /></Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900 text-sm transition-colors"><AutoTranslate text="Política de Privacidade" /></Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900 text-sm transition-colors">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
