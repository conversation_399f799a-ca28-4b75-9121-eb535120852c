'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Plus, Edit, Trash2, Eye, EyeOff, Settings } from 'lucide-react'

interface CustomCategory {
  id: string
  name: string
  description?: string
  icon?: string
  isActive: boolean
  sortOrder: number}

interface DefaultCategory {
  id: string
  name: string
  description?: string
  productCount: number
  isDefault: boolean}

interface CategoryManagerProps {
  subdomain: string}

export default function CategoryManager({ 'subdomain'}: CategoryManagerProps) {
  const { data: session } = useSession()
  const [customCategories, setCustomCategories] = useState<CustomCategory[]>([])
  const [defaultCategories, setDefaultCategories] = useState<DefaultCategory[]>([])
  const [showDefaultCategories, setShowDefaultCategories] = useState(true)
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    icon: ''
  })

  useEffect(() => {
    fetchCategories()
  }, [subdomain])

  const fetchCategories = async () => {
    try {
      console.log('Fetching categories for subdomain:', 'subdomain')
      const response = await fetch(`/api/shop/${subdomain}/custom-categories`)
      console.log('Response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Categories loaded:', 'data')
        setCustomCategories(data.customCategories || [])
        setDefaultCategories(data.defaultCategories || [])
        setShowDefaultCategories(data.showDefaultCategories)
      } else {
        console.error('Erro ao carregar categorias:', response.status, response.statusText)
        const errorText = await response.text()
        console.error('Error response:', 'errorText')
      }
    } catch (error) {
      console.error('Erro ao buscar categorias:', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleAddCategory = async () => {
    if (!newCategory.name.trim()) return

    try {
      const response = await fetch(`/api/shop/${subdomain}/custom-categories`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...newCategory,
          sortOrder: customCategories.length
        })
      })

      if (response.ok) {
        const data = await response.json()
        setCustomCategories([...customCategories, data.category])
        setNewCategory({ name: '', description: '', icon: '' })
        setShowAddForm(false)
      }
    } catch (error) {
      console.error('Erro ao adicionar categoria:', 'error')
    }
  }

  const handleToggleDefaultCategories = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/category-settings`, {
        method: PUT,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          showDefaultCategories: !showDefaultCategories
        })
      })

      if (response.ok) {
        setShowDefaultCategories(!showDefaultCategories)
      }
    } catch (error) {
      console.error('Erro ao atualizar configurações:', 'error')
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Gestão de Categorias</h2>
            <p className="text-sm text-gray-600 mt-1">
              Gerencie as categorias da sua loja
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Adicionar Categoria</span>
          </button>
        </div>
      </div>

      <div className="p-6">
        {/* Configurações de Categorias Padrão */}
        <div className="mb-8">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Categorias Padrão da Plataforma</h3>
              <p className="text-sm text-gray-600">Mostrar categorias padrão baseadas nos seus produtos</p>
            </div>
            <button
              onClick={handleToggleDefaultCategories}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                showDefaultCategories
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {showDefaultCategories ? (
                <>
                  <Eye className="w-4 h-4" />
                  <span>Visível</span>
                </>
              ) : (
                <>
                  <EyeOff className="w-4 h-4" />
                  <span>Oculto</span>
                </>
              )}
            </button>
          </div>

          {showDefaultCategories && defaultCategories.length > 0 && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {defaultCategories.map((category) => (
                <div key={category.id} className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-medium text-gray-900">{category.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {category.productCount} produtos
                  </p>
                  <span className="inline-block mt-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Padrão</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Categorias Personalizadas */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Categorias Personalizadas</h3>
          
          {showAddForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h4 className="font-medium text-gray-900 mb-3">Adicionar Nova Categoria</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Categoria</label>
                  <input
                    type="text"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Ex: Acessórios Premium"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Descrição (opcional)</label>
                  <textarea
                    value={newCategory.description}
                    onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    rows={2}
                    placeholder="Descrição da categoria..."
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleAddCategory}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                  >Adicionar</button>
                  <button
                    onClick={() => {
                      setShowAddForm(false)
                      setNewCategory({ name: '', description: '', icon: '' })
                    }}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>
          )}

          {customCategories.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {customCategories.map((category) => (
                <div key={category.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{category.name}</h4>
                      {category.description && (
                        <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                      )}
                      <span className="inline-block mt-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                        Personalizada
                      </span>
                    </div>
                    <div className="flex space-x-1 ml-2">
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-red-600">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma categoria personalizada</h3>
              <p className="text-gray-600 mb-4">Crie categorias personalizadas para organizar melhor os seus produtos</p>
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Criar Primeira Categoria
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
