import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar clientes únicos que fizeram encomendas no marketplace ou reparações
    const customers = await prisma.$queryRaw`
      SELECT DISTINCT
        u.id,
        u.name,
        u.email,
        p.phone,
        p.nif,
        u."createdAt",
        (
          SELECT COUNT(*)
          FROM orders o
          JOIN marketplace_order_items moi ON o.id = moi."orderId"
          JOIN marketplace_products mp ON moi."productId" = mp.id
          WHERE o."customerId" = u.id AND mp."sellerId" = ${session.user.id}
        ) as marketplace_orders_count,
        (
          SELECT COUNT(*)
          FROM repairs r
          WHERE r."customerId" = u.id AND r."repairShopId" = ${session.user.id}
        ) as repairs_count,
        (
          SELECT COALESCE(SUM(o.total), 0)
          FROM orders o
          JOIN marketplace_order_items moi ON o.id = moi."orderId"
          JOIN marketplace_products mp ON moi."productId" = mp.id
          WHERE o."customerId" = u.id AND mp."sellerId" = ${session.user.id}
        ) as marketplace_total_spent,
        (
          SELECT COALESCE(SUM(r."finalPrice"), 0)
          FROM repairs r
          WHERE r."customerId" = u.id AND r."repairShopId" = ${session.user.id}
          AND r.status = 'COMPLETED'
        ) as repairs_total_spent
      FROM users u
      LEFT JOIN profiles p ON u.id = p."userId"
      WHERE u.id IN (
        SELECT DISTINCT o."customerId"
        FROM orders o
        JOIN marketplace_order_items moi ON o.id = moi."orderId"
        JOIN marketplace_products mp ON moi."productId" = mp.id
        WHERE mp."sellerId" = ${session.user.id}
        UNION
        SELECT DISTINCT r."customerId"
        FROM repairs r
        WHERE r."repairShopId" = ${session.user.id}
      )
      ORDER BY u."createdAt" DESC
    ` as any[]

    const formattedCustomers = customers.map((customer: any) => ({
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      nif: customer.nif,
      createdAt: customer.createdAt,
      stats: {
        marketplaceOrders: Number(customer.marketplace_orders_count || 0),
        repairs: Number(customer.repairs_count || 0),
        totalSpent: Number(customer.marketplace_total_spent || 0) + Number(customer.repairs_total_spent || 0),
        marketplaceSpent: Number(customer.marketplace_total_spent || 0),
        repairsSpent: Number(customer.repairs_total_spent || 0)
      }
    }))

    return NextResponse.json({
      customers: formattedCustomers,
      summary: {
        total: formattedCustomers.length,
        totalRevenue: formattedCustomers.reduce((sum, c) => sum + c.stats.totalSpent, 0)
      }
    })

  } catch (error) {
    console.error('Erro ao buscar clientes:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
