'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Package, Eye, Calendar, MapPin, CreditCard, ArrowLeft, Truck } from 'lucide-react'
import Link from 'next/link'

interface SparePartOrder {
  id: string
  orderNumber: string
  status: string
  total: number
  shippingCost: number
  paymentMethod: string
  paymentStatus: string
  trackingCode: string | null
  estimatedDelivery: string | null
  deliveredAt: string | null
  createdAt: string
  items: {
    id: string
    quantity: number
    price: number
    part: {
      id: string
      name: string
      sku: string
      images: string[]
    }
  }[]
  shippingName: string
  shippingStreet: string
  shippingCity: string
  shippingPostalCode: string
  shippingCountry: string
}

export default function EncomendasPecasPage() {
  const { data: session } = useSession()
  const [orders, setOrders] = useState<SparePartOrder[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState<SparePartOrder | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/lojista/spare-part-orders')
      if (response.ok) {
        const data = await response.json()
        setOrders(data.orders)
      }
    } catch (error) {
      console.error('Erro ao buscar encomendas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': 'Pendente',
      'CONFIRMED': 'Confirmado',
      'PROCESSING': 'Em Processamento',
      'SHIPPED': 'Enviado',
      'DELIVERED': 'Entregue',
      'CANCELLED': 'Cancelado'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'CONFIRMED': 'bg-blue-100 text-blue-800',
      'PROCESSING': 'bg-purple-100 text-purple-800',
      'SHIPPED': 'bg-indigo-100 text-indigo-800',
      'DELIVERED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getPaymentStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'PENDING': 'Pendente',
      'COMPLETED': 'Pago',
      'FAILED': 'Falhado',
      'CANCELLED': 'Cancelado',
      'REFUNDED': 'Reembolsado'
    }
    return statusMap[status] || status
  }

  const getEstimatedDeliveryDate = (order: any) => {
    if (!order.items || order.items.length === 0) return 'N/A'

    // Encontrar o maior tempo de entrega entre todos os produtos
    const maxDeliveryDays = Math.max(...order.items.map((item: any) => {
      return item.part?.deliveryDays || 3 // Default 3 dias se não especificado
    }))

    const orderDate = new Date(order.createdAt)
    const estimatedDate = new Date(orderDate)
    estimatedDate.setDate(orderDate.getDate() + maxDeliveryDays)

    return estimatedDate.toLocaleDateString('pt-PT')
  }

  const getOrderTimeline = (order: any) => {
    const timeline = [
      {
        title: 'Encomenda Recebida',
        description: 'A sua encomenda foi recebida e está a ser processada',
        date: order.createdAt,
        completed: true
      },
      {
        title: 'Pagamento Confirmado',
        description: 'O pagamento foi processado com sucesso',
        date: order.paymentStatus === 'COMPLETED' ? order.createdAt : null,
        completed: order.paymentStatus === 'COMPLETED'
      },
      {
        title: 'Em Processamento',
        description: 'A encomenda está a ser preparada para envio',
        date: ['PROCESSING', 'SHIPPED', 'DELIVERED'].includes(order.status) ? order.updatedAt : null,
        completed: ['PROCESSING', 'SHIPPED', 'DELIVERED'].includes(order.status)
      },
      {
        title: 'Enviado',
        description: order.trackingCode ? `Código de rastreamento: ${order.trackingCode}` : 'A encomenda foi enviada',
        date: ['SHIPPED', 'DELIVERED'].includes(order.status) ? order.updatedAt : null,
        completed: ['SHIPPED', 'DELIVERED'].includes(order.status)
      },
      {
        title: 'Entregue',
        description: 'A encomenda foi entregue com sucesso',
        date: order.status === 'DELIVERED' ? order.updatedAt : null,
        completed: order.status === 'DELIVERED'
      }
    ]

    return timeline
  }

  const openDetails = (order: SparePartOrder) => {
    setSelectedOrder(order)
    setShowDetails(true)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Encomendas de Peças</h1>
            </div>
            <Link
              href="/lojista/loja-pecas"
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
            >
              <Package className="w-4 h-4 mr-2" />
              Comprar Peças
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Encomendas</p>
                <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Truck className="w-8 h-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Enviadas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {orders.filter(o => o.status === 'SHIPPED').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Entregues</p>
                <p className="text-2xl font-bold text-gray-900">
                  {orders.filter(o => o.status === 'DELIVERED').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CreditCard className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Gasto</p>
                <p className="text-2xl font-bold text-gray-900">
                  €{orders.reduce((sum, o) => sum + Number(o.total), 0).toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Histórico de Encomendas</h3>
          </div>

          {orders.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma encomenda</h3>
              <p className="mt-1 text-sm text-gray-500">
                Ainda não fez nenhuma encomenda de peças.
              </p>
              <div className="mt-6">
                <Link
                  href="/lojista/loja-pecas"
                  className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                >
                  <Package className="w-4 h-4 mr-2" />
                  Comprar Peças
                </Link>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Encomenda
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pagamento
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tracking
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.map((order) => (
                    <tr key={order.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            #{order.orderNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            {order.items.length} {order.items.length === 1 ? 'item' : 'itens'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(order.createdAt).toLocaleDateString('pt-PT')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {getStatusLabel(order.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          order.paymentStatus === 'COMPLETED' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {getPaymentStatusLabel(order.paymentStatus)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        €{Number(order.total).toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.trackingCode || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => openDetails(order)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {showDetails && selectedOrder && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowDetails(false)}></div>
            <div className="relative bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Detalhes da Encomenda #{selectedOrder.orderNumber}
                  </h2>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Order Info */}
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-3">Informações da Encomenda</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(selectedOrder.status)}`}>
                            {getStatusLabel(selectedOrder.status)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Pagamento:</span>
                          <span>{selectedOrder.paymentMethod}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Data:</span>
                          <span>{new Date(selectedOrder.createdAt).toLocaleDateString('pt-PT')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Entrega Estimada:</span>
                          <span>{getEstimatedDeliveryDate(selectedOrder)}</span>
                        </div>
                        {selectedOrder.trackingCode && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Tracking:</span>
                            <span className="font-mono">{selectedOrder.trackingCode}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-3">Timeline da Encomenda</h3>
                      <div className="space-y-4">
                        {getOrderTimeline(selectedOrder).map((event, index) => (
                          <div key={index} className="flex items-start">
                            <div className={`flex-shrink-0 w-3 h-3 rounded-full mt-1 ${
                              event.completed ? 'bg-green-500' : 'bg-gray-300'
                            }`}></div>
                            <div className="ml-4 flex-1">
                              <div className="flex items-center justify-between">
                                <p className={`text-sm font-medium ${
                                  event.completed ? 'text-gray-900' : 'text-gray-500'
                                }`}>
                                  {event.title}
                                </p>
                                {event.date && (
                                  <span className="text-xs text-gray-500">
                                    {new Date(event.date).toLocaleDateString('pt-PT')}
                                  </span>
                                )}
                              </div>
                              {event.description && (
                                <p className="text-xs text-gray-600 mt-1">{event.description}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Comentários */}
                      <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-3">Comentários</h4>
                        <div className="space-y-2 mb-3">
                          {selectedOrder.comments?.map((comment: any, index: number) => (
                            <div key={index} className={`p-3 rounded border ${
                              comment.isInternal ? 'bg-yellow-50 border-yellow-200' : 'bg-white border-gray-200'
                            }`}>
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <p className="text-sm text-gray-900">{comment.text}</p>
                                  <div className="flex items-center mt-1 space-x-2">
                                    <p className="text-xs text-gray-600">Por: {comment.author}</p>
                                    {comment.isInternal && (
                                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                        Interno
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <span className="text-xs text-gray-500">
                                  {new Date(comment.createdAt).toLocaleDateString('pt-PT')}
                                </span>
                              </div>
                            </div>
                          )) || (
                            <p className="text-sm text-gray-500">Nenhum comentário</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-4">
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="commentType"
                                value="public"
                                defaultChecked
                                className="mr-2"
                              />
                              <span className="text-sm text-gray-700">Visível ao cliente</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="commentType"
                                value="internal"
                                className="mr-2"
                              />
                              <span className="text-sm text-gray-700">Nota interna</span>
                            </label>
                          </div>
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              placeholder="Adicionar comentário..."
                              className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-black"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  const isInternal = (document.querySelector('input[name="commentType"]:checked') as HTMLInputElement)?.value === 'internal'
                                  console.log('Adicionar comentário:', {
                                    text: e.currentTarget.value,
                                    isInternal,
                                    orderId: selectedOrder.id
                                  })
                                  e.currentTarget.value = ''
                                }
                              }}
                            />
                            <button
                              onClick={() => {
                                const input = document.querySelector('input[placeholder="Adicionar comentário..."]') as HTMLInputElement
                                const isInternal = (document.querySelector('input[name="commentType"]:checked') as HTMLInputElement)?.value === 'internal'
                                if (input?.value.trim()) {
                                  console.log('Adicionar comentário:', {
                                    text: input.value,
                                    isInternal,
                                    orderId: selectedOrder.id
                                  })
                                  input.value = ''
                                }
                              }}
                              className="px-3 py-2 bg-black text-white text-sm rounded hover:bg-gray-800"
                            >
                              Adicionar
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-3">Endereço de Entrega</h3>
                      <div className="text-sm text-gray-600">
                        <p className="font-medium text-gray-900">{selectedOrder.shippingName}</p>
                        <p>{selectedOrder.shippingStreet}</p>
                        <p>{selectedOrder.shippingPostalCode} {selectedOrder.shippingCity}</p>
                        <p>{selectedOrder.shippingCountry}</p>
                      </div>
                    </div>
                  </div>

                  {/* Items */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Itens da Encomenda</h3>
                    <div className="space-y-3">
                      {selectedOrder.items.map((item) => (
                        <div key={item.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                            {item.part.images && item.part.images.length > 0 ? (
                              <img
                                src={item.part.images[0]}
                                alt={item.part.name}
                                className="w-full h-full object-cover rounded"
                              />
                            ) : (
                              <Package className="w-6 h-6 text-gray-400" />
                            )}
                          </div>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-gray-900">{item.part.name}</h4>
                            <p className="text-xs text-gray-600">SKU: {item.part.sku}</p>
                            <p className="text-sm text-gray-600">Qtd: {item.quantity}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              €{Number(item.price).toFixed(2)}
                            </p>
                            <p className="text-xs text-gray-600">
                              Total: €{(Number(item.price) * item.quantity).toFixed(2)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex justify-between text-sm text-gray-900">
                        <span>Subtotal:</span>
                        <span>€{Number(selectedOrder.total).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm text-gray-900">
                        <span>Envio:</span>
                        <span>€{Number(selectedOrder.shippingCost).toFixed(2)}</span>
                      </div>
                      <div className="border-t border-gray-200 mt-2 pt-2">
                        <div className="flex justify-between font-semibold text-gray-900">
                          <span>Total:</span>
                          <span>€{(Number(selectedOrder.total) + Number(selectedOrder.shippingCost)).toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
