import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const coupons = await prisma.coupon.findMany({
      where: {
        sellerId: session.user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(coupons)

  } catch (error) {
    console.error('Erro ao buscar cupões:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    // Validações
    if (!data.code || !data.name || !data.type || !data.value) {
      return NextResponse.json(
        { message: 'Campos obrigatórios em falta' },
        { status: 400 }
      )
    }

    if (data.type === 'PERCENTAGE' && (data.value < 0 || data.value > 100)) {
      return NextResponse.json(
        { message: 'Percentagem deve estar entre 0 e 100' },
        { status: 400 }
      )
    }

    if (data.type === 'FIXED_AMOUNT' && data.value < 0) {
      return NextResponse.json(
        { message: 'Valor fixo deve ser positivo' },
        { status: 400 }
      )
    }

    // Verificar se o código já existe
    const existingCoupon = await prisma.coupon.findUnique({
      where: { code: data.code }
    })

    if (existingCoupon) {
      return NextResponse.json(
        { message: 'Código de cupão já existe' },
        { status: 400 }
      )
    }

    // Criar cupão
    const coupon = await prisma.coupon.create({
      data: {
        sellerId: session.user.id,
        code: data.code.toUpperCase(),
        name: data.name,
        description: data.description || null,
        type: data.type,
        value: data.value,
        minOrderValue: data.minOrderValue || null,
        maxOrderValue: data.maxOrderValue || null,
        maxDiscountValue: data.maxDiscountValue || null,
        allowedCountries: data.allowedCountries || ['PT'],
        usageLimit: data.usageLimit || null,
        userUsageLimit: data.userUsageLimit || null,
        startsAt: data.startsAt ? new Date(data.startsAt) : null,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        isActive: data.isActive !== false
      }
    })

    return NextResponse.json(coupon, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar cupão:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
