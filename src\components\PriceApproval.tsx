'use client'

import { useState } from 'react'
import { CreditCard, AlertCircle, CheckCircle } from 'lucide-react'

interface PriceApprovalProps {
  repairId: string
  originalPrice: number
  newPrice: number
  onApproval: () => 'void'}

export default function PriceApproval({ repairId, originalPrice, newPrice, onApproval }: PriceApprovalProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('same') // same or 'new'
  
  const difference = newPrice - originalPrice
  const isIncrease = difference > 0

  const handleApprove = async () => {
    setIsProcessing(true)
    try {
      const response = await fetch(`/api/repairs/${repairId
}/approve-price`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          approvedPrice: newPrice,
          paymentMethod: paymentMethod})
      })

      if (response.ok) {
        onApproval()
      }
    } catch (error) {
      console.error('Erro ao aprovar preço:', 'error')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
    setIsProcessing(true)
    try {
      const response = await fetch(`/api/repairs/${repairId}/reject-price`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        onApproval()
      }
    } catch (error) {
      console.error('Erro ao rejeitar preço:', 'error')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border-2 border-orange-200 p-6">
      <div className="flex items-center mb-4">
        <AlertCircle className="w-6 h-6 text-orange-600 mr-2" />
        <h3 className="text-lg font-semibold text-orange-900">Aprovação de Preço Necessária</h3>
      </div>

      <div className="space-y-4">
        {/* Price Comparison */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-600">Preço Original</div>
              <div className="text-lg font-medium text-gray-900">
                €{originalPrice.toFixed(2)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Preço Final</div>
              <div className="text-lg font-medium text-gray-900">
                €{newPrice.toFixed(2)}
              </div>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Diferença:</span>
              <span className={`font-medium ${isIncrease ? 'text-red-600' : 'text-green-600'}`}>
                {isIncrease ? '+' : ''}€{difference.toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Payment Method Selection (only 'if increase') */}
        {isIncrease && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Como deseja pagar a diferença?</label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="same"
                  checked={paymentMethod === 'same'}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="mr-2"
                />
                <span className="text-sm">Usar o mesmo método de pagamento</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="new"
                  checked={paymentMethod === 'new'}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="mr-2"
                />
                <span className="text-sm">Escolher novo método de pagamento</span>
              </label>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleApprove}
            disabled={isProcessing}
            className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            {isProcessing ? 'Processando...' : 'Aprovar Preço'}
          </button>
          
          <button
            onClick={handleReject}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            Rejeitar
          </button>
        </div>

        {/* Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="text-sm text-blue-800">
            <strong>Nota:</strong> {isIncrease ? 
              'Ao aprovar, será cobrado o valor adicional no método de pagamento selecionado.' :
              'Ao aprovar, o valor reduzido será creditado na sua conta.'
            }
          </div>
        </div>
      </div>
    </div>
  )
}
