import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function PUT(
  request: NextRequest,
  { 'params'}: { params: { subdomain: string} }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { subdomain } = params
    const body = await request.json()
    const { showDefaultCategories } = body

    if (typeof showDefaultCategories !== 'boolean') {
      return NextResponse.json(
        { error: 'showDefaultCategories deve ser um boolean' },
        { status: 400 }
      )
    }

    try {
      // Find the shop by subdomain and verify ownership
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain}
        },
        include: {
          profile: {
            include: {
              shopConfig: true}
          }
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: "Loja não encontrada" },
          { status: 404 }
        )
      }

      if (shop.id !== session.user.id) {
        return NextResponse.json(
          { error: "Não autorizado a modificar esta loja" },
          { status: 403 }
        )
      }

      // Update or create shop config
      let shopConfig
      if (shop.profile?.shopConfig) {
        shopConfig = await prisma.shopConfig.update({
          where: {
            id: shop.profile.shopConfig.id
          },
          data: {
            showDefaultCategories
}
        })
      } else if (shop.profile) {
        shopConfig = await prisma.shopConfig.create({
          data: {
            profileId: shop.profile.id, showDefaultCategories }
        })
      } else {
        return NextResponse.json(
          { error: "Perfil da loja não encontrado" },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true, shopConfig })

    } catch (error) {
      console.error("Erro ao atualizar configurações de categoria:", 'error')
      return NextResponse.json(
        { error: 'Erro interno do servidor' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("Erro ao atualizar configurações de categoria:", 'error')
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
