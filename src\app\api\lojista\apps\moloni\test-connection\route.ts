import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { testMoloniConnection } from '@/lib/moloni'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { clientId, clientSecret, username, password } = await request.json()

    if (!clientId || !clientSecret || !username || !password) {
      return NextResponse.json(
        { message: "Todos os campos são obrigatórios" },
        { status: 400 }
      )
    }

    // Testar conexão com a nova implementação
    const result = await testMoloniConnection({
      clientId,
      clientSecret,
      username,
      password,
      companyId: ,
      documentSetId: '',
      autoIssue: false,
      isConnected: false
})

    if (result.success) {
      return NextResponse.json({
        message: 'Conexão estabelecida com sucesso',
        connected: true,
        companies: result.companies || []
      })
    } else {
      return NextResponse.json(
        { message: result.error || 'Erro na conexão' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('Erro ao testar conexão Moloni:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
