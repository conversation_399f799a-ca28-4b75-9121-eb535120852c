import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { subdomain: string } }
) {
  try {
    const subdomain = params.subdomain

    if (!subdomain) {
      return NextResponse.json(
        { message: 'Subdomínio é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomínio
    const shop = await prisma.user.findFirst({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          customSubdomain: subdomain
        }
      },
      include: {
        subscription: {
          include: {
            plan: {
              select: {
                individualRepairs: true
              }
            }
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se a loja tem acesso a reparações independentes
    if (!shop.subscription?.plan?.individualRepairs) {
      return NextResponse.json(
        { message: 'Reparações independentes não disponíveis para esta loja' },
        { status: 403 }
      )
    }

    const {
      categoryId,
      brandId,
      deviceId,
      problemTypeId,
      description,
      customerName,
      customerPhone,
      customerEmail,
      customerNif,
      paymentMethod,
      estimatedPrice,
      estimatedDays
    } = await request.json()

    // Validações
    if (!categoryId || !brandId || !problemTypeId || !description || !customerName || !customerPhone || !customerEmail || !estimatedPrice || !estimatedDays) {
      return NextResponse.json(
        { message: 'Todos os campos obrigatórios devem ser preenchidos' },
        { status: 400 }
      )
    }

    // Buscar dados relacionados
    const [category, brand, device, problemType] = await Promise.all([
      prisma.category.findUnique({ where: { id: categoryId } }),
      prisma.brand.findUnique({ where: { id: brandId } }),
      deviceId ? prisma.deviceModel.findUnique({ where: { id: deviceId } }) : null,
      prisma.problemType.findUnique({ where: { id: problemTypeId } })
    ])

    if (!category || !brand || !problemType) {
      return NextResponse.json(
        { message: 'Dados inválidos fornecidos' },
        { status: 400 }
      )
    }

    // Gerar código de tracking único
    const trackingCode = `REP${Date.now().toString().slice(-6)}`

    // Criar reparação independente
    const repair = await prisma.repair.create({
      data: {
        // Dados do cliente
        customerName,
        customerPhone,
        customerNif: customerNif || null,
        
        // Dados do dispositivo
        deviceBrand: brand.name,
        deviceModel: device?.name || 'Modelo não especificado',
        description,
        
        // Problema
        problemType: problemType.name,
        
        // Estimativas
        estimatedPrice: Number(estimatedPrice),
        estimatedCompletionDate: new Date(Date.now() + (Number(estimatedDays) * 24 * 60 * 60 * 1000)),
        
        // Loja responsável
        repairShopId: shop.id,
        
        // Método de entrega (sempre pickup na loja para independentes)
        deliveryMethod: 'STORE_PICKUP',
        
        // Status inicial
        status: 'PENDING',
        
        // Código de tracking
        trackingCode,
        
        // Marcar como reparação independente
        isIndependent: true,
        
        // Dados de criação
        createdAt: new Date(),
        updatedAt: new Date(),

        // Criar cliente se não existir
        customer: {
          connectOrCreate: {
            where: { email: customerEmail },
            create: {
              email: customerEmail,
              name: customerName,
              role: 'CUSTOMER',
              profile: {
                create: {
                  phone: customerPhone,
                  nif: customerNif || null
                }
              }
            }
          }
        }
      }
    })

    // Criar notificação para o lojista
    await prisma.notification.create({
      data: {
        userId: shop.id,
        type: 'REPAIR_CREATED',
        title: 'Nova Reparação Independente',
        message: `Reparação ${trackingCode} criada para ${customerName}`,
        data: {
          repairId: repair.id,
          trackingCode,
          customerName,
          deviceModel: device?.name || 'Modelo não especificado',
          problemType: problemType.name,
          paymentMethod
        }
      }
    })

    // Enviar email de confirmação para o cliente (opcional)
    // TODO: Implementar envio de email

    return NextResponse.json({
      success: true,
      message: 'Reparação criada com sucesso',
      trackingCode,
      repairId: repair.id,
      estimatedPrice: Number(estimatedPrice),
      estimatedDays: Number(estimatedDays),
      shop: {
        name: shop.name,
        email: shop.email,
        phone: shop.profile?.phone
      }
    })

  } catch (error) {
    console.error('Erro ao criar reparação independente:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
