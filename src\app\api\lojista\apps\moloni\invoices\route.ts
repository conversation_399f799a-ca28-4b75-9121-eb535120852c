import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar reparações concluídas que precisam de fatura
    const repairs = await prisma.$queryRaw`
      SELECT 
        r.id,
        r."repairNumber" as number,
        r."totalPrice" as amount,
        r."completedAt" as "createdAt",
        u.name as "customerName",
        REPAIR as type,
        CASE 
          WHEN r."invoiceIssued" = true THEN 'ISSUED'
          ELSE 'PENDING'
        END as status
      FROM repairs r
      JOIN users u ON r."customerId" = u.id
      WHERE r."repairShopId" = ${session.user.id
}
      AND r.status = 'COMPLETED'
      AND r."totalPrice" > 0
      ORDER BY r."completedAt" DESC
    `

    // Buscar vendas do marketplace que precisam de fatura
    const sales = await prisma.$queryRaw`
      SELECT 
        o.id,
        o."orderNumber" as number,
        o.total as amount,
        o."createdAt",
        u.name as "customerName",
        SALE as type,
        CASE 
          WHEN o."invoiceIssued" = true THEN 'ISSUED'
          ELSE 'PENDING'
        END as status
      FROM orders o
      JOIN users u ON o."customerId" = u.id
      JOIN order_items oi ON o.id = oi."orderId"
      JOIN marketplace_products mp ON oi."productId" = mp.id
      WHERE mp."sellerId" = ${session.user.id
}
      AND o.status = 'COMPLETED'
      AND o.total > 0
      GROUP BY o.id, o."orderNumber", o.total, o."createdAt", u.name, o."invoiceIssued"
      ORDER BY o."createdAt" DESC
    `

    // Combinar e formatar as faturas
    const invoices = [...repairs, ...sales].map((invoice: any) => ({
      id: invoice.id,
      number: invoice.number,
      customerName: invoice.customerName,
      amount: Number(invoice.amount),
      status: invoice.status,
      type: invoice.type,
      createdAt: invoice.createdAt
    }))

    return NextResponse.json({
      invoices
})

  } catch (error) {
    console.error('Erro ao buscar faturas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
