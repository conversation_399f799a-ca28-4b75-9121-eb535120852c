'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Users,
  Smartphone,
  Wrench,
  Package,
  BarChart3,
  Settings,
  Plus,
  Edit,
  Trash2,
  CreditCard,
  TrendingUp,
  Activity,
  DollarSign
} from 'lucide-react'

export default function AdminDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalShops: 0,
    totalRepairs: 0,
    totalRevenue: 0,
    userGrowth: 0,
    shopGrowth: 0,
    repairGrowth: 0,
    revenueGrowth: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error)
    } finally {
      setLoading(false)
    }
  }

  const menuItems = [
    {
      title: Gestão de Dispositivos,
      items: [
        { name: 'Categorias', href: '/admin/categories', icon: Package, description: Gerir categorias de dispositivos },
        { name: 'Marcas', href: '/admin/brands', icon: Smartphone, description: Gerir marcas de dispositivos },
        { name: 'Modelos', href: '/admin/models', icon: Smartphone, description: Gerir modelos de dispositivos },
      ]
    },
    {
      title: Gestão de Utilizadores,
      items: [
        { name: 'Utilizadores', href: '/admin/users', icon: Users, description: 'Gerir todos os utilizadores' },
        { name: 'Lojistas', href: '/admin/repair-shops', icon: Wrench, description: Gerir oficinas de reparação },
        { name: 'Planos', href: '/admin/planos', icon: Package, description: Gerir planos de subscrição },
        { name: Subscrições, href: '/admin/subscricoes', icon: CreditCard, description: Gerir subscrições ativas },
      ]
    },
    {
      title: Loja de Peças,
      items: [
        { name: Gestão de Peças, href: '/admin/spare-parts', icon: Package, description: Gerir catálogo de peças },
        { name: Encomendas de Peças, href: '/admin/spare-part-orders', icon: Package, description: Gerir encomendas de peças },
      ]
    },
    {
      title: Operações,
      items: [
        { name: Reparações, href: '/admin/repairs', icon: Wrench, description: Monitorizar reparações },
        { name: Encomendas, href: '/admin/orders', icon: Package, description: Gerir encomendas },
      ]
    },
    {
      title: 'App Store',
      items: [
        { name: Gestão de Apps, href: '/admin/apps', icon: Package, description: 'Gerir apps da loja' },
      ]
    },
    {
      title: Crescimento,
      items: [
        { name: Crescimento Viral, href: '/admin/viral-growth', icon: TrendingUp, description: Referrals, badges e network effects },
      ]
    },
    {
      title: Relatórios,
      items: [
        { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, description: Relatórios e estatísticas },
        { name: Configurações, href: '/admin/settings', icon: Settings, description: Configurações do sistema },
        { name: Integrações, href: '/admin/integracoes', icon: Settings, description: WhatsApp, CTT, Faturas Eletrónicas },
      ]
    }
  ]

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Utilizadores
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              +20.1% desde o mês passado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Reparações
            </CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRepairs}</div>
            <p className="text-xs text-muted-foreground">
              +180.1% desde o mês passado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Encomendas
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              +19% desde o mês passado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Receita
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">€{stats.totalRevenue}</div>
            <p className="text-xs text-muted-foreground">
              +201 desde ontem
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Menu Sections */}
      <div className="space-y-8">
        {menuItems.map((section, sectionIndex) => (
          <div key={sectionIndex}>
            <h2 className="text-lg font-semibold mb-4">{section.title}</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {section.items.map((item, itemIndex) => {
                const IconComponent = item.icon
                return (
                  <Card key={itemIndex} className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                      <div className="p-2 bg-muted rounded-lg mr-3">
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <CardTitle className="text-base">{item.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription>{item.description}</CardDescription>
                      <Link href={item.href} className="mt-2 inline-block">
                        <Button variant="outline" size="sm">
                          Abrir
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesso rápido às funcionalidades mais utilizadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Link href="/admin/categories">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Gerir Categorias
              </Button>
            </Link>
            <Link href="/admin/brands">
              <Button variant="secondary">
                <Plus className="w-4 h-4 mr-2" />
                Gerir Marcas
              </Button>
            </Link>
            <Link href="/admin/models">
              <Button variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                Gerir Modelos
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
