const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function seedCategories() {
  try {
    console.log('🌱 Seeding categories...')

    // Categorias básicas para marketplace de eletrônicos
    const categories = [
      {
        name: 'Smartphones',
        description: 'Telefones móveis e smartphones'
      },
      {
        name: 'Tablets',
        description: 'Tablets e iPads'
      },
      {
        name: 'Laptops',
        description: 'Computadores portáteis'
      },
      {
        name: 'Desktops',
        description: 'Computadores de mesa'
      },
      {
        name: 'Acessórios',
        description: 'Cabos, carregadores, capas e outros acessórios'
      },
      {
        name: 'Gaming',
        description: 'Consoles, jogos e acessórios para gaming'
      },
      {
        name: 'Audio',
        description: 'Fones de ouvido, caixas de som e equipamentos de áudio'
      },
      {
        name: 'Smartwatches',
        description: 'Relógios inteligentes e wearables'
      },
      {
        name: '<PERSON><PERSON>meras',
        description: 'Câmeras digitais e equipamentos fotográficos'
      },
      {
        name: 'Componentes',
        description: 'Peças e componentes para reparação'
      }
    ]

    for (const category of categories) {
      const existing = await prisma.category.findFirst({
        where: { name: category.name }
      })

      if (!existing) {
        await prisma.category.create({
          data: category
        })
        console.log(`✅ Created category: ${category.name}`)
      } else {
        console.log(`⏭️ Category already exists: ${category.name}`)
      }
    }

    console.log('🎉 Categories seeded successfully!')

  } catch (error) {
    console.error('❌ Error seeding categories:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedCategories()
