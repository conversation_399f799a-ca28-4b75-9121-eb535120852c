import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'
import { protectSuperAdmin, isSuperAdminEmail } from '@/lib/superadmin-protection'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    // Await params before using
    const { id } = await params

    // Buscar usuário com detalhes
    const user = await prisma.$queryRaw`
      SELECT
        u.*,
        p.phone,
        p."companyName",
        p."customerName",
        p."companyNif",
        p.description,
        p.street,
        p.city,
        p."postalCode"
      FROM users u
      LEFT JOIN profiles p ON u.id = p."userId"
      WHERE u.id = ${id}
    `

    if (!user || user.length === 0) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    const userData = user[0]

    // Buscar subscrição se for lojista
    let subscription = null
    if (userData.role === 'REPAIR_SHOP') {
      const subscriptionData = await prisma.$queryRaw`
        SELECT
          s.*,
          sp.name as plan_name
        FROM subscriptions s
        JOIN subscription_plans sp ON s."planId" = sp.id
        WHERE s."userId" = ${id}
      `

      if (subscriptionData.length > 0) {
        subscription = {
          ...subscriptionData[0],
          plan: {
            name: subscriptionData[0].plan_name
          }
        }
      }
    }

    // Buscar contadores
    const repairCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM repairs WHERE "customerId" = ${id} OR "repairShopId" = ${id}
    `

    const orderCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM orders WHERE "customerId" = ${id}
    `

    // Formatar resposta
    const formattedUser = {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      role: userData.role,
      createdAt: userData.createdAt,
      profile: {
        phone: userData.phone,
        companyName: userData.companyName,
        customerName: userData.customerName,
        companyNif: userData.companyNif,
        description: userData.description,
        street: userData.street,
        city: userData.city,
        postalCode: userData.postalCode
      },
      subscription,
      _count: {
        repairs: Number(repairCount[0]?.count || 0),
        orders: Number(orderCount[0]?.count || 0)
      }
    }

    return NextResponse.json({
      user: formattedUser
    })

  } catch (error) {
    console.error("Erro ao buscar usuário:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Await params before using
    const { id } = await params
    const { name, email, role, isVerified, isFeatured } = await request.json()
    const userId = id

    if (!name || !email || !role) {
      return NextResponse.json(
        { message: "Todos os campos são obrigatórios" },
        { status: 400 }
      )
    }

    // Verificar se o usuário existe
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    // Proteger superadmin usando sistema robusto
    const protection = await protectSuperAdmin(userId, 'UPDATE_ROLE')
    if (!protection.allowed) {
      return NextResponse.json(
        { message: protection.message },
        { status: 403 }
      )
    }

    // Verificar se o email já está em uso por outro usuário
    if (email !== existingUser.email) {
      const emailInUse = await prisma.user.findUnique({
        where: { email }
      })

      if (emailInUse) {
        return NextResponse.json(
          { message: "Este email já está em uso" },
          { status: 400 }
        )
      }
    }

    // Preparar dados de atualização
    const updateData: any = {
      name,
      email,
      role
    }

    // Adicionar campos de verificação apenas para lojistas
    if (role === 'REPAIR_SHOP') {
      if (typeof isVerified === 'boolean') {
        updateData.isVerified = isVerified
      }
      if (typeof isFeatured === 'boolean') {
        updateData.isFeatured = isFeatured
      }
    }

    // Atualizar usuário
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData
    })

    return NextResponse.json({
      message: "Usuário atualizado com sucesso",
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        isVerified: updatedUser.isVerified,
        isFeatured: updatedUser.isFeatured,
        createdAt: updatedUser.createdAt
      }
    })

  } catch (error) {
    console.error("Erro ao atualizar usuário:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Await params before using
    const { id } = await params
    const userId = id

    // Verificar se o usuário existe
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    // Proteger superadmin usando sistema robusto
    const protection = await protectSuperAdmin(userId, 'DELETE')
    if (!protection.allowed) {
      return NextResponse.json(
        { message: protection.message },
        { status: 403 }
      )
    }

    // Remover usuário e dados relacionados
    await prisma.$transaction(async (tx) => {
      // Remover perfil
      await tx.profile.deleteMany({
        where: { userId }
      })

      // Remover subscrições
      await tx.subscription.deleteMany({
        where: { userId }
      })

      // Remover notificações
      await tx.notification.deleteMany({
        where: { userId }
      })

      // Remover configurações de usuário
      await tx.userSettings.deleteMany({
        where: { userId }
      })

      // Remover apps instalados
      await tx.installedApp.deleteMany({
        where: { userId }
      })

      // Remover configurações de apps
      await tx.appConfig.deleteMany({
        where: { userId }
      })

      // Finalmente remover o usuário
      await tx.user.delete({
        where: { id: userId }
      })
    })

    return NextResponse.json({
      message: 'Usuário removido com sucesso'
    })

  } catch (error) {
    console.error('Erro ao remover usuário:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
