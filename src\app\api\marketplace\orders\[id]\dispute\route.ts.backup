import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const reason = formData.get('reason') as string
    const description = formData.get('description') as string
    const desiredOutcome = formData.get('desiredOutcome') as string

    if (!reason || !description || !desiredOutcome) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Verificar se a encomenda pertence ao cliente
    const order = await prisma.order.findFirst({
      where: {
        id: params.id,
        customerId: session.user.id
      },
      include: {
        marketplaceOrderItems: {
          include: {
            product: {
              include: {
                seller: {
                  select: {
                    id: true,
                    name: true,
                    email: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: 'Encomenda não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se já existe uma disputa
    const existingDispute = await prisma.dispute.findFirst({
      where: {
        orderId: params.id
      }
    })

    if (existingDispute) {
      return NextResponse.json(
        { message: 'Já existe uma disputa para esta encomenda' },
        { status: 400 }
      )
    }

    // Criar a disputa
    const dispute = await prisma.dispute.create({
      data: {
        orderId: params.id,
        customerId: session.user.id,
        reason,
        description,
        status: 'OPEN'
      }
    })

    // Atualizar status da encomenda para DISPUTE
    await prisma.order.update({
      where: {
        id: params.id
      },
      data: {
        status: 'DISPUTE'
      }
    })

    // Criar notificações para os vendedores envolvidos
    const sellers = new Set()
    order.marketplaceOrderItems.forEach(item => {
      if (item.product.seller) {
        sellers.add(item.product.seller.id)
      }
    })

    for (const sellerId of sellers) {
      await prisma.notification.create({
        data: {
          userId: sellerId as string,
          title: 'Nova Disputa Aberta',
          message: `Uma disputa foi aberta para a encomenda #${params.id.slice(-8)}. Acesse a plataforma para mais detalhes.`,
          type: 'DISPUTE_OPENED',
          metadata: {
            disputeId: dispute.id,
            orderId: params.id,
            reason
          }
        }
      })
    }

    return NextResponse.json({
      message: 'Disputa criada com sucesso',
      dispute: {
        id: dispute.id,
        status: dispute.status,
        reason: dispute.reason,
        description: dispute.description
      }
    })

  } catch (error) {
    console.error('Erro ao criar disputa:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
