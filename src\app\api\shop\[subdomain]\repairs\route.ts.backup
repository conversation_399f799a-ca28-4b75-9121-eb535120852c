import { NextRequest, NextResponse } from 'next/server'
import { sendEmail } from '@/lib/email'

// Dynamic import to avoid initialization issues
const getPrisma = async () => {
  const { PrismaClient } = await import('@prisma/client')
  return new PrismaClient()
}

export async function POST(
  request: NextRequest,
  { params }: { params: { subdomain: string } }
) {
  try {
    const prisma = await getPrisma()
    const { subdomain } = params
    const body = await request.json()

    const {
      customerName,
      customerEmail,
      customerPhone,
      deviceBrand,
      deviceModel,
      problemDescription,
      problemTypes,
      deliveryMethod,
      customerAddress
    } = body

    // Validate required fields
    if (!customerName || !customerEmail || !customerPhone || !deviceBrand || !deviceModel || !problemDescription) {
      return NextResponse.json(
        { error: 'Campos obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Find shop by subdomain
    const shop = await prisma.profile.findFirst({
      where: { customSubdomain: subdomain }
    })

    if (!shop) {
      return NextResponse.json(
        { error: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Find or create customer
    let customer = await prisma.customer.findFirst({
      where: {
        email: customerEmail,
        profileId: shop.id
      }
    })

    if (!customer) {
      customer = await prisma.customer.create({
        data: {
          name: customerName,
          email: customerEmail,
          phone: customerPhone,
          address: customerAddress,
          profileId: shop.id
        }
      })
    }

    // Find device brand
    const brand = await prisma.brand.findFirst({
      where: { name: deviceBrand }
    })

    if (!brand) {
      return NextResponse.json(
        { error: 'Marca não encontrada' },
        { status: 400 }
      )
    }

    // Find device model
    const model = await prisma.deviceModel.findFirst({
      where: {
        name: deviceModel,
        brandId: brand.id
      }
    })

    if (!model) {
      return NextResponse.json(
        { error: 'Modelo não encontrado' },
        { status: 400 }
      )
    }

    // Generate repair number
    const repairCount = await prisma.independentRepair.count({
      where: { profileId: shop.id }
    })
    const repairNumber = `REP-${subdomain.toUpperCase()}-${String(repairCount + 1).padStart(4, '0')}`

    // Create repair
    const repair = await prisma.independentRepair.create({
      data: {
        repairNumber,
        customerName,
        customerEmail,
        customerPhone,
        customerAddress,
        deviceBrand: brand.name,
        deviceModel: model.name,
        problemDescription,
        problemTypes: problemTypes || [],
        deliveryMethod: deliveryMethod || 'pickup',
        status: 'pending',
        profileId: shop.id,
        customerId: customer.id,
        brandId: brand.id,
        deviceModelId: model.id
      },
      include: {
        brand: true,
        deviceModel: true,
        customer: true
      }
    })

    // Send confirmation email
    try {
      await sendEmail(
        customerEmail,
        'repairCreated',
        {
          customerName,
          repairNumber,
          deviceBrand: brand.name,
          deviceModel: model.name,
          problemDescription,
          status: 'Pendente',
          deliveryMethod,
          shopName: shop.name || shop.companyName || 'Loja'
        }
      )
    } catch (emailError) {
      console.error('Erro ao enviar email de confirmação:', emailError)
      // Don't fail the repair creation if email fails
    }

    return NextResponse.json({
      success: true,
      repair,
      repairNumber
    })

  } catch (error) {
    console.error('Erro ao criar reparação:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const prisma = await getPrisma()
    const { subdomain } = await params
    const { searchParams } = new URL(request.url)
    
    const customerEmail = searchParams.get('customerEmail')
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    // Find shop by subdomain
    const shop = await prisma.profile.findFirst({
      where: { customSubdomain: subdomain }
    })

    if (!shop) {
      return NextResponse.json(
        { error: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Build where clause
    const where: any = {
      profileId: shop.id
    }

    if (customerEmail) {
      where.customerEmail = customerEmail
    }

    if (status) {
      where.status = status
    }

    // Get repairs with pagination
    const [repairs, total] = await Promise.all([
      prisma.independentRepair.findMany({
        where,
        include: {
          brand: true,
          deviceModel: true,
          customer: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.independentRepair.count({ where })
    ])

    return NextResponse.json({
      repairs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Erro ao buscar reparações:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { subdomain: string } }
) {
  try {
    const prisma = await getPrisma()
    const { subdomain } = params
    const body = await request.json()

    const { repairId, status, estimatedPrice, notes } = body

    if (!repairId) {
      return NextResponse.json(
        { error: 'ID da reparação é obrigatório' },
        { status: 400 }
      )
    }

    // Find shop by subdomain
    const shop = await prisma.profile.findFirst({
      where: { customSubdomain: subdomain }
    })

    if (!shop) {
      return NextResponse.json(
        { error: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Update repair
    const repair = await prisma.independentRepair.update({
      where: {
        id: repairId,
        profileId: shop.id
      },
      data: {
        ...(status && { status }),
        ...(estimatedPrice && { estimatedPrice }),
        ...(notes && { notes }),
        updatedAt: new Date()
      },
      include: {
        brand: true,
        deviceModel: true,
        customer: true
      }
    })

    return NextResponse.json({
      success: true,
      repair
    })

  } catch (error) {
    console.error('Erro ao atualizar reparação:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
