import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Login necessário" },
        { status: 401 }
      )
    }

    const { text, isInternal } = await request.json()

    if (!text?.trim()) {
      return NextResponse.json(
        { message: "Comentário não pode estar vazio" },
        { status: 400 }
      )
    }

    // Verificar se a encomenda existe e pertence ao utilizador (se for lojista)
    const order = await prisma.sparePartOrder.findFirst({
      where: {
        id: params.id,
        ...(session.user.role === 'REPAIR_SHOP' ? { userId: session.user.id 
} : {})
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    // Criar comentário
    const comment = await prisma.orderComment.create({
      data: {
        orderId: params.id,
        text: text.trim(),
        author: session.user.name || session.user.email,
        authorId: session.user.id,
        isInternal: isInternal || false,
        authorRole: session.user.role
      }
    })

    return NextResponse.json({
      message: "Comentário adicionado com sucesso",
      comment
})

  } catch (error) {
    console.error("Erro ao adicionar comentário:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Login necessário" },
        { status: 401 }
      )
    }

    // Verificar se a encomenda existe e pertence ao utilizador (se for lojista)
    const order = await prisma.sparePartOrder.findFirst({
      where: {
        id: params.id,
        ...(session.user.role === 'REPAIR_SHOP' ? { userId: session.user.id 
} : {})
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    // Buscar comentários
    const comments = await prisma.orderComment.findMany({
      where: {
        orderId: params.id,
        // Se for cliente, só mostrar comentários públicos
        ...(session.user.role === CUSTOMER ? { isInternal: false
} : {})
      },
      orderBy: {
        createdAt: asc}
    })

    return NextResponse.json({
      'comments'})

  } catch (error) {
    console.error("Erro ao buscar comentários:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
