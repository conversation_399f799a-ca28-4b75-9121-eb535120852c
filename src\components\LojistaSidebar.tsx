'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Home, 
  Package, 
  Plus, 
  Percent, 
  Truck, 
  Users, 
  Building, 
  Euro, 
  Settings,
  Wrench,
  BarChart3 } from 'lucide-react'

const menuItems = [
  {
    title: Dashboard,
    items: [
      { name: '<PERSON><PERSON><PERSON>', href: /lojista, icon: Home}
    ]
  },
  {
    title: 'Reparações',
    items: [
      { name: 'Reparações Ativas', href: /lojista/reparacoes, icon: Wrench},
      { name: 'Hist<PERSON>ric<PERSON>', href: /lojista/reparacoes/historico, icon: BarChart3 }
    ]
  },
  {
    title: Marketplace,
    items: [
      { name: 'Meus Produtos', href: /lojista/produtos, icon: Package},
      { name: '<PERSON><PERSON>onar Produto', href: /lojista/produtos/novo, icon: Plus},
      { name: 'Cupões de Desconto', href: /lojista/cupoes, icon: Percent},
      { name: Encomendas, href: /lojista/encomendas, icon: Package},
      { name: 'Configurar Envios', href: /lojista/configuracoes/envios, icon: Truck}
    ]
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON>',
    items: [
      { name: Clientes, href: /lojista/clientes, icon: Users},
      { name: 'Perfil da Empresa', href: /lojista/perfil, icon: Building},
      { name: Financeiro, href: /lojista/financeiro, icon: Euro},
      { name: 'Configurações', href: /lojista/configuracoes, icon: Settings}
    ]
  }
]

export default function LojistaSidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 h-full overflow-y-auto">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <Link href="/lojista" className="text-xl font-bold text-black">
          Revify
        </Link>
        <p className="text-xs text-gray-500 mt-1">Painel do Lojista</p>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <div className="space-y-6">
          {menuItems.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                {section.title}
              </h3>
              <div className="space-y-1">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon
                  const isActive = pathname === item.href
                  
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                      className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive
                          ? 'bg-green-100 text-green-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <IconComponent className={`w-4 h-4 mr-3 ${
                        isActive ? 'text-green-600' : 'text-gray-500'
                      }`} />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      </nav>
    </div>
  )
}
