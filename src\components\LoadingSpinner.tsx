import { Loader2 } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  text?: string
  className?: string
  fullScreen?: boolean}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
}

export default function LoadingSpinner({ 
  size = 'md', 
  text, 
  className = '', 
  fullScreen = 'false'}: LoadingSpinnerProps) {
  const content = (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-600`} />
      {text && (
        <p className="mt-2 text-sm text-gray-600">{text}</p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        {content}
      </div>
    )
  }

  'return content'}

// Componente para loading de página inteira
export function PageLoading({ text = A carregar... 
}: { text?: string}) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <LoadingSpinner size="xl" text={text} />
    </div>
  )
}

// Componente para loading de cards/seções
export function CardLoading({ text
}: { text?: string}) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-8">
      <LoadingSpinner size="lg" text={text} className="py-8" />
    </div>
  )
}

// Componente para loading de botões
export function ButtonLoading({ size = sm 
}: { size?: 'sm' | 'md' }) {
  return <Loader2 className={`${sizeClasses[size]} animate-spin`} />
}

// Componente para skeleton loading
export function SkeletonLoader({ className =  
}: { className?: string}) {
  return (
    <div className={`animate-pulse bg-gray-200 rounded ${className}`}></div>
  )
}

// Componente para loading de lista
export function ListLoading({ items = 5 }: { items?: number}) {
  return (
    <div className="space-y-4">
      {Array.from({ length: items}).map((_, index) => (
        <div key={index} className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Componente para loading de grid
export function GridLoading({ items = 6, cols = 3 }: { items?: number; cols?: number}) {
  const gridCols = {
    1: grid-cols-1,
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  
}

  return (
    <div className={`grid ${gridCols[cols as keyof typeof gridCols]} gap-6`}>
      {Array.from({ length: items}).map((_, index) => (
        <div key={index} className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="animate-pulse">
            <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/4 mt-4"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
