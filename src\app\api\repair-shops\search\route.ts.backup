import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { categoryId, brandId, deviceId, problemTypeId, location } = body

    console.log('Pesquisa de lojas com parâmetros:', { categoryId, brandId, deviceId, problemTypeId, location })

    // Buscar lojas que fazem reparações
    const shops = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          isNot: null
        }
      },
      include: {
        profile: {
          select: {
            companyName: true,
            phone: true,
            address: true,
            city: true,
            description: true,
            logoUrl: true
          }
        },
        // Buscar reparações feitas pela loja para calcular estatísticas
        repairsAsShop: {
          select: {
            id: true,
            status: true,
            createdAt: true
          },
          take: 100 // Limitar para performance
        }
      },
      take: 20 // Limitar número de lojas retornadas
    })

    // Processar dados das lojas
    const processedShops = shops.map(shop => {
      const repairs = shop.repairsAsShop || []
      const completedRepairs = repairs.filter(r => r.status === 'COMPLETED')
      const totalRepairs = repairs.length

      // Calcular estatísticas básicas
      const rating = totalRepairs > 0 ? Math.min(4.8, 3.5 + (completedRepairs.length / totalRepairs) * 1.3) : 4.0
      const reviewCount = Math.max(1, Math.floor(totalRepairs * 0.7))

      // Preço base simulado baseado na categoria
      let basePrice = 50
      if (categoryId === '1') basePrice = 80 // Smartphones
      if (categoryId === '2') basePrice = 120 // Tablets  
      if (categoryId === '3') basePrice = 150 // Laptops

      // Variação de preço por loja (±20%)
      const priceVariation = (Math.random() - 0.5) * 0.4
      const price = Math.round(basePrice * (1 + priceVariation))

      return {
        id: shop.id,
        name: shop.profile?.companyName || shop.name || 'Loja de Reparações',
        address: shop.profile?.address || 'Morada não disponível',
        city: shop.profile?.city || 'Cidade não disponível',
        phone: shop.profile?.phone || '',
        description: shop.profile?.description || 'Especialistas em reparações de dispositivos eletrónicos',
        logoUrl: shop.profile?.logoUrl || null,
        rating: Math.round(rating * 10) / 10,
        reviewCount,
        price,
        estimatedTime: Math.floor(Math.random() * 3) + 1, // 1-3 dias
        distance: Math.round((Math.random() * 15 + 1) * 10) / 10, // 0.1-15km
        specialties: ['Reparação de ecrãs', 'Substituição de baterias', 'Reparação de água'],
        availability: Math.random() > 0.3 ? 'Disponível' : 'Ocupado',
        totalRepairs: totalRepairs
      }
    })

    // Ordenar por rating e disponibilidade
    const sortedShops = processedShops.sort((a, b) => {
      if (a.availability === 'Disponível' && b.availability !== 'Disponível') return -1
      if (a.availability !== 'Disponível' && b.availability === 'Disponível') return 1
      return b.rating - a.rating
    })

    console.log(`Encontradas ${sortedShops.length} lojas`)

    return NextResponse.json({
      shops: sortedShops,
      total: sortedShops.length,
      searchParams: { categoryId, brandId, deviceId, problemTypeId, location }
    })

  } catch (error) {
    console.error('Erro ao pesquisar lojas:', error)
    return NextResponse.json(
      { error: 'Erro ao pesquisar lojas de reparação' },
      { status: 500 }
    )
  }
}
