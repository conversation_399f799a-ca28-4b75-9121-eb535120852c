import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a app está instalada
    const installedApp = await prisma.$queryRaw`
      SELECT * FROM installed_apps 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = 'tax-calculator' 
      AND "isActive" = true
    `

    if (!installedApp || installedApp.length === 0) {
      return NextResponse.json(
        { message: 'App Calculadora de Impostos não está instalada' },
        { status: 404 }
      )
    }

    // Buscar cálculos do usuário
    const calculations = await prisma.$queryRaw`
      SELECT * FROM tax_calculations 
      WHERE "userId" = ${session.user.id}
      ORDER BY "createdAt" DESC
    `

    return NextResponse.json({
      calculations: calculations.map((calc: any) => ({
        id: calc.id,
        name: calc.name,
        baseAmount: Number(calc.baseAmount),
        vatRate: Number(calc.vatRate),
        vatAmount: Number(calc.vatAmount),
        totalAmount: Number(calc.totalAmount),
        createdAt: calc.createdAt
      }))
    })

  } catch (error) {
    console.error('Erro ao buscar cálculos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, baseAmount, vatRate, vatAmount, totalAmount } = await request.json()

    if (!name || baseAmount === undefined || vatRate === undefined) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Salvar cálculo
    const calculation = await prisma.$queryRaw`
      INSERT INTO tax_calculations (
        "id", "userId", name, "baseAmount", "vatRate", "vatAmount", "totalAmount", "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${session.user.id}, ${name}, ${baseAmount}, ${vatRate}, 
        ${vatAmount}, ${totalAmount}, NOW(), NOW()
      ) RETURNING *
    `

    return NextResponse.json({
      message: 'Cálculo salvo com sucesso',
      calculation: calculation[0]
    })

  } catch (error) {
    console.error('Erro ao salvar cálculo:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
