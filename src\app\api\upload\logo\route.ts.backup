import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.formData()
    const file: File | null = data.get('logo') as unknown as File

    if (!file) {
      return NextResponse.json(
        { message: 'Nenhum arquivo enviado' },
        { status: 400 }
      )
    }

    // Validar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { message: 'Tipo de arquivo não permitido. Use JPG, PNG ou WebP.' },
        { status: 400 }
      )
    }

    // Validar tamanho (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { message: 'Arquivo muito grande. Máximo 5MB.' },
        { status: 400 }
      )
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Criar diretório se não existir
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'logos')
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const filename = `${session.user.id}_${timestamp}.${extension}`
    const filepath = join(uploadDir, filename)

    // Salvar arquivo
    await writeFile(filepath, buffer)

    // URL pública do arquivo
    const logoUrl = `/uploads/logos/${filename}`

    // Atualizar perfil do usuário com o novo logo
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()

    try {
      // Atualizar perfil com o novo logo
      const profile = await prisma.profile.upsert({
        where: { userId: session.user.id },
        update: { logo: logoUrl },
        create: {
          userId: session.user.id,
          logo: logoUrl
        },
        include: {
          shopConfig: true
        }
      })

      // Se existe configuração da loja, também atualizar o logo lá para manter consistência
      if (profile.shopConfig) {
        await prisma.shopConfig.update({
          where: { id: profile.shopConfig.id },
          data: { logoUrl: logoUrl }
        })
      }
    } finally {
      await prisma.$disconnect()
    }

    return NextResponse.json({
      message: 'Logo enviado com sucesso',
      logoUrl
    })

  } catch (error) {
    console.error('Erro ao fazer upload do logo:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
