import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const categories = await prisma.category.findMany({
      where: {
        isActive: true
      },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // Se não houver categorias, criar algumas básicas
    if (categories.length === 0) {
      const basicCategories = [
        { name: 'Smartphones', isActive: true },
        { name: 'Tablets', isActive: true },
        { name: 'Laptops', isActive: true },
        { name: 'Smartwatches', isActive: true },
        { name: 'Consolas', isActive: true },
        { name: 'Auscultadores', isActive: true }
      ]

      for (const category of basicCategories) {
        await prisma.category.create({
          data: category
        })
      }

      // Buscar novamente após criar
      const newCategories = await prisma.category.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              deviceModels: true
            }
          }
        },
        orderBy: { name: 'asc' }
      })

      return NextResponse.json(newCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        isActive: cat.isActive,
        productCount: cat._count.deviceModels
      })))
    }

    return NextResponse.json(categories)

  } catch (error) {
    console.error('Erro ao buscar categorias:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, description } = await request.json()
    console.log('Creating category:', { name, description })

    if (!name) {
      return NextResponse.json(
        { message: "Nome da categoria é obrigatório" },
        { status: 400 }
      )
    }

    // Verificar se a categoria já existe
    const existingCategory = await prisma.category.findUnique({
      where: { name }
    })

    if (existingCategory) {
      return NextResponse.json(
        { message: "Esta categoria já existe" },
        { status: 400 }
      )
    }

    const category = await prisma.category.create({
      data: {
        name,
        description: description || null,
        isActive: true
      },
      include: {
        _count: {
          select: {
            deviceModels: true
          }
        }
      }
    })

    return NextResponse.json(category, { status: 201 })

  } catch (error) {
    console.error("Erro ao criar categoria:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}


