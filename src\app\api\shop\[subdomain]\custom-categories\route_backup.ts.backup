import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { subdomain: string } }
) {
  try {
    const { subdomain } = params

    // Find the shop by subdomain
    const shop = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain
        }
      },
      include: {
        profile: {
          include: {
            shopConfig: true
          }
        },
        shopCategories: {
          where: {
            isActive: true
          },
          orderBy: {
            sortOrder: 'asc'
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { error: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Get default categories if enabled
    let defaultCategories = []
    if (shop.profile?.shopConfig?.showDefaultCategories !== false) {
      defaultCategories = await prisma.category.findMany({
        where: {
          isActive: true,
          marketplaceProducts: {
            some: {
              sellerId: shop.id,
              stock: {
                gt: 0
              }
            }
          }
        },
        include: {
          _count: {
            select: {
              marketplaceProducts: {
                where: {
                  sellerId: shop.id,
                  stock: {
                    gt: 0
                  }
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })
    }

    return NextResponse.json({
      success: true,
      customCategories: shop.shopCategories,
      defaultCategories: defaultCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        productCount: cat._count.marketplaceProducts,
        isDefault: true
      })),
      showDefaultCategories: shop.profile?.shopConfig?.showDefaultCategories !== false
    })

  } catch (error) {
    console.error('Erro ao buscar categorias personalizadas:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { subdomain: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      )
    }

    const { subdomain } = params
    const body = await request.json()
    const { name, description, icon, sortOrder } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Nome da categoria é obrigatório' },
        { status: 400 }
      )
    }

    // Find the shop by subdomain and verify ownership
    const shop = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { error: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    if (shop.id !== session.user.id) {
      return NextResponse.json(
        { error: 'Não autorizado para esta loja' },
        { status: 403 }
      )
    }

    // Create the custom category
    const category = await prisma.shopCategory.create({
      data: {
        shopId: shop.id,
        name,
        description: description || '',
        icon: icon || '',
        sortOrder: sortOrder || 0,
        isActive: true
      }
    })

    return NextResponse.json({
      success: true,
      category
    })

  } catch (error) {
    console.error('Erro ao criar categoria personalizada:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
