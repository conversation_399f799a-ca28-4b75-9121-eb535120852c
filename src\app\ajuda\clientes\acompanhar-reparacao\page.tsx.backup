'use client'

import Link from 'next/link'
import MainHeader from '@/components/MainHeader'
import { ArrowLeft, Eye, Bell, MessageCircle, Clock, CheckCircle } from 'lucide-react'

export default function AcompanharReparacaoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader subtitle="Central de Ajuda" />

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=clientes" className="hover:text-gray-700">Para Clientes</Link>
              <span className="mx-2">›</span>
              <span>Acompanhar o estado da reparação</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Acompanhar o estado da reparação</h1>
            <p className="text-lg text-gray-600">
              Saiba como acompanhar o progresso da sua reparação em tempo real.
            </p>
          </div>

          <div className="prose max-w-none">
            <h2>Como acompanhar a sua reparação</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Eye className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">1. Área do cliente</h3>
                  <p className="text-blue-800 mb-3">Aceda à sua conta para ver todas as reparações:</p>
                  <ul className="list-disc list-inside text-blue-800 space-y-1">
                    <li>Faça login na plataforma</li>
                    <li>Aceda ao seu dashboard de cliente</li>
                    <li>Veja a lista das suas reparações</li>
                    <li>Clique na reparação para ver detalhes</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Bell className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">2. Notificações automáticas</h3>
                  <p className="text-green-800 mb-3">Receba atualizações por:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li><strong>Email:</strong> Notificações detalhadas</li>
                    <li><strong>SMS:</strong> Atualizações importantes</li>
                    <li><strong>App móvel:</strong> Notificações push (em breve)</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <MessageCircle className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900 mb-2">3. Comunicação direta</h3>
                  <p className="text-purple-800 mb-3">Fale diretamente com a oficina:</p>
                  <ul className="list-disc list-inside text-purple-800 space-y-1">
                    <li>Chat integrado na plataforma</li>
                    <li>Telefone da oficina</li>
                    <li>Email direto</li>
                    <li>WhatsApp (se disponível)</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Estados da reparação</h2>
            <div className="space-y-4 my-6">
              <div className="flex items-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600 mr-4" />
                <div>
                  <h3 className="font-semibold text-yellow-900">Aguarda recolha</h3>
                  <p className="text-yellow-800 text-sm">O equipamento está à espera de ser recolhido</p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <Eye className="w-6 h-6 text-blue-600 mr-4" />
                <div>
                  <h3 className="font-semibold text-blue-900">Em diagnóstico</h3>
                  <p className="text-blue-800 text-sm">A oficina está a analisar o problema</p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <MessageCircle className="w-6 h-6 text-orange-600 mr-4" />
                <div>
                  <h3 className="font-semibold text-orange-900">Aguarda aprovação</h3>
                  <p className="text-orange-800 text-sm">Orçamento enviado, aguarda a sua aprovação</p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <Clock className="w-6 h-6 text-purple-600 mr-4" />
                <div>
                  <h3 className="font-semibold text-purple-900">Em reparação</h3>
                  <p className="text-purple-800 text-sm">A reparação está em curso</p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600 mr-4" />
                <div>
                  <h3 className="font-semibold text-green-900">Concluída</h3>
                  <p className="text-green-800 text-sm">Reparação terminada, pronta para entrega</p>
                </div>
              </div>
            </div>

            <h2>Informações disponíveis</h2>
            <p>Para cada reparação, pode consultar:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Detalhes técnicos</h3>
                <ul className="text-sm space-y-1">
                  <li>• Problema reportado</li>
                  <li>• Diagnóstico da oficina</li>
                  <li>• Peças necessárias</li>
                  <li>• Tempo estimado</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Informações comerciais</h3>
                <ul className="text-sm space-y-1">
                  <li>• Orçamento detalhado</li>
                  <li>• Estado do pagamento</li>
                  <li>• Garantia oferecida</li>
                  <li>• Data de entrega</li>
                </ul>
              </div>
            </div>

            <h2>Dicas importantes</h2>
            <div className="bg-blue-50 rounded-lg p-6">
              <ul className="space-y-2">
                <li><strong>Verifique regularmente:</strong> Aceda à plataforma pelo menos uma vez por dia</li>
                <li><strong>Responda rapidamente:</strong> Aprove orçamentos o mais rápido possível</li>
                <li><strong>Mantenha contactos atualizados:</strong> Para receber todas as notificações</li>
                <li><strong>Tire dúvidas:</strong> Contacte a oficina se tiver questões</li>
                <li><strong>Guarde comprovativo:</strong> Do pedido de reparação</li>
              </ul>
            </div>

            <h2>Problemas comuns</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Não recebo notificações</h3>
              <p className="mb-4">Verifique a pasta de spam e confirme se o email/telefone estão corretos no perfil.</p>
              
              <h3 className="font-semibold mb-3">Estado não atualiza</h3>
              <p className="mb-4">Contacte a oficina diretamente. Algumas atualizações podem ter atraso.</p>
              
              <h3 className="font-semibold mb-3">Prazo ultrapassado</h3>
              <p>Se o prazo estimado foi ultrapassado, contacte a oficina para esclarecimentos.</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda/clientes/fazer-reparacao"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior: Como fazer uma reparação
              </Link>
              <Link
                href="/ajuda/clientes/pagamento-online"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                Próximo: Como pagar online
                <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
