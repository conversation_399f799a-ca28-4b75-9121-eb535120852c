import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const price = await prisma.repairShopPrice.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!price) {
      return NextResponse.json(
        { message: 'Preço não encontrado' },
        { status: 404 }
      )
    }

    if (price.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: 'Sem permissão para eliminar este preço' },
        { status: 403 }
      )
    }

    await prisma.repairShopPrice.delete({
      where: { id: resolvedParams.id }
    })

    return NextResponse.json({ message: 'Preço eliminado com sucesso' })

  } catch (error) {
    console.error('Erro ao eliminar preço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { price, estimatedTime } = await request.json()

    if (!price || price <= 0) {
      return NextResponse.json(
        { message: 'Preço deve ser maior que zero' },
        { status: 400 }
      )
    }

    const existingPrice = await prisma.repairShopPrice.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!existingPrice) {
      return NextResponse.json(
        { message: 'Preço não encontrado' },
        { status: 404 }
      )
    }

    if (existingPrice.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: 'Sem permissão para editar este preço' },
        { status: 403 }
      )
    }

    const updatedPrice = await prisma.repairShopPrice.update({
      where: { id: resolvedParams.id },
      data: {
        price: parseFloat(price),
        estimatedTime: estimatedTime ? parseInt(estimatedTime) : null
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        deviceModel: {
          select: {
            name: true,
            brand: {
              select: {
                name: true
              }
            }
          }
        },
        problemType: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Preço atualizado com sucesso',
      price: updatedPrice
    })

  } catch (error) {
    console.error('Erro ao atualizar preço:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
