'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Plus, Edit, Trash2, MapPin, ArrowLeft, Home, Building } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
interface Address {
  id: string
  label: string
  street: string
  city: string
  postalCode: string
  country: string
  isDefault: boolean
  type: 'HOME' | 'WORK' | 'OTHER'
}

export default function ClienteEnderecosPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [addresses, setAddresses] = useState<Address[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)
  
  const [formData, setFormData] = useState({
    label: '',
    street: '',
    city: '',
    postalCode: '',
    country: Portugal,
    isDefault: false,
    type: 'HOME' as 'HOME' | 'WORK' | 'OTHER'
  })

  useEffect(() => {
    fetchAddresses()
  }, [])

  const fetchAddresses = async () => {
    try {
      const response = await fetch('/api/cliente/addresses')
      if (response.ok) {
        const data = await response.json()
        setAddresses(data.addresses || [])
      }
    } catch (error) {
      console.error('Erro ao carregar endereços:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      const url = editingAddress 
        ? `/api/cliente/addresses/${editingAddress.id}`
        : '/api/cliente/addresses'
      
      const method = editingAddress ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        await fetchAddresses()
        resetForm()
        alert(editingAddress ? 'Endereço atualizado!' : Endereço adicionado!)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar endereço')
      }
    } catch (error) {
      console.error('Erro ao salvar endereço:', 'error')
      alert('Erro ao salvar endereço')
    }
  }

  const handleEdit = (address: Address) => {
    setEditingAddress(address)
    setFormData({
      label: address.label,
      street: address.street,
      city: address.city,
      postalCode: address.postalCode,
      country: address.country,
      isDefault: address.isDefault,
      type: address.type
    })
    setShowAddForm(true)
  }

  const handleDelete = async (addressId: string) => {
    if (confirm('Tem certeza que deseja eliminar este endereço?')) {
      try {
        const response = await fetch(`/api/cliente/addresses/${addressId}`, {
          method: DELETE})

        if (response.ok) {
          await fetchAddresses()
          alert('Endereço eliminado!')
        } else {
          alert('Erro ao eliminar endereço')
        }
      } catch (error) {
        console.error('Erro ao eliminar endereço:', 'error')
        alert('Erro ao eliminar endereço')
      }
    }
  }

  const resetForm = () => {
    setFormData({
      label: '',
      street: '',
      city: '',
      postalCode: '',
      country: Portugal,
      isDefault: false,
      type: HOME})
    setEditingAddress(null)
    setShowAddForm(false)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'HOME': return <Home className="w-4 h-4" />
      case 'WORK': return <Building className="w-4 h-4" />
      default: return <MapPin className="w-4 h-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'HOME': return 'Casa'
      case 'WORK': return 'Trabalho'
      default: return 'Outro'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Meus Endereços</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href="/cliente" className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Voltar ao Dashboard
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Meus Endereços</h1>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />Adicionar Endereço</button>
          </div>

          {/* Lista de Endereços */}
          {addresses.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum endereço cadastrado</h3>
              <p className="text-gray-500 mb-4">Adicione um endereço para facilitar suas reparações</p>
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />Adicionar Primeiro Endereço</button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {addresses.map((address) => (
                <div key={address.id} className="border border-gray-300 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(address.type)}
                      <h3 className="font-medium text-gray-900">{address.label}</h3>
                      {address.isDefault && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Padrão</span>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(address)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(address.id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <p>{address.street}</p>
                    <p>{address.postalCode} {address.city}</p>
                    <p>{address.country}</p>
                  </div>
                  
                  <div className="mt-2 text-xs text-gray-500">
                    {getTypeLabel(address.type)}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Formulário de Adicionar/Editar */}
          {showAddForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h2 className="text-lg font-bold text-gray-900 mb-4">
                  {editingAddress ? 'Editar Endereço' : 'Adicionar Endereço'}
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nome do Endereço *</label>
                    <input
                      type="text"
                      value={formData.label}
                      onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="Ex: Casa, Trabalho"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tipo
                    </label>
                    <select
                      value={formData.type}
                      onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any}))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    >
                      <option value="HOME">Casa</option>
                      <option value="WORK">Trabalho</option>
                      <option value="OTHER">Outro</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Morada *</label>
                    <input
                      type="text"
                      value={formData.street}
                      onChange={(e) => setFormData(prev => ({ ...prev, street: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="Rua, número, andar"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Código Postal *</label>
                      <input
                        type="text"
                        value={formData.postalCode}
                        onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder="1000-001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Cidade *</label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder="Lisboa"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isDefault}
                        onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Definir como endereço padrão</span>
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSave}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingAddress ? 'Atualizar' : 'Adicionar'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
