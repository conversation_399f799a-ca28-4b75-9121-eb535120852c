'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { User, ArrowLeft, Package, Wrench, Euro, Phone, Mail, MapPin, Calendar, Eye, CheckCircle } from 'lucide-react'

interface MarketplaceOrder {
  id: string
  orderNumber: string
  status: string
  total: number
  createdAt: string
}

interface Repair {
  id: string
  repairNumber: string
  status: string
  deviceBrand: string
  deviceModel: string
  description: string
  estimatedPrice: number
  finalPrice: number
  createdAt: string
  completedDate?: string
}

interface Customer {
  id: string
  name: string
  email: string
  phone?: string
  nif?: string
  address?: string
  city?: string
  postalCode?: string
  createdAt: string
  marketplaceOrders: MarketplaceOrder[]
  repairs: Repair[]
  stats: {
    totalOrders: number
    totalRepairs: number
    totalSpent: number
    completedRepairs: number
  }
}

export default function CustomerDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchCustomerDetails()
    }
  }, [params.id])

  const fetchCustomerDetails = async () => {
    try {
      const response = await fetch(`/api/lojista/customers/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setCustomer(data.customer)
      } else if (response.status === 404) {
        router.push('/lojista/clientes')
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes do cliente:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800'
      case 'SHIPPED':
        return 'bg-purple-100 text-purple-800'
      case 'DELIVERED':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Cliente não encontrado</h2>
          <Link
            href="/lojista/clientes"
            className="text-black hover:underline"
          >
            Voltar aos clientes
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/lojista/clientes"
                className="flex items-center text-gray-600 hover:text-black transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-bold text-black">Revify</span>
              </Link>
              <span className="text-gray-400">|</span>
              <h1 className="text-xl font-semibold text-gray-900">
                {customer.name}
              </h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Customer Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-gray-600" />
              </div>
              <div className="ml-4">
                <h2 className="text-2xl font-semibold text-gray-900">{customer.name}</h2>
                <p className="text-gray-600">Cliente desde {new Date(customer.createdAt).toLocaleDateString('pt-PT')}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center">
              <Mail className="w-5 h-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="font-medium">{customer.email}</p>
              </div>
            </div>

            {customer.phone && (
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Telefone</p>
                  <p className="font-medium">{customer.phone}</p>
                </div>
              </div>
            )}

            {customer.nif && (
              <div className="flex items-center">
                <div className="w-5 h-5 text-gray-400 mr-3 flex items-center justify-center">
                  #
                </div>
                <div>
                  <p className="text-sm text-gray-600">NIF</p>
                  <p className="font-medium">{customer.nif}</p>
                </div>
              </div>
            )}

            {(customer.address || customer.city) && (
              <div className="flex items-center">
                <MapPin className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm text-gray-600">Morada</p>
                  <p className="font-medium">
                    {customer.address && <span>{customer.address}<br /></span>}
                    {customer.postalCode} {customer.city}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Encomendas</p>
                <p className="text-2xl font-bold text-gray-900">{customer.stats.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Wrench className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reparações</p>
                <p className="text-2xl font-bold text-gray-900">{customer.stats.totalRepairs}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Euro className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Gasto</p>
                <p className="text-2xl font-bold text-gray-900">€{customer.stats.totalSpent.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reparações Concluídas</p>
                <p className="text-2xl font-bold text-gray-900">{customer.stats.completedRepairs}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Orders and Repairs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Marketplace Orders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="w-5 h-5 mr-2" />
              Encomendas do Marketplace ({customer.marketplaceOrders.length})
            </h3>
            
            {customer.marketplaceOrders.length === 0 ? (
              <p className="text-gray-500 text-center py-4">Nenhuma encomenda encontrada</p>
            ) : (
              <div className="space-y-3">
                {customer.marketplaceOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">#{order.orderNumber}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(order.createdAt).toLocaleDateString('pt-PT')}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                      <p className="text-sm font-medium text-gray-900 mt-1">€{order.total.toFixed(2)}</p>
                    </div>
                  </div>
                ))}
                {customer.marketplaceOrders.length > 5 && (
                  <p className="text-sm text-gray-500 text-center">
                    E mais {customer.marketplaceOrders.length - 5} encomendas...
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Repairs */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Wrench className="w-5 h-5 mr-2" />
              Reparações ({customer.repairs.length})
            </h3>
            
            {customer.repairs.length === 0 ? (
              <p className="text-gray-500 text-center py-4">Nenhuma reparação encontrada</p>
            ) : (
              <div className="space-y-3">
                {customer.repairs.slice(0, 5).map((repair) => (
                  <div key={repair.id} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-gray-900">#{repair.repairNumber}</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(repair.status)}`}>
                        {repair.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{repair.deviceBrand} {repair.deviceModel}</p>
                    <div className="flex justify-between items-center mt-2">
                      <p className="text-xs text-gray-500">
                        {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                      </p>
                      <p className="text-sm font-medium text-gray-900">
                        €{(repair.finalPrice || repair.estimatedPrice).toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))}
                {customer.repairs.length > 5 && (
                  <p className="text-sm text-gray-500 text-center">
                    E mais {customer.repairs.length - 5} reparações...
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
