import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id } = await params

    // Buscar app específica
    const app = await prisma.$queryRaw`
      SELECT 
        ad.*,
        COUNT(ia.id) as install_count,
        COUNT(CASE WHEN ia."isPaid" = true THEN 1 END) as paid_installs,
        COUNT(CASE WHEN ia."isTrialActive" = true THEN 1 END) as trial_installs
      FROM app_definitions ad
      LEFT JOIN installed_apps ia ON ad."appId" = ia."appId" AND ia."isActive" = true
      WHERE ad.id = ${id}
      GROUP BY ad.id
      LIMIT 1
    ` as any[]

    if (app.length === 0) {
      return NextResponse.json(
        { message: 'App não encontrada' },
        { status: 404 }
      )
    }

    const appData = app[0]

    return NextResponse.json({
      app: {
        id: appData.id,
        appId: appData.appId,
        name: appData.name,
        description: appData.description,
        category: appData.category,
        isPaid: appData.isPaid,
        monthlyPrice: Number(appData.monthlyPrice || 0),
        hasTrialPeriod: appData.hasTrialPeriod,
        trialDays: appData.trialDays,
        features: appData.features,
        requiredPlans: appData.requiredPlans,
        isActive: appData.isActive,
        isPopular: appData.isPopular,
        stats: {
          totalInstalls: Number(appData.install_count || 0),
          paidInstalls: Number(appData.paid_installs || 0),
          trialInstalls: Number(appData.trial_installs || 0)
        },
        createdAt: appData.createdAt,
        updatedAt: appData.updatedAt
      }
    })

  } catch (error) {
    console.error('Erro ao buscar app:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id } = await params
    const {
      name,
      description,
      category,
      isPaid,
      monthlyPrice,
      hasTrialPeriod,
      trialDays,
      features,
      requiredPlans,
      isActive,
      isPopular
    } = await request.json()

    // Atualizar app
    const updatedApp = await prisma.$queryRaw`
      UPDATE app_definitions
      SET 
        name = ${name},
        description = ${description || ''},
        category = ${category},
        "isPaid" = ${isPaid || false},
        "monthlyPrice" = ${monthlyPrice || 0},
        "hasTrialPeriod" = ${hasTrialPeriod || false},
        "trialDays" = ${trialDays || 30},
        features = ${JSON.stringify(features || [])},
        "requiredPlans" = ${JSON.stringify(requiredPlans || [])},
        "isActive" = ${isActive !== false},
        "isPopular" = ${isPopular || false},
        "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    ` as any[]

    if (updatedApp.length === 0) {
      return NextResponse.json(
        { message: 'App não encontrada' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'App atualizada com sucesso',
      app: updatedApp[0]
    })

  } catch (error) {
    console.error('Erro ao atualizar app:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id } = await params

    // Verificar se há instalações ativas
    const activeInstalls = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM installed_apps ia
      JOIN app_definitions ad ON ia."appId" = ad."appId"
      WHERE ad.id = ${id} AND ia."isActive" = true
    ` as any[]

    const installCount = Number(activeInstalls[0]?.count || 0)

    if (installCount > 0) {
      return NextResponse.json(
        { 
          message: `Não é possível eliminar app com ${installCount} instalações ativas. Desative a app primeiro.` 
        },
        { status: 400 }
      )
    }

    // Eliminar app
    await prisma.$queryRaw`
      DELETE FROM app_definitions
      WHERE id = ${id}
    `

    return NextResponse.json({
      message: 'App eliminada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao eliminar app:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
