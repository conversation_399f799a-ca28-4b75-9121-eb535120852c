'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Award, Star, Zap, Crown, Shield, Target, Trophy, Medal } from 'lucide-react'
interface Badge {
  id: string
  name: string
  description: string
  icon: string
  category: string
  isEarned: boolean
  earnedAt?: string
  progress?: number
  maxProgress?: number}

const BADGE_ICONS: { [key: string]: React.ReactNode } = {
  star: <Star className="w-6 h-6" />,
  crown: <Crown className="w-6 h-6" />,
  shield: <Shield className="w-6 h-6" />,
  target: <Target className="w-6 h-6" />,
  trophy: <Trophy className="w-6 h-6" />,
  medal: <Medal className="w-6 h-6" />,
  zap: <Zap className="w-6 h-6" />,
  award: <Award className="w-6 h-6" />
}

export default function BadgeSystem() {
  const { data: session } = useSession()
  const [badges, setBadges] = useState<Badge[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  useEffect(() => {
    if (session?.user?.id) {
      fetchBadges()
    }
  }, [session])

  const fetchBadges = async () => {
    try {
      const response = await fetch('/api/viral/badges')
      if (response.ok) {
        const data = await response.json()
        // Combinar badges conquistados e em progresso
        const allBadges = [
          ...data.earnedBadges.map((badge: any) => ({
            ...badge,
            isEarned: true})),
          ...data.inProgressBadges.map((badge: any) => ({
            ...badge,
            isEarned: false}))
        ]
        setBadges(allBadges)
      }
    } catch (error) {
      console.error('Erro ao carregar badges:', error)
      // Mock data para demonstração
      setBadges([
        {
          id: '1',
          name: 'Especialista Samsung',
          description: 'Reparou 50+ dispositivos Samsung',
          icon: star,
          category: expertise,
          isEarned: true,
          earnedAt: '2024-01-15'
        
},
        {
          id: '2',
          name: '5 Estrelas',
          description: 'Mantém rating de 5 estrelas',
          icon: crown,
          category: performance,
          isEarned: true,
          earnedAt: '2024-01-20'
        },
        {
          id: '3',
          name: 'Reparador Rápido',
          description: 'Completa reparações em menos de 2h',
          icon: zap,
          category: performance,
          isEarned: false,
          progress: 15,
          maxProgress: 25
        },
        {
          id: '4',
          name: 'Milestone 100',
          description: 'Completou 100 reparações',
          icon: trophy,
          category: milestone,
          isEarned: false,
          progress: 67,
          maxProgress: 100
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    { id: all, name: Todos, count: badges.length },
    { id: expertise, name: 'Especialização', count: badges.filter(b => b.category === 'expertise').length },
    { id: performance, name: Performance, count: badges.filter(b => b.category === 'performance').length },
    { id: milestone, name: Marcos, count: badges.filter(b => b.category === 'milestone').length },
    { id: special, name: Especiais, count: badges.filter(b => b.category === 'special').length }
  ]

  const filteredBadges = selectedCategory === 'all' 
    ? badges 
    : badges.filter(badge => badge.category === 'selectedCategory')

  const earnedBadges = badges.filter(badge => badge.isEarned)

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-gray-200 rounded-lg h-32"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
            <Award className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Badges Revify
            </h3>
            <p className="text-sm text-gray-600">
              Conquista badges por promover a Revify
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-yellow-600">
            {earnedBadges.length}
          </div>
          <div className="text-xs text-gray-500">
            Badges conquistados
          </div>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Progresso geral
          </span>
          <span className="text-sm text-gray-600">
            {earnedBadges.length}/{badges.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${badges.length > 0 ? (earnedBadges.length / badges.length) * 100 : 0}%` }}
          ></div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category.id
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.name} ({category.count})
          </button>
        ))}
      </div>

      {/* Badges Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {filteredBadges.map((badge) => (
          <div
            key={badge.id}
            className={`relative p-4 rounded-lg border-2 transition-all duration-300 ${
              badge.isEarned
                ? 'border-yellow-300 bg-gradient-to-br from-yellow-50 to-orange-50 shadow-md'
                : 'border-gray-200 bg-gray-50 opacity-60'
            }`}
          >
            {/* Badge Icon */}
            <div className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${
              badge.isEarned
                ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white'
                : 'bg-gray-300 text-gray-500'
            }`}>
              {BADGE_ICONS[badge.icon] || <Award className="w-6 h-6" />}
            </div>

            {/* Badge Info */}
            <div className="text-center">
              <h4 className={`font-semibold text-sm mb-1 ${
                badge.isEarned ? 'text-gray-900' : 'text-gray-500'
              }`}>
                {badge.name}
              </h4>
              <p className={`text-xs leading-tight ${
                badge.isEarned ? 'text-gray-600' : 'text-gray-400'
              }`}>
                {badge.description}
              </p>
            </div>

            {/* Progress Bar (for 'badges in progress') */}
            {!badge.isEarned && badge.progress !== undefined && badge.maxProgress && (
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div 
                    className="bg-indigo-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${(badge.progress / badge.maxProgress) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 text-center mt-1">
                  {badge.progress}/{badge.maxProgress}
                </div>
              </div>
            )}

            {/* Earned Date */}
            {badge.isEarned && badge.earnedAt && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
            )}

            {/* Lock Icon for unearned badges */}
            {!badge.isEarned && (
              <div className="absolute top-2 right-2 text-gray-400">
                <Shield className="w-4 h-4" />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredBadges.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Award className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="text-sm">
            Nenhum badge nesta categoria
          </p>
        </div>
      )}

      {/* Recent Achievements */}
      {earnedBadges.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-100">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Conquistas recentes
          </h4>
          <div className="flex flex-wrap gap-2">
            {earnedBadges.slice(0, 5).map((badge) => (
              <div
                key={badge.id}
                className="flex items-center space-x-2 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs"
              >
                {BADGE_ICONS[badge.icon] && (
                  <div className="w-4 h-4">
                    {BADGE_ICONS[badge.icon]}
                  </div>
                )}
                <span>{badge.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
