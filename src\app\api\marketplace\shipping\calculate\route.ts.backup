import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { items, country = 'PT' } = await request.json()

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { message: 'Items são obrigatórios' },
        { status: 400 }
      )
    }

    // Agrupar items por vendedor
    const itemsBySeller: { [sellerId: string]: any[] } = {}
    
    for (const item of items) {
      const product = await prisma.marketplaceProduct.findUnique({
        where: { id: item.productId },
        include: {
          seller: {
            include: {
              profile: true
            }
          }
        }
      })

      if (product) {
        if (!itemsBySeller[product.sellerId]) {
          itemsBySeller[product.sellerId] = []
        }
        itemsBySeller[product.sellerId].push({
          ...item,
          product,
          subtotal: Number(product.price) * item.quantity
        })
      }
    }

    let totalShipping = 0
    const shippingDetails = []

    // Calcular envio para cada vendedor
    for (const [sellerId, sellerItems] of Object.entries(itemsBySeller)) {
      const seller = sellerItems[0].product.seller
      const sellerProfile = seller.profile
      
      if (!sellerProfile?.shippingEnabled) {
        // Se o vendedor não oferece envio, considerar como retirada na loja
        shippingDetails.push({
          sellerId,
          sellerName: seller.name,
          shippingCost: 0,
          method: 'PICKUP',
          note: 'Retirada na loja'
        })
        continue
      }

      // Calcular subtotal dos items deste vendedor
      const sellerSubtotal = sellerItems.reduce((sum, item) => sum + item.subtotal, 0)

      // Verificar se qualifica para portes grátis
      const freeShippingThreshold = sellerProfile.freeShippingThreshold ? Number(sellerProfile.freeShippingThreshold) : null
      const freeShippingCountries = sellerProfile.freeShippingCountries || []
      
      let shippingCost = 0
      let method = 'PAID_SHIPPING'
      let note = ''

      // Verificar portes grátis por valor
      if (freeShippingThreshold && sellerSubtotal >= freeShippingThreshold) {
        shippingCost = 0
        method = 'FREE_SHIPPING'
        note = `Portes grátis para encomendas acima de €${freeShippingThreshold}`
      }
      // Verificar portes grátis por país
      else if (freeShippingCountries.includes(country)) {
        shippingCost = 0
        method = 'FREE_SHIPPING'
        note = 'Portes grátis para este país'
      }
      // Calcular custo de envio
      else {
        const shippingRates = sellerProfile.shippingRates as any[]
        
        if (shippingRates && Array.isArray(shippingRates)) {
          const countryRate = shippingRates.find(rate => rate.country === country)
          
          if (countryRate && countryRate.type === 'FIXED') {
            shippingCost = Number(countryRate.fixedRate) || 0
            note = `Envio para ${countryRate.countryName || country}`
          } else {
            // Se não há tarifa para o país, usar tarifa padrão de Portugal
            const defaultRate = shippingRates.find(rate => rate.country === 'PT')
            if (defaultRate && defaultRate.type === 'FIXED') {
              shippingCost = Number(defaultRate.fixedRate) || 0
              note = `Envio padrão`
            }
          }
        }
      }

      totalShipping += shippingCost

      shippingDetails.push({
        sellerId,
        sellerName: seller.name,
        shippingCost,
        method,
        note,
        items: sellerItems.length,
        subtotal: sellerSubtotal
      })
    }

    return NextResponse.json({
      totalShipping,
      shippingDetails,
      country,
      currency: 'EUR'
    })

  } catch (error) {
    console.error('Erro ao calcular envio:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
