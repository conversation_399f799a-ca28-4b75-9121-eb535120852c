import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id: orderId } = await params

    // First, try to find a marketplace order
    const marketplaceOrder = await prisma.$queryRaw`
      SELECT
        o.*,
        u.name as customer_name,
        u.email as customer_email,
        p.phone as customer_phone,
        "marketplace" as order_type
      FROM orders o
      JOIN users u ON o."customerId" = u.id
      LEFT JOIN profiles p ON u.id = p."userId"
      JOIN marketplace_order_items moi ON o.id = moi."orderId"
      JOIN marketplace_products mp ON moi."productId" = mp.id
      WHERE o.id = ${orderId} AND mp."sellerId" = ${session.user.id}
      LIMIT 1
    ` as any[]

    // Get order items for marketplace order
    let orderItems = []
    if (marketplaceOrder.length > 0) {
      orderItems = await prisma.$queryRaw`
        SELECT
          moi.*,
          mp.name as product_name,
          mp.price as product_price,
          mp.images as product_images
        FROM marketplace_order_items moi
        JOIN marketplace_products mp ON moi."productId" = mp.id
        WHERE moi."orderId" = ${orderId} AND mp."sellerId" = ${session.user.id}
      ` as any[]
    }

    // If no marketplace order found, try to find a repair order
    let repairOrder = []
    if (marketplaceOrder.length === 0) {
      repairOrder = await prisma.$queryRaw`
        SELECT
          r.*,
          u.name as customer_name,
          u.email as customer_email,
          p.phone as customer_phone,
          dm.name as device_model_name,
          b.name as brand_name,
          repair as order_type
        FROM repairs r
        JOIN users u ON r."customerId" = u.id
        LEFT JOIN profiles p ON u.id = p."userId"
        LEFT JOIN device_models dm ON r."deviceModelId" = dm.id
        LEFT JOIN brands b ON dm."brandId" = b.id
        WHERE r.id = ${orderId
} AND r."repairShopId" = ${session.user.id}
        LIMIT 1
      ` as any[]
    }

    const order = marketplaceOrder.length > 0 ? marketplaceOrder[0] : repairOrder[0]

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    // Format the response
    let formattedOrder
    if (order.order_type === "marketplace") {
      formattedOrder = {
        id: order.id,
        orderNumber: order.orderNumber || `MP-${order.id.slice(-8)}`,
        status: order.status,
        total: Number(order.total || 0),
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        customer: {
          name: order.customer_name,
          email: order.customer_email,
          phone: order.customer_phone
        },
        shippingAddress: {
          name: order.shippingName,
          street: order.shippingStreet,
          city: order.shippingCity,
          postalCode: order.shippingPostalCode,
          country: order.shippingCountry
        },
        type: "marketplace",
        items: orderItems.map((item: any) => ({
          id: item.id,
          quantity: item.quantity,
          price: Number(item.price || item.product_price || 0),
          product: {
            name: item.product_name,
            images: item.product_images || []
          }
        }))
      }
    } else {
      // Repair order
      formattedOrder = {
        id: order.id,
        orderNumber: `REP-${order.id.slice(-8)}`,
        status: order.status,
        total: Number(order.finalPrice || order.estimatedPrice || 0),
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        customer: {
          name: order.customer_name,
          email: order.customer_email,
          phone: order.customer_phone
        },
        customerDetails: {
          name: order.customerName,
          phone: order.customerPhone,
          nif: order.customerNif
        },
        deliveryInfo: {
          method: order.deliveryMethod,
          pickupAddress: order.pickupAddress,
          deliveryAddress: order.deliveryAddress
        },
        type: repair,
        description: order.description,
        deviceInfo: {
          brand: order.brand_name,
          model: order.device_model_name,
          issue: order.description
        },
        items: [] // Repairs dont have items like marketplace orders'
}
    }

    return NextResponse.json({
      order: formattedOrder})

  } catch (error) {
    console.error('Erro ao buscar detalhes da encomenda:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { status } = await request.json()
    const { id: orderId } = await params

    // Try to update marketplace order first
    const marketplaceOrderExists = await prisma.$queryRaw`
      SELECT o.id
      FROM orders o
      JOIN order_items oi ON o.id = oi."orderId"
      JOIN marketplace_products mp ON oi."partId" = mp.id
      WHERE o.id = ${orderId} AND mp."sellerId" = ${session.user.id}
      LIMIT 1
    ` as any[]

    if (marketplaceOrderExists.length > 0) {
      await prisma.order.update({
        where: { id: orderId},
        data: { status
}
      })
    } else {
      // Try to update repair order
      await prisma.repair.update({
        where: { 
          id: orderId,
          repairShopId: session.user.id
        },
        data: { status
}
      })
    }

    return NextResponse.json({
      message: 'Status atualizado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar status da encomenda:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
