const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function seedBasicData() {
  try {
    console.log('🌱 Criando dados básicos...')

    // 1. Criar categorias básicas
    const categories = await prisma.category.createMany({
      data: [
        { name: 'Smartphones', description: 'Telemóveis e smartphones' },
        { name: 'Tablets', description: 'Tablets e iPads' },
        { name: 'Laptops', description: 'Computadores portáteis' }
      ],
      skipDuplicates: true
    })
    console.log('✅ Categorias criadas')

    // 2. Criar marcas básicas
    const brands = await prisma.brand.createMany({
      data: [
        { name: 'Apple' },
        { name: 'Samsung' },
        { name: '<PERSON><PERSON>' }
      ],
      skipDuplicates: true
    })
    console.log('✅ Marcas criadas')

    // 3. Buscar categorias e marcas criadas
    const categoryList = await prisma.category.findMany()
    const brandList = await prisma.brand.findMany()

    // 4. Criar modelos de dispositivos
    const deviceModels = [
      { name: 'iPhone 14 Pro', brandId: brandList.find(b => b.name === 'Apple')?.id, categoryId: categoryList.find(c => c.name === 'Smartphones')?.id },
      { name: 'Galaxy S23', brandId: brandList.find(b => b.name === 'Samsung')?.id, categoryId: categoryList.find(c => c.name === 'Smartphones')?.id },
      { name: 'Mi 13', brandId: brandList.find(b => b.name === 'Xiaomi')?.id, categoryId: categoryList.find(c => c.name === 'Smartphones')?.id },
      { name: 'iPad Air', brandId: brandList.find(b => b.name === 'Apple')?.id, categoryId: categoryList.find(c => c.name === 'Tablets')?.id },
      { name: 'MacBook Pro', brandId: brandList.find(b => b.name === 'Apple')?.id, categoryId: categoryList.find(c => c.name === 'Laptops')?.id }
    ]

    for (const model of deviceModels) {
      if (model.brandId && model.categoryId) {
        await prisma.deviceModel.create({
          data: model
        })
      }
    }
    console.log('✅ Modelos de dispositivos criados')

    // 5. Criar tipos de problemas
    const problemTypes = await prisma.problemType.createMany({
      data: [
        { name: 'Ecrã Partido' },
        { name: 'Bateria' },
        { name: 'Não Liga' },
        { name: 'Água' },
        { name: 'Botões' }
      ],
      skipDuplicates: true
    })
    console.log('✅ Tipos de problemas criados')

    // 6. Criar admin padrão
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    try {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Administrador',
          password: hashedPassword,
          role: 'ADMIN',
          isVerified: true,
          isSuperAdmin: true,
          profile: {
            create: {
              phone: '+351 912 000 000',
              address: 'Sede Revify',
              city: 'Lisboa',
              postalCode: '1000-001',
              country: 'PT'
            }
          }
        }
      })
      console.log('✅ Admin criado: <EMAIL> / admin123')
    } catch (error) {
      console.log('ℹ️  Admin já existe')
    }

    // 7. Criar plano básico com produtos recomendados
    try {
      await prisma.subscriptionPlan.create({
        data: {
          name: 'Revy Pro',
          description: 'Plano profissional com produtos recomendados',
          price: 49.99,
          monthlyPrice: 49.99,
          yearlyPrice: 499.99,
          billingCycle: 'MONTHLY',
          maxRepairs: 100,
          maxProducts: 50,
          commissionRate: 8.0,
          smsAccess: true,
          whatsappAccess: true,
          emailSupport: true,
          paymentDelayDays: 7,
          sparePartsDiscount: 10.0,
          recommendedProductsEnabled: true,
          recommendedProductsDays: 30,
          certifiedBadge: true,
          priority: 2,
          moloniIntegration: true,
          miniStore: true,
          individualRepairs: true,
          isPopular: true,
          isActive: true
        }
      })
      console.log('✅ Plano Revy Pro criado')
    } catch (error) {
      console.log('ℹ️  Plano já existe')
    }

    console.log('\n🎉 Dados básicos criados com sucesso!')
    console.log('🔑 Admin: <EMAIL> / admin123')

  } catch (error) {
    console.error('❌ Erro ao criar dados básicos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedBasicData()
