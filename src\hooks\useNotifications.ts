'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  isRead: boolean
  createdAt: string}

export function useNotifications() {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const fetchNotifications = async () => {
    if (!session?.user) return

    setIsLoading(true)
    try {
      let apiUrl = ''
      
      // Determinar a API correta baseada no role
      switch (session.user.role) {
        case 'CUSTOMER':
          apiUrl = '/api/cliente/notifications'
          break
        case 'REPAIR_SHOP':
          apiUrl = '/api/lojista/notifications'
          break
        case 'ADMIN':
          apiUrl = '/api/admin/notifications'
          break
        default:
          return
      }

      const response = await fetch(apiUrl)
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.notifications || [])
        setUnreadCount(data.unreadCount || 0)
      }
    } catch (error) {
      console.error('Erro ao buscar notificações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const markAsRead = async (notificationIds: string[]) => {
    if (!session?.user) return

    try {
      let apiUrl = ''
      
      switch (session.user.role) {
        case 'CUSTOMER':
          apiUrl = '/api/cliente/notifications'
          break
        case 'REPAIR_SHOP':
          apiUrl = '/api/lojista/notifications'
          break
        case 'ADMIN':
          apiUrl = '/api/admin/notifications'
          break
        default:
          return
      }

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          notificationIds,
          markAsRead: true})
      })

      if (response.ok) {
        // Atualizar estado local
        setNotifications(prev =>
          prev.map(notif =>
            notificationIds.includes(notif.id)
              ? { ...notif, isRead: true}
              : notif)
        )
        setUnreadCount(prev => Math.max(0, prev - notificationIds.length))
      }
    } catch (error) {
      console.error('Erro ao marcar como lida:', error)
    }
  }

  // Buscar notificações automaticamente
  useEffect(() => {
    if (session?.user) {
      fetchNotifications()
      
      // Verificar novas notificações a cada 30 segundos
      const interval = setInterval(fetchNotifications, 30000)
      
      return () => clearInterval(interval)
    }
  }, [session])

  return {
    notifications,
    unreadCount,
    isLoading,
    fetchNotifications,
    markAsRead
  }
}
