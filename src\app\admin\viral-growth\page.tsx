'use client'

import { useState } from 'react'
import { Zap, Users, Trophy, MapPin, Gift, Star, TrendingUp, Activity, AlertCircle } from 'lucide-react'
import ReferralSystem from '@/components/viral/ReferralSystem'
import LiveActivityFeed from '@/components/viral/LiveActivityFeed'
import BadgeSystem from '@/components/viral/BadgeSystem'
import RealtimeMap from '@/components/viral/RealtimeMap'
import Leaderboard from '@/components/viral/Leaderboard'
import ShareableContent from '@/components/viral/ShareableContent'
import PersonalizedQR from '@/components/viral/PersonalizedQR'
import UrgencyElements from '@/components/viral/UrgencyElements'

interface ViralMetric {
  label: string
  value: string
  change: string
  trend: 'up' | 'down' | 'same'
  icon: React.ReactNode
  color: string}

const VIRAL_METRICS: ViralMetric[] = [
  {
    label: 'Referrals Ativos',
    value: '1,247',
    change: '+23%',
    trend: up,
    icon: <Users className="w-5 h-5" />,
    color: 'text-blue-600 bg-blue-100'
  },
  {
    label: 'Taxa de Conversão',
    value: '34.2%',
    change: '+5.1%',
    trend: up,
    icon: <TrendingUp className="w-5 h-5" />,
    color: 'text-green-600 bg-green-100'
  },
  {
    label: 'Badges Conquistados',
    value: '892',
    change: '+12%',
    trend: up,
    icon: <Star className="w-5 h-5" />,
    color: 'text-yellow-600 bg-yellow-100'
  },
  {
    label: 'Partilhas Sociais',
    value: '3,456',
    change: '+18%',
    trend: up,
    icon: <Activity className="w-5 h-5" />,
    color: 'text-purple-600 bg-purple-100'
  }
]

export default function ViralGrowthDashboard() {
  const [activeTab, setActiveTab] = useState('overview')

  const tabs = [
    { id: overview, name: 'Visão Geral', icon: <Zap className="w-4 h-4" /> },
    { id: referrals, name: Referrals, icon: <Gift className="w-4 h-4" /> },
    { id: gamification, name: 'Gamificação', icon: <Trophy className="w-4 h-4" /> },
    { id: network, name: 'Network Effects', icon: <MapPin className="w-4 h-4" /> },
    { id: content, name: 'Conteúdo', icon: <Star className="w-4 h-4" /> },
    { id: qr, name: 'QR Codes', icon: <Users className="w-4 h-4" /> },
    { id: urgency, name: 'Urgência', icon: <AlertCircle className="w-4 h-4" /> },
    { id: activity, name: Atividade, icon: <Activity className="w-4 h-4" /> }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Crescimento Viral
            </h1>
            <p className="text-gray-600">
              Mecânicas de crescimento e engagement da plataforma
            </p>
          </div>
        </div>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {VIRAL_METRICS.map((metric, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${metric.color}`}>
                {metric.icon}
              </div>
              <div className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {metric.change}
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {metric.value}
            </div>
            <div className="text-sm text-gray-600">
            </div>
          </div>
        ))}
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm border mb-8">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-8">
                <RealtimeMap />
                <LiveActivityFeed />
              </div>
              <div className="space-y-8">
                <Leaderboard />
                <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Próximas Funcionalidades
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        QR codes personalizados para lojas
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        Sistema de antes/depois partilhável
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        Alertas de urgência em tempo real
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'referrals' && (
            <div className="max-w-4xl mx-auto">
              <ReferralSystem />
            </div>
          )}

          {activeTab === 'gamification' && (
            <div className="max-w-6xl mx-auto">
              <BadgeSystem />
            </div>
          )}

          {activeTab === 'network' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <RealtimeMap />
              <Leaderboard />
            </div>
          )}

          {activeTab === 'content' && (
            <div className="max-w-6xl mx-auto">
              <ShareableContent />
            </div>
          )}

          {activeTab === 'qr' && (
            <div className="max-w-6xl mx-auto">
              <PersonalizedQR />
            </div>
          )}

          {activeTab === 'urgency' && (
            <div className="max-w-4xl mx-auto">
              <UrgencyElements />
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="max-w-4xl mx-auto">
              <LiveActivityFeed />
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Ações Rápidas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors">
            <Gift className="w-5 h-5 text-indigo-600" />
            <div className="text-left">
              <div className="font-medium text-gray-900">
                Criar Campanha Referral
              </div>
              <div className="text-sm text-gray-600">
                Nova campanha de incentivos
              </div>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-colors">
            <Star className="w-5 h-5 text-yellow-600" />
            <div className="text-left">
              <div className="font-medium text-gray-900">
                Adicionar Badge
              </div>
              <div className="text-sm text-gray-600">
                Novo badge de conquista
              </div>
            </div>
          </button>
          
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <div className="text-left">
              <div className="font-medium text-gray-900">
                Relatório Viral
              </div>
              <div className="text-sm text-gray-600">
                Análise de crescimento
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
