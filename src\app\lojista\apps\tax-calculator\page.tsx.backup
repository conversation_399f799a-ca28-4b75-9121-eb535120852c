'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Calculator, FileText, Download, Save, Plus, Trash2 } from 'lucide-react'
import Link from 'next/link'

interface TaxCalculation {
  id: string
  name: string
  baseAmount: number
  vatRate: number
  vatAmount: number
  totalAmount: number
  createdAt: string
}

interface TaxConfig {
  defaultVatRate: number
  companyNif: string
  companyName: string
  companyAddress: string
}

export default function TaxCalculatorPage() {
  const { data: session } = useSession()
  const [calculations, setCalculations] = useState<TaxCalculation[]>([])
  const [config, setConfig] = useState<TaxConfig>({
    defaultVatRate: 23,
    companyNif: '',
    companyName: '',
    companyAddress: ''
  })
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'calculator' | 'history' | 'config'>('calculator')
  
  // Calculator form
  const [calculatorForm, setCalculatorForm] = useState({
    name: '',
    baseAmount: '',
    vatRate: '23'
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [calculationsRes, configRes] = await Promise.all([
        fetch('/api/lojista/apps/tax-calculator/calculations'),
        fetch('/api/lojista/apps/tax-calculator/config')
      ])

      if (calculationsRes.ok) {
        const data = await calculationsRes.json()
        setCalculations(data.calculations)
      }

      if (configRes.ok) {
        const data = await configRes.json()
        setConfig(data.config)
        setCalculatorForm(prev => ({ ...prev, vatRate: data.config.defaultVatRate.toString() }))
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateTax = () => {
    const baseAmount = parseFloat(calculatorForm.baseAmount)
    const vatRate = parseFloat(calculatorForm.vatRate)
    
    if (isNaN(baseAmount) || isNaN(vatRate)) return null
    
    const vatAmount = (baseAmount * vatRate) / 100
    const totalAmount = baseAmount + vatAmount
    
    return {
      baseAmount,
      vatRate,
      vatAmount,
      totalAmount
    }
  }

  const saveCalculation = async () => {
    const calculation = calculateTax()
    if (!calculation || !calculatorForm.name.trim()) {
      alert('Preencha todos os campos corretamente')
      return
    }

    try {
      const response = await fetch('/api/lojista/apps/tax-calculator/calculations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: calculatorForm.name,
          ...calculation
        })
      })

      if (response.ok) {
        alert('Cálculo salvo com sucesso!')
        setCalculatorForm({ name: '', baseAmount: '', vatRate: config.defaultVatRate.toString() })
        fetchData()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar cálculo')
      }
    } catch (error) {
      console.error('Erro ao salvar cálculo:', error)
      alert('Erro ao salvar cálculo')
    }
  }

  const deleteCalculation = async (id: string) => {
    if (!confirm('Tem certeza que deseja excluir este cálculo?')) return

    try {
      const response = await fetch(`/api/lojista/apps/tax-calculator/calculations/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setCalculations(prev => prev.filter(calc => calc.id !== id))
        alert('Cálculo excluído com sucesso!')
      } else {
        alert('Erro ao excluir cálculo')
      }
    } catch (error) {
      console.error('Erro ao excluir cálculo:', error)
      alert('Erro ao excluir cálculo')
    }
  }

  const saveConfig = async () => {
    try {
      const response = await fetch('/api/lojista/apps/tax-calculator/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        alert('Configuração salva com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar configuração')
      }
    } catch (error) {
      console.error('Erro ao salvar configuração:', error)
      alert('Erro ao salvar configuração')
    }
  }

  const exportToPDF = async (calculation: TaxCalculation) => {
    try {
      const response = await fetch('/api/lojista/apps/tax-calculator/export-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ calculationId: calculation.id })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `calculo-impostos-${calculation.name}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Erro ao exportar PDF')
      }
    } catch (error) {
      console.error('Erro ao exportar PDF:', error)
      alert('Erro ao exportar PDF')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  const currentCalculation = calculateTax()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/appstore"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <div className="flex items-center">
                <Calculator className="w-6 h-6 mr-2 text-green-600" />
                <h1 className="text-2xl font-bold text-black">Calculadora de Impostos</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('calculator')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'calculator'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Calculator className="w-4 h-4 inline mr-2" />
                Calculadora
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FileText className="w-4 h-4 inline mr-2" />
                Histórico ({calculations.length})
              </button>
              <button
                onClick={() => setActiveTab('config')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'config'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Configurações
              </button>
            </nav>
          </div>
        </div>

        {/* Calculator Tab */}
        {activeTab === 'calculator' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Form */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Calcular Impostos</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome do Cálculo
                  </label>
                  <input
                    type="text"
                    value={calculatorForm.name}
                    onChange={(e) => setCalculatorForm({...calculatorForm, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="Ex: Venda iPhone 12"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valor Base (€)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={calculatorForm.baseAmount}
                    onChange={(e) => setCalculatorForm({...calculatorForm, baseAmount: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Taxa de IVA (%)
                  </label>
                  <select
                    value={calculatorForm.vatRate}
                    onChange={(e) => setCalculatorForm({...calculatorForm, vatRate: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  >
                    <option value="0">Isento (0%)</option>
                    <option value="6">Taxa Reduzida (6%)</option>
                    <option value="13">Taxa Intermédia (13%)</option>
                    <option value="23">Taxa Normal (23%)</option>
                  </select>
                </div>

                <button
                  onClick={saveCalculation}
                  disabled={!calculatorForm.name.trim() || !calculatorForm.baseAmount}
                  className="w-full flex items-center justify-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Salvar Cálculo
                </button>
              </div>
            </div>

            {/* Results */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Resultado</h2>
              
              {currentCalculation ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-600">Valor Base:</span>
                    <span className="font-medium">€{currentCalculation.baseAmount.toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-600">IVA ({currentCalculation.vatRate}%):</span>
                    <span className="font-medium">€{currentCalculation.vatAmount.toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-3 bg-gray-50 px-4 rounded-lg">
                    <span className="text-lg font-semibold text-gray-900">Total:</span>
                    <span className="text-xl font-bold text-green-600">€{currentCalculation.totalAmount.toFixed(2)}</span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calculator className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p>Preencha os campos para ver o resultado</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Histórico de Cálculos</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor Base
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IVA
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {calculations.map((calculation) => (
                    <tr key={calculation.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {calculation.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        €{calculation.baseAmount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        €{calculation.vatAmount.toFixed(2)} ({calculation.vatRate}%)
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        €{calculation.totalAmount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(calculation.createdAt).toLocaleDateString('pt-PT')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => exportToPDF(calculation)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteCalculation(calculation.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {calculations.length === 0 && (
                <div className="text-center py-12">
                  <Calculator className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nenhum cálculo salvo
                  </h3>
                  <p className="text-gray-500">
                    Use a calculadora para criar e salvar cálculos de impostos.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Config Tab */}
        {activeTab === 'config' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configurações</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Taxa de IVA Padrão (%)
                </label>
                <select
                  value={config.defaultVatRate}
                  onChange={(e) => setConfig({...config, defaultVatRate: parseFloat(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                >
                  <option value={0}>Isento (0%)</option>
                  <option value={6}>Taxa Reduzida (6%)</option>
                  <option value={13}>Taxa Intermédia (13%)</option>
                  <option value={23}>Taxa Normal (23%)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  NIF da Empresa
                </label>
                <input
                  type="text"
                  value={config.companyNif}
                  onChange={(e) => setConfig({...config, companyNif: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="*********"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome da Empresa
                </label>
                <input
                  type="text"
                  value={config.companyName}
                  onChange={(e) => setConfig({...config, companyName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Minha Empresa Lda"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Morada da Empresa
                </label>
                <input
                  type="text"
                  value={config.companyAddress}
                  onChange={(e) => setConfig({...config, companyAddress: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Rua da Empresa, 123, 1000-000 Lisboa"
                />
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={saveConfig}
                className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
              >
                Salvar Configurações
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
