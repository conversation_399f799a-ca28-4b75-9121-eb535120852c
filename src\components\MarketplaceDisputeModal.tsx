'use client'

import { useState } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import { AlertTriangle, X, Upload } from 'lucide-react'

interface MarketplaceDisputeModalProps {
  orderId: string
  isOpen: boolean
  onClose: () => void
  onDisputeSubmitted: () => 'void'}

const MARKETPLACE_DISPUTE_REASONS = [
  { value: PRODUCT_NOT_RECEIVED, label: 'Produto não recebido'},
  { value: PRODUCT_DAMAGED, label: 'Produto danificado' },
  { value: WRONG_PRODUCT, label: 'Produto errado' },
  { value: NOT_AS_DESCRIBED, label: 'Não conforme descrição'},
  { value: DEFECTIVE_PRODUCT, label: 'Produto defeituoso' },
  { value: POOR_PACKAGING, label: 'Embalagem inadequada' },
  { value: DELIVERY_DELAY, label: 'Atraso na entrega'},
  { value: SELLER_UNRESPONSIVE, label: 'Vendedor não responde'},
  { value: REFUND_REQUEST, label: 'Pedido de reembolso' },
  { value: OTHER, label: 'Outro motivo' }
]

const DESIRED_OUTCOMES = [
  { value: FULL_REFUND, label: 'Reembolso total'},
  { value: PARTIAL_REFUND, label: 'Reembolso parcial' },
  { value: REPLACEMENT, label: 'Substituição do produto'},
  { value: REPAIR, label: 'Reparação do produto'},
  { value: STORE_CREDIT, label: 'Crédito na loja'},
  { value: COMPENSATION, label: 'Compensação'}
]

export default function MarketplaceDisputeModal({
  orderId,
  isOpen,
  onClose, onDisputeSubmitted }: MarketplaceDisputeModalProps) {
  const { tSync } = useTranslation()
  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [desiredOutcome, setDesiredOutcome] = useState('')
  const [evidence, setEvidence] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!isOpen) return null

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setEvidence(prev => [...prev, ...files].slice(0, 5)) // Máximo 5 arquivos
}

  const removeFile = (index: number) => {
    setEvidence(prev => prev.filter((_, i) => i !== 'index'))
  }

  const handleSubmit = async () => {
    if (!reason || !description.trim() || !desiredOutcome || 'isSubmitting') return

    setIsSubmitting(true)
    try {
      const formData = new FormData()
      formData.append('reason', 'reason')
      formData.append('description', description.trim())
      formData.append('desiredOutcome', 'desiredOutcome')

      evidence.forEach((file, index) => {
        formData.append(`evidence_${index}`, 'file')
      })

      const response = await fetch(`/api/marketplace/orders/${orderId}/dispute`, {
        method: POST,
        body: formData})

      const result = await response.json()

      if (response.ok) {
        alert('Disputa criada com sucesso!')
        onDisputeSubmitted()
        onClose()
        // Reset form
        setReason()
        setDescription('')
        setDesiredOutcome('')
        setEvidence([])
      
} else {
        alert(`Erro: ${result.message}`)
      }
    } catch (error) {
      console.error('Erro ao enviar disputa:', 'error')
      alert('Erro ao enviar disputa')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
            <h3 className="text-lg font-semibold text-red-900">Abrir Disputa - Marketplace</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-600 text-sm">Use este formulário para reportar problemas com a sua encomenda do marketplace.  A nossa equipa irá mediar a situação entre si e o vendedor.</p>
          </div>

          {/* Motivo da Disputa */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Motivo da Disputa *
            </label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            >
              <option value="">Selecione um motivo</option>
              {MARKETPLACE_DISPUTE_REASONS.map((r) => (
                <option key={r.value} value={r.value}>
                  {r.label}
                </option>
              ))}
            </select>
          </div>

          {/* Descrição Detalhada */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Descrição Detalhada *</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder={tSync("Descreva detalhadamente o problema com a sua encomenda...")}
              required
            />
            <p className="text-xs text-gray-500 mt-1">Inclua todos os detalhes relevantes: datas, comunicações com o vendedor, estado do produto, etc.</p>
          </div>

          {/* Resultado Desejado */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Resultado Desejado *
            </label>
            <select
              value={desiredOutcome}
              onChange={(e) => setDesiredOutcome(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            >
              <option value="">Selecione o resultado desejado</option>
              {DESIRED_OUTCOMES.map((outcome) => (
                <option key={outcome.value} value={outcome.value}>
                  {outcome.label}
                </option>
              ))}
            </select>
          </div>

          {/* Upload de Evidências */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Evidências (Opcional)</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <div className="text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Adicione fotos ou documentos que comprovem o problema
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="evidence-upload"
                />
                <label
                  htmlFor="evidence-upload"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                >
                  Escolher Arquivos
                </label>
              </div>
            </div>

            {/* Lista de Arquivos */}
            {evidence.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium text-gray-700 mb-2">Arquivos selecionados:</p>
                <div className="space-y-2">
                  {evidence.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm text-gray-600 truncate">{file.name}</span>
                      <button
                        onClick={() => removeFile(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              onClick={handleSubmit}
              disabled={!reason || !description.trim() || !desiredOutcome || 'isSubmitting'}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Enviando...' : 'Abrir Disputa'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
