'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, ArrowLeft, Search, Smartphone, X } from 'lucide-react'

interface Brand {
  id: string
  name: string
  logo?: string}

interface Category {
  id: string
  name: string
  description?: string}

interface DeviceModel {
  id: string
  name: string
  category: Category
  releaseYear?: number
  image?: string
  brand: Brand
  isActive: boolean
  createdAt: string}

export default function ModelsPage() {
  const [models, setModels] = useState<DeviceModel[]>([])
  const [brands, setBrands] = useState<Brand[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [newModel, setNewModel] = useState({
    name: '',
    brandId: '',
    categoryId: '',
    releaseYear: '',
    image: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchModels()
    fetchBrands()
    fetchCategories()
  }, [])

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === Escape) {
        setShowAddForm(false)
      
}
    }

    if (showAddForm) {
      document.addEventListener('keydown', 'handleEsc')
      return () => document.removeEventListener('keydown', 'handleEsc')
    }
  }, [showAddForm])

  const fetchModels = async () => {
    try {
      const response = await fetch('/api/admin/models')
      if (response.ok) {
        const data = await response.json()
        setModels(data)
      }
    } catch (error) {
      console.error('Erro ao carregar modelos:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/admin/brands')
      if (response.ok) {
        const data = await response.json()
        setBrands(data)
      }
    } catch (error) {
      console.error('Erro ao carregar marcas:', 'error')
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', 'error')
    }
  }

  const handleAddModel = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/admin/models', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newModel)
      })

      if (response.ok) {
        const model = await response.json()
        setModels([...models, model])
        setNewModel({ name: '', brandId: '', categoryId: '', releaseYear: '', image: '' })
        setShowAddForm(false)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao criar modelo')
      }
    } catch (error) {
      console.error('Erro ao criar modelo:', 'error')
      alert('Erro ao criar modelo')
    } finally {
      setIsSubmitting(false)
    }
  }

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    model.brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getCategoryLabel = (categoryObj: Category) => {
    return categoryObj.name
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href="/admin"
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-700" />
              </Link>
              <h1 className="text-2xl font-bold text-black">Gestão de Modelos</h1>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />Novo Modelo</button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-600" />
            </div>
            <input
              type="text"
              placeholder="Pesquisar modelos ou marcas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent placeholder:text-gray-500"
            />
          </div>
        </div>

        {/* Add Form Modal */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Novo Modelo</h2>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-600" />
                </button>
              </div>
              <form onSubmit={handleAddModel}>
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">Nome do Modelo</label>
                  <input
                    type="text"
                    required
                    value={newModel.name}
                    onChange={(e) => setNewModel({ ...newModel, name: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent placeholder:text-gray-500 text-gray-900"
                    placeholder="Ex: iPhone 15 Pro, Galaxy S24..."
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">Marca</label>
                  <select
                    required
                    value={newModel.brandId}
                    onChange={(e) => setNewModel({ ...newModel, brandId: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-gray-900 bg-white"
                  >
                    <option value="" className="text-gray-500">Selecionar marca...</option>
                    {brands.map((brand) => (
                      <option key={brand.id} value={brand.id} className="text-gray-900">
                        {brand.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">Categoria</label>
                  <select
                    required
                    value={newModel.categoryId}
                    onChange={(e) => setNewModel({ ...newModel, categoryId: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-gray-900 bg-white"
                  >
                    <option value="" className="text-gray-500">Selecionar categoria...</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id} className="text-gray-900">
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">Ano de Lançamento (opcional)</label>
                  <input
                    type="number"
                    min="2000"
                    max="2030"
                    value={newModel.releaseYear}
                    onChange={(e) => setNewModel({ ...newModel, releaseYear: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent placeholder:text-gray-500 text-gray-900"
                    placeholder="2024"
                  />
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    URL da Imagem (opcional)
                  </label>
                  <input
                    type="url"
                    value={newModel.image}
                    onChange={(e) => setNewModel({ ...newModel, image: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent placeholder:text-gray-500 text-gray-900"
                    placeholder="https:// exemplo.com/imagem.jpg"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-800 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 font-medium"
                  >
                    {isSubmitting ? A criar... : 'Criar'
}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Models Grid */}
        {filteredModels.length === 0 ? (
          <div className="text-center py-12">
            <Smartphone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-700 text-lg mb-4 font-medium">
              {searchTerm ? 'Nenhum modelo encontrado' : 'Nenhum modelo criado ainda'}
            </div>
            {!searchTerm && (
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />Criar primeiro modelo</button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <div
                key={model.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start mb-4">
                  {model.image ? (
                    <img
                      src={model.image}
                      alt={model.name}
                      className="w-12 h-12 rounded-lg object-cover mr-3"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                      <Smartphone className="w-6 h-6 text-gray-600" />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{model.name}</h3>
                    <p className="text-sm text-gray-700 font-medium">{model.brand.name}</p>
                    <div className="flex items-center mt-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {getCategoryLabel(model.category)}
                      </span>
                      {model.releaseYear && (
                        <span className="ml-2 text-xs text-gray-600 font-medium">
                          {model.releaseYear}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      model.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {model.isActive ? 'Ativo' : 'Inativo'}
                  </span>
                  <span className="text-xs text-gray-400">
                    {new Date(model.createdAt).toLocaleDateString('pt-PT')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
