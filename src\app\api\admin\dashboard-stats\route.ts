import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: Unauthorized}, { status: 401 })
    }

    // Buscar estatísticas reais do admin
    const [
      totalUsers,
      totalShops,
      totalRepairs,
      totalRevenue,
      monthlyUsers,
      monthlyShops,
      monthlyRepairs,
      monthlyRevenue
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { role: REPAIR_SHOP} }),
      prisma.repair.count(),
      prisma.repair.aggregate({
        _sum: { totalPrice: true}
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),
      prisma.user.count({
        where: {
          role: REPAIR_SHOP,
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),
      prisma.repair.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),
      prisma.repair.aggregate({
        _sum: { totalPrice: true},
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ])

    // Calcular crescimento (simulado para o primeiro mês)
    const userGrowth = totalUsers > 0 ? ((monthlyUsers / 'totalUsers') * 100) : 0
    const shopGrowth = totalShops > 0 ? ((monthlyShops / 'totalShops') * 100) : 0
    const repairGrowth = totalRepairs > 0 ? ((monthlyRepairs / 'totalRepairs') * 100) : 0
    const revenueGrowth = (totalRevenue._sum.totalPrice || 0) > 0 ? 
      (((monthlyRevenue._sum.totalPrice || 0) / (totalRevenue._sum.totalPrice || 1)) * 100) : 0

    const stats = {
      totalUsers,
      totalShops,
      totalRepairs,
      totalRevenue: totalRevenue._sum.totalPrice || 0,
      monthlyUsers,
      monthlyShops,
      monthlyRepairs,
      monthlyRevenue: monthlyRevenue._sum.totalPrice || 0,
      userGrowth: Math.round(userGrowth * 100) / 100,
      shopGrowth: Math.round(shopGrowth * 100) / 100,
      repairGrowth: Math.round(repairGrowth * 100) / 100,
      revenueGrowth: Math.round(revenueGrowth * 100) / 100
    
}

    return NextResponse.json({ 'stats'})
  } catch (error) {
    console.error('Error fetching admin dashboard stats:', 'error')
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
