#!/usr/bin/env node

/**
 * Script para tradução completa de toda a plataforma
 * Identifica e converte TODOS os textos hardcoded em português
 */

const fs = require('fs');
const path = require('path');

// Diretórios para processar
const dirsToProcess = [
  'src/app',
  'src/components', 
  'src/pages'
];

// Extensões de arquivo para processar
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

// Padrões para identificar textos em português
const portuguesePatterns = [
  // Acentos portugueses
  /[àáâãçéêíóôõú]/i,
  // Palavras tipicamente portuguesas
  /\b(reparação|reparações|dispositivo|dispositivos|técnico|técnicos|cliente|clientes|lojista|estafeta|configurações|encomenda|encomendas|carrinho|pagamento|entrega|garantia|orçamento|diagnóstico|gratuito|disponível|indisponível|ativo|inativo|pendente|confirmado|concluído|cancelado|eliminar|adicionar|remover|editar|salvar|guardar|pesquisar|filtrar|ordenar|contacto|contactos|sobre|ajuda|início|sair|entrar|registar|perfil|administração|marketplace|serviços|produtos|preço|quantidade|total|subtotal|descrição|categoria|marca|modelo|estado|data|telefone|morada|cidade|código|postal|país|nome|email)\b/i,
  // Frases comuns
  /\b(como funciona|porquê escolher|solicitar reparação|finalizar compra|central de ajuda|sobre nós|tornar-se parceiro|política de privacidade|termos de serviço|todos os direitos reservados|feito com|em portugal|versão)\b/i
];

function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Ignorar node_modules, .next, etc.
      if (!file.startsWith('.') && file !== 'node_modules') {
        getAllFiles(filePath, fileList);
      }
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function hasPortugueseText(text) {
  return portuguesePatterns.some(pattern => pattern.test(text));
}

function extractHardcodedTexts(content) {
  const texts = [];
  
  // Padrões para encontrar textos hardcoded
  const patterns = [
    // Strings entre aspas duplas
    /"([^"]{2,})"/g,
    // Strings entre aspas simples  
    /'([^']{2,})'/g,
    // Texto entre tags JSX
    />([^<]{3,})</g,
    // Placeholders
    /placeholder\s*=\s*["']([^"']+)["']/g,
    // Títulos e labels
    /title\s*=\s*["']([^"']+)["']/g,
    /alt\s*=\s*["']([^"']+)["']/g
  ];
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1].trim();
      
      // Filtrar textos que são claramente português
      if (text.length > 2 && 
          hasPortugueseText(text) && 
          !text.includes('{') && 
          !text.includes('\\') &&
          !text.startsWith('http') &&
          !text.includes('px') &&
          !text.includes('rem') &&
          !text.includes('#')) {
        
        texts.push({
          text,
          match: match[0],
          index: match.index
        });
      }
    }
  });
  
  return texts;
}

function addAutoTranslateImport(content) {
  if (content.includes("import AutoTranslate from '@/components/ui/AutoTranslate'") ||
      content.includes("import { T } from '@/components/ui/T'")) {
    return content;
  }
  
  const lines = content.split('\n');
  let importIndex = -1;
  
  // Encontrar onde inserir o import
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes("import") && 
        (lines[i].includes("'react'") || 
         lines[i].includes("'@/") || 
         lines[i].includes("'next/"))) {
      importIndex = i;
    }
  }
  
  if (importIndex !== -1) {
    lines.splice(importIndex + 1, 0, "import AutoTranslate from '@/components/ui/AutoTranslate'");
    return lines.join('\n');
  }
  
  return content;
}

function convertToAutoTranslate(content, hardcodedTexts) {
  let updatedContent = content;
  
  // Ordenar por índice decrescente para não afetar posições
  hardcodedTexts.sort((a, b) => b.index - a.index);
  
  hardcodedTexts.forEach(item => {
    const { text, match } = item;
    
    // Diferentes tipos de conversão baseado no contexto
    let replacement;
    
    if (match.includes('placeholder=')) {
      // Para placeholders, manter a estrutura
      replacement = match.replace(text, `{tSync("${text}")}`);
    } else if (match.startsWith('>') && match.endsWith('<')) {
      // Para texto entre tags JSX
      replacement = `><AutoTranslate text="${text}" /><`;
    } else if (match.startsWith('"') || match.startsWith("'")) {
      // Para strings normais, substituir por componente
      replacement = `<AutoTranslate text="${text}" />`;
    } else {
      // Caso geral
      replacement = `<AutoTranslate text="${text}" />`;
    }
    
    // Verificar se não está já dentro de AutoTranslate
    const before = updatedContent.substring(Math.max(0, item.index - 100), item.index);
    if (!before.includes('<AutoTranslate') && !before.includes('tSync(')) {
      updatedContent = updatedContent.replace(match, replacement);
    }
  });
  
  return updatedContent;
}

function processFile(filePath) {
  console.log(`🔍 Analisando: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ Arquivo não encontrado: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Extrair textos hardcoded em português
  const hardcodedTexts = extractHardcodedTexts(content);
  
  if (hardcodedTexts.length === 0) {
    console.log(`✅ ${filePath} - Nenhum texto português encontrado`);
    return;
  }
  
  console.log(`📝 ${filePath} - Encontrados ${hardcodedTexts.length} textos:`);
  hardcodedTexts.slice(0, 5).forEach((item, i) => {
    console.log(`   ${i + 1}. "${item.text}"`);
  });
  if (hardcodedTexts.length > 5) {
    console.log(`   ... e mais ${hardcodedTexts.length - 5} textos`);
  }
  
  // Adicionar import se necessário
  content = addAutoTranslateImport(content);
  
  // Converter textos
  content = convertToAutoTranslate(content, hardcodedTexts);
  
  if (content !== originalContent) {
    // Fazer backup
    fs.writeFileSync(`${filePath}.backup`, originalContent);
    
    // Salvar arquivo modificado
    fs.writeFileSync(filePath, content);
    console.log(`✅ ${filePath} - Convertido com sucesso`);
  }
}

// Função principal
function main() {
  console.log('🚀 Iniciando tradução completa da plataforma...\n');
  
  let allFiles = [];
  
  // Coletar todos os arquivos
  dirsToProcess.forEach(dir => {
    const files = getAllFiles(dir);
    allFiles = allFiles.concat(files);
  });
  
  console.log(`📁 Encontrados ${allFiles.length} arquivos para processar\n`);
  
  // Processar cada arquivo
  allFiles.forEach(processFile);
  
  console.log('\n✨ Tradução completa finalizada!');
  console.log('📋 Próximos passos:');
  console.log('1. Verificar os arquivos modificados');
  console.log('2. Testar a aplicação');
  console.log('3. Ajustar manualmente casos específicos');
  console.log('4. Remover arquivos .backup se tudo estiver OK');
}

main();
