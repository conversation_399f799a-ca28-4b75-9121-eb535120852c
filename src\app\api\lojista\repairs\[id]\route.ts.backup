import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { status, finalPrice, trackingCode, notes } = await request.json()

    // Verificar se a reparação pertence ao lojista
    const existingRepair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        repairShopId: session.user.id
      }
    })

    if (!existingRepair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Buscar reparação atual para comparar preços
    const currentRepair = await prisma.repair.findUnique({
      where: { id: params.id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!currentRepair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    const currentFinalPrice = currentRepair.finalPrice ? Number(currentRepair.finalPrice) : null
    const newFinalPrice = finalPrice ? parseFloat(finalPrice) : null
    const priceChanged = currentFinalPrice !== newFinalPrice && newFinalPrice !== null

    // Atualizar a reparação
    const updatedRepair = await prisma.repair.update({
      where: {
        id: params.id
      },
      data: {
        status: status,
        finalPrice: newFinalPrice,
        trackingCode: trackingCode || null,
        updatedAt: new Date()
      }
    })

    // Se o preço mudou, criar notificação para o cliente
    if (priceChanged) {
      await prisma.notification.create({
        data: {
          userId: currentRepair.customer.id,
          title: 'Preço Final Atualizado',
          message: `O preço final da sua reparação #${params.id.slice(-8)} foi atualizado para €${newFinalPrice?.toFixed(2)}. Por favor, aprove o novo valor.`,
          type: 'PRICE_UPDATE',
          metadata: {
            repairId: params.id,
            oldPrice: currentFinalPrice,
            newPrice: newFinalPrice
          }
        }
      })

      // Enviar email de notificação
      try {
        await fetch(`${process.env.NEXTAUTH_URL}/api/repairs/${params.id}/notify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'email',
            message: `O preço final da sua reparação foi atualizado para €${newFinalPrice?.toFixed(2)}. Acesse a plataforma para aprovar.`,
            isSystemMessage: true
          })
        })
      } catch (error) {
        console.error('Erro ao enviar email:', error)
      }
    }

    // Criar notificação para o cliente se o status mudou
    if (status !== existingRepair.status) {
      await prisma.notification.create({
        data: {
          userId: existingRepair.customerId,
          title: 'Atualização da Reparação',
          message: `O estado da sua reparação foi atualizado para: ${getStatusLabel(status)}${notes ? `. Nota: ${notes}` : ''}`,
          type: 'REPAIR_UPDATE',
          metadata: {
            repairId: params.id,
            oldStatus: existingRepair.status,
            newStatus: status,
            notes: notes
          }
        }
      })
    }

    return NextResponse.json({
      message: 'Reparação atualizada com sucesso',
      repair: updatedRepair
    })

  } catch (error) {
    console.error('Erro ao atualizar reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function getStatusLabel(status: string): string {
  const statusLabels: { [key: string]: string } = {
    CONFIRMED: 'Confirmado',
    RECEIVED: 'Recebido',
    DIAGNOSIS: 'Diagnóstico',
    WAITING_PARTS: 'Aguarda Peças',
    IN_REPAIR: 'Em Reparação',
    TESTING: 'Teste',
    COMPLETED: 'Concluído',
    DELIVERED: 'Entregue',
    CANCELLED: 'Cancelado'
  }
  return statusLabels[status] || status
}
