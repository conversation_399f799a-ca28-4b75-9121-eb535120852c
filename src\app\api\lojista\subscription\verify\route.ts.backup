import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createStripeInstance } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Não autorizado' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')
    const subscriptionId = searchParams.get('subscription_id')

    if (!sessionId && !subscriptionId) {
      return NextResponse.json(
        { message: 'Session ID ou Subscription ID é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar configurações do Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })
    
    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
    
    if (!stripeSecretKey || stripeSecretKey === 'sk_test_TL') {
      return NextResponse.json(
        { message: 'Stripe não configurado' },
        { status: 400 }
      )
    }

    const stripe = await createStripeInstance(stripeSecretKey)

    // Se temos subscriptionId, verificar diretamente na base de dados
    if (subscriptionId) {
      const subscription = await prisma.subscription.findFirst({
        where: {
          id: subscriptionId,
          userId: session.user.id
        },
        include: {
          plan: true,
          payments: {
            where: { status: 'PENDING' },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      })

      if (!subscription) {
        return NextResponse.json(
          { message: 'Subscrição não encontrada' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        subscription: {
          id: subscription.id,
          status: subscription.status,
          planName: subscription.plan.name,
          currentPeriodEnd: subscription.currentPeriodEnd,
          pendingPayment: subscription.payments[0] || null
        }
      })
    }

    // Verificar a sessão do Stripe
    const stripeSession = await stripe.checkout.sessions.retrieve(sessionId)

    if (stripeSession.payment_status === 'paid') {
      // Buscar ou criar a subscrição
      const subscription = await prisma.subscription.findFirst({
        where: { userId: session.user.id },
        include: { plan: true }
      })

      if (subscription) {
        // Atualizar status da subscrição
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: 'ACTIVE',
            stripeSubscriptionId: stripeSession.subscription as string
          }
        })

        // Atualizar pagamento
        await prisma.subscriptionPayment.updateMany({
          where: {
            subscriptionId: subscription.id,
            status: 'PENDING'
          },
          data: {
            status: 'COMPLETED',
            stripePaymentIntentId: stripeSession.payment_intent as string,
            paidAt: new Date()
          }
        })

        return NextResponse.json({
          success: true,
          planName: subscription.plan.name,
          currentPeriodEnd: subscription.currentPeriodEnd,
          status: 'active'
        })
      }
    }

    return NextResponse.json({
      success: false,
      status: stripeSession.payment_status
    })

  } catch (error) {
    console.error('Erro ao verificar sessão:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
