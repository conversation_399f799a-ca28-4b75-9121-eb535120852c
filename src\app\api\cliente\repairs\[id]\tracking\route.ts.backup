import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id

    // Buscar reparação com todos os dados necessários
    const repair = await prisma.repair.findUnique({
      where: { id: repairId },
      include: {
        customer: true,
        repairShop: {
          include: {
            profile: true
          }
        },
        deviceModel: {
          include: {
            brand: true,
            category: true
          }
        },
        problemType: true
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar se o usuário tem acesso
    if (repair.customerId !== session.user.id && repair.repairShopId !== session.user.id) {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    // Buscar histórico de status (simulado por agora)
    const statusHistory = [
      {
        status: 'CONFIRMED',
        timestamp: repair.confirmedAt?.toISOString() || repair.createdAt.toISOString(),
        description: 'Pagamento processado e reparação confirmada'
      },
      {
        status: 'RECEIVED',
        timestamp: repair.status === 'RECEIVED' ? new Date().toISOString() : null,
        description: 'Dispositivo chegou à loja de reparação'
      },
      {
        status: 'DIAGNOSIS',
        timestamp: repair.status === 'DIAGNOSIS' ? new Date().toISOString() : null,
        description: 'Técnico está a analisar o problema'
      },
      {
        status: 'IN_REPAIR',
        timestamp: repair.status === 'IN_REPAIR' ? new Date().toISOString() : null,
        description: 'Técnico está a reparar o dispositivo'
      },
      {
        status: 'TESTING',
        timestamp: repair.status === 'TESTING' ? new Date().toISOString() : null,
        description: 'A testar funcionamento após reparação'
      },
      {
        status: 'COMPLETED',
        timestamp: repair.completedDate?.toISOString() || null,
        description: 'Dispositivo reparado e pronto para entrega'
      },
      {
        status: 'DELIVERED',
        timestamp: repair.status === 'DELIVERED' ? new Date().toISOString() : null,
        description: 'Dispositivo entregue ao cliente'
      }
    ].filter(item => item.timestamp) // Só incluir status que já aconteceram

    return NextResponse.json({
      repair: {
        ...repair,
        statusHistory
      }
    })

  } catch (error) {
    console.error('Erro ao buscar tracking:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
