'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Gift, Users, Euro, Copy, Check, Share2, Trophy } from 'lucide-react'
interface ReferralData {
  referralCode: string
  totalReferrals: number
  pendingRewards: number
  completedRewards: number
  referralLink: string}

interface ReferralReward {
  lojista: number
  cliente: number
  estafeta: number}

const REFERRAL_REWARDS: ReferralReward = {
  lojista: 5, // €5 por cada novo lojista que se registe na Revify
  cliente: 10, // 10% desconto na primeira reparação
  estafeta: 25 // €25 bonus por recrutar estafeta para a Revify
}

export default function ReferralSystem() {
  const { data: session } = useSession()
  const [referralData, setReferralData] = useState<ReferralData | null>(null)
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user?.id) {
      fetchReferralData()
    }
  }, [session])

  const fetchReferralData = async () => {
    try {
      const response = await fetch('/api/viral/referrals')
      if (response.ok) {
        const data = await response.json()
        setReferralData(data)
      }
    } catch (error) {
      console.error('Erro ao carregar dados de referral:', error)
    } finally {
      setLoading(false)
    }
  }

  const copyReferralLink = async () => {
    if (referralData?.referralLink) {
      await navigator.clipboard.writeText(referralData.referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const shareReferralLink = async () => {
    if (referralData?.referralLink && navigator.share) {
      try {
        await navigator.share({
          title: 'Junta-te à Revify!',
          text: 'Descobre a melhor plataforma de reparações da Europa',
          url: referralData.referralLink
        })
      } catch (error) {
        // Fallback para copy
        copyReferralLink()
      }
    } else {
      copyReferralLink()
    }
  }

  const getRoleReward = () => {
    if (!session?.user?.role) return 0
    
    switch (session.user.role.toLowerCase()) {
      case 'lojista':
      case 'shop_owner':
        return REFERRAL_REWARDS.lojista
      case 'estafeta':
      case 'courier':
        return REFERRAL_REWARDS.estafeta
      default:
        return REFERRAL_REWARDS.cliente
    }
  }

  const getRoleRewardText = () => {
    if (!session?.user?.role) return ''
    
    switch (session.user.role.toLowerCase()) {
      case 'lojista':
      case 'shop_owner':
        return `€${REFERRAL_REWARDS.lojista} por cada novo lojista`
      case 'estafeta':
      case 'courier':
        return `€${REFERRAL_REWARDS.estafeta} por cada novo estafeta`
      default:
        return `${REFERRAL_REWARDS.cliente}% desconto na próxima reparação`
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
            <Gift className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Convida para a Revify
            </h3>
            <p className="text-sm text-gray-600">
              Ganha recompensas por cada pessoa que convidares para usar a Revify
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-indigo-600">
            €{referralData?.completedRewards || 0}
          </div>
          <div className="text-xs text-gray-500">
            Total ganho
          </div>
        </div>
      </div>

      {/* Reward Info */}
      <div className="bg-white rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Euro className="w-5 h-5 text-green-600" />
            <div>
              <div className="font-medium text-gray-900">
                Recompensa por referência
              </div>
              <div className="text-sm text-gray-600">
                {getRoleRewardText()}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-green-600">
              {referralData?.pendingRewards || 0}
            </div>
            <div className="text-xs text-gray-500">
              Pendente
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-white rounded-lg p-4 text-center">
          <Users className="w-6 h-6 text-indigo-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {referralData?.totalReferrals || 0}
          </div>
          <div className="text-sm text-gray-600">
            Amigos convidados
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 text-center">
          <Trophy className="w-6 h-6 text-yellow-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {Math.floor((referralData?.totalReferrals || 0) / 5)}
          </div>
          <div className="text-sm text-gray-600">
            Nível atual
          </div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="bg-white rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-gray-700">
            Teu link de referência
          </label>
          <div className="flex space-x-2">
            <button
              onClick={copyReferralLink}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4 text-green-600" />
                  <span className="text-green-600">
                    Copiado!
                  </span>
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4" />
                  Copiar
                </>
              )}
            </button>
            <button
              onClick={shareReferralLink}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors"
            >
              <Share2 className="w-4 h-4" />
              Partilhar
            </button>
          </div>
        </div>
        <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-600 font-mono break-all">
          {referralData?.referralLink || 'Carregando...'}
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600 mb-3">
          Partilha com amigos e família para ganharem ambos!
        </p>
        <div className="flex justify-center space-x-4 text-xs text-gray-500">
          <span>📱 WhatsApp</span>
          <span>📧 Email</span>
          <span>📲 Redes Sociais</span>
        </div>
      </div>
    </div>
  )
}
