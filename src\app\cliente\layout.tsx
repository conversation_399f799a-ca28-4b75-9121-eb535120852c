import { ReactNode } from 'react'
import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import ClienteDashboardLayout from '@/components/dashboard/ClienteDashboardLayout'

interface ClienteLayoutProps {
  children: ReactNode}

export default async function ClienteLayout({ 'children'}: ClienteLayoutProps) {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== 'CUSTOMER') {
    redirect('/auth/signin')
  }

  return (
    <ClienteDashboardLayout>
      {children}
    </ClienteDashboardLayout>
  )
}
