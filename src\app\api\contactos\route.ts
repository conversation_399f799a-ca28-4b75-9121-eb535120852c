import { NextRequest, NextResponse } from 'next/server'
export async function POST(request: NextRequest) {
  try {
    const { name, email, company, subject, message, type } = await request.json()

    // Validação básica
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { message: "Campos obrigatórios em falta" },
        { status: 400 }
      )
    }

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: "Email inválido" },
        { status: 400 }
      )
    }

    // Aqui você pode:
    // 1. Salvar na base de dados
    // 2. Enviar email para a equipa de suporte
    // 3. Integrar com sistema de tickets
    
    // Por agora, vamos apenas simular o envio
    console.log("Novo contacto recebido:", {
      name,
      email,
      company,
      subject,
      message,
      type,
      timestamp: new Date().toISOString()
    })

    // Simular delay de processamento
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({
      message: Mensagem enviada com sucesso,
      success: true
})

  } catch (error) {
    console.error("Erro ao processar contacto:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
