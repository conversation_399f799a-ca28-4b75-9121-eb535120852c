'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, User, Mail, Phone, Calendar, MapPin, Crown, Edit, Trash2 } from 'lucide-react'
import Link from 'next/link'

interface UserDetails {
  id: string
  name: string
  email: string
  role: string
  createdAt: string
  profile?: {
    phone?: string
    companyName?: string
    customerName?: string
    companyNif?: string
    description?: string
    street?: string
    city?: string
    postalCode?: string
  }
  subscription?: {
    plan: {
      name: string
    }
    status: string
    currentPeriodEnd: string
  }
  _count?: {
    repairs: number
    orders: number
  }
}

export default function UserDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<UserDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    fetchUser()
  }, [params.id])

  const fetchUser = async () => {
    try {
      const response = await fetch(`/api/admin/users/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        console.error('Erro ao buscar usuário')
      }
    } catch (error) {
      console.error('Erro ao buscar usuário:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user) return

    const confirmed = confirm(
      `Tem certeza que deseja remover o usuário "${user.name}"?\n\nEsta ação é irreversível e todos os dados serão permanentemente removidos.`
    )

    if (!confirmed) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('Usuário removido com sucesso!')
        router.push('/admin/users')
      } else {
        const error = await response.json()
        alert(`Erro ao remover usuário: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro ao remover usuário:', error)
      alert('Erro ao remover usuário')
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Usuário não encontrado</h2>
          <Link href="/admin/users" className="text-blue-600 hover:text-blue-800">
            Voltar para lista de usuários
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/admin/users"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Detalhes do Usuário</h1>
            </div>
            <div className="flex space-x-2">
              {user.role === 'REPAIR_SHOP' && (
                <Link
                  href={`/admin/users/${user.id}/subscription`}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Gerir Plano
                </Link>
              )}
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                <Edit className="w-4 h-4 mr-2" />
                Editar
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informações Básicas */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                  <div className="flex items-center">
                    <User className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-900">{user.name}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-900">{user.email}</span>
                  </div>
                </div>

                {user.profile?.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Telefone</label>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-gray-900">{user.profile.phone}</span>
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Conta</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.role === 'ADMIN' ? 'bg-red-100 text-red-800' :
                    user.role === 'REPAIR_SHOP' ? 'bg-blue-100 text-blue-800' :
                    user.role === 'COURIER' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {user.role === 'ADMIN' ? 'Administrador' :
                     user.role === 'REPAIR_SHOP' ? 'Lojista' :
                     user.role === 'COURIER' ? 'Estafeta' : 'Cliente'}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Data de Registo</label>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-900">
                      {new Date(user.createdAt).toLocaleDateString('pt-PT')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Informações específicas do lojista */}
              {user.role === 'REPAIR_SHOP' && user.profile && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Informações da Empresa</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.profile.companyName && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Empresa</label>
                        <span className="text-gray-900">{user.profile.companyName}</span>
                      </div>
                    )}

                    {user.profile.companyNif && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">NIF</label>
                        <span className="text-gray-900">{user.profile.companyNif}</span>
                      </div>
                    )}

                    {(user.profile.street || user.profile.city) && (
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Morada</label>
                        <div className="flex items-start">
                          <MapPin className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                          <div>
                            {user.profile.street && <div>{user.profile.street}</div>}
                            {user.profile.city && user.profile.postalCode && (
                              <div>{user.profile.postalCode} {user.profile.city}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {user.profile.description && (
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                        <p className="text-gray-900">{user.profile.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Subscrição */}
            {user.role === 'REPAIR_SHOP' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscrição</h3>
                
                {user.subscription ? (
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Plano</label>
                      <span className="text-gray-900 font-medium">{user.subscription.plan.name}</span>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.subscription.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                        user.subscription.status === 'SUSPENDED' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {user.subscription.status === 'ACTIVE' ? 'Ativa' :
                         user.subscription.status === 'SUSPENDED' ? 'Suspensa' : user.subscription.status}
                      </span>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Próxima Renovação</label>
                      <span className="text-gray-900">
                        {new Date(user.subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500 mb-3">Sem plano ativo</p>
                    <Link
                      href={`/admin/users/${user.id}/subscription`}
                      className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      Atribuir Plano
                    </Link>
                  </div>
                )}
              </div>
            )}

            {/* Estatísticas */}
            {user._count && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas</h3>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reparações</span>
                    <span className="font-medium text-gray-900">{user._count.repairs}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Encomendas</span>
                    <span className="font-medium text-gray-900">{user._count.orders}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Zona de Perigo */}
            <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
              <h3 className="text-lg font-semibold text-red-900 mb-4">Zona de Perigo</h3>
              <p className="text-sm text-red-600 mb-4">
                Esta ação é irreversível. Todos os dados do usuário serão permanentemente removidos.
              </p>
              <button
                onClick={handleDeleteUser}
                disabled={isDeleting}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                {isDeleting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                {isDeleting ? 'Removendo...' : 'Remover Usuário'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
