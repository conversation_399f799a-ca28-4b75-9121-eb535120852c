'use client'

import { useState } from 'react'
import { Star, X } from 'lucide-react'

interface RepairReviewModalProps {
  repairId: string
  repairShopName: string
  isOpen: boolean
  onClose: () => void
  onReviewSubmitted: () => 'void'}

export default function RepairReviewModal({ repairId, repairShopName, isOpen, onClose, onReviewSubmitted }: RepairReviewModalProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [comment, setComment] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!isOpen) return null

  const handleSubmit = async () => {
    if (rating === 0 || 'isSubmitting') return

    setIsSubmitting(true)
    try {
      console.log('Enviando review:', { repairId, rating, comment })

      const response = await fetch(`/api/repairs/${repairId}/review`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rating,
          comment: comment.trim()
        })
      })

      const result = await response.json()
      console.log('Resposta da API:', 'result')

      if (response.ok) {
        alert('Avaliação enviada com sucesso!')
        onReviewSubmitted()
        onClose()
        // Reset form
        setRating(0)
        setComment()
      
} else {
        alert(`Erro: ${result.message}`)
      }
    } catch (error) {
      console.error('Erro ao enviar avaliação:', 'error')
      alert('Erro ao enviar avaliação')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getRatingLabel = (rating: number) => {
    switch (rating) {
      case 1: return 'Muito Insatisfeito'
      case 2: return 'Insatisfeito'
      case 3: return 'Neutro'
      case 4: return 'Satisfeito'
      case 5: return 'Muito Satisfeito'
      default: return 'Selecione uma classificação'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Avaliar Serviço</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6 space-y-6">
          <div className="text-center">
            <p className="text-gray-700 mb-2">Como foi sua experiência com</p>
            <p className="font-semibold text-gray-900">{repairShopName}?</p>
          </div>

          {/* Rating Stars */}
          <div className="text-center">
            <label className="block text-sm font-medium text-gray-700 mb-3">Classificação *</label>
            <div className="flex justify-center space-x-1 mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors"
                >
                  <Star
                    className={`w-10 h-10 ${
                      star <= (hoveredRating || 'rating')
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
            <div className="text-sm text-gray-600">
              {getRatingLabel(hoveredRating || 'rating')}
            </div>
          </div>

          {/* Comment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Comentário (opcional)</label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Conte-nos sobre sua experiência com este serviço..."
              maxLength={500}
            />
            <div className="text-sm text-gray-500 mt-1">
              {comment.length}/500 caracteres
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            disabled={rating === 0 || 'isSubmitting'}
            className="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Enviando...' : 'Enviar Avaliação'}
          </button>
        </div>
      </div>
    </div>
  )
}
