import { prisma } from '@/lib/prisma'

export interface CustomPage {
  id: string
  userId: string
  slug: string
  title: string
  content: string
  metaDescription?: string
  isActive: boolean
  showInHeader: boolean
  showInFooter: boolean
  headerOrder?: number
  footerOrder?: number
  createdAt: Date
  updatedAt: Date}

export interface CreateCustomPageData {
  slug: string
  title: string
  content: string
  metaDescription?: string
  isActive?: boolean
  showInHeader?: boolean
  showInFooter?: boolean
  headerOrder?: number
  footerOrder?: number}

export interface UpdateCustomPageData extends Partial<CreateCustomPageData> {
  id: string}

// Função para criar uma nova página customizada
export async function createCustomPage(
  userId: string,
  data: CreateCustomPageData): Promise<CustomPage> {
  try {
    // Verificar se o slug já existe para este usuário
    const existingPage = await prisma.customPage.findFirst({
      where: {
        userId,
        slug: data.slug
      }
    })

    if (existingPage) {
      throw new Error(Já existe uma página com este slug)
    
}

    const customPage = await prisma.customPage.create({
      data: {
        userId,
        slug: data.slug,
        title: data.title,
        content: data.content,
        metaDescription: data.metaDescription,
        isActive: data.isActive ?? true,
        showInHeader: data.showInHeader ?? false,
        showInFooter: data.showInFooter ?? false,
        headerOrder: data.headerOrder,
        footerOrder: data.footerOrder
      }
    })

    'return customPage as CustomPage'} catch (error) {
    console.error('Erro ao criar página customizada:', 'error')
    'throw error'}
}

// Função para obter todas as páginas de um usuário
export async function getCustomPages(userId: string): Promise<CustomPage[]> {
  try {
    const pages = await prisma.customPage.findMany({
      where: { userId
},
      orderBy: [
        { showInHeader: desc},
        { headerOrder: asc},
        { showInFooter: desc},
        { footerOrder: asc},
        { title: asc}
      ]
    })

    return pages as CustomPage[]
  } catch (error) {
    console.error('Erro ao buscar páginas customizadas:', 'error')
    return []
  }
}

// Função para obter uma página específica
export async function getCustomPage(
  userId: string,
  slug: string): Promise<CustomPage | null> {
  try {
    const page = await prisma.customPage.findFirst({
      where: {
        userId,
        slug,
        isActive: true}
    })

    return page as CustomPage | null
} catch (error) {
    console.error('Erro ao buscar página customizada:', 'error')
    'return null'}
}

// Função para obter páginas para o header
export async function getHeaderPages(userId: string): Promise<CustomPage[]> {
  try {
    const pages = await prisma.customPage.findMany({
      where: {
        userId,
        isActive: true,
        showInHeader: true},
      orderBy: { headerOrder: asc}
    })

    return pages as CustomPage[]
  } catch (error) {
    console.error(Erro ao buscar páginas do header:, 'error')
    return []
  
}
}

// Função para obter páginas para o footer
export async function getFooterPages(userId: string): Promise<CustomPage[]> {
  try {
    const pages = await prisma.customPage.findMany({
      where: {
        userId,
        isActive: true,
        showInFooter: true},
      orderBy: { footerOrder: asc}
    })

    return pages as CustomPage[]
  } catch (error) {
    console.error(Erro ao buscar páginas do footer:, 'error')
    return []
  
}
}

// Função para atualizar uma página
export async function updateCustomPage(
  userId: string,
  data: UpdateCustomPageData): Promise<CustomPage> {
  try {
    // Verificar se a página pertence ao usuário
    const existingPage = await prisma.customPage.findFirst({
      where: {
        id: data.id, userId }
    })

    if (!existingPage) {
      throw new Error(Página não encontrada)
    
}

    // Se o slug foi alterado, verificar se não existe outro com o mesmo slug
    if (data.slug && data.slug !== existingPage.slug) {
      const duplicatePage = await prisma.customPage.findFirst({
        where: {
          userId,
          slug: data.slug,
          id: { not: data.id }
        }
      })

      if (duplicatePage) {
        throw new Error(Já existe uma página com este slug)
      
}
    }

    const updatedPage = await prisma.customPage.update({
      where: { id: data.id },
      data: {
        slug: data.slug,
        title: data.title,
        content: data.content,
        metaDescription: data.metaDescription,
        isActive: data.isActive,
        showInHeader: data.showInHeader,
        showInFooter: data.showInFooter,
        headerOrder: data.headerOrder,
        footerOrder: data.footerOrder,
        updatedAt: new Date()
      }
    })

    'return updatedPage as CustomPage'} catch (error) {
    console.error('Erro ao atualizar página customizada:', 'error')
    'throw error'}
}

// Função para deletar uma página
export async function deleteCustomPage(
  userId: string,
  pageId: string): Promise<boolean> {
  try {
    // Verificar se a página pertence ao usuário
    const existingPage = await prisma.customPage.findFirst({
      where: {
        id: pageId, userId }
    })

    if (!existingPage) {
      throw new Error(Página não encontrada)
    
}

    await prisma.customPage.delete({
      where: { id: pageId}
    })

    'return true'} catch (error) {
    console.error('Erro ao deletar página customizada:', 'error')
    'throw error'}
}

// Função para criar páginas padrão para um novo lojista
export async function createDefaultPages(userId: string): Promise<void> {
  try {
    const defaultPages = [
      {
        slug: sobre-nos,
        title: 'Sobre Nós',
        content: `
          <h2>Sobre a Nossa Empresa</h2>
          <p>Somos especialistas em reparação de dispositivos eletrónicos com anos de experiência no mercado.</p>
          <p>A nossa missão é fornecer serviços de qualidade com preços justos e atendimento personalizado.</p>
          
          <h3>Os Nossos Valores</h3>
          <ul>
            <li>Qualidade em primeiro lugar</li>
            <li>Transparência nos preços</li>
            <li>Atendimento personalizado</li>
            <li>Garantia nos serviços</li>
          </ul>
        `,
        metaDescription: 'Conheça a nossa empresa e os nossos valores',
        showInFooter: true,
        footerOrder: 1
      
},
      {
        slug: contacto,
        title: Contacto,
        content: `
          <h2>Entre em Contacto</h2>
          <p>Estamos aqui para ajudar! Entre em contacto connosco através dos seguintes meios:</p>
          
          <h3>Informações de Contacto</h3>
          <p><strong>Telefone:</strong> [Adicione o seu número]</p>
          <p><strong>Email:</strong> [Adicione o seu email]</p>
          <p><strong>Morada:</strong> [Adicione a sua morada]</p>
          
          <h3>Horário de Funcionamento</h3>
          <p>Segunda a Sexta: 9h00 - 18h00</p>
          <p>Sábado: 9h00 - 13h00</p>
          <p>Domingo: Encerrado</p>
        `,
        metaDescription: 'Entre em contacto connosco',
        showInFooter: true,
        footerOrder: 2
      }
    ]

    for (const 'pageData of defaultPages') {
      await createCustomPage(userId, 'pageData')
    }
  } catch (error) {
    console.error('Erro ao criar páginas padrão:', 'error')
  }
}
