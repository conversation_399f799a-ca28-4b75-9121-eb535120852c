'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { CheckCircle, Package, ArrowLeft, Copy } from 'lucide-react'

function SucessoLojaPageContent() {
  const searchParams = useSearchParams()
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isMultibanco = searchParams.get('multibanco') === 'true'
  const entity = searchParams.get('entity')
  const reference = searchParams.get('reference')
  const amount = searchParams.get('amount')
  const orderId = searchParams.get('order')

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails()
    }
  }, [orderId])

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(`/api/lojista/spare-part-orders/${orderId}`)
      if (response.ok) {
        const data = await response.json()
        setOrderDetails(data.order)
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes da encomenda:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert(Copiado para a área de transferência!)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Success Icon */}
          <div className="text-center mb-8">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
            <h1 className="mt-4 text-2xl font-bold text-gray-900">
              {isMultibanco ? Referência Multibanco Gerada! : Encomenda Confirmada!}
            </h1>
            <p className="mt-2 text-gray-600">
              {isMultibanco 
                ? Use os dados abaixo para efetuar o pagamento
                : 'A sua encomenda de peças foi processada com sucesso'}
            </p>
          </div>

          {/* Multibanco Details */}
          {isMultibanco && entity && reference && amount && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Dados para Pagamento Multibanco</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    Entidade
                  </label>
                  <div className="flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-900 font-mono">
                      {entity}
                    </span>
                    <button
                      onClick={() => copyToClipboard(entity)}
                      className="ml-2 p-1 text-blue-600 hover:text-blue-800"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="text-center">
                  <label className="block text-sm font-medium text-blue-700 mb-1">Referência</label>
                  <div className="flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-900 font-mono">
                      {reference}
                    </span>
                    <button
                      onClick={() => copyToClipboard(reference)}
                      className="ml-2 p-1 text-blue-600 hover:text-blue-800"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="text-center">
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    Valor
                  </label>
                  <div className="flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-900">
                      €{amount}
                    </span>
                    <button
                      onClick={() => copyToClipboard(`€${amount}`)}
                      className="ml-2 p-1 text-blue-600 hover:text-blue-800"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="mt-4 text-sm text-blue-700 text-center">
                <p>⚠️ O pagamento deve ser efetuado até 3 dias úteis</p>
                <p>Após o pagamento, as peças serão processadas automaticamente</p>
              </div>
            </div>
          )}

          {/* Order Details */}
          {orderDetails && (
            <div className="border border-gray-200 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Package className="w-5 h-5 mr-2" />Detalhes da Encomenda</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Número da Encomenda:</span>
                      <span className="font-semibold">#{orderDetails.orderNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Data:</span>
                      <span>{new Date(orderDetails.createdAt).toLocaleDateString('pt-PT')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-semibold">
                        {isMultibanco ? Aguarda Pagamento : 'Confirmado'}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Método de Pagamento:</span>
                      <span>{isMultibanco ? 'Multibanco' : 'Cartão de Crédito'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total:</span>
                      <span className="font-bold text-lg">€{Number(orderDetails.total).toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Items */}
              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-3">Itens Encomendados:</h4>
                <div className="space-y-2">
                  {orderDetails.items?.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <div>
                        <span className="font-medium">{item.part?.name || 'Peça'}</span>
                        <span className="text-gray-700 ml-2">x{item.quantity}</span>
                      </div>
                      <span className="font-semibold">€{(Number(item.price) * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Próximos Passos</h3>
            <div className="space-y-3">
              {isMultibanco ? (
                <>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
                    <p className="text-gray-700">Efetue o pagamento usando os dados Multibanco acima</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                    <p className="text-gray-700">Após confirmação do pagamento, processaremos a sua encomenda</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
                    <p className="text-gray-700">Receberá notificação quando as peças forem enviadas</p>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">✓</div>
                    <p className="text-gray-700">Pagamento confirmado e encomenda em processamento</p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                    <p className="text-gray-700">Receberá notificação quando as peças forem enviadas</p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href="/lojista/encomendas-pecas"
              className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <Package className="w-5 h-5 mr-2" />Ver Encomendas</Link>
            <Link
              href="/lojista/loja-pecas"
              className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Continuar Comprando
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function SucessoLojaPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>}>
      <SucessoLojaPageContent />
    </Suspense>
  )
}
