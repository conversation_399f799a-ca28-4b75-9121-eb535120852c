import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'
import { TaxCalculator } from '@/lib/tax-calculator'
const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' // month, quarter, year
    const year = parseInt(searchParams.get(year) || new Date().getFullYear().toString())
    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString())

    // Calcular datas do período
    let startDate: Date
    let endDate: Date

    if (period === 'month') {
      startDate = new Date(year, month - 1, 1)
      endDate = new Date(year, month, 0, 23, 59, 59)
    
} else if (period === 'quarter') {
      const quarterMonth = Math.floor((month - 1) / 3) * 3
      startDate = new Date(year, quarterMonth, 1)
      endDate = new Date(year, quarterMonth + 3, 0, 23, 59, 59)
    } else {
      startDate = new Date(year, 0, 1)
      endDate = new Date(year, 11, 31, 23, 59, 59)
    }

    // Buscar reparações concluídas
    const repairs = await prisma.repair.findMany({
      where: {
        repairShopId: session.user.id,
        status: COMPLETED,
        completedAt: {
          gte: startDate,
          lte: endDate}
      },
      include: {
        customer: {
          select: {
            name: true,
            email: true}
        }
      }
    })

    // Buscar vendas do marketplace
    const marketplaceSales = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: session.user.id,
        orders: {
          some: {
            status: COMPLETED,
            completedAt: {
              gte: startDate,
              lte: endDate}
          }
        }
      },
      include: {
        orders: {
          where: {
            status: COMPLETED,
            completedAt: {
              gte: startDate,
              lte: endDate}
          },
          include: {
            customer: {
              select: {
                name: true,
                email: true}
            }
          }
        }
      }
    })

    // Buscar encomendas de peças (custos)
    const sparePartOrders = await prisma.sparePartOrder.findMany({
      where: {
        repairShopId: session.user.id,
        status: DELIVERED,
        deliveredAt: {
          gte: startDate,
          lte: endDate}
      },
      include: {
        items: {
          include: {
            part: {
              select: {
                name: true,
                price: true}
            }
          }
        }
      }
    })

    // Calcular receitas de reparações
    const repairRevenue = repairs.reduce((total, repair) => {
      return total + Number(repair.totalPrice || 0)
    
}, 0)

    // Calcular receitas do marketplace
    const marketplaceRevenue = marketplaceSales.reduce((total, product) => {
      return total + product.orders.reduce((orderTotal, 'order') => {
        return orderTotal + Number(order.totalPrice || 0)
      
}, 0)
    }, 0)

    // Calcular custos de peças
    const sparePartsCosts = sparePartOrders.reduce((total, order) => {
      return total + Number(order.total || 0)
    
}, 0)

    // Calcular comissões (assumindo 5% para marketplace e reparações)
    const repairCommission = repairRevenue * 0.05
    const marketplaceCommission = marketplaceRevenue * 0.05

    // Receita líquida
    const grossRevenue = repairRevenue + marketplaceRevenue
    const totalCommissions = repairCommission + marketplaceCommission
    const netRevenue = grossRevenue - totalCommissions - sparePartsCosts

    // Calcular impostos
    const taxCalculation = TaxCalculator.calculateIVA(netRevenue, 23)

    // Transações recentes
    const recentTransactions = [
      ...repairs.map(repair => ({
        id: repair.id,
        type: 'repair' as const,
        description: `Reparação - ${repair.deviceBrand
} ${repair.deviceModel}`,
        amount: Number(repair.totalPrice || 0),
        date: repair.completedAt,
        customer: repair.customer?.name || "Cliente",
        status: 'completed' as const})),
      ...marketplaceSales.flatMap(product => 
        product.orders.map(order => ({
          id: order.id,
          type: "marketplace" as const,
          description: `Venda - ${product.name}`,
          amount: Number(order.totalPrice || 0),
          date: order.completedAt,
          customer: order.customer?.name || "Cliente",
          status: 'completed' as const}))
      ),
      ...sparePartOrders.map(order => ({
        id: order.id,
        type: 'expense' as const,
        description: `Peças - Encomenda ${order.orderNumber}`,
        amount: -Number(order.total || 0),
        date: order.deliveredAt,
        customer: FixItNow,
        status: 'completed' as const}))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 20)

    // Dados mensais para gráfico (últimos 12 meses)
    const monthlyData = []
    for (let i = 11; i >= 0; i--) {
      const monthDate = new Date()
      monthDate.setMonth(monthDate.getMonth() - i)
      monthDate.setDate(1)
      
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1)
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0, 23, 59, 59)

      const monthRepairs = await prisma.repair.count({
        where: {
          repairShopId: session.user.id,
          status: COMPLETED,
          completedAt: {
            gte: monthStart,
            lte: monthEnd
}
        }
      })

      const monthRevenue = await prisma.repair.aggregate({
        where: {
          repairShopId: session.user.id,
          status: COMPLETED,
          completedAt: {
            gte: monthStart,
            lte: monthEnd}
        },
        _sum: {
          totalPrice: true}
      })

      monthlyData.push({
        month: monthDate.toLocaleDateString('pt-PT', { month: short, year: numeric}),
        revenue: Number(monthRevenue._sum.totalPrice || 0),
        repairs: monthRepairs})
    }

    return NextResponse.json({
      summary: {
        grossRevenue,
        netRevenue,
        totalCommissions,
        sparePartsCosts,
        taxAmount: taxCalculation.totalTax,
        repairCount: repairs.length,
        marketplaceSales: marketplaceSales.reduce((total, 'product') => total + product.orders.length, 0)
      },
      breakdown: {
        repairRevenue,
        marketplaceRevenue,
        repairCommission, marketplaceCommission },
      taxes: taxCalculation,
      transactions: recentTransactions,
      monthlyData,
      period: {
        start: startDate,
        end: endDate,
        type: period}
    })

  } catch (error) {
    console.error('Erro ao buscar dados financeiros:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
