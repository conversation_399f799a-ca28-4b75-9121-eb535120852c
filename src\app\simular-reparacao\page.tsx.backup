'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import {
  ArrowLeft, ArrowRight, MapPin, Star, Clock, Euro, Wrench,
  CheckCircle, Phone, Mail, User, Calendar, Smartphone, Laptop, Tablet
} from 'lucide-react'
import ModernLayout from '@/components/ModernLayout'

interface Category {
  id: string
  name: string
  description: string
}

interface Brand {
  id: string
  name: string
  categoryId: string
}

interface Device {
  id: string
  name: string
  brandId: string
}

interface ProblemType {
  id: string
  name: string
  description: string
  categoryId: string
}

interface RepairShop {
  id: string
  name: string
  email: string
  profile?: {
    companyName: string
    phone: string
    description: string
    address: string
    city: string
  }
  rating: number
  basePrice: number
  isVerified: boolean
}

function SimularReparacaoPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()

  const [currentStep, setCurrentStep] = useState(1)
  const [categories, setCategories] = useState<Category[]>([])
  const [brands, setBrands] = useState<Brand[]>([])
  const [devices, setDevices] = useState<Device[]>([])
  const [problemTypes, setProblemTypes] = useState<ProblemType[]>([])
  const [shops, setShops] = useState<RepairShop[]>([])
  const [loading, setLoading] = useState(false)
  
  // Form data
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedBrand, setSelectedBrand] = useState('')
  const [selectedDevice, setSelectedDevice] = useState('')
  const [selectedProblemType, setSelectedProblemType] = useState('')
  const [selectedShop, setSelectedShop] = useState('')
  const [description, setDescription] = useState('')

  // Dados da simulação vindos da URL
  const urlProblemType = searchParams.get('problemType')
  const urlDevice = searchParams.get('device')
  const urlLocation = searchParams.get('location')

  useEffect(() => {
    fetchInitialData()

    // Se temos dados da URL, processar automaticamente
    if (urlDevice) {
      processUrlDevice(urlDevice)
    }
  }, [])

  const processUrlDevice = async (deviceName: string) => {
    try {
      // Tentar encontrar o dispositivo na base de dados
      const response = await fetch(`/api/device-models?search=${encodeURIComponent(deviceName)}`)
      if (response.ok) {
        const devices = await response.json()
        if (devices.length > 0) {
          const device = devices[0]

          // Auto-selecionar categoria, marca e dispositivo
          setSelectedCategory(device.brand?.categoryId || '')
          setSelectedBrand(device.brandId || '')
          setSelectedDevice(device.id)

          // Avançar para o passo 2 automaticamente
          setTimeout(() => {
            setCurrentStep(1) // Mostrar seleções feitas
          }, 500)
        }
      }
    } catch (error) {
      console.error('Erro ao processar dispositivo da URL:', error)
    }
  }

  const fetchBrandsByCategory = useCallback(async (categoryId: string) => {
    try {
      const response = await fetch(`/api/brands?categoryId=${categoryId}`)
      if (response.ok) {
        const data = await response.json()
        setBrands(data.brands || [])
      }
    } catch (error) {
      console.error('Erro ao buscar marcas:', error)
    }
  }, [])

  const fetchDevices = useCallback(async () => {
    if (!selectedCategory || !selectedBrand) return

    try {
      const response = await fetch(`/api/device-models?brandId=${selectedBrand}&categoryId=${selectedCategory}`)
      if (response.ok) {
        const data = await response.json()
        setDevices(data || [])
      }
    } catch (error) {
      console.error('Erro ao carregar dispositivos:', error)
      setDevices([])
    }
  }, [selectedCategory, selectedBrand])

  useEffect(() => {
    if (selectedCategory) {
      fetchBrandsByCategory(selectedCategory)
      // Limpar seleções dependentes
      setSelectedBrand('')
      setSelectedDevice('')
      setDevices([])
    }
  }, [selectedCategory, fetchBrandsByCategory])

  useEffect(() => {
    if (selectedCategory && selectedBrand) {
      fetchDevices()
    }
  }, [selectedCategory, selectedBrand, fetchDevices])

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes, problemTypesRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/brands'),
        fetch('/api/problem-types')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories || [])
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData.brands || brandsData || [])
      }

      if (problemTypesRes.ok) {
        const problemTypesData = await problemTypesRes.json()
        setProblemTypes(problemTypesData.problemTypes || problemTypesData || [])
      }
    } catch (error) {
      console.error('Erro ao carregar dados iniciais:', error)
    }
  }

  const fetchShops = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/repair-shops/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          categoryId: selectedCategory,
          brandId: selectedBrand,
          deviceId: selectedDevice,
          problemTypeId: selectedProblemType,
          location: urlLocation || 'Porto'
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        setShops(data.shops || [])
      }
    } catch (error) {
      console.error('Erro ao buscar lojas:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNextStep = () => {
    if (currentStep === 1 && selectedCategory && selectedBrand && selectedDevice && selectedProblemType) {
      fetchShops()
      setCurrentStep(2)
    }
  }

  const handleConfirmRepair = () => {
    if (!selectedShop) {
      alert('Por favor selecione uma loja')
      return
    }

    // Preparar dados para passar para a página de reparação
    const params = new URLSearchParams()
    params.set('categoryId', selectedCategory)
    params.set('brandId', selectedBrand)
    params.set('deviceId', selectedDevice)
    params.set('problemTypeId', selectedProblemType)
    params.set('shopId', selectedShop)
    if (description) params.set('description', description)

    if (!session) {
      // Redirecionar para login com dados preservados
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(`/cliente/reparacoes/nova-v2?${params.toString()}`)}`)
    } else {
      // Ir diretamente para criar reparação
      router.push(`/cliente/reparacoes/nova-v2?${params.toString()}`)
    }
  }

  const getCategoryIcon = (categoryName: string) => {
    if (categoryName.toLowerCase().includes('smartphone') || categoryName.toLowerCase().includes('telemóvel')) {
      return <Smartphone className="w-6 h-6" />
    }
    if (categoryName.toLowerCase().includes('laptop') || categoryName.toLowerCase().includes('portátil')) {
      return <Laptop className="w-6 h-6" />
    }
    if (categoryName.toLowerCase().includes('tablet')) {
      return <Tablet className="w-6 h-6" />
    }
    return <Wrench className="w-6 h-6" />
  }

  return (
    <ModernLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header com dados da URL se existirem */}
        {(urlDevice || urlLocation) && (
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                  <Wrench className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Simulação de Reparação</h2>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                    {urlDevice && (
                      <span className="bg-white px-3 py-1 rounded-full border">
                        📱 {urlDevice}
                      </span>
                    )}
                    {urlLocation && (
                      <span className="bg-white px-3 py-1 rounded-full border flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {urlLocation}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={() => {
                  // Limpar parâmetros da URL e resetar simulação
                  window.history.replaceState({}, '', '/simular-reparacao')
                  setSelectedCategory('')
                  setSelectedBrand('')
                  setSelectedDevice('')
                  setSelectedProblemType('')
                  setCurrentStep(0)
                  setShops([])
                  setDevices([])
                }}
                className="bg-white text-indigo-600 px-4 py-2 rounded-lg border border-indigo-200 hover:bg-indigo-50 transition-colors text-sm font-medium flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Alterar Simulação</span>
              </button>
            </div>
          </div>
        )}

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
              currentStep >= 1 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              1
            </div>
            <div className={`w-16 h-1 ${currentStep >= 2 ? 'bg-indigo-600' : 'bg-gray-200'}`}></div>
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
              currentStep >= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              2
            </div>
          </div>
        </div>

        {currentStep === 1 && (
          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Simular Reparação
              </h1>
              <p className="text-lg text-gray-600">
                Selecione o seu dispositivo e problema para encontrar lojas especializadas
              </p>
            </div>

            {/* Step 1: Category Selection */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">1. Categoria do Dispositivo</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`p-4 rounded-xl border-2 transition-all ${
                      selectedCategory === category.id
                        ? 'border-indigo-600 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`${selectedCategory === category.id ? 'text-indigo-600' : 'text-gray-400'}`}>
                        {getCategoryIcon(category.name)}
                      </div>
                      <div className="text-left">
                        <div className="font-medium text-gray-900">{category.name}</div>
                        <div className="text-sm text-gray-500">{category.description}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Step 2: Brand Selection */}
            {selectedCategory && brands.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">2. Marca</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {brands.map((brand) => (
                    <button
                      key={brand.id}
                      onClick={() => setSelectedBrand(brand.id)}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        selectedBrand === brand.id
                          ? 'border-indigo-600 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-gray-900">{brand.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Step 3: Device Selection */}
            {selectedBrand && devices.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">3. Modelo do Dispositivo</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {devices.map((device) => (
                    <button
                      key={device.id}
                      onClick={() => setSelectedDevice(device.id)}
                      className={`p-3 rounded-lg border-2 transition-all text-left ${
                        selectedDevice === device.id
                          ? 'border-indigo-600 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-gray-900">{device.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Step 4: Problem Type Selection */}
            {selectedDevice && problemTypes.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">4. Tipo de Problema</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {problemTypes.map((problemType) => (
                    <button
                      key={problemType.id}
                      onClick={() => setSelectedProblemType(problemType.id)}
                      className={`p-4 rounded-lg border-2 transition-all text-left ${
                        selectedProblemType === problemType.id
                          ? 'border-indigo-600 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-gray-900">{problemType.name}</div>
                      <div className="text-sm text-gray-500 mt-1">{problemType.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Description */}
            {selectedProblemType && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">5. Descrição Adicional (Opcional)</h3>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Descreva o problema em mais detalhe..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  rows={4}
                />
              </div>
            )}

            {/* Next Button */}
            {selectedCategory && selectedBrand && selectedDevice && selectedProblemType && (
              <div className="flex justify-center">
                <button
                  onClick={handleNextStep}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-semibold flex items-center space-x-2"
                >
                  <span>Encontrar Lojas</span>
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>
            )}
          </div>
        )}

        {/* Step 2: Shop Selection */}
        {currentStep === 2 && (
          <div className="space-y-8">
            <div className="flex items-center justify-between mb-8">
              <div className="text-center flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  Lojas Disponíveis
                </h1>
                <p className="text-lg text-gray-600">
                  Selecione a loja que prefere para a sua reparação
                </p>
              </div>
              <button
                onClick={() => {
                  setCurrentStep(0)
                  setShops([])
                }}
                className="bg-white text-indigo-600 px-4 py-2 rounded-lg border border-indigo-200 hover:bg-indigo-50 transition-colors text-sm font-medium flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Alterar Simulação</span>
              </button>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
                <p className="text-gray-600 mt-4">A procurar lojas especializadas...</p>
              </div>
            ) : shops.length > 0 ? (
              <div className="space-y-4">
                {shops.map((shop) => (
                  <div
                    key={shop.id}
                    className={`bg-white rounded-xl shadow-sm border-2 p-6 cursor-pointer transition-all ${
                      selectedShop === shop.id
                        ? 'border-indigo-600 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedShop(shop.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {shop.profile?.companyName || shop.name}
                          </h3>
                          {shop.isVerified && (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          )}
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                            <span>{shop.rating.toFixed(1)}</span>
                          </div>
                          {shop.profile?.address && (
                            <div className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              <span>{shop.profile.city}</span>
                            </div>
                          )}
                          {shop.profile?.phone && (
                            <div className="flex items-center">
                              <Phone className="w-4 h-4 mr-1" />
                              <span>{shop.profile.phone}</span>
                            </div>
                          )}
                        </div>

                        {shop.profile?.description && (
                          <p className="text-gray-600 text-sm mb-3">{shop.profile.description}</p>
                        )}
                      </div>

                      <div className="text-right">
                        <div className="text-2xl font-bold text-indigo-600">
                          €{shop.basePrice}
                        </div>
                        <div className="text-sm text-gray-500">a partir de</div>
                      </div>
                    </div>
                  </div>
                ))}

                {selectedShop && (
                  <div className="flex justify-center space-x-4 pt-6">
                    <button
                      onClick={() => setCurrentStep(1)}
                      className="px-6 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors font-semibold flex items-center space-x-2"
                    >
                      <ArrowLeft className="w-5 h-5" />
                      <span>Voltar</span>
                    </button>
                    <button
                      onClick={handleConfirmRepair}
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-semibold"
                    >
                      Confirmar Reparação
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Wrench className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma loja encontrada</h3>
                <p className="text-gray-600 mb-6">
                  Não encontrámos lojas especializadas para este tipo de reparação na sua área.
                </p>
                <button
                  onClick={() => setCurrentStep(1)}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-semibold flex items-center space-x-2 mx-auto"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span>Tentar Novamente</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </ModernLayout>
  )
}

export default function SimularReparacaoPage() {
  return (
    <Suspense fallback={
      <ModernLayout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-8"></div>
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </ModernLayout>
    }>
      <SimularReparacaoPageContent />
    </Suspense>
  )
}
