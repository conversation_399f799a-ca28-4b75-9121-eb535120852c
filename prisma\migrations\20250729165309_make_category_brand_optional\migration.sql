-- DropForeign<PERSON>ey
ALTER TABLE "marketplace_products" DROP CONSTRAINT "marketplace_products_brandId_fkey";

-- DropForeignKey
ALTER TABLE "marketplace_products" DROP CONSTRAINT "marketplace_products_categoryId_fkey";

-- AlterTable
ALTER TABLE "marketplace_products" ALTER COLUMN "categoryId" DROP NOT NULL,
ALTER COLUMN "brandId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "marketplace_products" ADD CONSTRAINT "marketplace_products_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketplace_products" ADD CONSTRAINT "marketplace_products_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;
