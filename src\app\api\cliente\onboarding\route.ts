import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    const { phone, address, city, postalCode, preferences } = await request.json()

    // Atualizar perfil do usuário
    await prisma.profile.upsert({
      where: {
        userId: session.user.id
      },
      update: {
        phone
},
      create: {
        userId: session.user.id, phone }
    })

    // Criar endereço principal se fornecido
    if (address && city && postalCode) {
      await prisma.address.create({
        data: {
          userId: session.user.id,
          street: address,
          city,
          postalCode,
          country: Portugal,
          isDefault: true
}
      })
    }

    // Criar notificação de boas-vindas
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        title: "Perfil Completado!",
        message: "Bem-vindo à Revify! Seu perfil foi configurado com sucesso.",
        type: WELCOME}
    })

    return NextResponse.json({
      message: "Perfil atualizado com sucesso"
    })

  } catch (error) {
    console.error("Erro no onboarding do cliente:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
