'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Search, CheckCircle, Package, Eye } from 'lucide-react'

interface Repair {
  id: string
  status: string
  description: string
  estimatedPrice?: number
  finalPrice?: number
  createdAt: string
  completedDate?: string
  deviceModel?: {
    name: string
    brand: {
      name: string
    }
    category: {
      name: string
    }
  }
  customer: {
    name: string
    email: string
  }
}

const STATUS_CONFIG = {
  COMPLETED: { label: 'Concluí<PERSON>', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'bg-gray-100 text-gray-800', icon: Package },
  CANCELLED: { label: 'Cancelado', color: 'bg-red-100 text-red-800', icon: Package }
}

export default function LojistaHistoricoPage() {
  const [repairs, setRepairs] = useState<Repair[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchCompletedRepairs()
  }, [])

  const fetchCompletedRepairs = async () => {
    try {
      const response = await fetch('/api/lojista/repairs?status=COMPLETED,DELIVERED,CANCELLED')
      if (response.ok) {
        const data = await response.json()
        setRepairs(data.repairs)
      }
    } catch (error) {
      console.error('Erro ao carregar histórico:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch = 
      (repair.id || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.deviceModel?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.deviceModel?.brand?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.customer?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (repair.description || '').toLowerCase().includes(searchTerm.toLowerCase())

    return matchesSearch
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-black">Histórico de Reparações</h1>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-gray-900 bg-white"
              placeholder="Pesquisar no histórico..."
            />
          </div>
        </div>

        {/* Repairs List */}
        {filteredRepairs.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-700 text-lg mb-4 font-medium">
              {searchTerm ? 'Nenhuma reparação encontrada' : 'Ainda não tem reparações concluídas'}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reparação
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dispositivo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRepairs.map((repair) => {
                    const statusConfig = STATUS_CONFIG[repair.status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.COMPLETED
                    const StatusIcon = statusConfig.icon

                    return (
                      <tr key={repair.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            #{repair.id.slice(-8)}
                          </div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {repair.description}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {repair.customer?.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {repair.customer?.email}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {repair.deviceModel?.brand?.name} {repair.deviceModel?.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {repair.deviceModel?.category?.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color}`}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {statusConfig.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {repair.finalPrice ? (
                            <span className="font-medium">€{Number(repair.finalPrice).toFixed(2)}</span>
                          ) : repair.estimatedPrice ? (
                            <span className="text-gray-500">€{Number(repair.estimatedPrice).toFixed(2)} (est.)</span>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {repair.completedDate ? 
                            new Date(repair.completedDate).toLocaleDateString('pt-PT') :
                            new Date(repair.createdAt).toLocaleDateString('pt-PT')
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link
                            href={`/lojista/reparacoes/${repair.id}`}
                            className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Ver
                          </Link>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
