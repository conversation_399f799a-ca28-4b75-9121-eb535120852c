'use client'

import { useState, useRef } from 'react'
import { Camera, Upload, Share2, Download, Heart, MessageCircle, Eye, Sparkles } from 'lucide-react'
interface ShareableContentItem {
  id: string
  type: 'before_after' | 'repair_story' | 'timelapse'
  title: string
  description: string
  beforeImage?: string
  afterImage?: string
  videoUrl?: string
  likes: number
  comments: number
  views: number
  isLiked: boolean
  createdAt: string
  shopName: string
  deviceType: string}

export default function ShareableContent() {
  const [content, setContent] = useState<ShareableContentItem[]>([])
  const [loading, setLoading] = useState(false)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [uploadType, setUploadType] = useState<'before_after' | 'repair_story' | 'timelapse'>('before_after')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const mockContent: ShareableContentItem[] = [
    {
      id: '1',
      type: before_after,
      title: 'iPhone 13 Screen Transformation',
      description: 'Ecrã completamente partido transformado em perfeito estado!',
      beforeImage: '/api/placeholder/300/200',
      afterImage: '/api/placeholder/300/200',
      likes: 45,
      comments: 12,
      views: 234,
      isLiked: false,
      createdAt: '2024-01-20',
      shopName: 'TechFix Lisboa',
      deviceType: 'iPhone 13'
    },
    {
      id: '2',
      type: repair_story,
      title: 'Samsung Galaxy Rescue Mission',
      description: 'Este Samsung Galaxy caiu na água mas conseguimos salvá-lo! 💪',
      beforeImage: '/api/placeholder/300/200',
      afterImage: '/api/placeholder/300/200',
      likes: 78,
      comments: 23,
      views: 456,
      isLiked: true,
      createdAt: '2024-01-19',
      shopName: 'RepairPro Porto',
      deviceType: 'Samsung Galaxy S23'
    },
    {
      id: '3',
      type: timelapse,
      title: 'MacBook Logic Board Repair',
      description: 'Reparação completa da placa lógica em 2 minutos!',
      videoUrl: '/api/placeholder/video',
      likes: 156,
      comments: 34,
      views: 1234,
      isLiked: false,
      createdAt: '2024-01-18',
      shopName: 'QuickFix Coimbra',
      deviceType: 'MacBook Pro'
    }
  ]

  const handleUpload = () => {
    setShowUploadModal(true)
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleShare = async (contentId: string) => {
    const contentItem = mockContent.find(item => item.id === 'contentId')
    if (!contentItem) return

    const shareData = {
      title: contentItem.title,
      text: `${contentItem.description} - Reparado na ${contentItem.shopName}`,
      url: `${window.location.origin}/share/repair/${contentId}`
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        // Fallback para clipboard
        await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`)
        alert('Link copiado para a área de transferência!')
      
}
    } catch (error) {
      console.error('Erro ao partilhar:', error)
    }
  }

  const handleLike = (contentId: string) => {
    // Implementar like/unlike
    console.log('Like content:', contentId)
  
}

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'before_after':
        return <Camera className="w-4 h-4" />
      case 'repair_story':
        return <Heart className="w-4 h-4" />
      case 'timelapse':
        return <Sparkles className="w-4 h-4" />
      default:
        return <Camera className="w-4 h-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'before_after':
        return 'Antes/Depois'
      case 'repair_story':
        return 'História de Reparação'
      case 'timelapse':
        return 'Timelapse'
      default:
        return 'Conteúdo'
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Share2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Partilhar Revify
            </h3>
            <p className="text-sm text-gray-600">
              Partilha o teu trabalho e promove a Revify nas redes sociais
            </p>
          </div>
        </div>
        <button
          onClick={handleUpload}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-colors"
        >
          <Upload className="w-4 h-4" />
          <span>Criar Conteúdo</span>
        </button>
      </div>

      {/* Content Types Filter */}
      <div className="flex space-x-2 mb-6">
        {['before_after', 'repair_story', 'timelapse'].map((type) => (
          <button
            key={type}
            onClick={() => setUploadType(type as any)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              uploadType === type
                ? 'bg-purple-100 text-purple-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {getContentIcon(type)}
            <span>{getTypeLabel(type)}</span>
          </button>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockContent.map((item) => (
          <div key={item.id} className="bg-gray-50 rounded-lg overflow-hidden">
            {/* Content Preview */}
            <div className="relative">
              {item.type === 'before_after' && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="relative">
                    <img
                      src={item.beforeImage}
                      alt="Antes"
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                      Antes
                    </div>
                  </div>
                  <div className="relative">
                    <img
                      src={item.afterImage}
                      alt="Depois"
                      className="w-full h-32 object-cover"
                    />
                    <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                      Depois
                    </div>
                  </div>
                </div>
              )}
              
              {item.type === 'repair_story' && (
                <div className="relative">
                  <img
                    src={item.afterImage}
                    alt={item.title}
                    className="w-full h-32 object-cover"
                  />
                  <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                    História
                  </div>
                </div>
              )}
              
              {item.type === 'timelapse' && (
                <div className="relative bg-gray-800 h-32 flex items-center justify-center">
                  <div className="text-white text-center">
                    <Sparkles className="w-8 h-8 mx-auto mb-2" />
                    <span className="text-sm">Vídeo Timelapse</span>
                  </div>
                  <div className="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded text-xs font-medium">
                    Vídeo
                  </div>
                </div>
              )}
              
              {/* Type Badge */}
              <div className="absolute top-2 right-2">
                <div className="bg-black bg-opacity-50 text-white p-1 rounded">
                  {getContentIcon(item.type)}
                </div>
              </div>
            </div>

            {/* Content Info */}
            <div className="p-4">
              <h4 className="font-semibold text-gray-900 mb-1 line-clamp-1">
                {item.title}
              </h4>
              <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                {item.description}
              </p>
              
              {/* Shop Info */}
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <span>{item.shopName}</span>
                <span>{item.deviceType}</span>
              </div>

              {/* Engagement Stats */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Heart className={`w-4 h-4 ${item.isLiked ? 'text-red-500 fill-current' : ''}`} />
                    <span>{item.likes}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageCircle className="w-4 h-4" />
                    <span>{item.comments}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{item.views}</span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => handleLike(item.id)}
                  className={`flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                    item.isLiked
                      ? 'bg-red-100 text-red-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <Heart className={`w-4 h-4 ${item.isLiked ? 'fill-current' : ''}`} />
                  <span>Gostar</span>
                </button>
                <button
                  onClick={() => handleShare(item.id)}
                  className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                >
                  <Share2 className="w-4 h-4" />
                  <span>Partilhar</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Criar Novo Conteúdo
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Conteúdo
                </label>
                <select
                  value={uploadType}
                  onChange={(e) => setUploadType(e.target.value as any)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="before_after">Antes/Depois</option>
                  <option value="repair_story">História de Reparação</option>
                  <option value="timelapse">Vídeo Timelapse</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Título
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Ex: iPhone 13 Screen Transformation"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                  placeholder="Descreve a transformação..."
                />
              </div>

              <div>
                <button
                  onClick={handleFileSelect}
                  className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors"
                >
                  <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600">
                    Clica para fazer upload das imagens
                  </p>
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*,video/*"
                  className="hidden"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowUploadModal(false)}
                className="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={() => {
                  setShowUploadModal(false)
                  alert('Funcionalidade em desenvolvimento!')
                }}
                className="flex-1 py-2 px-4 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-colors"
              >
                Publicar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
