'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import DeepLUsageMonitor from '@/components/admin/DeepLUsageMonitor'
import LanguageSelector from '@/components/LanguageSelector'
import AutoTranslate from '@/components/ui/AutoTranslate'
import { Globe, Settings, BarChart3, Users } from 'lucide-react'

/**
 * Dashboard administrativo para gerenciar traduções
 * Inclui monitoramento da API DeepL, estatísticas e configurações
 */
export default function TranslationsDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState({
    totalTranslations: 0,
    activeLanguages: 0,
    dailyUsage: 0,
    topLanguages: []
  })

  useEffect(() => {
    if (status === 'loading') return

    if (!session?.user || session.user.role !== 'ADMIN') {
      router.push('/login')
      return
    }

    // Aqui você pode carregar estatísticas adicionais
    // fetchTranslationStats()
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <Globe className="w-8 h-8 text-blue-600" />
              <div>
                <AutoTranslate text="Gestão de Traduções" as="h1" className="text-2xl font-bold text-gray-900" />
                <AutoTranslate text="Monitoramento e configuração do sistema de traduções" as="p" className="text-gray-600" />
              </div>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Usage Monitor - Coluna Principal */}
          <div className="lg:col-span-2">
            <DeepLUsageMonitor />
          </div>

          {/* Sidebar com Estatísticas */}
          <div className="space-y-6">
            
            {/* Quick Stats */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <AutoTranslate text="Estatísticas Rápidas" />
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Globe className="w-4 h-4 text-blue-500" />
                    <AutoTranslate text="Idiomas Ativos" as="span" className="text-sm text-gray-600" />
                  </div>
                  <span className="font-semibold">6</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-4 h-4 text-green-500" />
                    <AutoTranslate text="Cache Hit Rate" as="span" className="text-sm text-gray-600" />
                  </div>
                  <span className="font-semibold">87%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-purple-500" />
                    <AutoTranslate text="Usuários Ativos" as="span" className="text-sm text-gray-600" />
                  </div>
                  <span className="font-semibold">1,234</span>
                </div>
              </div>
            </div>

            {/* Supported Languages */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <AutoTranslate text="Idiomas Suportados" />
              </h3>
              
              <div className="space-y-3">
                {[
                  { code: 'pt', name: 'Português', flag: '🇵🇹', usage: 45 },
                  { code: 'en', name: 'English', flag: '🇬🇧', usage: 30 },
                  { code: 'es', name: 'Español', flag: '🇪🇸', usage: 15 },
                  { code: 'fr', name: 'Français', flag: '🇫🇷', usage: 7 },
                  { code: 'de', name: 'Deutsch', flag: '🇩🇪', usage: 2 },
                  { code: 'it', name: 'Italiano', flag: '🇮🇹', usage: 1 }
                ].map((lang) => (
                  <div key={lang.code} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{lang.flag}</span>
                      <span className="text-sm font-medium">{lang.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${lang.usage}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-500 w-8">{lang.usage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                <AutoTranslate text="Status do Sistema" />
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <AutoTranslate text="API DeepL" as="span" className="text-sm text-gray-600" />
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <AutoTranslate text="Online" as="span" className="text-xs text-green-600 font-medium" />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <AutoTranslate text="Cache Redis" as="span" className="text-sm text-gray-600" />
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <AutoTranslate text="Online" as="span" className="text-xs text-green-600 font-medium" />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <AutoTranslate text="Detecção de Idioma" as="span" className="text-sm text-gray-600" />
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <AutoTranslate text="Ativo" as="span" className="text-xs text-green-600 font-medium" />
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Configuration Section */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center space-x-2 mb-6">
              <Settings className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                <AutoTranslate text="Configurações Avançadas" />
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  <AutoTranslate text="Cache de Traduções" />
                </h4>
                <AutoTranslate text="Configurar tempo de vida do cache e políticas de limpeza" as="p" className="text-sm text-gray-600 mb-3" />
                <button className="text-blue-600 text-sm font-medium hover:text-blue-700">
                  <AutoTranslate text="Configurar" />
                </button>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  <AutoTranslate text="Detecção Automática" />
                </h4>
                <AutoTranslate text="Ajustar prioridades entre detecção por browser e IP" as="p" className="text-sm text-gray-600 mb-3" />
                <button className="text-blue-600 text-sm font-medium hover:text-blue-700">
                  <AutoTranslate text="Configurar" />
                </button>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  <AutoTranslate text="Novos Idiomas" />
                </h4>
                <AutoTranslate text="Adicionar suporte para novos idiomas na plataforma" as="p" className="text-sm text-gray-600 mb-3" />
                <button className="text-blue-600 text-sm font-medium hover:text-blue-700">
                  <AutoTranslate text="Adicionar" />
                </button>
              </div>

              <div className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  <AutoTranslate text="Teste de Performance" />
                </h4>
                <AutoTranslate text="Executar testes de performance e analisar métricas do sistema" as="p" className="text-sm text-gray-600 mb-3" />
                <Link href="/admin/translations/test" className="text-blue-600 text-sm font-medium hover:text-blue-700">
                  <AutoTranslate text="Executar Testes" />
                </Link>
              </div>
              
            </div>
          </div>
        </div>

      </div>
    </div>
  )
}
