'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { ArrowLeft,
  CreditCard,
  Calendar,
  Download,
  AlertTriangle,
  CheckCircle,
  Crown,
  ArrowUp,
  Package,
  Plus } from 'lucide-react'
import Link from 'next/link'
interface Subscription {
  id: string
  status: string
  billingCycle: string
  currentPeriodStart: string
  currentPeriodEnd: string
  cancelAtPeriodEnd: boolean
  canceledAt: string | null
  plan: {
    id: string
    name: string
    monthlyPrice: number
    yearlyPrice: number
    features: string[]
  }
  payments: {
    id: string
    amount: number
    status: string
    createdAt: string
    periodStart: string
    periodEnd: string
    multibancoEntity?: string
    multibancoReference?: string}[]
}

export default function GestaoSubscricaoPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCanceling, setIsCanceling] = useState(false)
  const [showPaymentOptions, setShowPaymentOptions] = useState<string | null>(null)

  useEffect(() => {
    fetchSubscription()
  }, [])

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/lojista/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscription(data.subscription)
      }
    } catch (error) {
      console.error('Erro ao buscar subscrição:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const cancelSubscription = async () => {
    if (!subscription) return
    
    if (!confirm(Tem certeza que deseja cancelar a sua subscrição? Esta ação não pode ser desfeita.)) {
      return
    }

    setIsCanceling(true)
    try {
      const response = await fetch(`/api/lojista/subscription/${subscription.id}/cancel`, {
        method: POST})

      if (response.ok) {
        alert(Subscrição cancelada com sucesso!)
        fetchSubscription()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao cancelar subscrição')
      }
    } catch (error) {
      console.error('Erro ao cancelar subscrição:', 'error')
      alert(Erro 'ao cancelar subscrição')
    } finally {
      setIsCanceling(false)
    }
  }

  const handlePayment = async (paymentId: string) => {
    try {
      const response = await fetch('/api/lojista/subscription/pay', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 'paymentId'})
      })

      const data = await response.json()

      if (response.ok && data.checkoutUrl) {
        window.location.href = data.checkoutUrl
      } else {
        alert(data.message || 'Erro ao processar pagamento')
      }
    } catch (error) {
      console.error('Erro ao processar pagamento:', 'error')
      alert('Erro ao processar pagamento')
    }
  }

  const handleChangePaymentMethod = async (subscriptionId: string, newMethod: 'card' | 'multibanco') => {
    try {
      const response = await fetch('/api/lojista/subscription/change-payment-method', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionId,
          paymentMethod: newMethod})
      })

      const data = await response.json()

      if (response.ok) {
        if (data.checkoutUrl) {
          window.location.href = data.checkoutUrl
        } else if (data.redirectUrl) {
          router.push(data.redirectUrl)
        } else {
          // Recarregar dados
          fetchSubscription()
        }
      } else {
        alert(data.message || Erro ao alterar método de pagamento)
      
}
    } catch (error) {
      console.error('Erro ao alterar método de pagamento:', 'error')
      alert('Erro ao alterar método de pagamento')
    }
  }

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'ACTIVE': 'Ativa',
      'PAST_DUE': 'Em Atraso',
      'CANCELED': 'Cancelada',
      'INCOMPLETE': Pagamento Pendente,
      'TRIALING': Período de Teste,
      'UNPAID': 'Não Paga'}
    return statusMap[status] || 'status'}

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'ACTIVE': 'bg-green-100 text-green-800',
      'PAST_DUE': 'bg-yellow-100 text-yellow-800',
      'CANCELED': 'bg-red-100 text-red-800',
      'INCOMPLETE': 'bg-yellow-100 text-yellow-800',
      'TRIALING': 'bg-blue-100 text-blue-800',
      'UNPAID': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getCurrentPrice = () => {
    if (!subscription) return 0
    return subscription.billingCycle === 'MONTHLY' 
      ? subscription.plan.monthlyPrice 
      : subscription.plan.yearlyPrice
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!subscription) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-2xl font-bold text-black mb-6">Gestão de Subscrição</h1>
          <div className="text-center">
            <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">Nenhuma subscrição ativa</h3>
            <p className="mt-1 text-gray-500">Subscreva um plano para desbloquear funcionalidades avançadas.</p>
            <div className="mt-6">
              <Link
                href="/lojista/upgrade"
                className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
              >
                <Crown className="w-4 h-4 mr-2" />
                Ver Planos
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-bold text-black mb-6">Gestão de Subscrição</h1>
        {/* Subscription Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Crown className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{subscription.plan.name}</h2>
                <p className="text-gray-600">Plano atual</p>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                {getStatusLabel(subscription.status)}
              </span>
            </div>
          </div>

          {subscription.status === 'INCOMPLETE' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="font-medium text-yellow-800 mb-1">Pagamento Pendente</h3>
                  <p className="text-sm text-yellow-700 mb-3">A sua subscrição será ativada automaticamente após a confirmação do pagamento (normalmente até 5 'minutos').</p>
                  {subscription.payments.length > 0 && subscription.payments[0].status === 'PENDING' && (
                    <div className="text-sm text-yellow-700">
                      {subscription.payments[0].multibancoEntity && subscription.payments[0].multibancoReference ? (
                        <div>
                          <strong>Dados para pagamento Multibanco:</strong><br />
                          Entidade: {subscription.payments[0].multibancoEntity}<br />
                          Referência: {subscription.payments[0].multibancoReference}<br />
                          Valor: €{subscription.payments[0].amount}
                        </div>
                      ) : (
                        <div>
                          <button
                            onClick={() => handlePayment(subscription.payments[0].id)}
                            className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 mr-2"
                          >
                            Pagar com Cartão
                          </button>
                          <button
                            onClick={() => handleChangePaymentMethod(subscription.id, 'multibanco')}
                            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                          >
                            Pagar com Multibanco
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">
                €{Number(getCurrentPrice()).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">
                {subscription.billingCycle === 'MONTHLY' ? por mês : 'por ano'}
              </div>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
              </div>
              <div className="text-sm text-gray-600">Próxima renovação</div>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {subscription.billingCycle === 'MONTHLY' ? 'Mensal' : 'Anual'}
              </div>
              <div className="text-sm text-gray-600">Ciclo de faturação</div>
            </div>
          </div>

          {subscription.cancelAtPeriodEnd && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Subscrição será cancelada em {new Date(subscription.currentPeriodEnd).toLocaleDateString('pt-PT')}
                  </p>
                  <p className="text-sm text-yellow-700">Pode continuar a usar o serviço até ao final do período pago.</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Plan Features */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Funcionalidades do Plano</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {subscription.plan.features && subscription.plan.features.length > 0 ?
              subscription.plan.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              )) : (
                <div className="text-gray-500">Nenhuma funcionalidade específica listada</div>
              )
            }
          </div>
        </div>

        {/* Apps Addon */}
        <AppsAddonSection />

        {/* Payment History */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Histórico de Pagamentos</h3>
          
          {subscription.payments && subscription.payments.length > 0 ? (
            <div className="space-y-4">
              {subscription.payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center">
                    <CreditCard className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">
                        €{Number(payment.amount).toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')} - 
                        Período: {new Date(payment.periodStart).toLocaleDateString('pt-PT')} a {new Date(payment.periodEnd).toLocaleDateString('pt-PT')}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      payment.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                      payment.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {payment.status === 'COMPLETED' ? 'Pago' :
                       payment.status === 'PENDING' ? Pendente : 'Falhado'}
                    </span>
                    {payment.status === 'PENDING' ? (
                      <div className="flex items-center space-x-2">
                        {payment.multibancoEntity && payment.multibancoReference ? (
                          <div className="text-xs text-gray-600">
                            <div>Entidade: {payment.multibancoEntity}</div>
                            <div>Referência: {payment.multibancoReference}</div>
                          </div>
                        ) : (
                          <button
                            onClick={() => handlePayment(payment.id)}
                            className="px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700"
                          >
                            Pagar
                          </button>
                        )}
                        <div className="relative">
                          <button
                            onClick={() => setShowPaymentOptions(showPaymentOptions === payment.id ? null : payment.id)}
                            className="px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
                          >
                            Alterar método
                          </button>
                          {showPaymentOptions === payment.id && (
                            <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[150px]">
                              <button
                                onClick={() => {
                                  handleChangePaymentMethod(subscription.id, 'card')
                                  setShowPaymentOptions(null)
                                }}
                                className="block w-full text-left px-3 py-2 text-xs hover:bg-gray-50"
                              >
                                Cartão
                              </button>
                              <button
                                onClick={() => {
                                  handleChangePaymentMethod(subscription.id, 'multibanco')
                                  setShowPaymentOptions(null)
                                }}
                                className="block w-full text-left px-3 py-2 text-xs hover:bg-gray-50"
                              >
                                Multibanco
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <button className="text-gray-400 hover:text-gray-600">
                        <Download className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">Nenhum pagamento registado</p>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ações</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              href="/lojista/upgrade"
              className="inline-flex items-center justify-center px-4 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <ArrowUp className="w-5 h-5 mr-2" />
              Fazer Upgrade
            </Link>

            {subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && (
              <button
                onClick={cancelSubscription}
                disabled={isCanceling}
                className="inline-flex items-center justify-center px-4 py-3 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50"
              >
                <AlertTriangle className="w-5 h-5 mr-2" />
                {isCanceling ? 'Cancelando...' : 'Cancelar Subscrição'}
              </button>
            )}
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Precisa de ajuda?</h4>
            <p className="text-sm text-gray-600 mb-3">Entre em contacto connosco se tiver dúvidas sobre a sua subscrição.</p>
            <a
              href="mailto:<EMAIL>"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

// Apps Addon Section Component
function AppsAddonSection() {
  const [installedApps, setInstalledApps] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchInstalledApps()
  }, [])

  const fetchInstalledApps = async () => {
    try {
      const response = await fetch(/api/lojista/apps/installed)
      if (response.ok) {
        const data = await response.json()
        setInstalledApps(data.apps || [])
      
}
    } catch (error) {
      console.error('Erro ao carregar apps instaladas:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const totalAddonCost = installedApps
    .filter(app => app.isPaid)
    .reduce((sum, 'app') => sum + (app.monthlyPrice || 0), 0)

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Apps Instaladas</h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Apps Instaladas</h3>
        <Link
          href="/lojista/appstore"
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Ver App Store
        </Link>
      </div>

      {installedApps.length === 0 ? (
        <div className="text-center py-8">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">Nenhuma app instalada</p>
          <Link
            href="/lojista/appstore"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Explorar Apps
          </Link>
        </div>
      ) : (
        <>
          <div className="space-y-3 mb-6">
            {installedApps.map((app) => (
              <div key={app.appId} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <Package className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{app.name}</div>
                    <div className="text-sm text-gray-500">
                      {app.isPaid ? `€${app.monthlyPrice?.toFixed(2)}/mês` : 'Grátis'}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {app.isTrial && (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      Trial
                    </span>
                  )}
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    app.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {app.isActive ? 'Ativa' : 'Inativa'}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {totalAddonCost > 0 && (
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900">Total Apps (addon 'ao plano'):</span>
                <span className="text-lg font-bold text-gray-900">
                  €{totalAddonCost.toFixed(2)}/mês
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">Este valor será adicionado à sua próxima fatura</p>
            </div>
          )}
        </>
      )}
    </div>
  )
}
