import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Buscar dados de urgência baseados em atividade real
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // Contar reparações agendadas hoje
    const todayRepairs = await prisma.repair.count({
      where: {
        createdAt: {
          gte: yesterday,
          lt: today}
      }
    })

    // Buscar lojas com alta atividade
    const activeShops = await prisma.repairShop.findMany({
      include: {
        _count: {
          select: {
            repairs: {
              where: {
                createdAt: {
                  gte: yesterday}
              }
            }
          }
        },
        profile: true},
      take: 10,
      orderBy: {
        repairs: {
          _count: desc}
      }
    })

    // Buscar lojas com melhor rating
    const topRatedShops = await prisma.repairShop.findMany({
      include: {
        profile: true,
        _count: {
          select: {
            repairs: true}
        }
      },
      where: {
        profile: {
          rating: {
            gte: 4.5
          }
        }
      },
      take: 5,
      orderBy: {
        profile: {
          rating: desc}
      }
    })

    // Gerar notificações de urgência
    const notifications = []

    // Atividade de agendamentos
    if (todayRepairs > 0) {
      notifications.push({
        id: booking-activity,
        type: booking_activity,
        message: `${todayRepairs
} pessoas agendaram reparação hoje`,
        count: todayRepairs,
        priority: todayRepairs > 10 ? 'high' : todayRepairs > 5 ? 'medium' : 'low',
        timeframe: hoje})
    }

    // Lojas populares
    activeShops.slice(0, 3).forEach((shop, index) => {
      if (shop._count.repairs > 0) {
        notifications.push({
          id: `popular-shop-${shop.id}`,
          type: trending_shop,
          message: `${shop.profile?.companyName || Loja
} teve ${shop._count.repairs} reparações hoje`,
          count: shop._count.repairs,
          priority: medium,
          shopName: shop.profile?.companyName || 'Loja',
          location: shop.profile?.address || 'Localização não definida'
        })
      }
    })

    // Lojas com alto rating
    topRatedShops.slice(0, 2).forEach((shop) => {
      notifications.push({
        id: `high-rating-${shop.id}`,
        type: high_rating,
        message: `${shop.profile?.companyName || Loja
} mantém ${shop.profile?.rating || 5}⭐ de rating`,
        percentage: (shop.profile?.rating || 5) * 20, // Converter para percentagem
        priority: low,
        shopName: shop.profile?.companyName || Loja,
        location: shop.profile?.address || 'Localização não definida'
      
})
    })

    // Disponibilidade de lojas (simulado baseado em dados reais)
    const shopAvailability = activeShops.map(shop => {
      const totalSlots = 20 // Slots diários simulados
      const usedSlots = Math.min(shop._count.repairs, 'totalSlots')
      const availableSlots = totalSlots - usedSlots
      
      return {
        shopId: shop.id,
        shopName: shop.profile?.companyName || 'Loja',
        location: shop.profile?.address || 'Localização não definida',
        availableSlots,
        totalSlots,
        rating: shop.profile?.rating || 4.0,
        recentBookings: shop._count.repairs,
        isPopular: shop._count.repairs > 5
      
}
    })

    return NextResponse.json({
      notifications: notifications.slice(0, 10), // Limitar a 10 notificações
      shopAvailability: shopAvailability.slice(0, 8) // Limitar a 8 lojas
})

  } catch (error) {
    console.error('Erro ao buscar dados de urgência:', 'error')
    
    // Retornar dados mock em caso de erro
    return NextResponse.json({
      notifications: [
        {
          id: 1,
          type: booking_activity,
          message: '12 pessoas agendaram reparação de iPhone hoje',
          count: 12,
          priority: high,
          timeframe: hoje
},
        {
          id: '2',
          type: limited_slots,
          message: 'Apenas 3 vagas disponíveis para hoje em Lisboa',
          count: 3,
          priority: high,
          location: Lisboa},
        {
          id: '3',
          type: high_rating,
          message: 'TechFix Lisboa mantém 4.9⭐ de rating',
          percentage: 98,
          priority: medium,
          shopName: 'TechFix Lisboa',
          location: Lisboa}
      ],
      shopAvailability: [
        {
          shopId: '1',
          shopName: 'TechFix Lisboa',
          location: Lisboa,
          availableSlots: 3,
          totalSlots: 20,
          rating: 4.9,
          recentBookings: 17,
          isPopular: true},
        {
          shopId: '2',
          shopName: 'RepairPro Porto',
          location: Porto,
          availableSlots: 8,
          totalSlots: 20,
          rating: 4.7,
          recentBookings: 12,
          isPopular: true}
      ]
    })
  }
}
