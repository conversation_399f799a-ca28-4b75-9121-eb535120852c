import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      // Buscar atividades recentes (últimas 24 'horas')
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      
      const activities = await prisma.viralActivity.findMany({
        where: {
          createdAt: {
            gte: twentyFourHoursAgo
},
          isPublic: true},
        orderBy: {
          createdAt: desc},
        take: 50,
        include: {
          user: {
            select: {
              name: true}
          }
        }
      })

      // Formatar atividades para o frontend
      const formattedActivities = activities.map(activity => ({
        id: activity.id,
        type: activity.type.toLowerCase(),
        description: activity.description,
        location: activity.location,
        timeAgo: getTimeAgo(activity.createdAt),
        userName: activity.user?.name
      }))

      return NextResponse.json(formattedActivities)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao buscar atividades:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" 
},
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, type, description, location, metadata } = await request.json()

    if (!type || !description) {
      return NextResponse.json(
        { error: "Tipo e descrição são obrigatórios" },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      const activity = await prisma.viralActivity.create({
        data: {
          userId,
          type: type.toUpperCase(),
          description,
          location,
          metadata,
          isPublic: true
}
      })

      return NextResponse.json(activity)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao criar atividade:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Função auxiliar para calcular tempo decorrido
function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return Agora mesmo
  
} else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} min atrás`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}h atrás`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}d atrás`
  }
}
