'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Home,
  Package,
  Package2,
  PanelLeft,
  Settings,
  ShoppingCart,
  Users2,
  Store,
  Trophy,
  BarChart3,
  Calendar,
  MessageSquare } from 'lucide-react'

import { Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger } from '@/components/ui/tooltip'
import UserDropdown from '@/components/UserDropdown'
import NotificationDropdown from '@/components/NotificationDropdown'

import { SearchInput } from '@/components/ui/search'

interface LojistaDashboardLayoutProps {
  children: React.ReactNode
}

export default function LojistaDashboardLayout({ 'children'}: LojistaDashboardLayoutProps) {
  const { data: session } = useSession()

  return (
    <TooltipProvider>
      <main className="flex min-h-screen w-full flex-col bg-muted/40">
        <DesktopNav />
        <div className="flex flex-col sm:gap-4 sm:py-4 sm:pl-14">
          <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6">
            <MobileNav />
            <DashboardBreadcrumb />
            <div className="relative ml-auto flex-1 md:grow-0">
              <SearchInput placeholder="Pesquisar..." />
            </div>
            <NotificationDropdown />
            <UserDropdown user={session?.user} />
          </header>
          <main className="grid flex-1 items-start gap-2 p-4 sm:px-6 sm:py-0 md:gap-4 bg-muted/40">
            {children}
          </main>
        </div>
      </main>
    </TooltipProvider>
  )
}

function DesktopNav() {
  const pathname = usePathname()

  const navItems = [
    { href: '/lojista', label: Dashboard, icon: Home},
    { href: '/lojista/reparacoes', label: 'Reparações', icon: Package},
    { href: '/lojista/loja-online', label: 'Loja Online', icon: Store},
    { href: '/lojista/clientes', label: Clientes, icon: Users2 },
    { href: '/lojista/agenda', label: Agenda, icon: Calendar},
    { href: '/lojista/chat', label: Chat, icon: MessageSquare},
    { href: '/lojista/revify-rewards', label: 'Revify Rewards', icon: Trophy},
    { href: '/lojista/relatorios', label: 'Relatórios', icon: BarChart3 },
  ]

  return (
    <aside className="fixed inset-y-0 left-0 z-10 hidden w-14 flex-col border-r bg-background sm:flex">
      <nav className="flex flex-col items-center gap-4 px-2 sm:py-5">
        <Link
          href="/lojista"
          className="group flex h-9 w-9 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:h-8 md:w-8 md:text-base"
        >
          <Store className="h-3 w-3 transition-all group-hover:scale-110" />
          <span className="sr-only">Revify Lojista</span>
        </Link>

        {navItems.map((item) => (
          <NavItem 
            key={item.href} 
            href={item.href} 
            label={item.label}
            isActive={pathname === item.href || pathname.startsWith(item.href + '/')}
          >
            <item.icon className="h-5 w-5" />
          </NavItem>
        ))}
      </nav>
      <nav className="mt-auto flex flex-col items-center gap-4 px-2 sm:py-5">
        <Tooltip>
          <TooltipTrigger asChild>
            <Link
              href="/lojista/configuracoes"
              className="flex h-9 w-9 items-center justify-center rounded-lg text-muted-foreground transition-colors hover:text-foreground md:h-8 md:w-8"
            >
              <Settings className="h-5 w-5" />
              <span className="sr-only">Settings</span>
            </Link>
          </TooltipTrigger>
          <TooltipContent side="right">
            Configurações
          </TooltipContent>
        </Tooltip>
      </nav>
    </aside>
  )
}

function MobileNav() {
  const pathname = usePathname()

  const navItems = [
    { href: '/lojista', label: Dashboard, icon: Home},
    { href: '/lojista/reparacoes', label: 'Reparações', icon: Package},
    { href: '/lojista/loja-online', label: 'Loja Online', icon: Store},
    { href: '/lojista/clientes', label: Clientes, icon: Users2 },
    { href: '/lojista/agenda', label: Agenda, icon: Calendar},
    { href: '/lojista/chat', label: Chat, icon: MessageSquare},
    { href: '/lojista/revify-rewards', label: 'Revify Rewards', icon: Trophy},
    { href: '/lojista/relatorios', label: 'Relatórios', icon: BarChart3 },
    { href: '/lojista/configuracoes', label: 'Configurações', icon: Settings},
  ]

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button size="icon" variant="outline" className="sm:hidden">
          <PanelLeft className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="sm:max-w-xs">
        <nav className="grid gap-6 text-lg font-medium">
          <Link
            href="/lojista"
            className="group flex h-10 w-10 shrink-0 items-center justify-center gap-2 rounded-full bg-primary text-lg font-semibold text-primary-foreground md:text-base"
          >
            <Store className="h-5 w-5 transition-all group-hover:scale-110" />
            <span className="sr-only">Revify Lojista</span>
          </Link>
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center gap-4 px-2.5 ${
                pathname === item.href || pathname.startsWith(item.href + '/')
                  ? 'text-foreground' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <item.icon className="h-5 w-5" />
              {item.label}
            </Link>
          ))}
        </nav>
      </SheetContent>
    </Sheet>
  )
}

function NavItem({ 
  href, 
  label, 
  children, isActive }: { 
  href: string
  label: string
  children: React.ReactNode
  isActive?: boolean}) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Link
          href={href}
          className={`flex h-9 w-9 items-center justify-center rounded-lg transition-colors md:h-8 md:w-8 ${
            isActive
              ? 'bg-accent text-accent-foreground'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          {children}
          <span className="sr-only">{label}</span>
        </Link>
      </TooltipTrigger>
      <TooltipContent side="right">
        {label}
      </TooltipContent>
    </Tooltip>
  )
}

function DashboardBreadcrumb() {
  const pathname = usePathname()
  const segments = pathname.split('/').filter(Boolean)

  const capitalizeTitle = (segment: string) => {
    // Mapeamento de títulos específicos
    const titleMap: Record<string, string> = {
      appstore: 'App Store',
      'precos': 'Preços',
      'configuracoes': 'Configurações',
      'relatorios': 'Relatórios',
      'reparacoes': 'Reparações',
      'produtos': 'Produtos',
      'encomendas': 'Encomendas',
      'clientes': 'Clientes',
      'analytics': 'Analytics',
      'loja-online': 'Loja Online',
      'loja-pecas': 'Loja de Peças'
    
}

    return titleMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
  }

  return (
    <Breadcrumb className="hidden md:flex">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/lojista">
              Lojista
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {segments.length > 1 && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {capitalizeTitle(segments[segments.length - 1])}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
