'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Save, User, Phone, FileText, ArrowLeft } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
interface CustomerProfile {
  customerName: string
  customerPhone: string
  customerNif: string}

export default function ClientePerfilPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  
  const [profileData, setProfileData] = useState<CustomerProfile>({
    customerName: '',
    customerPhone: '',
    customerNif: ''
  })

  useEffect(() => {
    fetchProfile()
  }, [])

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/cliente/profile')
      if (response.ok) {
        const profile = await response.json()
        setProfileData({
          customerName: profile.customerName || '',
          customerPhone: profile.customerPhone || '',
          customerNif: profile.customerNif || ''
        })
      }
    } catch (error) {
      console.error('Erro ao carregar perfil:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/cliente/profile', {
        method: PUT,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileData)
      })

      if (response.ok) {
        alert('Perfil atualizado com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar perfil')
      }
    } catch (error) {
      console.error('Erro ao salvar perfil:', 'error')
      alert('Erro ao salvar perfil')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/cliente" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Meu Perfil</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href="/cliente" className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Voltar ao Dashboard
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Meu Perfil</h1>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'A guardar...' : 'Guardar'}
            </button>
          </div>

          <div className="space-y-6">
            {/* Informações Pessoais */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />Informações Pessoais</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nome Completo *</label>
                  <input
                    type="text"
                    value={profileData.customerName}
                    onChange={(e) => setProfileData(prev => ({ ...prev, customerName: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    placeholder="Seu nome completo"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Telefone *</label>
                  <input
                    type="tel"
                    value={profileData.customerPhone}
                    onChange={(e) => setProfileData(prev => ({ ...prev, customerPhone: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                    placeholder="+351 xxx xxx xxx"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  NIF (opcional)
                </label>
                <input
                  type="text"
                  value={profileData.customerNif}
                  onChange={(e) => setProfileData(prev => ({ ...prev, customerNif: e.target.value }))}
                  className="block w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  placeholder="123456789"
                />
                <p className="text-sm text-gray-500 mt-1">Necessário para fatura com dados fiscais</p>
              </div>
            </div>

            {/* Informações da Conta */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />Informações da Conta</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Email:</span>
                    <p className="text-gray-600">{session?.user?.email}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Tipo de Conta:</span>
                    <p className="text-gray-600">Cliente</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Membro desde:</span>
                    <p className="text-gray-600">{new Date().toLocaleDateString('pt-PT')}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Status:</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Ativo</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Links Rápidos */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Links Rápidos</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link
                  href="/cliente/enderecos"
                  className="p-4 border border-gray-300 rounded-lg hover:border-blue-600 hover:bg-blue-50 transition-colors"
                >
                  <h3 className="font-medium text-gray-900 mb-1">Gerir Endereços</h3>
                  <p className="text-sm text-gray-600">Adicionar e editar endereços de entrega</p>
                </Link>
                <Link
                  href="/cliente/reparacoes"
                  className="p-4 border border-gray-300 rounded-lg hover:border-blue-600 hover:bg-blue-50 transition-colors"
                >
                  <h3 className="font-medium text-gray-900 mb-1">Minhas Reparações</h3>
                  <p className="text-sm text-gray-600">Ver histórico de reparações</p>
                </Link>
              </div>
            </div>
          </div>

          {/* Botão Salvar */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="w-full md:w-auto inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 font-medium"
            >
              <Save className="w-5 h-5 mr-2" />
              {isSaving ? 'A guardar perfil...' : 'Guardar Perfil'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
