# 🌍 Guia de Migração: Sistema de Traduções 100% DeepL

## 📋 Visão Geral

Este guia mostra como migrar o sistema atual de traduções para usar **100% DeepL API**, eliminando a necessidade de manter arquivos de tradução estáticos.

## 🚀 Vantagens do Sistema 100% DeepL

### ✅ **Benefícios:**
- **Sem manutenção manual**: Não precisa traduzir textos manualmente
- **Sempre atualizado**: Novas funcionalidades são traduzidas automaticamente
- **Consistência**: Todas as traduções vêm da mesma fonte (DeepL)
- **Economia de tempo**: Desenvolvedores focam no código, não nas traduções
- **Qualidade superior**: DeepL oferece traduções mais naturais

### 💰 **Custo com API Free:**
- **500.000 caracteres/mês** gratuitos
- Suficiente para uma plataforma média
- Monitoramento automático de uso

## 🔧 Implementação

### 1. **Componente AutoTranslate**

Use o componente `AutoTranslate` para traduzir qualquer texto:

```tsx
import AutoTranslate from '@/components/ui/AutoTranslate'

// Texto simples
<AutoTranslate text="Reparações de dispositivos" />

// Com elemento HTML específico
<AutoTranslate text="Título Principal" as="h1" className="text-2xl" />

// Com fallback
<AutoTranslate text="Texto" fallback="Texto padrão" />
```

### 2. **Hook useTranslation Atualizado**

```tsx
import { useTranslation } from '@/hooks/useTranslation'

function MeuComponente() {
  const { t, tSync, tBatch } = useTranslation()
  
  // Tradução assíncrona
  const traduzirTexto = async () => {
    const resultado = await t('Texto para traduzir')
    setTexto(resultado)
  }
  
  // Tradução síncrona (se já estiver em cache)
  const textoTraduzido = tSync('Texto em cache')
  
  // Tradução em lote (mais eficiente)
  const traduzirLista = async () => {
    const textos = ['Texto 1', 'Texto 2', 'Texto 3']
    const traduzidos = await tBatch(textos)
    setListaTraduzida(traduzidos)
  }
}
```

### 3. **Listas com AutoTranslateList**

```tsx
import { AutoTranslateList } from '@/components/ui/AutoTranslate'

<AutoTranslateList
  items={produtos}
  textExtractor={(produto) => produto.nome}
  renderItem={(produto, nomeTrauzido) => (
    <div key={produto.id}>
      <h3>{nomeTrauzido}</h3>
      <AutoTranslate text={produto.descricao} />
    </div>
  )}
/>
```

## 📝 Migração Passo a Passo

### **Passo 1: Substituir Traduções Estáticas**

**Antes:**
```tsx
const { t } = useTranslation()
return <h1>{t('marketplace')}</h1>
```

**Depois:**
```tsx
return <h1><AutoTranslate text="Marketplace" /></h1>
```

### **Passo 2: Migrar Listas de Produtos**

**Antes:**
```tsx
{produtos.map(produto => (
  <div key={produto.id}>
    <h3>{produto.nome}</h3>
    <p>{produto.descricao}</p>
  </div>
))}
```

**Depois:**
```tsx
<AutoTranslateList
  items={produtos}
  textExtractor={(p) => p.nome}
  renderItem={(produto, nomeTrauzido) => (
    <div key={produto.id}>
      <h3>{nomeTrauzido}</h3>
      <AutoTranslate text={produto.descricao} as="p" />
    </div>
  )}
/>
```

### **Passo 3: Otimizar Performance**

```tsx
// Para textos que mudam frequentemente
const { tBatch } = useTranslation()

useEffect(() => {
  const traduzirTodos = async () => {
    const textos = produtos.map(p => p.nome)
    const traduzidos = await tBatch(textos)
    // Usar traduzidos...
  }
  traduzirTodos()
}, [produtos])
```

## 🎯 Exemplos Práticos

### **1. Header de Navegação**

```tsx
function Header() {
  return (
    <nav>
      <AutoTranslate text="Marketplace" as="a" href="/marketplace" />
      <AutoTranslate text="Central de Ajuda" as="a" href="/ajuda" />
      <AutoTranslate text="Contactos" as="a" href="/contactos" />
    </nav>
  )
}
```

### **2. Formulários**

```tsx
function FormularioReparacao() {
  return (
    <form>
      <label>
        <AutoTranslate text="Nome completo" />
        <input type="text" placeholder={tSync("Digite seu nome")} />
      </label>
      
      <label>
        <AutoTranslate text="Descrição do problema" />
        <textarea placeholder={tSync("Descreva o problema")} />
      </label>
      
      <button type="submit">
        <AutoTranslate text="Enviar solicitação" />
      </button>
    </form>
  )
}
```

### **3. Tabelas de Dados**

```tsx
function TabelaProdutos({ produtos }) {
  return (
    <table>
      <thead>
        <tr>
          <th><AutoTranslate text="Nome" /></th>
          <th><AutoTranslate text="Preço" /></th>
          <th><AutoTranslate text="Stock" /></th>
          <th><AutoTranslate text="Ações" /></th>
        </tr>
      </thead>
      <tbody>
        {produtos.map(produto => (
          <tr key={produto.id}>
            <td><AutoTranslate text={produto.nome} /></td>
            <td>€{produto.preco}</td>
            <td>{produto.stock}</td>
            <td>
              <button><AutoTranslate text="Editar" /></button>
              <button><AutoTranslate text="Eliminar" /></button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}
```

## 📊 Monitoramento de Uso

### **Verificar Uso da API:**

```tsx
// Adicionar ao dashboard admin
function ApiUsageMonitor() {
  const [usage, setUsage] = useState(null)
  
  useEffect(() => {
    // Implementar endpoint para verificar uso da API DeepL
    fetch('/api/deepl/usage')
      .then(res => res.json())
      .then(setUsage)
  }, [])
  
  return (
    <div className="bg-blue-50 p-4 rounded">
      <h3>Uso da API DeepL</h3>
      <p>Caracteres usados: {usage?.used || 0} / 500.000</p>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full" 
          style={{ width: `${(usage?.used / 500000) * 100}%` }}
        />
      </div>
    </div>
  )
}
```

## 🚨 Considerações Importantes

### **1. Cache Inteligente**
- O sistema mantém cache de traduções para evitar chamadas desnecessárias
- Cache persiste durante a sessão do usuário
- Textos comuns são pré-carregados

### **2. Fallbacks**
- Se a API falhar, mostra o texto original
- Fallbacks personalizados podem ser definidos
- Sistema gracioso que nunca quebra a interface

### **3. Performance**
- Use `tBatch()` para traduzir múltiplos textos
- `AutoTranslateList` otimiza listas automaticamente
- Cache reduz latência significativamente

### **4. Limites da API**
- Monitore o uso mensal
- Implemente alertas quando próximo do limite
- Considere upgrade para plano pago se necessário

## 🎉 Resultado Final

Com este sistema, você terá:

- ✅ **Zero manutenção** de arquivos de tradução
- ✅ **Traduções automáticas** de qualquer texto
- ✅ **Performance otimizada** com cache inteligente
- ✅ **Interface consistente** em todos os idiomas
- ✅ **Escalabilidade** para novos idiomas
- ✅ **Qualidade superior** das traduções

**A plataforma ficará verdadeiramente internacional com mínimo esforço!** 🌍
