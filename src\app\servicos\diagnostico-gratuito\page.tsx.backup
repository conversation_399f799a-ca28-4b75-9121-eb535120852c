'use client'

import Link from 'next/link'
import {
  Search, CheckCircle, Clock,
  Shield, Wrench, Smartphone, Laptop, Tablet,
  ArrowRight, Phone, FileText, Zap
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import MainHeader from '@/components/MainHeader'
import Footer from '@/components/Footer'

export default function FreeDiagnosticPage() {
  const { t } = useTranslation()

  const diagnosticSteps = [
    {
      step: 1,
      icon: <Search className="w-8 h-8" />,
      title: 'Análise Inicial',
      description: 'Exame visual completo do dispositivo para identificar danos externos',
      time: '5 min'
    },
    {
      step: 2,
      icon: <Zap className="w-8 h-8" />,
      title: 'Teste de Funcionamento',
      description: 'Verificação de todas as funções básicas: ecrã, som, conectividade',
      time: '10 min'
    },
    {
      step: 3,
      icon: <FileText className="w-8 h-8" />,
      title: 'Relatório Detalhado',
      description: 'Documento completo com problemas identificados e soluções propostas',
      time: '5 min'
    }
  ]

  const deviceTypes = [
    {
      icon: <Smartphone className="w-12 h-12" />,
      title: 'Smartphones',
      description: 'iPhone, Samsung, Huawei, Xiaomi e todas as marcas',
      features: ['Teste de ecrã e touch', 'Verificação de bateria', 'Teste de câmaras', 'Conectividade']
    },
    {
      icon: <Laptop className="w-12 h-12" />,
      title: 'Laptops',
      description: 'MacBook, Dell, HP, Lenovo e outros portáteis',
      features: ['Teste de arranque', 'Verificação de disco', 'Teste de bateria', 'Portas e conectores']
    },
    {
      icon: <Tablet className="w-12 h-12" />,
      title: 'Tablets',
      description: 'iPad, Samsung Galaxy Tab, Surface e mais',
      features: ['Teste táctil', 'Verificação de bateria', 'Teste de som', 'Conectividade WiFi']
    }
  ]

  const benefits = [
    'Diagnóstico 100% gratuito sem compromisso',
    'Relatório detalhado por escrito',
    'Orçamento transparente e sem surpresas',
    'Técnicos certificados e experientes',
    'Sem custos ocultos ou taxas adicionais',
    'Processo rápido em apenas 20 minutos'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-600 via-green-600 to-teal-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Search className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('freeDiagnosticTitle')}
            </h1>
            <p className="text-xl text-emerald-100 max-w-3xl mx-auto mb-8">
              {t('freeDiagnosticSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/simular-reparacao"
                className="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Search className="w-5 h-5" />
                <span>Agendar Diagnóstico</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/contactos"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Falar Connosco</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Como Funciona o Diagnóstico
            </h2>
            <p className="text-xl text-gray-600">
              Processo simples e transparente em 3 passos
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {diagnosticSteps.map((step) => (
              <div key={step.step} className="text-center">
                <div className="w-20 h-20 bg-emerald-100 rounded-3xl flex items-center justify-center mx-auto mb-6 text-emerald-600">
                  {step.icon}
                </div>
                <div className="w-8 h-8 bg-emerald-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-lg font-bold">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                <p className="text-gray-600 mb-4">{step.description}</p>
                <div className="flex items-center justify-center text-emerald-600 font-medium">
                  <Clock className="w-4 h-4 mr-1" />
                  {step.time}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Device Types */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Diagnóstico para Todos os Dispositivos
            </h2>
            <p className="text-xl text-gray-600">
              Análise especializada para cada tipo de equipamento
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {deviceTypes.map((device, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mb-6 text-emerald-600">
                  {device.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{device.title}</h3>
                <p className="text-gray-600 mb-6">{device.description}</p>
                <div className="space-y-3">
                  {device.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-emerald-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Porque Escolher o Nosso Diagnóstico?
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-emerald-500 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8">
                <Link
                  href="/simular-reparacao"
                  className="bg-emerald-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-emerald-700 transition-colors inline-flex items-center space-x-2"
                >
                  <Search className="w-5 h-5" />
                  <span>Agendar Agora</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Shield className="w-8 h-8 text-emerald-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  100% Gratuito
                </h3>
                <p className="text-gray-600 mb-6">
                  O nosso diagnóstico é completamente gratuito e sem compromisso. 
                  Só paga se decidir avançar com a reparação.
                </p>
                <div className="bg-white rounded-xl p-4">
                  <div className="text-3xl font-bold text-emerald-600 mb-2">€0</div>
                  <div className="text-gray-600">Diagnóstico completo</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Perguntas Frequentes
            </h2>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Quanto tempo demora o diagnóstico?
              </h3>
              <p className="text-gray-600">
                O diagnóstico completo demora aproximadamente 20 minutos. Em casos mais complexos, 
                pode demorar até 30 minutos para garantir uma análise completa.
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                O diagnóstico é mesmo gratuito?
              </h3>
              <p className="text-gray-600">
                Sim, o diagnóstico é 100% gratuito e sem compromisso. Só paga se decidir 
                avançar com a reparação após receber o orçamento.
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Preciso de agendar?
              </h3>
              <p className="text-gray-600">
                Recomendamos agendar para garantir disponibilidade imediata, mas também 
                aceitamos diagnósticos sem marcação (sujeito a disponibilidade).
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Pronto para Descobrir o Problema?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Diagnóstico gratuito em apenas 20 minutos
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/simular-reparacao"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Search className="w-5 h-5" />
              <span>Agendar Diagnóstico</span>
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
