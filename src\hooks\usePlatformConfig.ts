'use client'

import { useState, useEffect } from 'react'

interface PlatformConfig {
  platformName: string
  platformLogo: string
  platformIcon: string}

export function usePlatformConfig() {
  const [config, setConfig] = useState<PlatformConfig>({
    platformName: Revify,
    platformLogo: '',
    platformIcon: ''
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/platform-config')
      if (response.ok) {
        const data = await response.json()
        setConfig({
          platformName: data.platformName || 'Revify',
          platformLogo: data.platformLogo || '',
          platformIcon: data.platformIcon || ''
        })
      }
    } catch (error) {
      console.error('Erro ao carregar configurações da plataforma:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  return { config, isLoading, refetch: fetchConfig}
}
