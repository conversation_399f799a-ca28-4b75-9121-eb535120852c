import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { appId } = await request.json()

    if (!appId) {
      return NextResponse.json(
        { message: 'ID da app é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar definição da app
    const appDefinition = await prisma.$queryRaw`
      SELECT * FROM app_definitions
      WHERE "appId" = ${appId}
      AND "isActive" = true
      AND "isPaid" = true
      LIMIT 1
    ` as any[]

    if (appDefinition.length === 0) {
      return NextResponse.json(
        { message: 'App não encontrada ou não é paga' },
        { status: 404 }
      )
    }

    const app = appDefinition[0]

    // Verificar se já tem subscrição ativa para esta app
    const existingInstall = await prisma.$queryRaw`
      SELECT * FROM installed_apps
      WHERE "userId" = ${session.user.id}
      AND "appId" = ${appId}
      AND ("isPaid" = true OR ("isTrialActive" = true AND "trialEndDate" > NOW()))
      LIMIT 1
    ` as any[]

    if (existingInstall.length > 0 && existingInstall[0].isPaid) {
      return NextResponse.json(
        { message: 'App já está subscrita' },
        { status: 400 }
      )
    }

    // Buscar chave do Stripe das configurações do admin
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
      return NextResponse.json(
        { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
        { status: 400 }
      )
    }

    // Criar subscrição no Stripe
    const stripe = await createStripeInstance(stripeSecretKey)

    // Buscar ou criar customer no Stripe
    let stripeCustomerId = session.user.stripeCustomerId

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: session.user.email || '',
        name: session.user.name || '',
        metadata: {
          userId: session.user.id
        }
      })
      stripeCustomerId = customer.id

      // Salvar customer ID no usuário
      await prisma.user.update({
        where: { id: session.user.id },
        data: { stripeCustomerId: stripeCustomerId }
      })
    }

    // Criar produto no Stripe se não existir
    let stripeProduct
    try {
      stripeProduct = await stripe.products.retrieve(`app_${appId}`)
    } catch (error) {
      stripeProduct = await stripe.products.create({
        id: `app_${appId}`,
        name: `App: ${app.name}`,
        description: app.description || `Subscrição mensal para ${app.name}`,
        metadata: {
          appId: appId,
          type: 'app_addon'
        }
      })
    }

    // Criar preço no Stripe se não existir
    let stripePrice
    try {
      const prices = await stripe.prices.list({
        product: stripeProduct.id,
        active: true,
        type: 'recurring'
      })
      stripePrice = prices.data[0]
    } catch (error) {
      // Criar preço se não existir
    }

    if (!stripePrice) {
      stripePrice = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: Math.round(Number(app.monthlyPrice) * 100), // Converter para centavos
        currency: 'eur',
        recurring: {
          interval: 'month'
        },
        metadata: {
          appId: appId
        }
      })
    }

    // Verificar se já tem subscrição ativa para adicionar à próxima fatura
    const existingSubscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id }
    })

    if (existingSubscription && existingSubscription.status === 'ACTIVE') {
      // Adicionar addon à próxima fatura em vez de criar nova subscrição
      await prisma.pendingAddon.create({
        data: {
          userId: session.user.id,
          appId: appId,
          monthlyPrice: Number(app.monthlyPrice),
          addedAt: new Date(),
          nextBillingDate: existingSubscription.currentPeriodEnd
        }
      })

      // Atualizar instalação da app
      await prisma.$queryRaw`
        INSERT INTO installed_apps (
          "id", "userId", "appId", "isActive", "installedAt",
          "isTrialActive", "isPaid", "pendingBilling"
        )
        VALUES (
          gen_random_uuid(), ${session.user.id}, ${appId}, true, NOW(),
          false, false, true
        )
        ON CONFLICT ("userId", "appId")
        DO UPDATE SET
          "isActive" = true,
          "isPaid" = false,
          "pendingBilling" = true,
          "isTrialActive" = false,
          "trialStartDate" = NULL,
          "trialEndDate" = NULL
      `

      return NextResponse.json({
        message: 'Addon será adicionado à próxima fatura',
        nextBillingDate: existingSubscription.currentPeriodEnd,
        amount: Number(app.monthlyPrice)
      })
    }

    // Se não tem subscrição ativa, criar nova subscrição
    const subscription = await stripe.subscriptions.create({
      customer: stripeCustomerId,
      items: [{
        price: stripePrice.id
      }],
      metadata: {
        userId: session.user.id,
        appId: appId,
        type: 'app_addon'
      },
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent']
    })

    // Atualizar ou criar instalação da app
    await prisma.$queryRaw`
      INSERT INTO installed_apps (
        "id", "userId", "appId", "isActive", "installedAt",
        "isTrialActive", "isPaid", "addonSubscriptionId", "nextBillingDate"
      )
      VALUES (
        gen_random_uuid(), ${session.user.id}, ${appId}, true, NOW(),
        false, true, ${subscription.id}, ${new Date(subscription.current_period_end * 1000)}
      )
      ON CONFLICT ("userId", "appId")
      DO UPDATE SET 
        "isActive" = true,
        "isPaid" = true,
        "isTrialActive" = false,
        "trialStartDate" = NULL,
        "trialEndDate" = NULL,
        "addonSubscriptionId" = ${subscription.id},
        "nextBillingDate" = ${new Date(subscription.current_period_end * 1000)}
    `

    const invoice = subscription.latest_invoice as any
    const paymentIntent = invoice.payment_intent

    return NextResponse.json({
      message: 'Subscrição criada com sucesso',
      subscriptionId: subscription.id,
      clientSecret: paymentIntent.client_secret,
      status: subscription.status
    })

  } catch (error) {
    console.error('Erro ao criar subscrição addon:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
