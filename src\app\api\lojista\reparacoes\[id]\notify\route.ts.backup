import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendRepairStatusUpdateEmail } from '@/lib/email-service'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { message, subject } = await request.json()

    // Verificar se a reparação existe e pertence ao lojista
    const repair = await prisma.repair.findFirst({
      where: {
        id: params.id,
        repairShopId: session.user.id
      },
      include: {
        brand: true,
        problemType: true,
        repairShop: {
          include: {
            profile: true
          }
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    if (!repair.customerEmail) {
      return NextResponse.json(
        { message: 'Cliente não tem email cadastrado' },
        { status: 400 }
      )
    }

    // Enviar email de notificação personalizada
    let emailSent = false
    try {
      emailSent = await sendRepairStatusUpdateEmail({
        customerName: repair.customerName,
        customerEmail: repair.customerEmail,
        trackingCode: repair.trackingCode,
        deviceBrand: repair.brand?.name || repair.deviceBrand || 'N/A',
        deviceModel: repair.deviceModel || 'N/A',
        problemType: repair.problemType?.name || 'N/A',
        status: repair.status,
        shopName: repair.repairShop.name,
        shopPhone: repair.repairShop.profile?.phone || undefined,
        shopEmail: repair.repairShop.email,
        notes: message || undefined
      })

      // Registrar a notificação no histórico da reparação
      if (emailSent) {
        await prisma.repair.update({
          where: { id: params.id },
          data: {
            notes: repair.notes 
              ? `${repair.notes}\n\n[${new Date().toLocaleString('pt-PT')}] Notificação enviada: ${message || 'Atualização de status'}`
              : `[${new Date().toLocaleString('pt-PT')}] Notificação enviada: ${message || 'Atualização de status'}`
          }
        })
      }
    } catch (error) {
      console.error('Erro ao enviar notificação:', error)
      return NextResponse.json(
        { message: 'Erro ao enviar notificação por email' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: emailSent ? 'Notificação enviada com sucesso' : 'Falha ao enviar notificação',
      emailSent,
      customerEmail: repair.customerEmail
    })

  } catch (error) {
    console.error('Erro ao enviar notificação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
