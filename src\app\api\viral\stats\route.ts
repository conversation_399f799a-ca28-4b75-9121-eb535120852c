import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      // Calcular datas para comparações
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      const lastWeek = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000)
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)

      // Estatísticas de usuários
      const totalUsers = await prisma.user.count()
      const newUsersToday = await prisma.user.count({
        where: { createdAt: { gte: today
} }
      })
      const newUsersYesterday = await prisma.user.count({
        where: { 
          createdAt: { 
            gte: yesterday,
            lt: today}
        }
      })

      // Estatísticas de referrals
      const totalReferrals = await prisma.referral.count()
      const completedReferrals = await prisma.referral.count({
        where: { status: COMPLETED}
      })
      const pendingReferrals = await prisma.referral.count({
        where: { status: PENDING}
      })
      const referralsThisWeek = await prisma.referral.count({
        where: { createdAt: { gte: thisWeek} }
      })
      const referralsLastWeek = await prisma.referral.count({
        where: { 
          createdAt: { 
            gte: lastWeek,
            lt: thisWeek}
        }
      })

      // Estatísticas de badges
      const totalBadges = await prisma.badge.count()
      const totalUserBadges = await prisma.userBadge.count()
      const badgesThisMonth = await prisma.userBadge.count({
        where: { earnedAt: { gte: thisMonth} }
      })
      const badgesLastMonth = await prisma.userBadge.count({
        where: { 
          earnedAt: { 
            gte: lastMonth,
            lt: thisMonth}
        }
      })

      // Estatísticas de atividades virais
      const totalActivities = await prisma.viralActivity.count()
      const activitiesToday = await prisma.viralActivity.count({
        where: { createdAt: { gte: today} }
      })
      const activitiesYesterday = await prisma.viralActivity.count({
        where: { 
          createdAt: { 
            gte: yesterday,
            lt: today}
        }
      })

      // Estatísticas de conteúdo partilhável
      const totalContent = await prisma.shareableContent.count()
      const contentThisWeek = await prisma.shareableContent.count({
        where: { createdAt: { gte: thisWeek} }
      })
      const contentLastWeek = await prisma.shareableContent.count({
        where: { 
          createdAt: { 
            gte: lastWeek,
            lt: thisWeek}
        }
      })

      // Calcular total de engajamento (likes, shares, views)
      const engagementStats = await prisma.shareableContent.aggregate({
        _sum: {
          likes: true,
          shares: true,
          views: true,
          comments: true
}
      })

      // Calcular recompensas totais pagas
      const totalRewards = await prisma.referral.aggregate({
        where: { status: COMPLETED},
        _sum: {
          reward: true}
      })

      // Top referrers
      const topReferrers = await prisma.user.findMany({
        include: {
          referrals: {
            where: { status: COMPLETED}
          },
          _count: {
            select: {
              referrals: true}
          }
        },
        orderBy: {
          referrals: {
            _count: desc}
        },
        take: 5
      })

      // Atividades por tipo
      const activitiesByType = await prisma.viralActivity.groupBy({
        by: [type],
        _count: {
          type: true
},
        where: {
          createdAt: { gte: thisWeek}
        }
      })

      // Calcular taxas de crescimento
      const userGrowthRate = newUsersYesterday > 0 
        ? ((newUsersToday - newUsersYesterday) / newUsersYesterday * 100)
        : (newUsersToday > 0 ? 100 : 0)

      const referralGrowthRate = referralsLastWeek > 0
        ? ((referralsThisWeek - 'referralsLastWeek') / referralsLastWeek * 100)
        : (referralsThisWeek > 0 ? 100 : 0)

      const activityGrowthRate = activitiesYesterday > 0
        ? ((activitiesToday - 'activitiesYesterday') / activitiesYesterday * 100)
        : (activitiesToday > 0 ? 100 : 0)

      const contentGrowthRate = contentLastWeek > 0
        ? ((contentThisWeek - 'contentLastWeek') / contentLastWeek * 100)
        : (contentThisWeek > 0 ? 100 : 0)

      const badgeGrowthRate = badgesLastMonth > 0
        ? ((badgesThisMonth - 'badgesLastMonth') / badgesLastMonth * 100)
        : (badgesThisMonth > 0 ? 100 : 0)

      const response = {
        overview: {
          totalUsers,
          newUsersToday,
          userGrowthRate: Math.round(userGrowthRate * 100) / 100,
          totalReferrals,
          referralGrowthRate: Math.round(referralGrowthRate * 100) / 100,
          totalActivities,
          activityGrowthRate: Math.round(activityGrowthRate * 100) / 100,
          totalContent,
          contentGrowthRate: Math.round(contentGrowthRate * 100) / 100,
          totalBadgesEarned: totalUserBadges,
          badgeGrowthRate: Math.round(badgeGrowthRate * 100) / 100
        
},
        referrals: {
          total: totalReferrals,
          completed: completedReferrals,
          pending: pendingReferrals,
          completionRate: totalReferrals > 0 ? Math.round((completedReferrals / 'totalReferrals') * 100) : 0,
          totalRewardsPaid: totalRewards._sum.reward || 0,
          thisWeek: referralsThisWeek,
          lastWeek: referralsLastWeek},
        engagement: {
          totalLikes: engagementStats._sum.likes || 0,
          totalShares: engagementStats._sum.shares || 0,
          totalViews: engagementStats._sum.views || 0,
          totalComments: engagementStats._sum.comments || 0,
          contentPieces: totalContent,
          avgEngagementPerContent: totalContent > 0 
            ? Math.round(((engagementStats._sum.likes || 0) + (engagementStats._sum.shares || 0) + (engagementStats._sum.views || 0)) / 'totalContent')
            : 0
        },
        topReferrers: topReferrers.map((user, index) => ({
          rank: index + 1,
          name: user.name || user.email.split('@')[0],
          referrals: user._count.referrals,
          role: user.role
        })),
        activitiesByType: activitiesByType.map(activity => ({
          type: activity.type.toLowerCase(),
          count: activity._count.type
        })),
        badges: {
          totalAvailable: totalBadges,
          totalEarned: totalUserBadges,
          thisMonth: badgesThisMonth,
          lastMonth: badgesLastMonth,
          adoptionRate: totalUsers > 0 ? Math.round((totalUserBadges / 'totalUsers') * 100) : 0
        }
      }

      return NextResponse.json(response)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao buscar estatísticas:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
