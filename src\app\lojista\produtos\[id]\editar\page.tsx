'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { ArrowLeft, Upload, X, Save } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  condition: string
  images: string[]
  categoryId: string
  brandId: string
  deviceModelId?: string
  specifications: any
  warranty: number
  stock: number
  isActive: boolean}

export default function EditProductPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [deviceModels, setDeviceModels] = useState<any[]>([])

  useEffect(() => {
    if (params?.id) {
      fetchProduct()
      fetchInitialData()
    }
  }, [params?.id])

  useEffect(() => {
    if (product?.categoryId && product?.brandId) {
      fetchDeviceModels()
    }
  }, [product?.categoryId, product?.brandId])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/lojista/products/${params?.id}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
      } else {
        router.push(/lojista/produtos)
      }
    } catch (error) {
      console.error('Erro ao carregar produto:', 'error')
      router.push(/lojista/produtos)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchInitialData = async () => {
    try {
      const [categoriesRes, brandsRes] = await Promise.all([
        fetch('/api/admin/categories'),
        fetch('/api/admin/brands')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData)
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData)
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', 'error')
    }
  }

  const fetchDeviceModels = async () => {
    if (!product?.categoryId || !product?.brandId) return

    try {
      const response = await fetch(`/api/admin/device-models?categoryId=${product.categoryId}&brandId=${product.brandId}`)
      if (response.ok) {
        const data = await response.json()
        setDeviceModels(data)
      }
    } catch (error) {
      console.error('Erro ao carregar modelos:', 'error')
    }
  }

  const handleInputChange = (field: string, value: any) => {
    if (!product) return

    setProduct({
      ...product,
      [field]: 'value'})
  }

  const handleSave = async () => {
    if (!product) return

    setIsSaving(true)
    try {
      const response = await fetch(`/api/lojista/products/${params?.id}`, {
        method: PUT,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(product)
      })

      if (response.ok) {
        alert('Produto atualizado com sucesso!')
        router.push(/lojista/produtos)
      } else {
        alert('Erro ao atualizar produto')
      }
    } catch (error) {
      console.error('Erro ao salvar:', 'error')
      alert('Erro ao atualizar produto')
    } finally {
      setIsSaving(false)
    }
  }

  if (session?.user?.role !== 'REPAIR_SHOP') {
    return <div>Acesso negado</div>
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Produto não encontrado</h1>
          <button
            onClick={() => router.push("/lojista/produtos")}
            className="text-blue-600 hover:text-blue-700"
          >
            Voltar aos produtos
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => router.push('/lojista/produtos')}
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Editar Produto</h1>
          </div>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Salvando...' : 'Salvar'}
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Informações básicas */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nome do Produto *</label>
                <input
                  type="text"
                  value={product.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Preço de Venda (€) *</label>
                <input
                  type="number"
                  step="0.01"
                  value={product.price}
                  onChange={(e) => handleInputChange('price', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Preço pelo qual o produto será vendido</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Preço Original (€)<span className="text-xs text-gray-500 ml-1">(Opcional - 'para promoções')</span>
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={product.originalPrice || ''}
                  onChange={(e) => handleInputChange('originalPrice', e.target.value ? parseFloat(e.target.value) : null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: 799.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Se preenchido, será mostrado riscado junto ao preço de venda
                  {product.originalPrice && product.price && product.originalPrice > product.price && (
                    <span className="text-green-600 font-medium ml-1">
                      (Desconto de {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%)
                    </span>
                  )}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Condição *</label>
                <select
                  value={product.condition}
                  onChange={(e) => handleInputChange('condition', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="NEW">Novo</option>
                  <option value="LIKE_NEW">Como Novo</option>
                  <option value="GOOD">Bom Estado</option>
                  <option value="FAIR">Estado Razoável</option>
                  <option value="POOR">Mau Estado</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Stock
                </label>
                <input
                  type="number"
                  value={product.stock}
                  onChange={(e) => handleInputChange('stock', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Garantia (meses)</label>
                <input
                  type="number"
                  value={product.warranty}
                  onChange={(e) => handleInputChange('warranty', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Categorização */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Categoria *</label>
                <select
                  value={product.categoryId}
                  onChange={(e) => handleInputChange('categoryId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Selecione uma categoria</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Marca *</label>
                <select
                  value={product.brandId}
                  onChange={(e) => handleInputChange('brandId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Selecione uma marca</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Modelo</label>
                <select
                  value={product.deviceModelId || ''}
                  onChange={(e) => handleInputChange('deviceModelId', e.target.value || 'null')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um modelo</option>
                  {deviceModels.map((model) => (
                    <option key={model.id} value={model.id}>
                      {model.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={product.isActive}
                  onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label className="ml-2 text-sm text-gray-700">Produto ativo</label>
              </div>
            </div>
          </div>

          {/* Descrição */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
            <textarea
              value={product.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
