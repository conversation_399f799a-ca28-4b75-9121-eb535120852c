const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkSubscriptionStatus() {
  try {
    console.log('🔍 Verificando status das subscriptions...\n')

    // Buscar todos os lojistas
    const lojistas = await prisma.user.findMany({
      where: { role: 'REPAIR_SHOP' },
      select: {
        id: true,
        name: true,
        email: true
      }
    })

    console.log(`📋 Encontrados ${lojistas.length} lojistas:\n`)

    for (const lojista of lojistas) {
      console.log(`👤 Lojista: ${lojista.name} (${lojista.email})`)
      console.log(`   ID: ${lojista.id}`)

      // Buscar subscription usando a mesma query da API
      const subscriptions = await prisma.$queryRaw`
        SELECT 
          s.*,
          sp.name as plan_name,
          sp.features,
          sp."maxProducts",
          sp."maxRepairs"
        FROM subscriptions s
        JOIN subscription_plans sp ON s."planId" = sp.id
        WHERE s."userId" = ${lojista.id}
        AND s.status IN ('ACTIVE', 'INCOMPLETE')
        ORDER BY s."createdAt" DESC
        LIMIT 1
      `

      if (subscriptions.length === 0) {
        console.log('   ❌ Nenhuma subscription ativa/incompleta encontrada')
        
        // Verificar se tem subscription com outros status
        const allSubscriptions = await prisma.$queryRaw`
          SELECT s.*, sp.name as plan_name
          FROM subscriptions s
          JOIN subscription_plans sp ON s."planId" = sp.id
          WHERE s."userId" = ${lojista.id}
          ORDER BY s."createdAt" DESC
        `
        
        if (allSubscriptions.length > 0) {
          console.log(`   📊 Subscriptions encontradas com outros status:`)
          allSubscriptions.forEach((sub, index) => {
            console.log(`      ${index + 1}. Status: ${sub.status}, Plano: ${sub.plan_name}`)
            console.log(`         Criado: ${sub.createdAt}`)
            console.log(`         Período: ${sub.currentPeriodStart} - ${sub.currentPeriodEnd}`)
          })
        }
      } else {
        const subscription = subscriptions[0]
        console.log(`   ✅ Subscription encontrada:`)
        console.log(`      Status: ${subscription.status}`)
        console.log(`      Plano: ${subscription.plan_name}`)
        console.log(`      Período: ${subscription.currentPeriodStart} - ${subscription.currentPeriodEnd}`)
        console.log(`      Criado: ${subscription.createdAt}`)
      }

      console.log('') // Linha em branco
    }

    // Verificar também os planos disponíveis
    console.log('📋 Planos disponíveis:')
    const plans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true },
      select: {
        id: true,
        name: true,
        monthlyPrice: true,
        isActive: true
      }
    })

    plans.forEach(plan => {
      console.log(`   - ${plan.name} (€${plan.monthlyPrice}/mês) - ID: ${plan.id}`)
    })

  } catch (error) {
    console.error('❌ Erro ao verificar subscriptions:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkSubscriptionStatus()
