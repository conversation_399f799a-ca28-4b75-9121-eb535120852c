const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createSystemConfig() {
  try {
    console.log('🔧 Criando configuração do sistema...')
    
    // Verificar se já existe configuração
    const existingConfig = await prisma.systemConfig.findFirst()
    
    if (existingConfig) {
      console.log('✅ Configuração do sistema já existe')
      console.log(`   Platform Name: ${existingConfig.platformName}`)
      console.log(`   Platform Logo: ${existingConfig.platformLogo || 'Não definido'}`)
      console.log(`   Platform Icon: ${existingConfig.platformIcon || 'Não definido'}`)
      console.log(`   AWS Access Key: ${existingConfig.awsAccessKeyId ? '✅ Definida' : '❌ Não definida'}`)
      console.log(`   AWS Secret Key: ${existingConfig.awsSecretAccessKey ? '✅ Definida' : '❌ Não definida'}`)
      console.log(`   AWS S3 Bucket: ${existingConfig.awsS3Bucket || 'Não definido'}`)
      console.log(`   AWS Region: ${existingConfig.awsRegion}`)
      return
    }
    
    // Criar configuração inicial
    const config = await prisma.systemConfig.create({
      data: {
        platformName: 'Revify',
        platformLogo: null,
        platformIcon: null,
        awsAccessKeyId: null,
        awsSecretAccessKey: null,
        awsS3Bucket: null,
        awsRegion: 'eu-west-1'
      }
    })
    
    console.log('✅ Configuração do sistema criada com sucesso!')
    console.log(`   ID: ${config.id}`)
    console.log(`   Platform Name: ${config.platformName}`)
    console.log(`   AWS Region: ${config.awsRegion}`)
    
    console.log('\n💡 Para configurar AWS:')
    console.log('1. Acesse /admin/configuracoes')
    console.log('2. Configure as credenciais AWS na seção de configurações')
    console.log('3. OU defina as variáveis de ambiente AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_S3_BUCKET')

  } catch (error) {
    console.error('❌ Erro ao criar configuração do sistema:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSystemConfig()
