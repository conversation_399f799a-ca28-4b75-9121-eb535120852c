'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Save, Key, MapPin, CreditCard, Webhook, Settings, Image, Globe, BarChart3, Users, Plus } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import ImageUpload from '@/components/ImageUpload'
import DeepLUsageMonitor from '@/components/admin/DeepLUsageMonitor'

interface SystemConfig {
  stripePublishableKey: string
  stripeSecretKey: string
  stripeWebhookSecret: string
  googleMapsApiKey: string
  awsAccessKeyId: string
  awsSecretAccessKey: string
  awsS3Bucket: string
  awsRegion: string
  platformCommission: number
  escrowDays: number
  // Platform branding
  platformLogo: string
  platformIcon: string
  platformName: string
  // Email settings
  emailEnabled: boolean
  smtpHost: string
  smtpPort: number
  smtpUser: string
  smtpPassword: string
  fromEmail: string
  fromName: string
  // SMS settings
  smsEnabled: boolean
  smsProvider: string
  // Twilio
  twilioAccountSid: string
  twilioAuthToken: string
  twilioPhoneNumber: string
  // Vonage
  vonageApiKey: string
  vonageApiSecret: string
  // AWS SNS
  awsSnsAccessKeyId: string
  awsSnsSecretAccessKey: string
  awsSnsRegion: string
  // WhatsApp settings
  whatsappEnabled: boolean
  whatsappApiKey: string
  whatsappPhoneNumber: string
  // Translation settings
  translationEnabled: boolean
  deeplApiKey: string
  deeplUsageLimit: number
  deeplUsageCurrent: number
}

export default function AdminConfiguracoesPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('general')
  
  const [config, setConfig] = useState<SystemConfig>({
    stripePublishableKey: '',
    stripeSecretKey: '',
    stripeWebhookSecret: '',
    googleMapsApiKey: '',
    awsAccessKeyId: '',
    awsSecretAccessKey: '',
    awsS3Bucket: '',
    awsRegion: 'eu-west-1',
    platformCommission: 5,
    escrowDays: 3,
    // Platform branding
    platformLogo: '',
    platformIcon: '',
    platformName: 'Revify',
    // Email settings
    emailEnabled: false,
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: '',
    // SMS settings
    smsEnabled: false,
    smsProvider: 'none',
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    vonageApiKey: '',
    vonageApiSecret: '',
    awsSnsAccessKeyId: '',
    awsSnsSecretAccessKey: '',
    awsSnsRegion: 'eu-west-1',
    // WhatsApp settings
    whatsappEnabled: false,
    whatsappApiKey: '',
    whatsappPhoneNumber: '',
    // Translation settings
    translationEnabled: false,
    deeplApiKey: '',
    deeplUsageLimit: 500000,
    deeplUsageCurrent: 0
  })

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/admin/system-config')
      if (response.ok) {
        const data = await response.json()
        setConfig(prev => ({ ...prev, ...data }))
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/admin/system-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        alert('Configurações atualizadas com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao atualizar configurações')
      }
    } catch (error) {
      console.error('Erro ao salvar configurações:', error)
      alert('Erro ao salvar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-black">Revify Admin</Link>
              <span className="ml-3 text-xs text-gray-500">Configurações do Sistema</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs Navigation */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('general')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'general'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Settings className="w-4 h-4 mr-2 inline" />
                Configurações Gerais
              </button>
              <button
                onClick={() => setActiveTab('translations')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'translations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Globe className="w-4 h-4 mr-2 inline" />
                Gestão de Idiomas
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'general' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900">Configurações do Sistema</h1>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4 mr-2" />
                {isSaving ? 'A guardar...' : 'Guardar'}
              </button>
            </div>

          <div className="space-y-8">
            {/* Platform Branding */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Image className="w-5 h-5 mr-2" />
                Identidade da Plataforma
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome da Plataforma
                  </label>
                  <input
                    type="text"
                    value={config.platformName}
                    onChange={(e) => setConfig(prev => ({ ...prev, platformName: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="Revify"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Nome exibido no header e em toda a plataforma
                  </p>
                </div>

                <div className="md:col-span-2">
                  <ImageUpload
                    currentImage={config.platformLogo}
                    onImageChange={(url) => setConfig(prev => ({ ...prev, platformLogo: url }))}
                    label="Logo da Plataforma"
                    description="Logo principal da plataforma exibido no header"
                    recommendedSize="200x50px"
                    maxSize={2}
                  />
                </div>

                <div className="md:col-span-2">
                  <ImageUpload
                    currentImage={config.platformIcon}
                    onImageChange={(url) => setConfig(prev => ({ ...prev, platformIcon: url }))}
                    label="Ícone da Plataforma"
                    description="Ícone usado como favicon e em páginas de login/signup"
                    recommendedSize="64x64px ou 512x512px"
                    maxSize={1}
                  />
                </div>
              </div>
            </div>

            {/* Stripe Configuration */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <CreditCard className="w-5 h-5 mr-2" />
                Configuração Stripe
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Publishable Key *
                  </label>
                  <input
                    type="text"
                    value={config.stripePublishableKey}
                    onChange={(e) => setConfig(prev => ({ ...prev, stripePublishableKey: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Secret Key *
                  </label>
                  <input
                    type="password"
                    value={config.stripeSecretKey}
                    onChange={(e) => setConfig(prev => ({ ...prev, stripeSecretKey: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="sk_test_..."
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Webhook Secret *
                  </label>
                  <input
                    type="password"
                    value={config.stripeWebhookSecret}
                    onChange={(e) => setConfig(prev => ({ ...prev, stripeWebhookSecret: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="whsec_..."
                  />
                </div>
              </div>
            </div>

            {/* Google Maps Configuration */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                Google Maps API
              </h2>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API Key *
                </label>
                <input
                  type="password"
                  value={config.googleMapsApiKey}
                  onChange={(e) => setConfig(prev => ({ ...prev, googleMapsApiKey: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  placeholder="AIza..."
                />
                <p className="text-sm text-gray-500 mt-1">
                  Necessário para cálculo de distâncias e geocodificação
                </p>
              </div>
            </div>

            {/* AWS Configuration */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Key className="w-5 h-5 mr-2" />
                AWS S3 (Upload de Imagens)
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Access Key ID *
                  </label>
                  <input
                    type="password"
                    value={config.awsAccessKeyId}
                    onChange={(e) => setConfig(prev => ({ ...prev, awsAccessKeyId: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="AKIA..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Secret Access Key *
                  </label>
                  <input
                    type="password"
                    value={config.awsSecretAccessKey}
                    onChange={(e) => setConfig(prev => ({ ...prev, awsSecretAccessKey: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    S3 Bucket *
                  </label>
                  <input
                    type="text"
                    value={config.awsS3Bucket}
                    onChange={(e) => setConfig(prev => ({ ...prev, awsS3Bucket: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                    placeholder="revify-uploads"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Região
                  </label>
                  <select
                    value={config.awsRegion}
                    onChange={(e) => setConfig(prev => ({ ...prev, awsRegion: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  >
                    <option value="eu-west-1">EU West 1 (Ireland)</option>
                    <option value="eu-central-1">EU Central 1 (Frankfurt)</option>
                    <option value="us-east-1">US East 1 (N. Virginia)</option>
                    <option value="us-west-2">US West 2 (Oregon)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Platform Settings */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Configurações da Plataforma
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Comissão da Plataforma (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="50"
                    step="0.1"
                    value={config.platformCommission}
                    onChange={(e) => setConfig(prev => ({ ...prev, platformCommission: parseFloat(e.target.value) }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Percentagem cobrada em cada transação
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dias de Escrow
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="30"
                    value={config.escrowDays}
                    onChange={(e) => setConfig(prev => ({ ...prev, escrowDays: parseInt(e.target.value) }))}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Dias para reter pagamento antes de libertar para a loja
                  </p>
                </div>
              </div>
            </div>

            {/* SMS Configuration */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Configurações SMS
              </h2>

              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="smsEnabled"
                    checked={config.smsEnabled}
                    onChange={(e) => setConfig(prev => ({ ...prev, smsEnabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="smsEnabled" className="ml-2 block text-sm text-gray-900">
                    Ativar notificações SMS
                  </label>
                </div>

                {config.smsEnabled && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Provider SMS
                      </label>
                      <select
                        value={config.smsProvider}
                        onChange={(e) => setConfig(prev => ({ ...prev, smsProvider: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                      >
                        <option value="none">Nenhum</option>
                        <option value="twilio">Twilio</option>
                        <option value="vonage">Vonage</option>
                        <option value="aws">AWS SNS</option>
                      </select>
                    </div>

                    {config.smsProvider === 'twilio' && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Account SID
                          </label>
                          <input
                            type="password"
                            value={config.twilioAccountSid}
                            onChange={(e) => setConfig(prev => ({ ...prev, twilioAccountSid: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                            placeholder="AC..."
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Auth Token
                          </label>
                          <input
                            type="password"
                            value={config.twilioAuthToken}
                            onChange={(e) => setConfig(prev => ({ ...prev, twilioAuthToken: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Número de Telefone
                          </label>
                          <input
                            type="text"
                            value={config.twilioPhoneNumber}
                            onChange={(e) => setConfig(prev => ({ ...prev, twilioPhoneNumber: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                            placeholder="+**********"
                          />
                        </div>
                      </div>
                    )}

                    {config.smsProvider === 'vonage' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            API Key
                          </label>
                          <input
                            type="password"
                            value={config.vonageApiKey}
                            onChange={(e) => setConfig(prev => ({ ...prev, vonageApiKey: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            API Secret
                          </label>
                          <input
                            type="password"
                            value={config.vonageApiSecret}
                            onChange={(e) => setConfig(prev => ({ ...prev, vonageApiSecret: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          />
                        </div>
                      </div>
                    )}

                    {config.smsProvider === 'aws' && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Access Key ID
                          </label>
                          <input
                            type="password"
                            value={config.awsSnsAccessKeyId}
                            onChange={(e) => setConfig(prev => ({ ...prev, awsSnsAccessKeyId: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Secret Access Key
                          </label>
                          <input
                            type="password"
                            value={config.awsSnsSecretAccessKey}
                            onChange={(e) => setConfig(prev => ({ ...prev, awsSnsSecretAccessKey: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Região
                          </label>
                          <select
                            value={config.awsSnsRegion}
                            onChange={(e) => setConfig(prev => ({ ...prev, awsSnsRegion: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600"
                          >
                            <option value="eu-west-1">Europa (Irlanda)</option>
                            <option value="us-east-1">US East (N. Virginia)</option>
                            <option value="us-west-2">US West (Oregon)</option>
                          </select>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Webhook URLs */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Webhook className="w-5 h-5 mr-2" />
                URLs de Webhook
              </h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Stripe Webhook:</span>
                    <code className="ml-2 text-blue-600">{process.env.NEXTAUTH_URL}/api/webhooks/stripe</code>
                  </div>
                  <p className="text-gray-500 text-xs">
                    Configure este URL no dashboard do Stripe para receber notificações de pagamento
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Translation Configuration */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Configurações de Tradução
            </h2>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="translationEnabled"
                  checked={config.translationEnabled}
                  onChange={(e) => setConfig(prev => ({ ...prev, translationEnabled: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="translationEnabled" className="ml-2 block text-sm text-gray-900">
                  Ativar sistema de traduções automáticas
                </label>
              </div>

              {config.translationEnabled && (
                <div className="space-y-4 pl-6 border-l-2 border-blue-100">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Chave API DeepL
                    </label>
                    <input
                      type="password"
                      value={config.deeplApiKey}
                      onChange={(e) => setConfig(prev => ({ ...prev, deeplApiKey: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="952460ac-39b4-4bdd-9b9b-fe2825f3dad7:fx"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Obtenha sua chave API em <a href="https://www.deepl.com/pro-api" target="_blank" className="text-blue-600 hover:underline">deepl.com/pro-api</a>
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Limite Mensal (caracteres)
                      </label>
                      <input
                        type="number"
                        value={config.deeplUsageLimit}
                        onChange={(e) => setConfig(prev => ({ ...prev, deeplUsageLimit: parseInt(e.target.value) || 0 }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Uso Atual (caracteres)
                      </label>
                      <input
                        type="number"
                        value={config.deeplUsageCurrent}
                        onChange={(e) => setConfig(prev => ({ ...prev, deeplUsageCurrent: parseInt(e.target.value) || 0 }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        readOnly
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-blue-900">Uso da API DeepL</span>
                      <span className="text-sm text-blue-700">
                        {((config.deeplUsageCurrent / config.deeplUsageLimit) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((config.deeplUsageCurrent / config.deeplUsageLimit) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-blue-700 mt-2">
                      {config.deeplUsageCurrent.toLocaleString()} / {config.deeplUsageLimit.toLocaleString()} caracteres utilizados
                    </p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Idiomas Suportados</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
                      <div>🇵🇹 Português</div>
                      <div>🇬🇧 English</div>
                      <div>🇪🇸 Español</div>
                      <div>🇫🇷 Français</div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Estes são os idiomas ativos no seletor de idiomas do sistema
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          </div>
        )}

        {/* Translations Tab */}
        {activeTab === 'translations' && (
          <div className="space-y-6">
            {/* Header with Save Button */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-3">
                  <Globe className="w-8 h-8 text-blue-600" />
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Gestão de Idiomas</h1>
                    <p className="text-gray-600">Monitoramento e configuração do sistema de traduções</p>
                  </div>
                </div>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {isSaving ? 'A guardar...' : 'Guardar'}
                </button>
              </div>

              {/* Translation Configuration */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="translationEnabledTab"
                    checked={config.translationEnabled}
                    onChange={(e) => setConfig(prev => ({ ...prev, translationEnabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="translationEnabledTab" className="ml-2 block text-sm text-gray-900">
                    Ativar sistema de traduções automáticas
                  </label>
                </div>

                {config.translationEnabled && (
                  <div className="space-y-4 pl-6 border-l-2 border-blue-100">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Chave API DeepL
                      </label>
                      <input
                        type="password"
                        value={config.deeplApiKey}
                        onChange={(e) => setConfig(prev => ({ ...prev, deeplApiKey: e.target.value }))}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="952460ac-39b4-4bdd-9b9b-fe2825f3dad7:fx"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Obtenha sua chave API em <a href="https://www.deepl.com/pro-api" target="_blank" className="text-blue-600 hover:underline">deepl.com/pro-api</a>
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Limite Mensal (caracteres)
                        </label>
                        <input
                          type="number"
                          value={config.deeplUsageLimit}
                          onChange={(e) => setConfig(prev => ({ ...prev, deeplUsageLimit: parseInt(e.target.value) || 0 }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Uso Atual (caracteres)
                        </label>
                        <input
                          type="number"
                          value={config.deeplUsageCurrent}
                          onChange={(e) => setConfig(prev => ({ ...prev, deeplUsageCurrent: parseInt(e.target.value) || 0 }))}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          readOnly
                        />
                      </div>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-blue-900">Uso da API DeepL</span>
                        <span className="text-sm text-blue-700">
                          {((config.deeplUsageCurrent / config.deeplUsageLimit) * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min((config.deeplUsageCurrent / config.deeplUsageLimit) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-blue-700 mt-2">
                        {config.deeplUsageCurrent.toLocaleString()} / {config.deeplUsageLimit.toLocaleString()} caracteres utilizados
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Translation Features */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

              {/* Usage Monitor - Main Column */}
              <div className="lg:col-span-2">
                <DeepLUsageMonitor />
              </div>

              {/* Sidebar with Statistics */}
              <div className="space-y-6">

                {/* Quick Stats */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Estatísticas Rápidas
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Globe className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-gray-600">Idiomas Ativos</span>
                      </div>
                      <span className="font-semibold">4</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <BarChart3 className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-600">Cache Hit Rate</span>
                      </div>
                      <span className="font-semibold">87%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-purple-500" />
                        <span className="text-sm text-gray-600">Usuários Ativos</span>
                      </div>
                      <span className="font-semibold">1,234</span>
                    </div>
                  </div>
                </div>

                {/* Supported Languages */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Idiomas Suportados
                    </h3>
                    <button
                      onClick={() => alert('Funcionalidade em desenvolvimento. Para adicionar novos idiomas, contacte o suporte técnico.')}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      Adicionar
                    </button>
                  </div>

                  <div className="space-y-3">
                    {[
                      { code: 'pt', name: 'Português', flag: '🇵🇹', usage: 45, active: true },
                      { code: 'en', name: 'English', flag: '🇬🇧', usage: 30, active: true },
                      { code: 'es', name: 'Español', flag: '🇪🇸', usage: 15, active: true },
                      { code: 'fr', name: 'Français', flag: '🇫🇷', usage: 7, active: true },
                      { code: 'de', name: 'Deutsch', flag: '🇩🇪', usage: 2, active: false },
                      { code: 'it', name: 'Italiano', flag: '🇮🇹', usage: 1, active: false }
                    ].map((lang) => (
                      <div key={lang.code} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{lang.flag}</span>
                          <span className="text-sm font-medium">{lang.name}</span>
                          {lang.active && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                              Ativo
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${lang.usage}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500 w-8">{lang.usage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Status */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Status do Sistema
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">API DeepL</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600 font-medium">Online</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Cache Redis</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600 font-medium">Online</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Detecção de Idioma</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-600 font-medium">Ativo</span>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </div>

            {/* Configuration Section */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Settings className="w-5 h-5 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Configurações Avançadas
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">
                    Cache de Traduções
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Configurar tempo de vida do cache e políticas de limpeza
                  </p>
                  <button
                    onClick={() => alert('Configuração de cache em desenvolvimento. Contacte o suporte para configurações avançadas.')}
                    className="text-blue-600 text-sm font-medium hover:text-blue-700"
                  >
                    Configurar
                  </button>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">
                    Detecção Automática
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Ajustar prioridades entre detecção por browser e IP
                  </p>
                  <button
                    onClick={() => alert('Detecção automática de idioma está ativa. Configurações avançadas em desenvolvimento.')}
                    className="text-blue-600 text-sm font-medium hover:text-blue-700"
                  >
                    Configurar
                  </button>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">
                    Novos Idiomas
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Adicionar suporte para novos idiomas na plataforma
                  </p>
                  <button
                    onClick={() => alert('Para adicionar novos idiomas, contacte o suporte técnico. Atualmente suportamos: PT, EN, ES, FR, DE, IT.')}
                    className="text-blue-600 text-sm font-medium hover:text-blue-700"
                  >
                    Adicionar
                  </button>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">
                    Teste de Performance
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Executar testes de performance e analisar métricas do sistema
                  </p>
                  <Link href="/admin/translations/test" className="text-blue-600 text-sm font-medium hover:text-blue-700">
                    Executar Testes
                  </Link>
                </div>

              </div>
            </div>

          </div>
        )}

      </div>
    </div>
  )
}
