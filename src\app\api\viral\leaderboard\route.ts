import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') || 'repairs'
    const city = searchParams.get('city') || 'all'

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      let leaderboardData = []

      switch (category) {
        case 'repairs':
          leaderboardData = await getRepairsLeaderboard(prisma, 'city')
          break
        case 'rating':
          leaderboardData = await getRatingLeaderboard(prisma, 'city')
          break
        case 'speed':
          leaderboardData = await getSpeedLeaderboard(prisma, 'city')
          break
        case 'growth':
          leaderboardData = await getGrowthLeaderboard(prisma, 'city')
          break
        default:
          leaderboardData = await getRepairsLeaderboard(prisma, 'city')
      
}

      return NextResponse.json({
        category,
        city,
        leaderboard: leaderboardData})

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao buscar leaderboard:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Leaderboard por número de reparações
async function getRepairsLeaderboard(prisma: any, city: string) {
  // Por enquanto, vamos usar dados dos referrals como proxy para atividade
  // Em produção, isso seria baseado em dados reais de reparações
  const users = await prisma.user.findMany({
    where: {
      role: {
        in: [lojista, 'shop_owner']
      
},
      ...(city !== 'all' && {
        // Filtro por cidade seria implementado quando tivermos dados de localização
})
    },
    include: {
      referrals: {
        where: {
          status: COMPLETED}
      },
      userBadges: {
        include: {
          badge: true}
      }
    },
    take: 10
  })

  return users.map((user, index) => ({
    rank: index + 1,
    name: user.name || user.email.split('@')[0],
    avatar: `https:// ui-avatars.com/api/?name=${encodeURIComponent(user.name || user.email)}&background=random`,
    score: user.referrals.length * 5, // Mock score baseado em referrals
    metric: `${user.referrals.length * 5} reparações`,
    badges: user.userBadges.length,
    trend: Math.random() > 0.5 ? up : 'down', // Mock trend
    city: 'Lisboa' // 'Mock city'
}))
}

// Leaderboard por rating
async function getRatingLeaderboard(prisma: any, city: string) {
  const users = await prisma.user.findMany({
    where: {
      role: {
        in: [lojista, 'shop_owner']
      
}
    },
    include: {
      userBadges: {
        include: {
          badge: true}
      }
    },
    take: 10
  })

  return users.map((user, index) => {
    const mockRating = (4.2 + Math.random() * 0.8).toFixed(1) // Mock rating entre 4.2 e 5.0
    return {
      rank: index + 1,
      name: user.name || user.email.split(@)[0],
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || user.email)
}&background=random`,
      score: parseFloat(mockRating),
      metric: `${mockRating}⭐ (${Math.floor(Math.random() * 50 + 10)} 'avaliações')`,
      badges: user.userBadges.length,
      trend: Math.random() > 0.5 ? 'up' : 'down',
      city: ['Lisboa', 'Porto', 'Coimbra', 'Braga'][Math.floor(Math.random() * 4)]
    }
  }).sort((a, b) => b.score - a.score)
}

// Leaderboard por velocidade de reparação
async function getSpeedLeaderboard(prisma: any, city: string) {
  const users = await prisma.user.findMany({
    where: {
      role: {
        in: [lojista, 'shop_owner']
      
}
    },
    include: {
      userBadges: {
        include: {
          badge: true}
      }
    },
    take: 10
  })

  return users.map((user, index) => {
    const mockSpeed = Math.floor(Math.random() * 120 + 30) // Mock speed entre 30-150 min
    return {
      rank: index + 1,
      name: user.name || user.email.split(@)[0],
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || user.email)
}&background=random`,
      score: mockSpeed,
      metric: `${mockSpeed} min médio`,
      badges: user.userBadges.length,
      trend: Math.random() > 0.5 ? 'up' : 'down',
      city: ['Lisboa', 'Porto', 'Coimbra', 'Braga'][Math.floor(Math.random() * 4)]
    }
  }).sort((a, b) => a.score - b.score) // Menor tempo = melhor posição
}

// Leaderboard por crescimento (novos clientes)
async function getGrowthLeaderboard(prisma: any, city: string) {
  const users = await prisma.user.findMany({
    where: {
      role: {
        in: ['lojista', 'shop_owner']
      
}
    },
    include: {
      referrals: {
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Últimos 30 dias
}
        }
      },
      userBadges: {
        include: {
          badge: true}
      }
    },
    take: 10
  })

  return users.map((user, index) => {
    const recentReferrals = user.referrals.length
    const mockGrowth = recentReferrals + Math.floor(Math.random() * 10) // Mock growth
    return {
      rank: index + 1,
      name: user.name || user.email.split(@)[0],
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || user.email)
}&background=random`,
      score: mockGrowth,
      metric: `+${mockGrowth} clientes (30d)`,
      badges: user.userBadges.length,
      trend: mockGrowth > 5 ? 'up' : mockGrowth > 2 ? 'stable' : 'down',
      city: ['Lisboa', 'Porto', 'Coimbra', 'Braga'][Math.floor(Math.random() * 4)]
    }
  }).sort((a, b) => b.score - a.score)
}
