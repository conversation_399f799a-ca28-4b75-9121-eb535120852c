import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { getTemplateById, renderTemplate } from '@/lib/newsletter-templates'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a app está instalada
    const installedApp = await prisma.$queryRaw`
      SELECT * FROM installed_apps
      WHERE "userId" = ${session.user.id}
      AND "appId" = newsletter-pro
      AND "isActive" = true
      LIMIT 1
    ` as any[]

    if (installedApp.length === 0) {
      return NextResponse.json(
        { message: "Newsletter Pro não está instalado" 
},
        { status: 403 }
      )
    }

    const { templateId, variables, name } = await request.json()

    if (!templateId || !variables || !name) {
      return NextResponse.json(
        { message: "Template ID, variáveis e nome são obrigatórios" },
        { status: 400 }
      )
    }

    // Buscar template pré-definido
    const predefinedTemplate = getTemplateById(templateId)

    if (!predefinedTemplate) {
      return NextResponse.json(
        { message: "Template não encontrado" },
        { status: 404 }
      )
    }

    // Renderizar template com as variáveis
    const renderedContent = renderTemplate(predefinedTemplate.content, variables)

    // Salvar como template personalizado
    const customTemplate = await prisma.$queryRaw`
      INSERT INTO newsletter_templates (
        "id", "userId", name, subject, content, "isActive", "createdAt", "updatedAt"
      )
      VALUES (
        gen_random_uuid(), ${session.user.id
}, ${name}, ${variables.subject || 'name'},
        ${renderedContent}, true, NOW(), NOW()
      )
      RETURNING *
    ` as any[]

    return NextResponse.json({
      message: 'Template criado com sucesso',
      template: {
        id: customTemplate[0].id,
        name: customTemplate[0].name,
        subject: customTemplate[0].subject,
        content: customTemplate[0].content,
        basedOn: predefinedTemplate.name,
        createdAt: customTemplate[0].createdAt
      }
    })

  } catch (error) {
    console.error("Erro ao usar template pré-definido:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
