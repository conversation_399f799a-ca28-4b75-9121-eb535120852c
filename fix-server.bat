@echo off
echo Matando processos Node.js...
taskkill /F /IM node.exe 2>nul

echo Aguardando 2 segundos...
timeout /t 2 /nobreak >nul

echo Verificando porta 3001...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
    echo Matando processo %%a na porta 3001...
    taskkill /F /PID %%a 2>nul
)

echo Aguardando 2 segundos...
timeout /t 2 /nobreak >nul

echo Removendo cache do Next.js...
if exist .next rmdir /s /q .next

echo Aguardando 2 segundos...
timeout /t 2 /nobreak >nul

echo Iniciando servidor...
npm run dev
