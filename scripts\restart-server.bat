@echo off
echo 🔧 Reiniciando servidor Revify...

echo 1️⃣ Parando processos Node.js...
taskkill /f /im node.exe /t >nul 2>&1

echo 2️⃣ Limpando cache do Next.js...
if exist .next rmdir /s /q .next >nul 2>&1

echo 3️⃣ Limpando cache do npm...
npm cache clean --force >nul 2>&1

echo 4️⃣ Iniciando servidor...
echo 🚀 Servidor será iniciado em: http://localhost:3001
echo 🔄 Se houver ChunkLoadError, pressione Ctrl+F5 para refrescar

start "Revify Server" npm run dev

echo ✅ Servidor iniciado!
echo 🌐 Acesse: http://localhost:3001
pause
