'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Store, Globe, CreditCard, Save, ExternalLink, Copy } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
export default function MiniLojaConfigPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasAccess, setHasAccess] = useState(false)
  
  const [customSubdomain, setCustomSubdomain] = useState('')
  const [customDomain, setCustomDomain] = useState('')
  const [ifthenPayEntityId, setIfthenPayEntityId] = useState('')
  const [ifthenPaySubEntityId, setIfthenPaySubEntityId] = useState('')
  const [ifthenPayApiKey, setIfthenPayApiKey] = useState('')

  useEffect(() => {
    if (session?.user?.id) {
      fetchConfig()
    }
  }, [session])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/lojista/mini-loja-config')
      if (response.ok) {
        const data = await response.json()
        setHasAccess(data.hasAccess)
        setCustomSubdomain(data.customSubdomain || ') setCustomDomain(data.customDomain ||) setIfthenPayEntityId(data.ifthenPayEntityId ||) setIfthenPaySubEntityId(data.ifthenPaySubEntityId ||) setIfthenPayApiKey(data.ifthenPayApiKey ||')
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/lojista/mini-loja-config', {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customSubdomain,
          customDomain,
          ifthenPayEntityId,
          ifthenPaySubEntityId, ifthenPayApiKey })
      })

      if (response.ok) {
        alert('Configurações salvas com sucesso!')
      } else {
        const error = await response.json()
        alert(`Erro: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro ao salvar:', 'error')
      alert('Erro ao salvar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('Copiado para a área de transferência!')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-14">
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold text-black">Revify</Link>
                <span className="ml-3 text-xs text-gray-500">Mini-Loja</span>
              </div>
              <div className="flex items-center space-x-3">
                <NotificationDropdown />
                <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
                <UserDropdown user={session?.user || {}} />
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Mini-Loja Não Disponível</h1>
            <p className="text-gray-600 mb-8">A funcionalidade de Mini-Loja e Reparações Independentes não está disponível no seu plano atual.</p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-blue-900 mb-2">O que inclui a Mini-Loja?</h3>
              <ul className="text-sm text-blue-800 space-y-2 text-left">
                <li>• Subdomínio personalizado (ex: suaempresa.revify.pt)</li>
                <li>• Domínio próprio (ex: reparacoes.suaempresa.pt)</li>
                <li>• Reparações independentes (walk-ins, 'clientes habituais')</li>
                <li>• Sistema de códigos únicos para clientes</li>
                <li>• Pagamentos diretos via IfthenPay (sem 'escrow')</li>
                <li>• Comissões reduzidas</li>
              </ul>
            </div>
            <Link
              href="/lojista/planos"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >Ver Planos Disponíveis</Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Mini-Loja</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Mini-Loja</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Configurações da Mini-Loja</h1>
          <p className="text-gray-600">Configure o seu subdomínio, domínio personalizado e pagamentos diretos</p>
        </div>

        <div className="space-y-6">
          {/* Domínios */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <Globe className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Domínios</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subdomínio Revify</label>
                <div className="flex">
                  <input
                    type="text"
                    value={customSubdomain}
                    onChange={(e) => setCustomSubdomain(e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="suaempresa"
                  />
                  <span className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg text-gray-600">
                    .revify.pt
                  </span>
                </div>
                {customSubdomain && (
                  <div className="mt-2 flex items-center space-x-2">
                    <span className="text-sm text-green-600">
                      Disponível em: https:// {customSubdomain}.revify.pt
                    </span>
                    <button
                      onClick={() => copyToClipboard(`https://${customSubdomain}.revify.pt`)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Domínio Personalizado (Opcional)</label>
                <input
                  type="text"
                  value={customDomain}
                  onChange={(e) => setCustomDomain(e.target.value.toLowerCase())}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="reparacoes.suaempresa.pt"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Configure um CNAME para {customSubdomain}.revify.pt ou contacte-nos para configuração
                </p>
              </div>
            </div>
          </div>

          {/* IfthenPay */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <CreditCard className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Configurações IfthenPay</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entity ID
                </label>
                <input
                  type="text"
                  value={ifthenPayEntityId}
                  onChange={(e) => setIfthenPayEntityId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="12345"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sub-Entity ID
                </label>
                <input
                  type="text"
                  value={ifthenPaySubEntityId}
                  onChange={(e) => setIfthenPaySubEntityId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="123"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chave API
                </label>
                <input
                  type="password"
                  value={ifthenPayApiKey}
                  onChange={(e) => setIfthenPayApiKey(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="sua-chave-api-ifthen-pay"
                />
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-sm text-yellow-800">
                  <strong>Importante:</strong>Com estas configurações, os pagamentos das reparações independentes  serão processados diretamente para a sua conta IfthenPay, sem passar pelo sistema de escrow da Revify.</p>
              </div>
            </div>
          </div>

          {/* Botão Salvar */}
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isSaving ? Salvando... : 'Salvar Configurações'
}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
