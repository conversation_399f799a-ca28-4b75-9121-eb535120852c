import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            deviceModels: true}
        }
      },
      orderBy: {
        name: asc}
    })

    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      productCount: category._count.deviceModels
    }))

    return NextResponse.json(formattedCategories)

  } catch (error) {
    console.error('Erro ao buscar categorias:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
