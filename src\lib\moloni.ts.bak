import { prisma } from '@/lib/prisma'

interface MoloniConfig {
  clientId: string
  clientSecret: string
  username: string
  password: string
  companyId: string
  documentSetId: string
  autoIssue: boolean
  isConnected: boolean
  accessToken?: string
  refreshToken?: string
  tokenExpiresAt?: Date
}

interface MoloniTokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  token_type: string
}

interface MoloniCustomer {
  customer_id?: number
  vat: string
  number: string
  name: string
  language_id: number
  address: string
  zip_code: string
  city: string
  country_id: number
  email: string
  phone?: string
  payment_method_id?: number
  maturity_date_id?: number
}

interface MoloniProduct {
  product_id?: number
  name: string
  summary?: string
  price: number
  qty: number
  discount?: number
  taxes: {
    tax_id: number
    value: number
    order: number
    cumulative: number
  }[]
}

interface InvoiceData {
  customerName: string
  customerEmail: string
  customerNif?: string
  customerPhone?: string
  customerAddress?: string
  customerCity?: string
  customerZipCode?: string
  items: {
    name: string
    description: string
    price: number
    quantity: number
    tax?: number
  }[]
  total: number
  type: 'REPAIR' | 'SALE'
}

// Classe principal para integração com Moloni API
export class MoloniAPI {
  private config: MoloniConfig
  private baseUrl = 'https://api.moloni.pt/v1'

  constructor(config: MoloniConfig) {
    this.config = config
  }

  // Autenticação OAuth2
  async authenticate(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/grant/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          grant_type: 'password',
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          username: this.config.username,
          password: this.config.password
        })
      })

      if (!response.ok) {
        throw new Error(`Erro de autenticação: ${response.status}`)
      }

      const tokenData: MoloniTokenResponse = await response.json()

      // Atualizar configuração com tokens
      this.config.accessToken = tokenData.access_token
      this.config.refreshToken = tokenData.refresh_token
      this.config.tokenExpiresAt = new Date(Date.now() + tokenData.expires_in * 1000)

      return true
    } catch (error) {
      console.error('Erro na autenticação Moloni:', error)
      return false
    }
  }

  // Renovar token de acesso
  async refreshAccessToken(): Promise<boolean> {
    if (!this.config.refreshToken) {
      return await this.authenticate()
    }

    try {
      const response = await fetch(`${this.baseUrl}/grant/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          grant_type: 'refresh_token',
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          refresh_token: this.config.refreshToken
        })
      })

      if (!response.ok) {
        return await this.authenticate()
      }

      const tokenData: MoloniTokenResponse = await response.json()

      this.config.accessToken = tokenData.access_token
      this.config.refreshToken = tokenData.refresh_token
      this.config.tokenExpiresAt = new Date(Date.now() + tokenData.expires_in * 1000)

      return true
    } catch (error) {
      console.error('Erro ao renovar token:', error)
      return await this.authenticate()
    }
  }

  // Verificar se token é válido
  private async ensureValidToken(): Promise<boolean> {
    if (!this.config.accessToken || !this.config.tokenExpiresAt) {
      return await this.authenticate()
    }

    if (new Date() >= this.config.tokenExpiresAt) {
      return await this.refreshAccessToken()
    }

    return true
  }

  // Fazer chamada autenticada à API
  private async apiCall(endpoint: string, data: any = {}): Promise<any> {
    if (!(await this.ensureValidToken())) {
      throw new Error('Falha na autenticação')
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.accessToken}`
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`Erro na API: ${response.status} - ${await response.text()}`)
    }

    return await response.json()
  }

  /*async getCompanyInfo(): Promise<any> {
    return await this.apiCall('/companies/getOne/', {
      company_id: this.config.companyId
    })
  }*/

  // Buscar ou criar cliente
  async findOrCreateCustomer(customerData: {
    name: string
    email: string
    vat?: string
    phone?: string
    address?: string
    city?: string
    zipCode?: string
  }): Promise<number> {
    try {
      // Primeiro, tentar encontrar cliente existente por email ou NIF
      const searchCriteria = customerData.vat ?
        { company_id: this.config.companyId, vat: customerData.vat } :
        { company_id: this.config.companyId, email: customerData.email }

      const existingCustomers = await this.apiCall('/customers/getAll/', {
        company_id: this.config.companyId,
        search: customerData.email,
        qty: 1
      })

      if (existingCustomers && existingCustomers.length > 0) {
        return existingCustomers[0].customer_id
      }

      // Se não encontrou, criar novo cliente
      const newCustomer: MoloniCustomer = {
        vat: customerData.vat || '*********', // NIF genérico para consumidor final
        number: `CLI${Date.now()}`,
        name: customerData.name,
        language_id: 1, // Português
        address: customerData.address || 'Não especificado',
        zip_code: customerData.zipCode || '0000-000',
        city: customerData.city || 'Não especificado',
        country_id: 1, // Portugal
        email: customerData.email,
        phone: customerData.phone || ''
      }

      const result = await this.apiCall('/customers/insert/', {
        company_id: this.config.companyId,
        ...newCustomer
      })

      return result.customer_id
    } catch (error) {
      console.error('Erro ao buscar/criar cliente:', error)
      throw error
    }
  }

  // Criar fatura
  async createInvoice(invoiceData: InvoiceData): Promise<{ documentId: number; invoiceNumber: string }> {
    try {
      // Buscar ou criar cliente
      const customerId = await this.findOrCreateCustomer({
        name: invoiceData.customerName,
        email: invoiceData.customerEmail,
        vat: invoiceData.customerNif,
        phone: invoiceData.customerPhone,
        address: invoiceData.customerAddress,
        city: invoiceData.customerCity,
        zipCode: invoiceData.customerZipCode
      })

      // Preparar produtos
      const products: MoloniProduct[] = invoiceData.items.map(item => ({
        name: item.name,
        summary: item.description,
        price: item.price,
        qty: item.quantity,
        discount: 0,
        taxes: [{
          tax_id: 1, // IVA normal (assumindo ID 1 para 23%)
          value: item.tax || 23,
          order: 0,
          cumulative: 0
        }]
      }))

      // Criar fatura
      const invoicePayload = {
        company_id: this.config.companyId,
        customer_id: customerId,
        date: new Date().toISOString().split('T')[0],
        expiration_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 dias
        document_set_id: this.config.documentSetId,
        products: products,
        status: 1, // Fechado
        notes: `Fatura gerada automaticamente - ${invoiceData.type}`
      }

      const result = await this.apiCall('/invoices/insert/', invoicePayload)

      return {
        documentId: result.document_id,
        invoiceNumber: result.number
      }
    } catch (error) {
      console.error('Erro ao criar fatura:', error)
      throw error
    }
  }

  // Enviar fatura por email
  async sendInvoiceByEmail(documentId: number, email: string, subject?: string, message?: string): Promise<boolean> {
    try {
      await this.apiCall('/invoices/sendEmail/', {
        company_id: this.config.companyId,
        document_id: documentId,
        email: email,
        subject: subject || 'A sua fatura',
        message: message || 'Em anexo encontra a sua fatura. Obrigado!'
      })
      return true
    } catch (error) {
      console.error('Erro ao enviar fatura por email:', error)
      return false
    }
  }

  // Obter PDF da fatura
  async getInvoicePDF(documentId: number): Promise<string | null> {
    try {
      const result = await this.apiCall('/documents/getPDF/', {
        company_id: this.config.companyId,
        document_id: documentId
      })
      return result.pdf || null
    } catch (error) {
      console.error('Erro ao obter PDF da fatura:', error)
      return null
    }
  }
}

// Funções utilitárias para compatibilidade com código existente

// Função para obter configuração do Moloni de um lojista
export async function getMoloniConfig(userId: string): Promise<MoloniConfig | null> {
  try {
    const config = await prisma.appConfig.findUnique({
      where: {
        userId_appId: {
          userId,
          appId: 'moloni'
        }
      }
    })

    if (!config) return null

    return config.settings as MoloniConfig
  } catch (error) {
    console.error('Erro ao buscar configuração Moloni:', error)
    return null
  }
}

// Função para verificar se o Moloni está ativo para um lojista
export async function isMoloniActive(userId: string): Promise<boolean> {
  try {
    const installedApp = await prisma.installedApp.findUnique({
      where: {
        userId_appId: {
          userId,
          appId: 'moloni'
        }
      }
    })

    return installedApp?.isActive && installedApp?.isPaid
  } catch (error) {
    console.error('Erro ao verificar status Moloni:', error)
    return false
  }
}

// Função para salvar configuração atualizada
export async function saveMoloniConfig(userId: string, config: MoloniConfig): Promise<boolean> {
  try {
    await prisma.appConfig.upsert({
      where: {
        userId_appId: {
          userId,
          appId: 'moloni'
        }
      },
      update: {
        settings: config,
        updatedAt: new Date()
      },
      create: {
        userId,
        appId: 'moloni',
        settings: config
      }
    })
    return true
  } catch (error) {
    console.error('Erro ao salvar configuração Moloni:', error)
    return false
  }
}

// Função para emitir fatura automaticamente (versão melhorada)
export async function autoIssueInvoice(
  userId: string,
  invoiceData: InvoiceData,
  referenceId: string,
  referenceType: 'REPAIR' | 'ORDER'
): Promise<{ success: boolean; invoiceNumber?: string; documentId?: number; error?: string }> {
  try {
    // Verificar se Moloni está ativo
    const isActive = await isMoloniActive(userId)
    if (!isActive) {
      return { success: false, error: 'Moloni não está ativo' }
    }

    // Obter configuração
    const config = await getMoloniConfig(userId)
    if (!config || !config.autoIssue) {
      return { success: false, error: 'Emissão automática não está configurada' }
    }

    // Criar instância da API
    const moloniAPI = new MoloniAPI(config)

    // Criar fatura
    const result = await moloniAPI.createInvoice({
      ...invoiceData,
      type: referenceType
    })

    // Salvar tokens atualizados
    await saveMoloniConfig(userId, moloniAPI['config'])

    return {
      success: true,
      invoiceNumber: result.invoiceNumber,
      documentId: result.documentId
    }

    // Atualizar registro no banco
    if (referenceType === 'REPAIR') {
      await prisma.repair.update({
        where: { id: referenceId },
        data: {
          invoiceIssued: true,
          invoiceIssuedAt: new Date(),
          moloniInvoiceId: result.documentId.toString(),
          invoiceNumber: result.invoiceNumber
        }
      })
    } else {
      await prisma.order.update({
        where: { id: referenceId },
        data: {
          invoiceIssued: true,
          invoiceIssuedAt: new Date(),
          moloniInvoiceId: result.documentId.toString(),
          invoiceNumber: result.invoiceNumber
        }
      })
    }

    return {
      success: true,
      invoiceNumber: result.invoiceNumber,
      documentId: result.documentId
    }

  } catch (error) {
    console.error('Erro na emissão automática de fatura:', error)
    return {
      success: false,
      error: error.message || 'Erro desconhecido'
    }
  }
}

// Função para emitir fatura quando reparação é concluída
export async function onRepairCompleted(repairId: string) {
  try {
    const repair = await prisma.repair.findUnique({
      where: { id: repairId },
      include: {
        customer: {
          select: {
            name: true,
            email: true,
            profile: {
              select: {
                customerNif: true
              }
            }
          }
        },
        repairShop: {
          select: {
            id: true,
            name: true
          }
        },
        deviceModel: {
          select: {
            name: true,
            brand: {
              select: {
                name: true
              }
            }
          }
        },
        problemType: {
          select: {
            name: true
          }
        }
      }
    })

    if (!repair || !repair.finalPrice) return

    const invoiceData: InvoiceData = {
      customerName: repair.customer.name || 'Cliente',
      customerEmail: repair.customer.email,
      customerNif: repair.customer.profile?.customerNif,
      items: [
        {
          name: `Reparação - ${repair.deviceModel?.brand?.name} ${repair.deviceModel?.name}`,
          description: repair.problemType?.name || repair.description || 'Reparação',
          price: Number(repair.finalPrice),
          quantity: 1,
          tax: 23
        }
      ],
      total: Number(repair.finalPrice),
      type: 'REPAIR'
    }

    const result = await autoIssueInvoice(
      repair.repairShopId!,
      invoiceData,
      repairId,
      'REPAIR'
    )

    if (result.success) {
      console.log(`Fatura emitida automaticamente para reparação ${repairId}: ${result.invoiceNumber}`)
    } else {
      console.warn(`Falha na emissão automática para reparação ${repairId}: ${result.error}`)
    }

    return result

  } catch (error) {
    console.error('Erro ao processar conclusão de reparação:', error)
    return { success: false, error: error.message }
  }
}

// Função para emitir fatura quando encomenda é entregue
export async function onOrderDelivered(orderId: string) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        customer: {
          select: {
            name: true,
            email: true,
            profile: {
              select: {
                customerNif: true
              }
            }
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                name: true,
                sellerId: true
              }
            }
          }
        }
      }
    })

    if (!order || !order.total) return

    // Agrupar itens por vendedor
    const itemsBySeller = order.orderItems.reduce((acc, item) => {
      const sellerId = item.product?.sellerId
      if (!sellerId) return acc

      if (!acc[sellerId]) {
        acc[sellerId] = []
      }

      acc[sellerId].push({
        name: item.product?.name || 'Produto',
        description: `Venda marketplace - ${item.product?.name}`,
        price: Number(item.price),
        quantity: item.quantity,
        tax: 23
      })

      return acc
    }, {} as Record<string, any[]>)

    // Emitir fatura para cada vendedor
    const results = []
    for (const [sellerId, items] of Object.entries(itemsBySeller)) {
      const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)

      const invoiceData: InvoiceData = {
        customerName: order.customer.name || 'Cliente',
        customerEmail: order.customer.email,
        customerNif: order.customer.profile?.customerNif,
        items,
        total,
        type: 'SALE'
      }

      const result = await autoIssueInvoice(
        sellerId,
        invoiceData,
        orderId,
        'ORDER'
      )

      results.push({ sellerId, ...result })
    }

    return results

  } catch (error) {
    console.error('Erro ao processar entrega de encomenda:', error)
    return [{ success: false, error: error.message }]
  }
}

// Função para testar conexão com Moloni
export async function testMoloniConnection(config: Partial<MoloniConfig>): Promise<{ success: boolean; companies?: any[]; error?: string }> {
  try {
    if (!config.clientId || !config.clientSecret || !config.username || !config.password) {
      return { success: false, error: 'Configuração incompleta' }
    }

    const moloniAPI = new MoloniAPI(config as MoloniConfig)
    const authenticated = await moloniAPI.authenticate()

    if (!authenticated) {
      return { success: false, error: 'Falha na autenticação' }
    }

    // Obter lista de empresas
    const companies = await moloniAPI.apiCall('/companies/getAll/')

    return {
      success: true,
      companies: companies || []
    }
  } catch (error) {
    console.error('Erro ao testar conexão:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro de conexão'
    }
  }
}

// Função para obter dados da empresa
export async function getMoloniCompanyData(userId: string): Promise<any> {
  try {
    const config = await getMoloniConfig(userId)
    if (!config) {
      throw new Error('Configuração Moloni não encontrada')
    }

    const moloniAPI = new MoloniAPI(config)
    return await moloniAPI.getCompanyInfo()
  } catch (error) {
    console.error('Erro ao obter dados da empresa:', error)
    throw error
  }
}

// Função para enviar fatura por email
export async function sendMoloniInvoiceByEmail(
  userId: string,
  documentId: number,
  email: string,
  subject?: string,
  message?: string
): Promise<boolean> {
  try {
    const config = await getMoloniConfig(userId)
    if (!config) {
      return false
    }

    const moloniAPI = new MoloniAPI(config)
    return await moloniAPI.sendInvoiceByEmail(documentId, email, subject, message)
  } catch (error) {
    console.error('Erro ao enviar fatura por email:', error)
    return false
  }
}

// Função para obter PDF da fatura
export async function getMoloniInvoicePDF(userId: string, documentId: number): Promise<string | null> {
  try {
    const config = await getMoloniConfig(userId)
    if (!config) {
      return null
    }

    const moloniAPI = new MoloniAPI(config)
    return await moloniAPI.getInvoicePDF(documentId)
  } catch (error) {
    console.error('Erro ao obter PDF da fatura:', error)
    return null
  }
}
