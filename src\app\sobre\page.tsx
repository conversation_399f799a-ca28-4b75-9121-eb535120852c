'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  ArrowRight,
  Users,
  Target,
  Award,
  Globe,
  Smartphone,
  Laptop,
  Tablet,
  Watch,
  CheckCircle,
  Star,
  Heart,
  Zap
} from 'lucide-react'
import LexendLayout from '@/components/LexendLayout'
import AutoTranslate from '@/components/ui/AutoTranslate'

export default function SobrePage() {
  const stats = [
    { number: '50K+', label: 'Reparações Realizadas', icon: CheckCircle },
    { number: '1000+', label: 'Técnicos Certificados', icon: Users },
    { number: '98%', label: 'Taxa de Satisfação', icon: Star },
    { number: '24h', label: 'Tempo Médio de Reparação', icon: Zap }
  ]

  const values = [
    {
      icon: Target,
      title: 'Missão',
      description: 'Tornar as reparações de dispositivos acessíveis, rápidas e confiáveis para todos os europeus.'
    },
    {
      icon: Heart,
      title: 'Val<PERSON>',
      description: 'Transparência, qualidade, sustentabilidade e compromisso com a satisfação do cliente.'
    },
    {
      icon: Globe,
      title: 'Visão',
      description: 'Ser a maior plataforma de reparações da Europa, conectando pessoas e técnicos especializados.'
    }
  ]

  return (
    <LexendLayout>
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gray-50 dark:bg-gray-900">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6">
              <span className="text-gray-900 dark:text-white">Sobre a </span>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Revify
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              <AutoTranslate text="A maior plataforma de reparações da Europa, conectando pessoas a técnicos especializados em toda a região." />
            </p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300">
                    <AutoTranslate text={stat.label} />
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Os Nossos Valores" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              <AutoTranslate text="Princípios que guiam cada decisão e ação na Revify" />
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all border border-gray-200 dark:border-gray-700">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    <AutoTranslate text={value.title} />
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    <AutoTranslate text={value.description} />
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                <AutoTranslate text="A Nossa História" />
              </h2>
              <div className="space-y-6 text-gray-600 dark:text-gray-300">
                <p>
                  <AutoTranslate text="A Revify nasceu da frustração de encontrar técnicos confiáveis para reparar dispositivos eletrónicos. Em 2020, decidimos criar uma plataforma que conectasse pessoas a profissionais qualificados de forma simples e transparente." />
                </p>
                <p>
                  <AutoTranslate text="Começámos em Portugal e rapidamente expandimos para toda a Europa, tornando-nos a maior rede de reparações do continente. Hoje, milhares de técnicos certificados utilizam a nossa plataforma para oferecer serviços de qualidade." />
                </p>
                <p>
                  <AutoTranslate text="O nosso compromisso é com a sustentabilidade - cada dispositivo reparado é um passo em direção a um futuro mais verde e responsável." />
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl p-8 flex items-center justify-center">
                <div className="grid grid-cols-2 gap-4">
                  <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                    <Smartphone className="w-8 h-8 text-white" />
                  </div>
                  <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                    <Laptop className="w-8 h-8 text-white" />
                  </div>
                  <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                    <Tablet className="w-8 h-8 text-white" />
                  </div>
                  <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center">
                    <Watch className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            <AutoTranslate text="Pronto para reparar o seu dispositivo?" />
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            <AutoTranslate text="Junte-se a milhares de clientes satisfeitos e encontre o técnico perfeito para as suas necessidades." />
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
            >
              <span><AutoTranslate text="Encontrar Técnico" /></span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all"
            >
              <span><AutoTranslate text="Contactar-nos" /></span>
            </Link>
          </div>
        </div>
      </section>
    </LexendLayout>
  )
}
