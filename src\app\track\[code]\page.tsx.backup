'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Search, Clock, CheckCircle, AlertCircle, Package, Wrench, User, Phone, Mail } from 'lucide-react'

interface RepairTracking {
  id: string
  trackingCode: string
  status: string
  customerName: string
  customerPhone: string
  customerEmail?: string
  deviceBrand: string
  deviceModel: string
  deviceDescription?: string
  problemType: string
  problemDescription: string
  estimatedPrice: number
  estimatedCompletionDate: string
  createdAt: string
  updatedAt: string
  repairShop: {
    name: string
    profile?: {
      phone?: string
      companyName?: string
      address?: string
      city?: string
    }
  }
  isIndependent: boolean
}

const statusConfig = {
  PENDING: {
    label: 'Pendente',
    color: 'yellow',
    icon: Clock,
    description: 'Reparação registada, aguardando início'
  },
  IN_PROGRESS: {
    label: 'Em Progresso',
    color: 'blue',
    icon: Wrench,
    description: 'Reparação em andamento'
  },
  WAITING_PARTS: {
    label: '<PERSON><PERSON>and<PERSON>',
    color: 'orange',
    icon: Package,
    description: 'Aguardando chegada de peça<PERSON>'
  },
  COMPLETED: {
    label: 'Concluída',
    color: 'green',
    icon: CheckCircle,
    description: 'Reparação concluída com sucesso'
  },
  CANCELLED: {
    label: 'Cancelada',
    color: 'red',
    icon: AlertCircle,
    description: 'Reparação cancelada'
  }
}

export default function TrackRepairPage() {
  const params = useParams()
  const [repair, setRepair] = useState<RepairTracking | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (params.code) {
      fetchRepairData(params.code as string)
    }
  }, [params.code])

  const fetchRepairData = async (trackingCode: string) => {
    try {
      const response = await fetch(`/api/track/${trackingCode}`)
      
      if (response.ok) {
        const data = await response.json()
        setRepair(data.repair)
      } else if (response.status === 404) {
        setError('Código de reparação não encontrado')
      } else {
        setError('Erro ao carregar dados da reparação')
      }
    } catch (error) {
      console.error('Erro ao buscar reparação:', error)
      setError('Erro ao carregar dados da reparação')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dados da reparação...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-900 mb-2">Reparação Não Encontrada</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Search className="w-4 h-4 mr-2" />
            Procurar Outra Reparação
          </Link>
        </div>
      </div>
    )
  }

  if (!repair) {
    return null
  }

  const statusInfo = statusConfig[repair.status as keyof typeof statusConfig] || statusConfig.PENDING
  const StatusIcon = statusInfo.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-xl font-bold text-black">
              Revify
            </Link>
            <div className="text-sm text-gray-600">
              Acompanhamento de Reparação
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Status Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              Reparação #{repair.trackingCode}
            </h1>
            <div className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
              statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
              statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
              statusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800' :
              'bg-red-100 text-red-800'
            }`}>
              <StatusIcon className="w-4 h-4 mr-2" />
              {statusInfo.label}
            </div>
          </div>

          <p className="text-gray-600 mb-4">{statusInfo.description}</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Criada em:</span>
              <div className="font-medium">
                {new Date(repair.createdAt).toLocaleDateString('pt-PT', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
            <div>
              <span className="text-gray-500">Previsão de conclusão:</span>
              <div className="font-medium">
                {new Date(repair.estimatedCompletionDate).toLocaleDateString('pt-PT', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric'
                })}
              </div>
            </div>
            <div>
              <span className="text-gray-500">Preço estimado:</span>
              <div className="font-medium text-green-600">€{repair.estimatedPrice.toFixed(2)}</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Dados do Cliente */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <User className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Dados do Cliente</h3>
            </div>

            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-500">Nome:</span>
                <div className="font-medium">{repair.customerName}</div>
              </div>
              <div>
                <span className="text-sm text-gray-500">Telefone:</span>
                <div className="font-medium flex items-center">
                  <Phone className="w-4 h-4 mr-2 text-gray-400" />
                  {repair.customerPhone}
                </div>
              </div>
              {repair.customerEmail && (
                <div>
                  <span className="text-sm text-gray-500">Email:</span>
                  <div className="font-medium flex items-center">
                    <Mail className="w-4 h-4 mr-2 text-gray-400" />
                    {repair.customerEmail}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Dados do Dispositivo */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <Package className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Dispositivo</h3>
            </div>

            <div className="space-y-3">
              <div>
                <span className="text-sm text-gray-500">Marca e Modelo:</span>
                <div className="font-medium">{repair.deviceBrand} {repair.deviceModel}</div>
              </div>
              {repair.deviceDescription && (
                <div>
                  <span className="text-sm text-gray-500">Descrição:</span>
                  <div className="font-medium">{repair.deviceDescription}</div>
                </div>
              )}
              <div>
                <span className="text-sm text-gray-500">Tipo de Problema:</span>
                <div className="font-medium">{repair.problemType}</div>
              </div>
              <div>
                <span className="text-sm text-gray-500">Descrição do Problema:</span>
                <div className="font-medium">{repair.problemDescription}</div>
              </div>
            </div>
          </div>

          {/* Dados da Loja */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 lg:col-span-2">
            <div className="flex items-center mb-4">
              <Wrench className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Loja Responsável</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-500">Nome da Loja:</span>
                <div className="font-medium">
                  {repair.repairShop.profile?.companyName || repair.repairShop.name}
                </div>
              </div>
              {repair.repairShop.profile?.phone && (
                <div>
                  <span className="text-sm text-gray-500">Telefone:</span>
                  <div className="font-medium flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-gray-400" />
                    {repair.repairShop.profile.phone}
                  </div>
                </div>
              )}
              {repair.repairShop.profile?.address && (
                <div className="md:col-span-2">
                  <span className="text-sm text-gray-500">Endereço:</span>
                  <div className="font-medium">
                    {repair.repairShop.profile.address}
                    {repair.repairShop.profile.city && `, ${repair.repairShop.profile.city}`}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Informações Adicionais */}
        {repair.isIndependent && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-blue-600 mr-2" />
              <div>
                <h4 className="font-medium text-blue-900">Reparação Independente</h4>
                <p className="text-sm text-blue-700">
                  Esta reparação foi criada diretamente pela loja. Para questões sobre pagamento, 
                  contacte diretamente a loja responsável.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Botão de Contacto */}
        <div className="mt-6 text-center">
          <p className="text-gray-600 mb-4">
            Tem questões sobre a sua reparação?
          </p>
          {repair.repairShop.profile?.phone && (
            <a
              href={`tel:${repair.repairShop.profile.phone}`}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Phone className="w-4 h-4 mr-2" />
              Contactar Loja
            </a>
          )}
        </div>
      </div>
    </div>
  )
}
