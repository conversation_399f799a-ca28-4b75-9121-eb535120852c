'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, CreditCard, MapPin, User, Mail, Phone, Lock, Tag, X, Check } from 'lucide-react'

interface CartItem {
  id: string
  productId: string
  quantity: number
  product: {
    id: string
    name: string
    price: number
    images: string[]
    condition: string
    seller: {
      name: string
    }
  }
}

interface Address {
  id: string
  street: string
  city: string
  postalCode: string
  country: string
  isDefault: boolean
}

export default function CheckoutPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [addresses, setAddresses] = useState<Address[]>([])
  const [selectedAddress, setSelectedAddress] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('card')

  // Estados para cupões
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null)
  const [couponError, setCouponError] = useState('')
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false)

  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  })

  const [newAddress, setNewAddress] = useState({
    label: '',
    street: '',
    city: '',
    postalCode: '',
    country: 'Portugal'
  })

  const [showNewAddressForm, setShowNewAddressForm] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [cartRes, addressesRes, profileRes] = await Promise.all([
        fetch('/api/marketplace/cart'),
        fetch('/api/cliente/addresses'),
        fetch('/api/cliente/profile')
      ])

      if (cartRes.ok) {
        const cartData = await cartRes.json()
        setCartItems(cartData.items)
        
        if (cartData.items.length === 0) {
          router.push('/marketplace/cart')
          return
        }
      }

      if (addressesRes.ok) {
        const addressData = await addressesRes.json()
        setAddresses(addressData.addresses)
        
        const defaultAddress = addressData.addresses.find((addr: Address) => addr.isDefault)
        if (defaultAddress) {
          setSelectedAddress(defaultAddress.id)
        }
      }

      if (profileRes.ok) {
        const profileData = await profileRes.json()
        setCustomerInfo({
          name: profileData.customerName || session?.user?.name || '',
          email: session?.user?.email || '',
          phone: profileData.customerPhone || ''
        })
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const addNewAddress = async () => {
    if (!newAddress.label || !newAddress.street || !newAddress.city || !newAddress.postalCode) {
      alert('Por favor, preencha todos os campos do endereço')
      return
    }

    try {
      const response = await fetch('/api/cliente/addresses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newAddress)
      })

      if (response.ok) {
        const addressData = await response.json()
        setAddresses([...addresses, addressData.address])
        setSelectedAddress(addressData.address.id)
        setShowNewAddressForm(false)
        setNewAddress({
          label: '',
          street: '',
          city: '',
          postalCode: '',
          country: 'Portugal'
        })
      }
    } catch (error) {
      console.error('Erro ao adicionar endereço:', error)
    }
  }

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError('Digite um código de cupão')
      return
    }

    setIsValidatingCoupon(true)
    setCouponError('')

    try {
      const response = await fetch('/api/marketplace/validate-coupon', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: couponCode.toUpperCase(),
          cartItems: cartItems,
          subtotal: calculateSubtotal()
        })
      })

      const data = await response.json()

      if (response.ok) {
        setAppliedCoupon(data.coupon)
        setCouponError('')
      } else {
        setCouponError(data.message || 'Cupão inválido')
        setAppliedCoupon(null)
      }
    } catch (error) {
      console.error('Erro ao validar cupão:', error)
      setCouponError('Erro ao validar cupão')
      setAppliedCoupon(null)
    } finally {
      setIsValidatingCoupon(false)
    }
  }

  const removeCoupon = () => {
    setAppliedCoupon(null)
    setCouponCode('')
    setCouponError('')
  }

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  }

  const calculateDiscount = () => {
    if (!appliedCoupon) return 0

    const subtotal = calculateSubtotal()
    let discount = 0

    if (appliedCoupon.type === 'PERCENTAGE') {
      discount = (subtotal * appliedCoupon.value) / 100
      if (appliedCoupon.maxDiscountValue && discount > appliedCoupon.maxDiscountValue) {
        discount = appliedCoupon.maxDiscountValue
      }
    } else {
      discount = appliedCoupon.value
    }

    return Math.min(discount, subtotal)
  }

  const calculateTotal = () => {
    return calculateSubtotal() - calculateDiscount()
  }

  const processCheckout = async () => {
    if (!selectedAddress) {
      alert('Por favor, selecione um endereço de entrega')
      return
    }

    if (!customerInfo.name || !customerInfo.email) {
      alert('Por favor, preencha suas informações pessoais')
      return
    }

    setIsProcessing(true)
    try {
      const response = await fetch('/api/marketplace/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          addressId: selectedAddress,
          customerInfo,
          paymentMethod,
          coupon: appliedCoupon ? {
            id: appliedCoupon.id,
            code: appliedCoupon.code,
            discountValue: calculateDiscount()
          } : null
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.redirectUrl) {
          // Para Multibanco, redirecionar diretamente para página de sucesso
          window.location.href = data.redirectUrl
        } else if (data.checkoutUrl) {
          // Para outros métodos, ir para Stripe Checkout
          window.location.href = data.checkoutUrl
        }
      } else {
        const error = await response.json()
        alert(`Erro: ${error.message}`)
      }
    } catch (error) {
      console.error('Erro no checkout:', error)
      alert('Erro ao processar pagamento')
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/marketplace/cart"
                className="flex items-center text-gray-600 hover:text-black transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar ao Carrinho
              </Link>
            </div>
            <h1 className="text-xl font-semibold text-gray-900">Finalizar Compra</h1>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                Informações Pessoais
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nome Completo *
                  </label>
                  <input
                    type="text"
                    value={customerInfo.name}
                    onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Telefone
                  </label>
                  <input
                    type="tel"
                    value={customerInfo.phone}
                    onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  />
                </div>
              </div>
            </div>

            {/* Delivery Address */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                Endereço de Entrega
              </h2>

              {addresses.length > 0 && (
                <div className="space-y-3 mb-4">
                  {addresses.map((address) => (
                    <label key={address.id} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="address"
                        value={address.id}
                        checked={selectedAddress === address.id}
                        onChange={(e) => setSelectedAddress(e.target.value)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {address.street}
                        </div>
                        <div className="text-sm text-gray-600">
                          {address.city}, {address.postalCode}, {address.country}
                        </div>
                        {address.isDefault && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            Padrão
                          </span>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              )}

              <button
                onClick={() => setShowNewAddressForm(!showNewAddressForm)}
                className="text-black hover:text-gray-700 text-sm font-medium"
              >
                + Adicionar novo endereço
              </button>

              {showNewAddressForm && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nome do Endereço *
                      </label>
                      <input
                        type="text"
                        value={newAddress.label}
                        onChange={(e) => setNewAddress({...newAddress, label: e.target.value})}
                        placeholder="Ex: Casa, Trabalho, etc."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Rua e Número *
                      </label>
                      <input
                        type="text"
                        value={newAddress.street}
                        onChange={(e) => setNewAddress({...newAddress, street: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Cidade *
                      </label>
                      <input
                        type="text"
                        value={newAddress.city}
                        onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Código Postal *
                      </label>
                      <input
                        type="text"
                        value={newAddress.postalCode}
                        onChange={(e) => setNewAddress({...newAddress, postalCode: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-4">
                    <button
                      onClick={() => setShowNewAddressForm(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={addNewAddress}
                      className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                    >
                      Adicionar
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Resumo do Pedido
              </h2>

              {/* Items */}
              <div className="space-y-3 mb-6">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      {item.product.images.length > 0 ? (
                        <img
                          src={item.product.images[0]}
                          alt={item.product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                          Sem Imagem
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {item.product.name}
                      </p>
                      <p className="text-sm text-gray-600">
                        Qtd: {item.quantity}
                      </p>
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      €{(item.product.price * item.quantity).toFixed(2)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Coupon Section */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <Tag className="w-4 h-4 mr-2" />
                  Cupão de Desconto
                </h3>

                {!appliedCoupon ? (
                  <div className="space-y-3">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                        placeholder="Digite o código do cupão"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 text-sm"
                        disabled={isValidatingCoupon}
                      />
                      <button
                        onClick={validateCoupon}
                        disabled={isValidatingCoupon || !couponCode.trim()}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                      >
                        {isValidatingCoupon ? 'Validando...' : 'Aplicar'}
                      </button>
                    </div>

                    {couponError && (
                      <p className="text-sm text-red-600 flex items-center">
                        <X className="w-4 h-4 mr-1" />
                        {couponError}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Check className="w-4 h-4 text-green-600 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-green-800">
                            {appliedCoupon.code}
                          </p>
                          <p className="text-xs text-green-600">
                            {appliedCoupon.name}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-semibold text-green-800">
                          -€{calculateDiscount().toFixed(2)}
                        </span>
                        <button
                          onClick={removeCoupon}
                          className="text-green-600 hover:text-green-800"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Totals */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">€{calculateSubtotal().toFixed(2)}</span>
                </div>
                {appliedCoupon && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Desconto ({appliedCoupon.code})</span>
                    <span className="font-medium text-green-600">-€{calculateDiscount().toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Envio</span>
                  <span className="font-medium text-green-600">Grátis</span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-base font-semibold text-gray-900">Total</span>
                    <span className="text-base font-semibold text-gray-900">
                      €{calculateTotal().toFixed(2)}
                    </span>
                  </div>
                  {appliedCoupon && (
                    <div className="text-xs text-green-600 mt-1">
                      Poupou €{calculateDiscount().toFixed(2)} com o cupão!
                    </div>
                  )}
                </div>
              </div>

              {/* Payment Method Selection */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Método de Pagamento</h3>
                <div className="space-y-3">
                  <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mr-3"
                    />
                    <CreditCard className="w-5 h-5 mr-3 text-gray-600" />
                    <div>
                      <div className="font-medium text-gray-900">Cartão de Crédito/Débito</div>
                      <div className="text-sm text-gray-600">Visa, Mastercard, American Express</div>
                    </div>
                  </label>

                  <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="multibanco"
                      checked={paymentMethod === 'multibanco'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mr-3"
                    />
                    <div className="w-5 h-5 mr-3 bg-red-600 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">MB</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Multibanco</div>
                      <div className="text-sm text-gray-600">Referência Multibanco</div>
                    </div>
                  </label>

                  <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="klarna"
                      checked={paymentMethod === 'klarna'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="mr-3"
                    />
                    <div className="w-5 h-5 mr-3 bg-pink-500 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-bold">K</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Klarna</div>
                      <div className="text-sm text-gray-600">Pague em 3x sem juros</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Payment Info */}
              {paymentMethod === 'multibanco' && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-red-600 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">MB</span>
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-800">
                        <strong>Multibanco:</strong> Será gerada uma referência para pagamento após confirmação.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {paymentMethod === 'klarna' && (
                <div className="bg-pink-50 border border-pink-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-6 h-6 bg-pink-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs font-bold">K</span>
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-pink-800">
                        <strong>Klarna:</strong> Pague em 3 prestações sem juros.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Checkout Button */}
              <button
                onClick={processCheckout}
                disabled={isProcessing || !selectedAddress}
                className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isProcessing ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                ) : (
                  <CreditCard className="w-5 h-5 mr-2" />
                )}
                {isProcessing ? 'Processando...' : `Pagar com ${
                  paymentMethod === 'card' ? 'Cartão' :
                  paymentMethod === 'multibanco' ? 'Multibanco' :
                  paymentMethod === 'klarna' ? 'Klarna' : 'Cartão'
                }`}
              </button>

              <div className="flex items-center justify-center mt-4 text-xs text-gray-500">
                <Lock className="w-4 h-4 mr-1" />
                Pagamento seguro com Stripe
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
