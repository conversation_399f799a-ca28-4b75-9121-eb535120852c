const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testSearchAPI() {
  try {
    console.log('🔍 Testando API de pesquisa...')
    
    // Encontrar o lojista de teste
    const lojista = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        role: 'REPAIR_SHOP'
      }
    })

    if (!lojista) {
      console.log('❌ Lojista de teste não encontrado')
      return
    }

    console.log('✅ Lojista encontrado:', lojista.name, `(${lojista.email})`)

    // Simular a lógica da API de pesquisa
    const query = 'iPhone'
    const allResults = []

    // 1. Pesquisar produtos
    console.log('\n🔍 Pesquisando produtos...')
    const products = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: lojista.id,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        name: true,
        price: true,
        stock: true,
        isActive: true
      },
      take: 5
    })

    products.forEach(product => {
      allResults.push({
        id: product.id,
        type: 'product',
        title: product.name,
        subtitle: `Produto - €${Number(product.price).toFixed(2)}`,
        description: `Stock: ${product.stock} | ${product.isActive ? 'Ativo' : 'Inativo'}`,
        url: `/lojista/loja-online/produtos/${product.id}`
      })
    })

    console.log(`✅ Produtos encontrados: ${products.length}`)
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - €${Number(product.price).toFixed(2)}`)
    })

    // 2. Pesquisar clientes
    console.log('\n🔍 Pesquisando clientes...')
    const customers = await prisma.user.findMany({
      where: {
        role: 'CUSTOMER',
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        name: true,
        email: true
      },
      take: 5
    })

    customers.forEach(customer => {
      allResults.push({
        id: customer.id,
        type: 'customer',
        title: customer.name || 'Cliente sem nome',
        subtitle: `Cliente - ${customer.email}`,
        description: 'Cliente da plataforma',
        url: `/lojista/clientes/${customer.id}`
      })
    })

    console.log(`✅ Clientes encontrados: ${customers.length}`)
    customers.forEach((customer, index) => {
      console.log(`${index + 1}. ${customer.name} - ${customer.email}`)
    })

    // 3. Pesquisar reparações
    console.log('\n🔍 Pesquisando reparações...')
    const repairs = await prisma.repair.findMany({
      where: {
        repairShopId: lojista.id,
        OR: [
          { description: { contains: query, mode: 'insensitive' } },
          { customerName: { contains: query, mode: 'insensitive' } },
          { customerPhone: { contains: query, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        description: true,
        customerName: true,
        status: true
      },
      take: 5
    })

    repairs.forEach(repair => {
      allResults.push({
        id: repair.id,
        type: 'repair',
        title: `Reparação - ${repair.customerName}`,
        subtitle: `Reparação - Status: ${repair.status}`,
        description: repair.description,
        url: `/lojista/reparacoes/${repair.id}`
      })
    })

    console.log(`✅ Reparações encontradas: ${repairs.length}`)
    repairs.forEach((repair, index) => {
      console.log(`${index + 1}. ${repair.customerName} - ${repair.description}`)
    })

    console.log(`\n🔍 Total de resultados: ${allResults.length}`)
    console.log('\n📋 Resultados finais:')
    allResults.forEach((result, index) => {
      console.log(`${index + 1}. [${result.type.toUpperCase()}] ${result.title}`)
      console.log(`   ${result.subtitle}`)
      console.log(`   ${result.description}`)
      console.log(`   URL: ${result.url}`)
      console.log('')
    })

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testSearchAPI()
