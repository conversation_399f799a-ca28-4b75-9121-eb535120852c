import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { sendCampaign } from '@/lib/newsletter'

export async function POST(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const campaignId = params.id

    // Enviar campanha
    const result = await sendCampaign(campaignId, session.user.id)

    if (result.success) {
      return NextResponse.json({
        message: Campanha enviada com sucesso,
        stats: result.stats
      
})
    } else {
      return NextResponse.json(
        { message: result.error || 'Erro ao enviar campanha' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Erro ao enviar campanha:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
