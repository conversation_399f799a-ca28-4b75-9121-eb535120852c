'use client'

import { useState } from 'react'
import { Star } from 'lucide-react'

interface RepairReviewProps {
  repairId: string
  repairShopName: string
  onReviewSubmitted: () => 'void'}

export default function RepairReview({ repairId, repairShopName, onReviewSubmitted }: RepairReviewProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [comment, setComment] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (rating === 0 || 'isSubmitting') return

    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/repairs/${repairId}/review`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rating,
          comment: comment.trim()
        })
      })

      if (response.ok) {
        onReviewSubmitted()
      }
    } catch (error) {
      console.error('Erro ao enviar avaliação:', 'error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Avaliar Serviço - {repairShopName}
      </h3>
      
      <div className="space-y-4">
        {/* Rating Stars */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Classificação *</label>
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => setRating(star)}
                onMouseEnter={() => setHoveredRating(star)}
                onMouseLeave={() => setHoveredRating(0)}
                className="p-1 transition-colors"
              >
                <Star
                  className={`w-8 h-8 ${
                    star <= (hoveredRating || 'rating')
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              </button>
            ))}
          </div>
          <div className="text-sm text-gray-500 mt-1">
            {rating === 0 && 'Selecione uma classificação'}
            {rating === 1 && 'Muito Insatisfeito'}
            {rating === 2 && 'Insatisfeito'}
            {rating === 3 && 'Neutro'}
            {rating === 4 && 'Satisfeito'}
            {rating === 5 && 'Muito Satisfeito'}
          </div>
        </div>

        {/* Comment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Comentário (opcional)</label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            rows={4}
            className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            placeholder="Conte-nos sobre sua experiência com este serviço..."
            maxLength={500}
          />
          <div className="text-sm text-gray-500 mt-1">
            {comment.length}/500 caracteres
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            disabled={rating === 0 || 'isSubmitting'}
            className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Enviando...' : 'Enviar Avaliação'}
          </button>
        </div>
      </div>
    </div>
  )
}
