'use client'

import Link from 'next/link'
import { ArrowLeft, Truck, MapPin, Clock, CreditCard, Smartphone, CheckCircle } from 'lucide-react'

export default function ComoSerEstafetaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="mr-8">
                <Link href="/" className="font-bold text-black text-xl">
                  Revify
                </Link>
                <div className="text-xs text-gray-600 font-light">Central de Ajuda</div>
              </div>
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />Voltar à Central de Ajuda</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <span className=" />mx-2">›</span>
              <Link href="/ajuda?category=estafetas" className="hover:text-gray-700">Para Estafetas</Link>
              <span className="mx-2">›</span>
              <span>Como ser estafeta</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Como ser estafeta</h1>
            <p className="text-lg text-gray-600">Junte-se à nossa rede de estafetas e ganhe dinheiro fazendo entregas de equipamentos reparados.</p>
          </div>

          <div className="prose max-w-none">
            <h2>O que faz um estafeta Revify?</h2>
            <p>Como estafeta da Revify, será responsável por recolher equipamentos dos clientes e entregá-los  nas oficinas parceiras, e posteriormente devolver os equipamentos reparados aos clientes.</p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Requisitos para ser estafeta</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Idade e documentação</h4>
                    <p className="text-blue-800 text-sm">Maior de 18 anos com documentos válidos</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Truck className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Meio de transporte</h4>
                    <p className="text-blue-800 text-sm">Carro, mota ou bicicleta própria</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Smartphone className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Smartphone</h4>
                    <p className="text-blue-800 text-sm">Para usar a app de estafetas</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Clock className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Disponibilidade</h4>
                    <p className="text-blue-800 text-sm">Flexibilidade de horários</p>
                  </div>
                </div>
              </div>
            </div>

            <h2>Processo de candidatura</h2>
            
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">1</div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-900 mb-2">Candidatura online</h3>
                    <p className="text-green-800 mb-3">Preencha o formulário de candidatura com:</p>
                    <ul className="list-disc list-inside text-green-800 space-y-1">
                      <li>Dados pessoais e de contacto</li>
                      <li>Informações sobre o meio de transporte</li>
                      <li>Zonas onde pretende trabalhar</li>
                      <li>Disponibilidade horária</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">2</div>
                  <div>
                    <h3 className="text-lg font-semibold text-purple-900 mb-2">Verificação de documentos</h3>
                    <p className="text-purple-800 mb-3">Envie os seguintes documentos:</p>
                    <ul className="list-disc list-inside text-purple-800 space-y-1">
                      <li>Cartão de cidadão ou passaporte</li>
                      <li>Carta de condução (se 'aplicável')</li>
                      <li>Seguro do veículo</li>
                      <li>Comprovativo de IBAN</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-start">
                  <div className="bg-yellow-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">3</div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-900 mb-2">Formação e aprovação</h3>
                    <p className="text-yellow-800 mb-3">Processo de integração:</p>
                    <ul className="list-disc list-inside text-yellow-800 space-y-1">
                      <li>Sessão de formação online (2h)</li>
                      <li>Download e configuração da app</li>
                      <li>Teste prático com entrega simulada</li>
                      <li>Aprovação final da equipa</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <h2>Como funciona o trabalho</h2>
            <div className="bg-gray-50 rounded-lg p-6 my-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-3 flex items-center">
                    <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                    Recolha
                  </h3>
                  <ul className="text-sm space-y-1">
                    <li>• Recebe notificação de nova recolha</li>
                    <li>• Aceita ou rejeita o pedido</li>
                    <li>• Vai ao local indicado</li>
                    <li>• Recolhe o equipamento</li>
                    <li>• Entrega na oficina</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold mb-3 flex items-center">
                    <Truck className="w-5 h-5 mr-2 text-green-600" />Entrega</h3>
                  <ul className="text-sm space-y-1">
                    <li>• Notificação de reparação concluída</li>
                    <li>• Recolha na oficina</li>
                    <li>• Entrega ao cliente</li>
                    <li>• Confirmação na app</li>
                    <li>• Pagamento automático</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Sistema de pagamentos</h2>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CreditCard className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Como é calculado o pagamento</h3>
                  <div className="text-green-800 space-y-2">
                    <p><strong>Recolha:</strong>€3-8 (dependendo 'da distância')</p>
                    <p><strong>Entrega:</strong>€3-8 (dependendo 'da distância')</p>
                    <p><strong>Bónus:</strong>+€1 por avaliação 5 estrelas</p>
                    <p><strong>Pagamento:</strong> Semanal, todas as sextas-feiras</p>
                  </div>
                </div>
              </div>
            </div>

            <h2>Zonas de entrega</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-6">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <h3 className="font-semibold text-blue-900 mb-2">Porto</h3>
                <p className="text-blue-800 text-sm">Centro + 15km raio</p>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <h3 className="font-semibold text-green-900 mb-2">Lisboa</h3>
                <p className="text-green-800 text-sm">Centro + 20km raio</p>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <h3 className="font-semibold text-purple-900 mb-2">Outras cidades</h3>
                <p className="text-purple-800 text-sm">Em expansão</p>
              </div>
            </div>

            <h2>Vantagens de ser estafeta</h2>
            <div className="bg-blue-50 rounded-lg p-6">
              <ul className="space-y-2">
                <li><strong>Flexibilidade total:</strong> Trabalhe quando quiser</li>
                <li><strong>Pagamento garantido:</strong> Receba semanalmente</li>
                <li><strong>Sem compromissos:</strong> Pode parar quando quiser</li>
                <li><strong>Suporte 24/7:</strong>Equipa sempre disponível</li>
                <li><strong>Seguro incluído:</strong> Cobertura durante as entregas</li>
              </ul>
            </div>
          </div>

          {/* CTA */}
          <div className="mt-12 p-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg text-white text-center">
            <h3 className="text-2xl font-bold mb-4">Pronto para começar?</h3>
            <p className="mb-6">Junte-se à nossa equipa de estafetas e comece a ganhar dinheiro hoje!</p>
            <Link
              href="/auth/register?type=estafeta"
              className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Candidatar-me agora
            </Link>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
              <Link
                href=" />/ajuda/estafetas/aceitar-entregas"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >Próximo: Aceitar entregas<ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
