'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import {
  Store,
  Package,
  Users,
  ShoppingCart,
  Settings,
  BarChart3,
  Globe,
  Plus,
  Eye,
  TrendingUp,
  DollarSign,
  FileText,
  Tag
} from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'


interface OnlineStoreStats {
  totalProducts: number
  totalOrders: number
  totalCustomers: number
  monthlyRevenue: number
  isActive: boolean
  customSubdomain?: string
}

export default function LojaOnlinePage() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<OnlineStoreStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCustomers: 0,
    monthlyRevenue: 0,
    isActive: false
  })
  const [isLoading, setIsLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    if (session?.user?.id) {
      checkAccess()
      fetchStats()
    }
  }, [session])

  const checkAccess = async () => {
    try {
      const response = await fetch('/api/lojista/loja-online/config')
      if (response.ok) {
        const data = await response.json()
        setHasAccess(data.hasAccess)
        setStats(prev => ({
          ...prev,
          isActive: data.isActive,
          customSubdomain: data.customSubdomain
        }))
      }
    } catch (error) {
      console.error('Erro ao verificar acesso:', error)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/lojista/loja-online/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(prev => ({ ...prev, ...data.stats }))
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-14">
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold text-black">Revify</Link>
                <span className="ml-3 text-xs text-gray-500">Loja Online</span>
              </div>
              <div className="flex items-center space-x-3">
                <NotificationDropdown />
                <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
                <UserDropdown user={session?.user || {}} />
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja Online Não Disponível</h1>
            <p className="text-gray-600 mb-8">
              A funcionalidade de Loja Online não está disponível no seu plano atual.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h3 className="font-semibold text-blue-900 mb-2">O que inclui a Loja Online?</h3>
              <ul className="text-sm text-blue-800 space-y-2 text-left">
                <li>• E-commerce completo com carrinho de compras</li>
                <li>• Área de cliente isolada</li>
                <li>• Catálogo de produtos personalizado</li>
                <li>• Sistema de pagamentos e envios</li>
                <li>• Gestão de cupões e descontos</li>
                <li>• Subdomínio personalizado</li>
                <li>• Relatórios e analytics</li>
              </ul>
            </div>
            <Link
              href="/lojista/planos"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Ver Planos Disponíveis
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const quickActions = [
    { name: 'Produtos', href: '/lojista/loja-online/produtos', icon: Package, description: 'Gerir catálogo de produtos' },
    { name: 'Categorias', href: '/lojista/loja-online/categorias', icon: Tag, description: 'Gerir categorias da loja' },
    { name: 'Páginas', href: '/lojista/loja-online/paginas', icon: FileText, description: 'Gerir páginas customizadas' },
    { name: 'Encomendas', href: '/lojista/loja-online/encomendas', icon: ShoppingCart, description: 'Ver e gerir encomendas' },
    { name: 'Clientes', href: '/lojista/loja-online/clientes', icon: Users, description: 'Gerir base de clientes' },
    { name: 'Configurações', href: '/lojista/loja-online/configuracoes', icon: Settings, description: 'Configurar loja online' },
  ]

  return (
    <div className="min-h-screen bg-gray-50">

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Loja Online</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Loja Online</h1>
            <p className="text-gray-600">Gerencie o seu e-commerce</p>
          </div>
          <div className="flex items-center space-x-3">
            {stats.customSubdomain && (
              <a
                href={`https://${stats.customSubdomain}.revify.pt`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Globe className="w-4 h-4 mr-2" />
                Ver Loja
              </a>
            )}
            <Link
              href="/lojista/loja-online/configuracoes"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              <Settings className="w-4 h-4 mr-2" />
              Configurações
            </Link>
          </div>
        </div>

        {/* Status da Loja */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${stats.isActive ? 'bg-indigo-500' : 'bg-red-500'}`}></div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Status da Loja: {stats.isActive ? 'Ativa' : 'Inativa'}
                </h3>
                <p className="text-gray-600">
                  {stats.customSubdomain 
                    ? `Disponível em: ${stats.customSubdomain}.revify.pt`
                    : 'Configure o subdomínio para ativar a loja'
                  }
                </p>
              </div>
            </div>
            {!stats.isActive && (
              <Link
                href="/lojista/loja-online/configuracoes"
                className="px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700"
              >
                Ativar Loja
              </Link>
            )}
          </div>
        </div>

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Produtos</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <ShoppingCart className="w-6 h-6 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Encomendas</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Clientes</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Receita Mensal</p>
                <p className="text-2xl font-bold text-gray-900">€{stats.monthlyRevenue.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Ações Rápidas</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link
                key={action.name}
                href={action.href}
                className="group p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
              >
                <div className="flex items-center mb-3">
                  <action.icon className="w-6 h-6 text-gray-600 group-hover:text-blue-600" />
                  <h4 className="ml-3 font-medium text-gray-900 group-hover:text-blue-900">
                    {action.name}
                  </h4>
                </div>
                <p className="text-sm text-gray-600">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
