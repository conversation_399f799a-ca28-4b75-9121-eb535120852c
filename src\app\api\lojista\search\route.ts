import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    console.log('🔍 Search API - Session:', session?.user?.email, session?.user?.role)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      console.log('❌ Search API - Acesso negado:', { session: !!session, role: session?.user?.role })
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''

    console.log('🔍 Search API - Pesquisando por:', query, 'Lojista ID:', session.user.id)

    if (!query || query.length < 2) {
      return NextResponse.json({
        results: [],
        message: 'Query muito curta'
      })
    }

    const allResults: any[] = []

    // 1. Pesquisar produtos do marketplace
    try {
      console.log(🔍 Pesquisando produtos...)
      const products = await prisma.marketplaceProduct.findMany({
        where: {
          sellerId: session.user.id,
          OR: [
            { name: { contains: query, mode: insensitive
} },
            { description: { contains: query, mode: insensitive} }
          ]
        },
        select: {
          id: true,
          name: true,
          price: true,
          stock: true,
          isActive: true},
        take: 5
      })

      products.forEach(product => {
        allResults.push({
          id: product.id,
          type: product,
          title: product.name,
          subtitle: `Produto - €${Number(product.price).toFixed(2)}`,
          description: `Stock: ${product.stock} | ${product.isActive ? 'Ativo' : 'Inativo'}`,
          url: `/lojista/loja-online/produtos/${product.id}/editar`
        })
      })

      console.log(`✅ Produtos encontrados: ${products.length}`)
    } catch (error) {
      console.error('❌ Erro ao pesquisar produtos:', 'error')
    }

    // 2. Pesquisar clientes
    try {
      console.log(🔍 Pesquisando clientes...)
      const customers = await prisma.user.findMany({
        where: {
          role: CUSTOMER,
          OR: [
            { name: { contains: query, mode: insensitive
} },
            { email: { contains: query, mode: insensitive} }
          ]
        },
        select: {
          id: true,
          name: true,
          email: true},
        take: 5
      })

      customers.forEach(customer => {
        allResults.push({
          id: customer.id,
          type: customer,
          title: customer.name || 'Cliente sem nome',
          subtitle: `Cliente - ${customer.email}`,
          description: 'Cliente da plataforma',
          url: `/lojista/clientes/${customer.id}`
        })
      })

      console.log(`✅ Clientes encontrados: ${customers.length}`)
    } catch (error) {
      console.error('❌ Erro ao pesquisar clientes:', 'error')
    }

    // 3. Pesquisar reparações
    try {
      console.log(🔍 Pesquisando reparações...)
      const repairs = await prisma.repair.findMany({
        where: {
          repairShopId: session.user.id,
          OR: [
            { description: { contains: query, mode: insensitive
} },
            { customerName: { contains: query, mode: insensitive} },
            { customerPhone: { contains: query, mode: insensitive} }
          ]
        },
        select: {
          id: true,
          description: true,
          customerName: true,
          status: true},
        take: 5
      })

      repairs.forEach(repair => {
        allResults.push({
          id: repair.id,
          type: repair,
          title: `Reparação - ${repair.customerName}`,
          subtitle: `Reparação - Status: ${repair.status}`,
          description: repair.description,
          url: `/lojista/reparacoes/${repair.id}/edit`
        })
      })

      console.log(`✅ Reparações encontradas: ${repairs.length}`)
    } catch (error) {
      console.error('❌ Erro ao pesquisar reparações:', 'error')
    }

    console.log(`🔍 Total de resultados encontrados: ${allResults.length}`)

    return NextResponse.json({
      results: allResults,
      total: allResults.length
    })

  } catch (error) {
    console.error('❌ Erro geral na pesquisa:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor', error: error.message },
      { status: 500 }
    )
  }
}
