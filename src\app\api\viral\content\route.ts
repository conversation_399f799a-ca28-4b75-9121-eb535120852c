import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'all'
    const userId = searchParams.get('userId') || session.user.id

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      const whereClause: any = {
        userId: userId
}

      if (type !== 'all') {
        whereClause.type = type.toUpperCase()
      }

      const content = await prisma.shareableContent.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              name: true,
              email: true}
          }
        },
        orderBy: {
          createdAt: desc},
        take: 20
      })

      const formattedContent = content.map(item => ({
        id: item.id,
        type: item.type.toLowerCase(),
        title: item.title,
        description: item.description,
        imageUrl: item.imageUrl,
        videoUrl: item.videoUrl,
        metadata: item.metadata || {},
        likes: item.likes || 0,
        comments: item.comments || 0,
        views: item.views || 0,
        shares: item.shares || 0,
        createdAt: item.createdAt.toISOString(),
        user: {
          name: item.user.name || item.user.email.split('@')[0]
        }
      }))

      return NextResponse.json(formattedContent)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao buscar conteúdo:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { type, title, description, imageUrl, videoUrl, metadata } = await request.json()

    if (!type || !title) {
      return NextResponse.json(
        { error: "Tipo e título são obrigatórios" },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      const content = await prisma.shareableContent.create({
        data: {
          userId: session.user.id,
          type: type.toUpperCase(),
          title,
          description,
          imageUrl,
          videoUrl,
          metadata: metadata || {
},
          likes: 0,
          comments: 0,
          views: 0,
          shares: 0
        },
        include: {
          user: {
            select: {
              name: true,
              email: true}
          }
        }
      })

      // Criar atividade viral para o conteúdo criado
      await prisma.viralActivity.create({
        data: {
          userId: session.user.id,
          type: CONTENT_SHARED,
          description: `${content.user.name || content.user.email.split(@)[0]
} partilhou: ${title}`,
          location: Portugal, // Seria obtido do perfil do usuário
          metadata: {
            contentId: content.id,
            contentType: type},
          isPublic: true}
      })

      const formattedContent = {
        id: content.id,
        type: content.type.toLowerCase(),
        title: content.title,
        description: content.description,
        imageUrl: content.imageUrl,
        videoUrl: content.videoUrl,
        metadata: content.metadata || {},
        likes: content.likes || 0,
        comments: content.comments || 0,
        views: content.views || 0,
        shares: content.shares || 0,
        createdAt: content.createdAt.toISOString(),
        user: {
          name: content.user.name || content.user.email.split(@)[0]
        
}
      }

      return NextResponse.json(formattedContent)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao criar conteúdo:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

// Endpoint para interações (like, comment, view, share)
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" 
},
        { status: 401 }
      )
    }

    const { contentId, action } = await request.json()

    if (!contentId || !action) {
      return NextResponse.json(
        { error: "ID do conteúdo e ação são obrigatórios" },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      const updateData: any = {
}

      switch (action) {
        case 'like':
          updateData.likes = { increment: 1 }
          break
        case 'view':
          updateData.views = { increment: 1 }
          break
        case 'share':
          updateData.shares = { increment: 1 }
          break
        case 'comment':
          updateData.comments = { increment: 1 }
          break
        default:
          return NextResponse.json(
            { error: "Ação inválida" },
            { status: 400 }
          )
      }

      const updatedContent = await prisma.shareableContent.update({
        where: { id: contentId},
        data: updateData,
        include: {
          user: {
            select: {
              name: true,
              email: true}
          }
        }
      })

      const formattedContent = {
        id: updatedContent.id,
        type: updatedContent.type.toLowerCase(),
        title: updatedContent.title,
        description: updatedContent.description,
        imageUrl: updatedContent.imageUrl,
        videoUrl: updatedContent.videoUrl,
        metadata: updatedContent.metadata || {},
        likes: updatedContent.likes || 0,
        comments: updatedContent.comments || 0,
        views: updatedContent.views || 0,
        shares: updatedContent.shares || 0,
        createdAt: updatedContent.createdAt.toISOString(),
        user: {
          name: updatedContent.user.name || updatedContent.user.email.split('@')[0]
        }
      }

      return NextResponse.json(formattedContent)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao atualizar conteúdo:", 'error')
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
