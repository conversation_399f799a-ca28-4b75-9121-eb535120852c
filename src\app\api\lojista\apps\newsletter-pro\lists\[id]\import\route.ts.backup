import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id: listId } = await params

    // Verificar se a lista pertence ao usuário
    const list = await prisma.$queryRaw`
      SELECT * FROM newsletter_lists
      WHERE id = ${listId} AND "userId" = ${session.user.id}
      LIMIT 1
    ` as any[]

    if (list.length === 0) {
      return NextResponse.json(
        { message: 'Lista não encontrada' },
        { status: 404 }
      )
    }

    const { contacts, source } = await request.json()

    if (!contacts || !Array.isArray(contacts)) {
      return NextResponse.json(
        { message: 'Lista de contactos é obrigatória' },
        { status: 400 }
      )
    }

    let processedContacts = []
    let errors = []

    // Processar contactos baseado na fonte
    if (source === 'csv') {
      // Contactos vêm de CSV upload
      processedContacts = contacts.map((contact: any, index: number) => {
        const email = contact.email?.trim().toLowerCase()
        const name = contact.name?.trim() || contact.nome?.trim() || ''
        const phone = contact.phone?.trim() || contact.telefone?.trim() || ''

        if (!email || !isValidEmail(email)) {
          errors.push(`Linha ${index + 1}: Email inválido ou em falta`)
          return null
        }

        return {
          email,
          name,
          phone,
          source: 'csv_import'
        }
      }).filter(Boolean)
    } else if (source === 'customers') {
      // Importar clientes existentes
      const customerIds = contacts.map((c: any) => c.id).filter(Boolean)
      
      if (customerIds.length === 0) {
        return NextResponse.json(
          { message: 'Nenhum cliente selecionado' },
          { status: 400 }
        )
      }

      // Buscar dados dos clientes
      const customers = await prisma.$queryRaw`
        SELECT u.id, u.name, u.email, p.phone
        FROM users u
        LEFT JOIN profiles p ON u.id = p."userId"
        WHERE u.id = ANY(${customerIds})
        AND u.email IS NOT NULL
      ` as any[]

      processedContacts = customers.map((customer: any) => ({
        email: customer.email.toLowerCase(),
        name: customer.name || '',
        phone: customer.phone || '',
        source: 'customer_import',
        customerId: customer.id
      }))
    } else if (source === 'manual') {
      // Contactos adicionados manualmente
      processedContacts = contacts.map((contact: any, index: number) => {
        const email = contact.email?.trim().toLowerCase()
        const name = contact.name?.trim() || ''
        const phone = contact.phone?.trim() || ''

        if (!email || !isValidEmail(email)) {
          errors.push(`Contacto ${index + 1}: Email inválido`)
          return null
        }

        return {
          email,
          name,
          phone,
          source: 'manual_entry'
        }
      }).filter(Boolean)
    }

    if (processedContacts.length === 0) {
      return NextResponse.json(
        { 
          message: 'Nenhum contacto válido para importar',
          errors 
        },
        { status: 400 }
      )
    }

    // Verificar contactos duplicados na lista
    const existingEmails = await prisma.$queryRaw`
      SELECT email FROM newsletter_subscribers
      WHERE "listId" = ${listId}
      AND email = ANY(${processedContacts.map(c => c.email)})
    ` as any[]

    const existingEmailSet = new Set(existingEmails.map((e: any) => e.email))
    const newContacts = processedContacts.filter(contact => !existingEmailSet.has(contact.email))
    const duplicateCount = processedContacts.length - newContacts.length

    // Inserir novos contactos
    let insertedCount = 0
    for (const contact of newContacts) {
      try {
        await prisma.$queryRaw`
          INSERT INTO newsletter_subscribers (
            "id", "listId", email, name, phone, source, "customerId",
            "isActive", "subscribedAt", "createdAt", "updatedAt"
          )
          VALUES (
            gen_random_uuid(), ${listId}, ${contact.email}, ${contact.name}, 
            ${contact.phone}, ${contact.source}, ${contact.customerId || null},
            true, NOW(), NOW(), NOW()
          )
        `
        insertedCount++
      } catch (error) {
        errors.push(`Erro ao inserir ${contact.email}: ${error}`)
      }
    }

    // Atualizar contador da lista
    await prisma.$queryRaw`
      UPDATE newsletter_lists
      SET "subscriberCount" = (
        SELECT COUNT(*) FROM newsletter_subscribers
        WHERE "listId" = ${listId} AND "isActive" = true
      ),
      "updatedAt" = NOW()
      WHERE id = ${listId}
    `

    return NextResponse.json({
      message: `Importação concluída`,
      summary: {
        total: processedContacts.length,
        imported: insertedCount,
        duplicates: duplicateCount,
        errors: errors.length
      },
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Erro ao importar contactos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
