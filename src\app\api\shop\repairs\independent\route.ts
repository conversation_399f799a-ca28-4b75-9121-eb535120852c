import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    // Import dinâmico do Prisma para evitar problemas de inicialização
    const { prisma } = await import(@/lib/prisma)
    const formData = await request.formData()
    
    // Extrair dados do formulário
    const categoryId = formData.get('categoryId') as string
    const brandId = formData.get('brandId') as string
    const deviceId = formData.get('deviceId') as string
    const problemTypeId = formData.get('problemTypeId') as string
    const description = formData.get('description') as string
    const customerName = formData.get('customerName') as string
    const customerPhone = formData.get('customerPhone') as string
    const customerNif = formData.get('customerNif') as string
    const paymentMethod = formData.get('paymentMethod') as string
    const shopSubdomain = formData.get('shopSubdomain') as string

    // Validar dados obrigatórios
    if (!categoryId || !brandId || !problemTypeId || !description || !customerName || !customerPhone || !shopSubdomain) {
      return NextResponse.json(
        { message: "Dados obrigatórios em falta" 
},
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomain
    const shopUser = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: shopSubdomain},
        role: REPAIR_SHOP},
      include: {
        profile: true}
    })

    if (!shopUser) {
      return NextResponse.json(
        { message: "Loja não encontrada" },
        { status: 404 }
      )
    }

    const repairShopId = shopUser.id

    // Buscar informações do dispositivo e problema
    const [category, brand, deviceModel, problemType] = await Promise.all([
      prisma.category.findUnique({ where: { id: categoryId} }),
      prisma.brand.findUnique({ where: { id: brandId} }),
      deviceId ? prisma.deviceModel.findUnique({ where: { id: deviceId} }) : null,
      prisma.problemType.findUnique({ where: { id: problemTypeId} })
    ])

    if (!category || !brand || !problemType) {
      return NextResponse.json(
        { message: "Dados do dispositivo inválidos" },
        { status: 400 }
      )
    }

    // Processar imagens se existirem
    const images: string[] = []
    const problemImages = formData.getAll(problemImages) as File[]
    
    if (problemImages.length > 0) {
      const uploadDir = join(process.cwd(), 'public', 'uploads', 'repairs')
      if (!existsSync(uploadDir)) {
        await mkdir(uploadDir, { recursive: true
})
      }

      for (const 'file of problemImages') {
        if (file.size > 0) {
          const bytes = await file.arrayBuffer()
          const buffer = Buffer.from(bytes)
          
          const timestamp = Date.now()
          const extension = file.name.split('.').pop()
          const filename = `repair_${timestamp}_${Math.random().toString(36).substring(7)}.${extension}`
          const filepath = join(uploadDir, 'filename')
          
          await writeFile(filepath, 'buffer')
          images.push(`/uploads/repairs/${filename}`)
        }
      }
    }

    // Gerar código de tracking único
    const trackingCode = `REP-${Date.now().toString().slice(-8)}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`

    // Buscar preço estimado
    let estimatedPrice = null
    try {
      const basePrice = await prisma.basePrice.findFirst({
        where: {
          categoryId: categoryId,
          problemTypeId: problemTypeId}
      })
      
      if (basePrice) {
        estimatedPrice = basePrice.price
      }
    } catch (error) {
      console.log(Erro ao buscar preço base:, 'error')
    
}

    // Criar reparação independente
    const repair = await prisma.repair.create({
      data: {
        customerId: repairShopId, // Para reparações independentes, o customer é a própria loja
        repairShopId: repairShopId,
        deviceModelId: deviceId || null,
        problemTypeId: problemTypeId,
        description: description,
        images: images,
        status: PENDING,
        customerName: customerName,
        customerPhone: customerPhone,
        customerNif: customerNif || null,
        deliveryMethod: STORE_PICKUP, // Reparações independentes são sempre pickup na loja
        trackingCode: trackingCode,
        estimatedPrice: estimatedPrice,
        // Campos específicos para identificar como independente
        deviceBrand: brand.name,
        deviceModel: deviceModel?.name || `${brand.name} (Modelo não especificado)`,
        problemType: problemType.name,
        // Método de pagamento será usado para determinar o fluxo
        stripeSessionId: paymentMethod === 'in_store' ? null : `payment_method_${paymentMethod
}`
      }
    })

    // Se for pagamento na loja, marcar como confirmado
    if (paymentMethod === in_store) {
      await prisma.repair.update({
        where: { id: repair.id 
},
        data: {
          status: CONFIRMED,
          confirmedAt: new Date()
        }
      })
    }

    return NextResponse.json({
      success: true,
      repairId: repair.id,
      trackingCode: repair.trackingCode,
      message: 'Reparação criada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao criar reparação independente:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
