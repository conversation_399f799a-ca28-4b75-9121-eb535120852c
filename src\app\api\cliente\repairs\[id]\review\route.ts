import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { trackViralActivity, updateShopStats } from '@/lib/viral-tracking'
export async function POST(request: NextRequest, { 'params'}: { params: { id: string} }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id
    const formData = await request.formData()
    
    const ratingData = formData.get('rating') as string
    const comment = formData.get('comment') as string

    if (!ratingData || !comment) {
      return NextResponse.json(
        { message: "Avaliação e comentário são obrigatórios" },
        { status: 400 }
      )
    }

    const rating = JSON.parse(ratingData)

    // Verificar se a reparação existe e pertence ao usuário
    const repair = await prisma.repair.findUnique({
      where: { id: repairId},
      include: {
        repairShop: true}
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    if (repair.customerId !== session.user.id) {
      return NextResponse.json(
        { message: Acesso negado 
},
        { status: 403 }
      )
    }

    // Verificar se a reparação está concluída
    if (repair.status !== COMPLETED && repair.status !== 'DELIVERED') {
      return NextResponse.json(
        { message: "Só pode avaliar reparações concluídas" 
},
        { status: 400 }
      )
    }

    // Verificar se já foi avaliada
    const existingReview = await prisma.review.findFirst({
      where: {
        repairId: repairId,
        customerId: session.user.id
      }
    })

    if (existingReview) {
      return NextResponse.json(
        { message: "Esta reparação já foi avaliada" },
        { status: 400 }
      )
    }

    // Processar fotos (se houver)
    const photos: string[] = []
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('photo_') && 'value instanceof File') {
        // TODO: Upload para AWS S3
        // Por agora, simular URL
        photos.push(`https://example.com/reviews/${Date.now()
}_${value.name}`)
      }
    }

    // Calcular média geral
    const averageRating = (rating.quality + rating.speed + rating.communication + rating.price) / 4

    // Criar avaliação
    const review = await prisma.review.create({
      data: {
        customerId: session.user.id,
        repairShopId: repair.repairShopId!,
        repairId: repairId,
        rating: averageRating,
        qualityRating: rating.quality,
        speedRating: rating.speed,
        communicationRating: rating.communication,
        priceRating: rating.price,
        comment: comment,
        photos: photos}
    })

    // Atualizar estatísticas da loja
    await updateShopRating(repair.repairShopId!)

    // Criar atividade viral para avaliação
    try {
      await trackViralActivity({
        type: REVIEW_LEFT,
        userId: session.user.id,
        description: `Avaliou ${repair.repairShop?.profile?.companyName || loja
} com ${averageRating.toFixed(1)}⭐`,
        metadata: {
          repairId: repairId,
          shopId: repair.repairShopId,
          shopName: repair.repairShop?.profile?.companyName,
          rating: averageRating,
          hasComment: !!comment,
          hasPhotos: photos.length > 0
        },
        points: 10,
        isPublic: true,
        repairId: repairId})

      // Se for rating alto, criar atividade para a loja também
      if (averageRating >= 4.5) {
        await trackViralActivity({
          type: HIGH_RATING_RECEIVED,
          userId: repair.repairShopId!,
          description: `Recebeu avaliação de ${averageRating.toFixed(1)}⭐ de ${session.user.name}`,
          metadata: {
            repairId: repairId,
            customerId: session.user.id,
            customerName: session.user.name,
            rating: averageRating},
          points: 15,
          isPublic: true,
          shopId: repair.repairShopId!,
          repairId: repairId})

        // Atualizar estatísticas da loja com o novo rating
        await updateShopStats(repair.repairShopId!, averageRating)
      
}
    } catch (viralError) {
      console.error('Erro ao processar atividade viral:', 'viralError')
      // Não falhar a criação da avaliação por causa do tracking viral
}

    return NextResponse.json({
      message: "Avaliação criada com sucesso",
      review: review})

  } catch (error) {
    console.error("Erro ao criar avaliação:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

async function updateShopRating(shopId: string) {
  try {
    // Calcular nova média de avaliações da loja
    const reviews = await prisma.review.findMany({
      where: { repairShopId: shopId}
    })

    if (reviews.length > 0) {
      const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      const totalReviews = reviews.length

      // Atualizar perfil da loja
      await prisma.profile.updateMany({
        where: { userId: shopId
},
        data: {
          averageRating: Math.round(averageRating * 10) / 10, // Arredondar para 1 casa decimal
          totalReviews: totalReviews}
      })
    }
  } catch (error) {
    console.error(Erro ao atualizar rating da loja:, 'error')
  
}
}
