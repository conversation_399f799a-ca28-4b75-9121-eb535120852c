import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedSubscriptions() {
  try {
    // Primeiro, criar planos se não existirem
    const existingPlans = await prisma.subscriptionPlan.count()
    
    if (existingPlans === 0) {
      const plans = [
        {
          name: 'Básico',
          description: 'Ideal para começar',
          monthlyPrice: 29.99,
          yearlyPrice: 299.99,
          features: ['Até 50 reparações/mês', 'Gestão básica de clientes'],
          maxRepairs: 50,
          maxUsers: 1,
          hasAdvancedReports: false,
          hasApiAccess: false,
          hasPrioritySupport: false,
          isActive: true,
          priority: 1
        },
        {
          name: 'Profissional',
          description: 'Para negócios em crescimento',
          monthlyPrice: 59.99,
          yearlyPrice: 599.99,
          features: ['Até 200 reparações/mês', 'Gestão avançada de clientes'],
          maxRepairs: 200,
          maxUsers: 3,
          hasAdvancedReports: true,
          hasApiAccess: false,
          hasPrioritySupport: true,
          isActive: true,
          priority: 2
        }
      ]

      for (const plan of plans) {
        await prisma.subscriptionPlan.create({ data: plan })
      }
      console.log('Planos criados!')
    }

    // Buscar usuários lojistas
    const repairShops = await prisma.user.findMany({
      where: { role: 'REPAIR_SHOP' },
      take: 3
    })

    if (repairShops.length === 0) {
      console.log('Nenhum lojista encontrado para criar subscrições')
      return
    }

    // Buscar planos
    const plans = await prisma.subscriptionPlan.findMany()
    
    if (plans.length === 0) {
      console.log('Nenhum plano encontrado')
      return
    }

    // Verificar se já existem subscrições
    const existingSubscriptions = await prisma.subscription.count()
    
    if (existingSubscriptions > 0) {
      console.log('Subscrições já existem, pulando seed...')
      return
    }

    // Criar subscrições de exemplo
    for (let i = 0; i < repairShops.length && i < plans.length; i++) {
      const user = repairShops[i]
      const plan = plans[i % plans.length]
      
      await prisma.subscription.create({
        data: {
          userId: user.id,
          planId: plan.id,
          status: i === 0 ? 'ACTIVE' : (i === 1 ? 'PAST_DUE' : 'CANCELLED'),
          billingCycle: i % 2 === 0 ? 'MONTHLY' : 'YEARLY',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + (i % 2 === 0 ? 30 : 365) * 24 * 60 * 60 * 1000),
          stripeSubscriptionId: `sub_${Math.random().toString(36).substr(2, 9)}`,
          stripeCustomerId: `cus_${Math.random().toString(36).substr(2, 9)}`
        }
      })
    }

    console.log('Subscrições de exemplo criadas com sucesso!')

  } catch (error) {
    console.error('Erro ao criar subscrições:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedSubscriptions()
