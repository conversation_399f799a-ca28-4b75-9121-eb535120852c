'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, Tag } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import CategoryManager from '@/components/shop/CategoryManager'

export default function LojaOnlineCategoriasPage() {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)
  const [customSubdomain, setCustomSubdomain] = useState('')

  useEffect(() => {
    if (session?.user?.id) {
      checkAccess()
    }
  }, [session])

  const checkAccess = async () => {
    try {
      const response = await fetch('/api/lojista/loja-online/config')
      if (response.ok) {
        const data = await response.json()
        console.log('Config data:', data)
        setHasAccess(data.hasAccess)
        setCustomSubdomain(data.customSubdomain || '')
        console.log('Custom subdomain set to:', data.customSubdomain)
      }
    } catch (error) {
      console.error('Erro ao verificar acesso:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Loja Online não disponível
          </h1>
          <p className="text-gray-600 mb-8">
            Para aceder à gestão de categorias da loja online, precisa de um plano que inclua esta funcionalidade.
          </p>
          <Link
            href="/lojista/planos"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Ver Planos Disponíveis
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <Link href="/lojista/loja-online" className="text-gray-500 hover:text-gray-700">
                  Loja Online
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900">Categorias</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Link
              href="/lojista/loja-online"
              className="mr-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Gestão de Categorias</h1>
              <p className="text-gray-600">Organize os produtos da sua loja online</p>
            </div>
          </div>
        </div>

      {/* Category Manager Component */}
      {customSubdomain && (
        <CategoryManager subdomain={customSubdomain} />
      )}
    </div>
  )
}
