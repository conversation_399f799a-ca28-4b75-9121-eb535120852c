'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Star, Trophy, Crown, Shield, Zap, Heart, Target, Award } from 'lucide-react'
interface ClientBadge {
  id: string
  name: string
  description: string
  icon: string
  color: string
  earned: boolean
  earnedAt?: string
  progress?: number
  maxProgress?: number}

const BADGE_ICONS: { [key: string]: React.ReactNode } = {
  star: <Star className="w-6 h-6" />,
  trophy: <Trophy className="w-6 h-6" />,
  crown: <Crown className="w-6 h-6" />,
  shield: <Shield className="w-6 h-6" />,
  zap: <Zap className="w-6 h-6" />,
  heart: <Heart className="w-6 h-6" />,
  target: <Target className="w-6 h-6" />,
  award: <Award className="w-6 h-6" />
}

export default function ClientBadgeShowcase() {
  const { data: session } = useSession()
  const [badges, setBadges] = useState<ClientBadge[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user?.id) {
      fetchClientBadges()
    }
  }, [session])

  const fetchClientBadges = async () => {
    try {
      // Mock data para demonstração
      const mockBadges: ClientBadge[] = [
        {
          id: 1,
          name: 'Primeiro Cliente',
          description: 'Primeira reparação na plataforma',
          icon: star,
          color: 'from-yellow-400 to-orange-500',
          earned: true,
          earnedAt: '2024-01-15'
        
},
        {
          id: '2',
          name: 'Cliente Fiel',
          description: 'Mais de 5 reparações realizadas',
          icon: heart,
          color: 'from-pink-400 to-red-500',
          earned: true,
          earnedAt: '2024-02-20'
        },
        {
          id: '3',
          name: Embaixador,
          description: 'Referiu 3 ou mais amigos',
          icon: crown,
          color: 'from-purple-400 to-indigo-500',
          earned: true,
          earnedAt: '2024-03-10'
        },
        {
          id: '4',
          name: 'Avaliador Expert',
          description: 'Deixou 10 avaliações detalhadas',
          icon: trophy,
          color: 'from-blue-400 to-cyan-500',
          earned: false,
          progress: 7,
          maxProgress: 10
        },
        {
          id: '5',
          name: 'Eco Warrior',
          description: 'Reparou em vez de comprar novo 10 vezes',
          icon: shield,
          color: 'from-green-400 to-emerald-500',
          earned: false,
          progress: 6,
          maxProgress: 10
        },
        {
          id: '6',
          name: 'Speed Demon',
          description: 'Agendou reparação em menos de 5 minutos',
          icon: zap,
          color: 'from-yellow-400 to-amber-500',
          earned: false,
          progress: 0,
          maxProgress: 1
        }
      ]
      setBadges(mockBadges)
    } catch (error) {
      console.error('Erro ao carregar badges do cliente:', 'error')
    } finally {
      setLoading(false)
    }
  }

  if (!session?.user) return null

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const earnedBadges = badges.filter(badge => badge.earned)
  const inProgressBadges = badges.filter(badge => !badge.earned)

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
          <Trophy className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Os Teus Badges
          </h3>
          <p className="text-sm text-gray-600">
            Conquistas desbloqueadas na plataforma
          </p>
        </div>
      </div>

      {/* Earned Badges */}
      {earnedBadges.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Badges Conquistados ({earnedBadges.length})
          </h4>
          <div className="grid grid-cols-3 sm:grid-cols-6 gap-3">
            {earnedBadges.map((badge) => (
              <div
                key={badge.id}
                className="group relative"
              >
                <div className={`w-16 h-16 bg-gradient-to-br ${badge.color} rounded-xl flex items-center justify-center text-white shadow-lg transform transition-transform group-hover:scale-105`}>
                  {BADGE_ICONS[badge.icon]}
                </div>
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                  <div className="bg-gray-900 text-white text-xs rounded-lg py-2 px-3 whitespace-nowrap">
                    <div className="font-medium">{badge.name}</div>
                    <div className="text-gray-300">{badge.description}</div>
                    {badge.earnedAt && (
                      <div className="text-gray-400 text-xs mt-1">
                        Conquistado em {new Date(badge.earnedAt).toLocaleDateString('pt-PT')}
                      </div>
                    )}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* In Progress Badges */}
      {inProgressBadges.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Próximos Badges
          </h4>
          <div className="space-y-3">
            {inProgressBadges.slice(0, 3).map((badge) => (
              <div
                key={badge.id}
                className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg"
              >
                <div className={`w-12 h-12 bg-gradient-to-br ${badge.color} opacity-50 rounded-lg flex items-center justify-center text-white`}>
                  {BADGE_ICONS[badge.icon]}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h5 className="font-medium text-gray-900">{badge.name}</h5>
                    {badge.progress !== undefined && badge.maxProgress && (
                      <span className="text-sm text-gray-600">
                        {badge.progress}/{badge.maxProgress}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{badge.description}</p>
                  
                  {badge.progress !== undefined && badge.maxProgress && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`bg-gradient-to-r ${badge.color} h-2 rounded-full transition-all duration-300`}
                        style={{ width: `${(badge.progress / badge.maxProgress) * 100}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Call to Action */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Target className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <h5 className="font-medium text-gray-900">
                Continue a conquistar!
              </h5>
              <p className="text-sm text-gray-600">
                Agenda mais reparações e refere amigos para desbloquear novos badges
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
