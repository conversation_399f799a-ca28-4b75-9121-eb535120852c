import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const {
      categoryId,
      problemTypeId,
      deviceId,
      repairShopId
    } = await request.json()

    if (!categoryId || !problemTypeId) {
      return NextResponse.json(
        { message: 'Categoria e tipo de problema são obrigatórios' },
        { status: 400 }
      )
    }

    let estimatedPrice = 50 // Preço base padrão
    let estimatedTime = 7 // Tempo base padrão em dias

    try {
      // Tentar buscar preço específico da loja
      if (repairShopId) {
        const shopPrice = await prisma.repairShopPrice.findFirst({
          where: {
            repairShopId,
            OR: [
              {
                deviceModelId: deviceId,
                problemTypeId
              },
              {
                categoryId,
                problemTypeId,
                deviceModelId: null
              }
            ]
          }
        })

        if (shopPrice) {
          estimatedPrice = Number(shopPrice.price)
          estimatedTime = shopPrice.estimatedTime || 7
        }
      }

      // Se não encontrou preço específico, buscar preço base
      if (estimatedPrice === 50) {
        const basePrice = await prisma.basePrice.findFirst({
          where: {
            categoryId,
            problemTypeId
          }
        })

        if (basePrice) {
          estimatedPrice = Number(basePrice.price)
          estimatedTime = basePrice.estimatedTime || 7
        }
      }

      // Buscar informações adicionais para contexto
      const [category, problemType] = await Promise.all([
        prisma.category.findUnique({ where: { id: categoryId } }),
        prisma.problemType.findUnique({ where: { id: problemTypeId } })
      ])

      // Ajustar preço baseado no tipo de problema (estimativas simples)
      if (problemType?.name.toLowerCase().includes('ecrã') || problemType?.name.toLowerCase().includes('tela')) {
        estimatedPrice = Math.max(estimatedPrice, 80)
        estimatedTime = Math.max(estimatedTime, 3)
      } else if (problemType?.name.toLowerCase().includes('bateria')) {
        estimatedPrice = Math.max(estimatedPrice, 60)
        estimatedTime = Math.max(estimatedTime, 2)
      } else if (problemType?.name.toLowerCase().includes('água') || problemType?.name.toLowerCase().includes('líquido')) {
        estimatedPrice = Math.max(estimatedPrice, 100)
        estimatedTime = Math.max(estimatedTime, 5)
      }

      // Ajustar preço baseado na categoria
      if (category?.name.toLowerCase().includes('laptop') || category?.name.toLowerCase().includes('portátil')) {
        estimatedPrice = estimatedPrice * 1.5
        estimatedTime = Math.max(estimatedTime, 5)
      } else if (category?.name.toLowerCase().includes('tablet')) {
        estimatedPrice = estimatedPrice * 1.2
        estimatedTime = Math.max(estimatedTime, 3)
      }

    } catch (dbError) {
      console.warn('Erro ao buscar preços da base de dados, usando valores padrão:', dbError)
    }

    return NextResponse.json({
      success: true,
      estimatedPrice: Math.round(estimatedPrice),
      estimatedTime: Math.round(estimatedTime),
      currency: 'EUR',
      note: 'Preço estimado. O valor final pode variar após diagnóstico.'
    })

  } catch (error) {
    console.error('Erro ao calcular estimativa:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Erro ao calcular estimativa',
        estimatedPrice: 50,
        estimatedTime: 7,
        currency: 'EUR'
      },
      { status: 500 }
    )
  }
}
