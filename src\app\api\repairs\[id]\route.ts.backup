import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repair = await prisma.repair.findUnique({
      where: { id: resolvedParams.id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                phone: true
              }
            }
          }
        },
        deviceModel: {
          include: {
            brand: true,
            category: true
          }
        },
        problemType: true,
        repairShop: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                companyName: true,
                phone: true
              }
            }
          }
        },
        payments: {
          select: {
            amount: true,
            status: true,
            createdAt: true,
            platformFee: true,
            shopAmount: true
          }
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar permissões
    const canAccess = 
      session.user.role === 'ADMIN' ||
      (session.user.role === 'CUSTOMER' && repair.customerId === session.user.id) ||
      (session.user.role === 'REPAIR_SHOP' && repair.repairShopId === session.user.id) ||
      (session.user.role === 'COURIER' && repair.courierId === session.user.id)

    if (!canAccess) {
      return NextResponse.json(
        { message: 'Acesso negado a esta reparação' },
        { status: 403 }
      )
    }

    return NextResponse.json(repair)

  } catch (error) {
    console.error('Erro ao buscar reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repair = await prisma.repair.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada' },
        { status: 404 }
      )
    }

    // Verificar permissões para atualizar
    const canUpdate = 
      session.user.role === 'ADMIN' ||
      (session.user.role === 'REPAIR_SHOP' && repair.repairShopId === session.user.id) ||
      (session.user.role === 'COURIER' && repair.courierId === session.user.id)

    if (!canUpdate) {
      return NextResponse.json(
        { message: 'Sem permissão para atualizar esta reparação' },
        { status: 403 }
      )
    }

    const {
      status,
      diagnosis,
      estimatedCost,
      finalCost,
      notes,
      estimatedCompletionDate,
      courierId
    } = await request.json()

    const updateData: Record<string, any> = {}

    if (status) updateData.status = status
    if (diagnosis) updateData.diagnosis = diagnosis
    if (estimatedCost !== undefined) updateData.estimatedCost = parseFloat(estimatedCost)
    if (finalCost !== undefined) updateData.finalCost = parseFloat(finalCost)
    if (notes) updateData.notes = notes
    if (estimatedCompletionDate) updateData.estimatedCompletionDate = new Date(estimatedCompletionDate)
    if (courierId) updateData.courierId = courierId

    const updatedRepair = await prisma.repair.update({
      where: { id: resolvedParams.id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        device: {
          include: {
            brand: true,
            category: true
          }
        },
        repairShop: {
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                companyName: true
              }
            }
          }
        },
        courier: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // Criar entrada no histórico de status se o status mudou
    if (status && status !== repair.status) {
      await prisma.repairStatusHistory.create({
        data: {
          repairId: resolvedParams.id,
          status,
          notes: notes || `Status alterado para ${status}`,
          changedBy: session.user.id
        }
      })
    }

    return NextResponse.json(updatedRepair)

  } catch (error) {
    console.error('Erro ao atualizar reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
