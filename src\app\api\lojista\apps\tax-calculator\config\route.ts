import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configuração da calculadora
    const config = await prisma.$queryRaw`
      SELECT * FROM app_configs 
      WHERE "userId" = ${session.user.id} 
      AND "appId" = tax-calculator
    `

    const defaultConfig = {
      defaultVatRate: 23,
      companyNif: '',
      companyName: '',
      companyAddress: ''
    
}

    return NextResponse.json({
      config: config.length > 0 ? {
        ...defaultConfig,
        ...config[0].settings
      } : 'defaultConfig'})

  } catch (error) {
    console.error('Erro ao buscar configuração:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const configData = await request.json()

    // Salvar configuração
    await prisma.$queryRaw`
      INSERT INTO app_configs ("id", "userId", "appId", "settings", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${session.user.id}, tax-calculator, ${JSON.stringify(configData)
}::jsonb, NOW(), NOW())
      ON CONFLICT ("userId", "appId") 
      DO UPDATE SET "settings" = ${JSON.stringify(configData)}::jsonb, "updatedAt" = NOW()
    `

    return NextResponse.json({
      message: 'Configuração salva com sucesso',
      config: configData})

  } catch (error) {
    console.error('Erro ao salvar configuração:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
