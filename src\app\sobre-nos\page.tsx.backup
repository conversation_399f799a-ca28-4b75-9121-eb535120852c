'use client'

import Link from 'next/link'
import { 
  Users, Target, Award, Heart, 
  CheckCircle, Star, ArrowRight, Phone,
  Wrench, Globe, Shield, Zap
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'

export default function AboutUsPage() {
  const { t } = useTranslation()

  const values = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: 'Paixão pela Excelência',
      description: 'Dedicamo-nos a fornecer o melhor serviço de reparação em Portugal, superando sempre as expectativas dos nossos clientes.'
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Transparência Total',
      description: 'Preços claros, prazos realistas e comunicação honesta. Nunca há surpresas desagradáveis no final.'
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Inovação Constante',
      description: 'Investimos continuamente em tecnologia e formação para oferecer as soluções mais avançadas do mercado.'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Foco no Cliente',
      description: 'Cada cliente é único. Adaptamos o nosso serviço às necessidades específicas de cada pessoa e situação.'
    }
  ]

  const team = [
    {
      name: 'João Silva',
      role: 'CEO & Fundador',
      description: 'Especialista em tecnologia com 15 anos de experiência no setor de reparações.',
      image: '👨‍💼'
    },
    {
      name: 'Maria Santos',
      role: 'CTO',
      description: 'Engenheira de software responsável pela plataforma tecnológica da Revify.',
      image: '👩‍💻'
    },
    {
      name: 'Pedro Costa',
      role: 'Diretor de Operações',
      description: 'Gere a rede nacional de técnicos e garante a qualidade do serviço.',
      image: '👨‍🔧'
    },
    {
      name: 'Ana Ferreira',
      role: 'Diretora de Atendimento',
      description: 'Lidera a equipa de suporte ao cliente e garante a satisfação total.',
      image: '👩‍💼'
    }
  ]

  const stats = [
    { number: '50,000+', label: 'Reparações Realizadas' },
    { number: '500+', label: 'Técnicos Parceiros' },
    { number: '50+', label: 'Cidades Cobertas' },
    { number: '98%', label: 'Satisfação do Cliente' }
  ]

  const milestones = [
    {
      year: '2020',
      title: 'Fundação da Revify',
      description: 'Início da jornada com a missão de revolucionar o setor de reparações em Portugal.'
    },
    {
      year: '2021',
      title: 'Expansão Nacional',
      description: 'Chegámos às principais cidades portuguesas com uma rede de técnicos certificados.'
    },
    {
      year: '2022',
      title: 'Marketplace Lançado',
      description: 'Introduzimos o marketplace de peças e dispositivos recondicionados.'
    },
    {
      year: '2023',
      title: 'Liderança de Mercado',
      description: 'Tornámo-nos a plataforma #1 de reparações em Portugal.'
    },
    {
      year: '2024',
      title: 'Inovação Contínua',
      description: 'Novos serviços e tecnologias para melhor servir os nossos clientes.'
    }
  ]

  return (
    <ModernLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Heart className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('aboutUsTitle')}
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
              {t('aboutUsSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/cliente/reparacoes/nova-v2"
                className="bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Wrench className="w-5 h-5" />
                <span>Começar Agora</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/contactos"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Falar Connosco</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                A Nossa Missão
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Democratizar o acesso a reparações de qualidade em Portugal, conectando clientes 
                a técnicos especializados através de uma plataforma transparente, rápida e confiável.
              </p>
              <p className="text-lg text-gray-600 mb-8">
                Acreditamos que todos merecem ter os seus dispositivos reparados com qualidade, 
                a preços justos e com total transparência. É por isso que criámos a Revify.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Técnicos certificados e verificados</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Preços transparentes sem surpresas</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Garantia de 6 meses em todas as reparações</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">Suporte ao cliente excecional</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Target className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  A Nossa Visão
                </h3>
                <p className="text-gray-600 mb-6">
                  Ser a plataforma de referência em reparações de dispositivos eletrónicos 
                  em Portugal, reconhecida pela excelência, inovação e satisfação do cliente.
                </p>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">50k+</div>
                    <div className="text-sm text-gray-600">Reparações</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">98%</div>
                    <div className="text-sm text-gray-600">Satisfação</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Os Nossos Valores
            </h2>
            <p className="text-xl text-gray-600">
              Princípios que guiam tudo o que fazemos
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6 text-blue-600">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Números que Falam por Si
            </h2>
            <p className="text-xl text-blue-100">
              O nosso impacto em Portugal
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold mb-2">{stat.number}</div>
                <div className="text-lg text-blue-100">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              A Nossa Jornada
            </h2>
            <p className="text-xl text-gray-600">
              Marcos importantes na história da Revify
            </p>
          </div>
          
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start space-x-6">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                    {milestone.year}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{milestone.title}</h3>
                  <p className="text-gray-600">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              A Nossa Equipa
            </h2>
            <p className="text-xl text-gray-600">
              As pessoas por trás da Revify
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow text-center">
                <div className="text-6xl mb-4">{member.image}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                <div className="text-blue-600 font-medium mb-4">{member.role}</div>
                <p className="text-gray-600 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Junte-se à Revolução das Reparações
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Faça parte da comunidade Revify hoje mesmo
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/cliente/reparacoes/nova-v2"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Wrench className="w-5 h-5" />
              <span>Reparar Agora</span>
            </Link>
            <Link
              href="/tornar-se-parceiro"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Users className="w-5 h-5" />
              <span>Ser Parceiro</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
