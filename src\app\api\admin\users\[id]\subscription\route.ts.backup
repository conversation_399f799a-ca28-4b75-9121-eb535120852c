import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Await params before using
    const { id } = await params
    const { planId, billingCycle } = await request.json()

    if (!planId || !billingCycle) {
      return NextResponse.json(
        { message: 'Plano e ciclo de faturação são obrigatórios' },
        { status: 400 }
      )
    }

    // Verificar se o usuário existe e é lojista
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user || user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Usuário não encontrado ou não é lojista' },
        { status: 404 }
      )
    }

    // Verificar se o plano existe
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId }
    })

    if (!plan) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    // Calcular datas
    const now = new Date()
    const periodEnd = new Date(now)
    
    if (billingCycle === 'MONTHLY') {
      periodEnd.setMonth(periodEnd.getMonth() + 1)
    } else {
      periodEnd.setFullYear(periodEnd.getFullYear() + 1)
    }

    // Verificar se já tem subscrição
    const existingSubscription = await prisma.$queryRaw`
      SELECT * FROM subscriptions WHERE "userId" = ${id}
    `

    let subscription
    if (existingSubscription.length > 0) {
      // Atualizar subscrição existente
      subscription = await prisma.$queryRaw`
        UPDATE subscriptions SET
          "planId" = ${planId},
          status = 'ACTIVE',
          "billingCycle" = ${billingCycle}::"BillingCycle",
          "currentPeriodStart" = ${now},
          "currentPeriodEnd" = ${periodEnd},
          "canceledAt" = NULL,
          "cancelAtPeriodEnd" = false,
          "updatedAt" = NOW()
        WHERE "userId" = ${id}
        RETURNING *
      `
    } else {
      // Criar nova subscrição
      subscription = await prisma.$queryRaw`
        INSERT INTO subscriptions (
          "id", "userId", "planId", status, "billingCycle",
          "currentPeriodStart", "currentPeriodEnd", "createdAt", "updatedAt"
        ) VALUES (
          gen_random_uuid(), ${id}, ${planId}, 'ACTIVE', ${billingCycle}::"BillingCycle",
          ${now}, ${periodEnd}, NOW(), NOW()
        ) RETURNING *
      `
    }

    // Criar registro de pagamento (como se fosse pago pelo admin)
    const subscriptionId = Array.isArray(subscription) ? subscription[0].id : subscription.id
    await prisma.$queryRaw`
      INSERT INTO subscription_payments (
        "id", "subscriptionId", amount, currency, status, "periodStart", "periodEnd", "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${subscriptionId}, ${billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice},
        'EUR', 'COMPLETED', ${now}, ${periodEnd}, NOW(), NOW()
      )
    `

    return NextResponse.json({
      message: 'Subscrição atualizada com sucesso',
      subscription
    })

  } catch (error) {
    console.error('Erro ao atualizar subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
