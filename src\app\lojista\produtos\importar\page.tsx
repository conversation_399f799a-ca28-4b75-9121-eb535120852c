'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Upload, FileText, Download, AlertCircle, CheckCircle, X } from 'lucide-react'
import Link from 'next/link'
interface ImportResult {
  success: boolean
  message: string
  imported: number
  errors: string[]
}

export default function ImportarProdutosPage() {
  const { data: session } = useSession()
  const [selectedMethod, setSelectedMethod] = useState<'csv' | 'xlsx' | 'woocommerce' | 'shopify'>('csv')
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<any[]>([])
  const [importProgress, setImportProgress] = useState<string>('')

  // Configurações para integrações
  const [wooConfig, setWooConfig] = useState({
    url: ,
    consumerKey: '',
    consumerSecret: ''
  
})

  const [shopifyConfig, setShopifyConfig] = useState({
    shopUrl: '',
    accessToken: ''
  })

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setImportResult(null)
    }
  }

  const downloadTemplate = (type: 'csv' | 'xlsx') => {
    const templateUrl = `/api/lojista/produtos/template?type=${type}`
    const link = document.createElement('a')
    link.href = templateUrl
    link.download = `template_produtos.${type}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const previewImport = async () => {
    if (!file) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', 'file')
      formData.append('method', 'selectedMethod')
      formData.append('preview', 'true')

      const response = await fetch('/api/lojista/produtos/importar', {
        method: POST,
        body: formData})

      const result = await response.json()
      
      if (response.ok) {
        setPreviewData(result.preview || [])
        setShowPreview(true)
      } else {
        setImportResult({
          success: false,
          message: result.message || 'Erro ao fazer preview',
          imported: 0,
          errors: result.errors || []
        })
      }
    } catch (error) {
      setImportResult({
        success: false,
        message: 'Erro ao processar arquivo',
        imported: 0,
        errors: [error instanceof Error ? error.message : 'Erro desconhecido']
      })
    } finally {
      setIsUploading(false)
    }
  }

  const validateWooConfig = () => {
    const errors = []
    if (!wooConfig.url) errors.push('URL da loja é obrigatória')
    if (!wooConfig.consumerKey) errors.push('Consumer Key é obrigatória')
    if (!wooConfig.consumerSecret) errors.push('Consumer Secret é obrigatória')

    // Validar formato da URL
    if (wooConfig.url && !wooConfig.url.startsWith(http)) {
      errors.push('URL deve começar com http:// ou https://')
    
}

    'return errors'}

  const validateShopifyConfig = () => {
    const errors = []
    if (!shopifyConfig.shopUrl) errors.push('URL da loja é obrigatória')
    if (!shopifyConfig.accessToken) errors.push('Access Token é obrigatório')

    // Validar formato da URL do Shopify
    if (shopifyConfig.shopUrl && !shopifyConfig.shopUrl.includes(.myshopify.com)) {
      errors.push('URL deve ser no formato: loja.myshopify.com')
    
}

    'return errors'}

  const handleImport = async () => {
    if (!file && selectedMethod !== 'woocommerce' && selectedMethod !== 'shopify') {
      alert('Por favor, selecione um arquivo')
      return
    }

    // Validar configurações
    if (selectedMethod === woocommerce) {
      const errors = validateWooConfig()
      if (errors.length > 0) {
        alert('Erros na configuração do WooCommerce:\n' + errors.join('\n'))
        return
      
}
    } else if (selectedMethod === 'shopify') {
      const errors = validateShopifyConfig()
      if (errors.length > 0) {
        alert('Erros na configuração do Shopify:\n' + errors.join('\n'))
        return
      }
    }

    setIsUploading(true)
    setImportResult(null)
    setImportProgress('Iniciando importação...')

    try {
      let response: Response

      if (selectedMethod === 'woocommerce') {
        setImportProgress('Conectando com WooCommerce...')
        response = await fetch('/api/lojista/produtos/importar', {
          method: POST,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            method: woocommerce,
            config: wooConfig})
        })
      } else if (selectedMethod === 'shopify') {
        setImportProgress('Conectando com Shopify...')
        response = await fetch('/api/lojista/produtos/importar', {
          method: POST,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            method: shopify,
            config: shopifyConfig})
        })
      } else {
        setImportProgress('Processando arquivo...')
        const formData = new FormData()
        formData.append('file', file!)
        formData.append('method', 'selectedMethod')

        response = await fetch('/api/lojista/produtos/importar', {
          method: POST,
          body: formData})
      }

      setImportProgress('Salvando produtos...')
      const result = await response.json()
      setImportResult(result)
      
      if (result.success) {
        setFile(null)
        setShowPreview(false)
        setPreviewData([])
      }
    } catch (error) {
      setImportResult({
        success: false,
        message: 'Erro ao importar produtos',
        imported: 0,
        errors: [error instanceof Error ? error.message : 'Erro desconhecido']
      })
    } finally {
      setIsUploading(false)
      setImportProgress('')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/produtos"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Importar Produtos</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Method Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Método de Importação</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { id: csv, name: CSV, icon: FileText, description: 'Arquivo CSV separado por vírgulas' },
              { id: xlsx, name: Excel, icon: FileText, description: 'Arquivo Excel (.xlsx)' },
              { id: woocommerce, name: WooCommerce, icon: Upload, description: 'Importar do WooCommerce' },
              { id: shopify, name: Shopify, icon: Upload, description: 'Importar do Shopify' }
            ].map((method) => (
              <button
                key={method.id}
                onClick={() => setSelectedMethod(method.id as any)}
                className={`p-4 border-2 rounded-lg text-left transition-colors ${
                  selectedMethod === method.id
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <method.icon className="w-8 h-8 text-indigo-600 mb-2" />
                <h3 className="font-medium text-gray-900">{method.name}</h3>
                <p className="text-sm text-gray-500 mt-1">{method.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* File Upload or API Configuration */}
        {(selectedMethod === 'csv' || selectedMethod === 'xlsx') && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Upload de Arquivo</h2>
              <button
                onClick={() => downloadTemplate(selectedMethod)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg hover:bg-indigo-100"
              >
                <Download className="w-4 h-4 mr-2" />
                Baixar Template
              </button>
            </div>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Clique para fazer upload ou arraste o arquivo aqui
                    </span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      accept={selectedMethod === 'csv' ? '.csv' : '.xlsx'}
                      className="sr-only"
                      onChange={handleFileSelect}
                    />
                  </label>
                  <p className="mt-1 text-xs text-gray-500">
                    {selectedMethod === 'csv' ? 'CSV até 10MB' : 'XLSX até 10MB'}
                  </p>
                </div>
              </div>
            </div>

            {file && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-gray-400 mr-2" />
                    <span className="text-sm font-medium text-gray-900">{file.name}</span>
                    <span className="text-sm text-gray-500 ml-2">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <button
                    onClick={() => setFile(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* WooCommerce Configuration */}
        {selectedMethod === 'woocommerce' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuração WooCommerce</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL da Loja
                </label>
                <input
                  type="url"
                  value={wooConfig.url}
                  onChange={(e) => setWooConfig(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https:// minhaloja.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="text-xs text-gray-500 mt-1">URL completa da sua loja WooCommerce</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Consumer Key
                </label>
                <input
                  type="text"
                  value={wooConfig.consumerKey}
                  onChange={(e) => setWooConfig(prev => ({ ...prev, consumerKey: e.target.value }))}
                  placeholder="ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Consumer Secret
                </label>
                <input
                  type="password"
                  value={wooConfig.consumerSecret}
                  onChange={(e) => setWooConfig(prev => ({ ...prev, consumerSecret: e.target.value }))}
                  placeholder="cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Como obter as credenciais:</h4>
                <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                  <li>Aceda ao painel admin do WooCommerce</li>
                  <li>Vá para WooCommerce → Configurações → Avançado → REST API</li>
                  <li>Clique em Adicionar chave</li>
                  <li>Copie a Consumer Key e Consumer Secret</li>
                </ol>
              </div>
            </div>
          </div>
        )
}

        {/* Shopify Configuration */}
        {selectedMethod === 'shopify' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuração Shopify</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL da Loja
                </label>
                <input
                  type="text"
                  value={shopifyConfig.shopUrl}
                  onChange={(e) => setShopifyConfig(prev => ({ ...prev, shopUrl: e.target.value }))}
                  placeholder="minhaloja.myshopify.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="text-xs text-gray-500 mt-1">URL no formato: loja.myshopify.com</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Access Token
                </label>
                <input
                  type="password"
                  value={shopifyConfig.accessToken}
                  onChange={(e) => setShopifyConfig(prev => ({ ...prev, accessToken: e.target.value }))}
                  placeholder="shpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Como obter o Access Token:</h4>
                <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                  <li>Aceda ao painel admin do Shopify</li>
                  <li>Vá para Apps → Develop apps → Create an app</li>
                  <li>Instale a app e copie o Admin API access token</li>
                </ol>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {((file && (selectedMethod === 'csv' || selectedMethod === 'xlsx')) ||
          (selectedMethod === 'woocommerce' && wooConfig.url && wooConfig.consumerKey && wooConfig.consumerSecret) ||
          (selectedMethod === 'shopify' && shopifyConfig.shopUrl && shopifyConfig.accessToken)) && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex space-x-4">
              {(selectedMethod === 'csv' || selectedMethod === 'xlsx') && (
                <button
                  onClick={previewImport}
                  disabled={isUploading}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  {isUploading ? 'Processando...' : 'Preview'}
                </button>
              )}
              <button
                onClick={handleImport}
                disabled={isUploading}
                className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
              >
                {isUploading ?
                  'Importando...' :
                  selectedMethod === 'woocommerce' ? 'Importar do WooCommerce' :
                  selectedMethod === 'shopify' ? 'Importar do Shopify' :
                  'Importar'
                }
              </button>
            </div>
          </div>
        )}

        {/* Import Progress */}
        {isUploading && importProgress && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-blue-800">Importando Produtos</h3>
                <p className="text-sm text-blue-700 mt-1">{importProgress}</p>
              </div>
            </div>
          </div>
        )}

        {/* Import Result */}
        {importResult && (
          <div className={`rounded-lg p-4 mb-6 ${
            importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-start">
              {importResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-3" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3" />
              )}
              <div className="flex-1">
                <h3 className={`text-sm font-medium ${
                  importResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {importResult.success ? 'Importação Concluída' : 'Erro na Importação'}
                </h3>
                <p className={`text-sm mt-1 ${
                  importResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {importResult.message}
                </p>
                {importResult.success && importResult.imported > 0 && (
                  <p className="text-sm text-green-700 mt-1">
                    {importResult.imported} produtos importados com sucesso
                  </p>
                )}
                {importResult.errors.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-red-800">Erros encontrados:</p>
                    <ul className="text-sm text-red-700 mt-1 list-disc list-inside">
                      {importResult.errors.slice(0, 5).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {importResult.errors.length > 5 && (
                        <li>... e mais {importResult.errors.length - 5} erros</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
