import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso à loja online
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!user?.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: 'Funcionalidade não disponível no seu plano' },
        { status: 403 }
      )
    }

    // Buscar produtos do marketplace do lojista
    const products = await prisma.marketplaceProduct.findMany({
      where: {
        sellerId: session.user.id
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Formatar dados para o frontend
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      stock: product.stock,
      condition: product.condition,
      isActive: product.isActive,
      images: product.images,
      category: product.category,
      brand: product.brand,
      isOnlineStoreExclusive: false, // Por enquanto todos vêm do marketplace
      createdAt: product.createdAt.toISOString()
    }))

    return NextResponse.json({
      success: true,
      products: formattedProducts,
      total: formattedProducts.length
    })

  } catch (error) {
    console.error('Erro ao buscar produtos da loja online:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso à loja online
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!user?.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: 'Funcionalidade não disponível no seu plano' },
        { status: 403 }
      )
    }

    const {
      name,
      description,
      price,
      originalPrice,
      stock,
      condition,
      images,
      categoryId,
      brandId,
      deviceModelId,
      isOnlineStoreExclusive = true
    } = await request.json()

    // Validações
    if (!name || !price || !stock || !condition) {
      return NextResponse.json(
        { message: 'Campos obrigatórios: nome, preço, stock e condição' },
        { status: 400 }
      )
    }

    // Criar produto exclusivo da loja online
    const product = await prisma.marketplaceProduct.create({
      data: {
        name,
        description: description || '',
        price: parseFloat(price.toString()),
        originalPrice: originalPrice ? parseFloat(originalPrice.toString()) : null,
        stock: parseInt(stock.toString()),
        condition,
        images: images || [],
        categoryId: categoryId || null,
        brandId: brandId || null,
        deviceModelId: deviceModelId || null,
        sellerId: session.user.id,
        isActive: true,
        // Marcar como exclusivo da loja online (pode ser um campo adicional no futuro)
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Produto criado com sucesso',
      product: {
        id: product.id,
        name: product.name,
        description: product.description,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        stock: product.stock,
        condition: product.condition,
        isActive: product.isActive,
        images: product.images,
        category: product.category,
        brand: product.brand,
        isOnlineStoreExclusive,
        createdAt: product.createdAt.toISOString()
      }
    })

  } catch (error) {
    console.error('Erro ao criar produto da loja online:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
