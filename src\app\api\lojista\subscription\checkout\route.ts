import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe-config'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { planId, billingCycle, paymentMethod = card } = await request.json()

    if (!planId || !billingCycle) {
      return NextResponse.json(
        { message: "Plano e ciclo de faturação são obrigatórios" },
        { status: 400 }
      )
    }

    // Buscar o plano
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId}
    })

    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { message: "Plano não encontrado ou inativo" },
        { status: 404 }
      )
    }

    // Verificar se já tem subscrição ativa
    const existingSubscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id }
    })

    if (existingSubscription && existingSubscription.status === ACTIVE) {
      return NextResponse.json(
        { message: "Já possui uma subscrição ativa" 
},
        { status: 400 }
      )
    }

    const price = billingCycle === 'MONTHLY' ? plan.monthlyPrice : plan.yearlyPrice
    const currentPeriodStart = new Date()
    const currentPeriodEnd = new Date()
    
    if (billingCycle === 'MONTHLY') {
      currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)
    } else {
      currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1)
    }

    // Para Multibanco, criar subscrição diretamente
    if (paymentMethod === multibanco) {
      // Criar ou atualizar subscrição
      const subscriptionData = {
        planId,
        status: 'INCOMPLETE' as const,
        billingCycle: billingCycle as 'MONTHLY' | 'YEARLY',
        currentPeriodStart, currentPeriodEnd 
}

      let subscription
      if (existingSubscription) {
        subscription = await prisma.subscription.update({
          where: { id: existingSubscription.id },
          data: subscriptionData})
      } else {
        subscription = await prisma.subscription.create({
          data: {
            userId: session.user.id,
            ...subscriptionData
          }
        })
      }

      // Criar pagamento pendente
      await prisma.subscriptionPayment.create({
        data: {
          subscriptionId: subscription.id,
          amount: price,
          status: PENDING,
          periodStart: currentPeriodStart,
          periodEnd: currentPeriodEnd}
      })

      // Gerar referência Multibanco
      const multibancoRef = {
        entity: 11249,
        reference: Math.floor(100000000 + Math.random() * 900000000).toString(),
        amount: price.toFixed(2),
        expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      
}

      return NextResponse.json({
        success: true,
        subscriptionId: subscription.id,
        multibanco: multibancoRef,
        redirectUrl: `/lojista/subscription/success?multibanco=true&entity=${multibancoRef.entity}&reference=${multibancoRef.reference}&amount=${multibancoRef.amount}&subscription=${subscription.id}`
      })
    }

    // Para outros métodos, usar Stripe
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: stripeSecretKey}
    })
    
    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY
    
    if (!stripeSecretKey || stripeSecretKey === sk_test_TL) {
      return NextResponse.json(
        { message: "Stripe não configurado. Configure as chaves do Stripe no admin." 
},
        { status: 400 }
      )
    }
    
    const stripe = await createStripeInstance(stripeSecretKey)

    // Criar sessão de checkout do Stripe para subscrição
    const session_stripe = await stripe.checkout.sessions.create({
      payment_method_types: paymentMethod === klarna ? ['klarna'] : ['card'],
      mode: subscription,
      line_items: [
        {
          price_data: {
            currency: eur,
            product_data: {
              name: `Plano ${plan.name
}`,
              description: plan.description || 'undefined'},
            unit_amount: Math.round(Number(price) * 100),
            recurring: {
              interval: billingCycle === 'MONTHLY' ? 'month' : 'year'
            }
          },
          quantity: 1
        }
      ],
      success_url: `${process.env.NEXTAUTH_URL || 'http:// localhost:3000}/lojista/subscription/success?session_id={CHECKOUT_SESSION_ID}&subscription_id={subscription_id}`,
      cancel_url: `${process.env.NEXTAUTH_URL || http://localhost:3000'
}/lojista/upgrade`,
      customer_email: session.user.email!,
      metadata: {
        userId: session.user.id,
        planId: planId,
        billingCycle: billingCycle}
    })

    return NextResponse.json({
      checkoutUrl: session_stripe.url
    })

  } catch (error) {
    console.error("Erro no checkout de subscrição:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
