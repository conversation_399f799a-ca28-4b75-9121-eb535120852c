'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  Search, Star, MapPin, Clock, ArrowRight, Euro, Wrench, Smartphone, Laptop, Tablet, Watch,
  Shield, Zap, Users, CheckCircle, TrendingUp, Award, Globe, Truck, RotateCcw,
  Play, ChevronRight, Sparkles, Target, Heart, MessageCircle, Mail, Phone, Settings,
  FileText, BarChart3, Headphones, Bot, Cpu, Database, Layers
} from 'lucide-react'
import { useTranslation, useGeolocation } from '@/hooks/useTranslation'
import AutoTranslate from '@/components/ui/AutoTranslate'
import LexendLayout from '@/components/LexendLayout'


interface RepairShop {
  id: string
  name: string
  email: string
  profile?: {
    companyName: string
    phone: string
    description: string
  }
  price?: number
  estimatedTime?: number
  rating: number
  distance: number
  isAvailable: boolean
}

export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { t, currentLanguage, changeLanguage } = useTranslation()
  const { country } = useGeolocation()

  const [recommendedShops, setRecommendedShops] = useState<RepairShop[]>([])
  const [searchLocation, setSearchLocation] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null)
  const [deviceInput, setDeviceInput] = useState('')

  useEffect(() => {
    if (status === 'loading') return
    fetchData()
    getUserLocation()
  }, [status])

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          }
          setUserLocation(location)
          // Recarregar lojas com a nova localização
          fetchShopsWithLocation(location)
        },
        (error) => {
          console.warn('Erro ao obter localização:', error.message)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      )
    }
  }

  const fetchShopsWithLocation = async (location: {lat: number, lng: number}) => {
    try {
      const shopsUrl = `/api/repair-shops?lat=${location.lat}&lng=${location.lng}&radius=20`
      const shopsRes = await fetch(shopsUrl)
      if (shopsRes.ok) {
        const shopsData = await shopsRes.json()
        const shops = shopsData.shops || shopsData
        setRecommendedShops(Array.isArray(shops) ? shops.slice(0, 4) : [])
      }
    } catch (error) {
      console.error('Erro ao carregar lojas com localização:', error)
    }
  }

  const handleSearch = async () => {
    if (!deviceInput.trim()) return

    setIsSearching(true)
    try {
      const searchParams = new URLSearchParams({
        device: deviceInput,
        location: searchLocation || 'Portugal'
      })
      router.push(`/simular-reparacao?${searchParams.toString()}`)
    } catch (error) {
      console.error('Erro na pesquisa:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleBookRepair = () => {
    router.push('/cliente/reparacoes/nova-v2')
  }

  const handleViewShop = (shopId: string) => {
    router.push(`/loja/${shopId}`)
  }



  const fetchData = async () => {
    try {
      console.log('Iniciando fetch dos dados...')



      // Buscar lojas recomendadas
      const shopsUrl = userLocation
        ? `/api/repair-shops?lat=${userLocation.lat}&lng=${userLocation.lng}&radius=20`
        : '/api/repair-shops'

      const shopsRes = await fetch(shopsUrl)
      if (shopsRes.ok) {
        try {
          const shopsData = await shopsRes.json()
          console.log('Lojas recebidas:', shopsData)
          const shops = shopsData.shops || shopsData
          setRecommendedShops(Array.isArray(shops) ? shops.slice(0, 4) : [])
        } catch (parseError) {
          console.error('Erro ao fazer parse das lojas:', parseError)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    }
  }







  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <LexendLayout>
      {/* Hero Section - Lexend Style */}
      <section className="relative pt-20 pb-32 overflow-hidden bg-white dark:bg-gray-900">
        {/* Background Vectors */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full">
            <div className="grid grid-cols-6 gap-8 opacity-5">
              {Array.from({ length: 30 }).map((_, i) => (
                <div key={i} className="flex items-center justify-center">
                  {i % 4 === 0 && <Smartphone className="w-8 h-8 text-indigo-600" />}
                  {i % 4 === 1 && <Laptop className="w-8 h-8 text-purple-600" />}
                  {i % 4 === 2 && <Tablet className="w-8 h-8 text-green-600" />}
                  {i % 4 === 3 && <Watch className="w-8 h-8 text-orange-600" />}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            {/* Badge */}
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200 dark:border-indigo-700 rounded-full px-6 py-3">
              <Sparkles className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
              <span className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
                A plataforma de reparações mais confiável da Europa
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
              <span className="text-gray-900 dark:text-white">Repare os seus</span>
              <br />
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                dispositivos
              </span>
              <br />
              <span className="text-gray-900 dark:text-white">com</span>
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent ml-4">
                garantia
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Conecte-se aos melhores técnicos especializados da Europa. Transparência total,
              garantia completa e preços justos para todas as reparações.
            </p>

            {/* CTA Button */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <button
                onClick={handleSearch}
                className="flex items-center space-x-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-medium text-lg shadow-lg hover:shadow-xl group"
              >
                <Search className="w-6 h-6" />
                <span>Começar reparação gratuita</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>

            {/* Features Icons */}
            <div className="flex items-center justify-center space-x-8 pt-8">
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                  <Shield className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium">Garantia de 6 meses</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium">Reparação em 24-48h</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium">Preços transparentes</span>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="pt-12">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Confiado por mais de 50.000 clientes em toda a Europa
              </p>
              <div className="flex items-center justify-center space-x-8 opacity-60">
                {/* Brand logos would go here */}
                <div className="text-2xl font-bold text-gray-400">Samsung</div>
                <div className="text-2xl font-bold text-gray-400">Apple</div>
                <div className="text-2xl font-bold text-gray-400">Xiaomi</div>
                <div className="text-2xl font-bold text-gray-400">Huawei</div>
                <div className="text-2xl font-bold text-gray-400">OnePlus</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integrations Section - Adapted for Device Types */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Reparações especializadas para todos os dispositivos
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Veja como ajudamos a resolver os problemas mais comuns dos seus dispositivos.
            </p>
          </div>

          {/* Device Categories Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {[
              { name: 'Smartphones', active: true },
              { name: 'Laptops', active: false },
              { name: 'Tablets', active: false },
              { name: 'Smartwatches', active: false },
              { name: 'Consolas', active: false }
            ].map((tab, index) => (
              <button
                key={index}
                className={`px-6 py-3 rounded-lg font-medium transition-all ${
                  tab.active
                    ? 'bg-indigo-600 text-white shadow-lg'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </div>

          {/* Device Repair Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: '/assets/images/integrations/iphone.png',
                name: 'iPhone',
                description: 'Reparação de ecrã, bateria, câmara e mais',
                category: 'Smartphone',
                color: 'from-blue-500 to-indigo-600'
              },
              {
                icon: '/assets/images/integrations/samsung.png',
                name: 'Samsung Galaxy',
                description: 'Especialistas em dispositivos Samsung Galaxy',
                category: 'Smartphone',
                color: 'from-purple-500 to-pink-600'
              },
              {
                icon: '/assets/images/integrations/xiaomi.png',
                name: 'Xiaomi',
                description: 'Reparação rápida e económica para Xiaomi',
                category: 'Smartphone',
                color: 'from-orange-500 to-red-600'
              },
              {
                icon: '/assets/images/integrations/macbook.png',
                name: 'MacBook',
                description: 'Reparação profissional de MacBooks',
                category: 'Laptop',
                color: 'from-gray-500 to-gray-700'
              },
              {
                icon: '/assets/images/integrations/ipad.png',
                name: 'iPad',
                description: 'Especialistas em reparação de iPads',
                category: 'Tablet',
                color: 'from-green-500 to-emerald-600'
              },
              {
                icon: '/assets/images/integrations/applewatch.png',
                name: 'Apple Watch',
                description: 'Reparação de Apple Watch e acessórios',
                category: 'Smartwatch',
                color: 'from-pink-500 to-rose-600'
              }
            ].map((device, index) => (
              <div key={index} className="bg-white dark:bg-gray-700 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-4 mb-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${device.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    {index === 0 && <Smartphone className="w-8 h-8 text-white" />}
                    {index === 1 && <Smartphone className="w-8 h-8 text-white" />}
                    {index === 2 && <Smartphone className="w-8 h-8 text-white" />}
                    {index === 3 && <Laptop className="w-8 h-8 text-white" />}
                    {index === 4 && <Tablet className="w-8 h-8 text-white" />}
                    {index === 5 && <Watch className="w-8 h-8 text-white" />}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white">{device.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{device.category}</p>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">{device.description}</p>
                <button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all font-medium text-sm">
                  Solicitar Orçamento
                </button>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/simular-reparacao"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-medium shadow-lg hover:shadow-xl group"
            >
              <span>Ver todos os dispositivos</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Solutions Section - Lexend Style */}
      <section className="py-20 bg-white dark:bg-gray-900 relative">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-10 w-64 h-64 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Soluções de reparação especializadas
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Oferecemos uma plataforma unificada que promove a inovação enquanto fornece gestão completa de reparações.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: 'Rápido e Confiável',
                description: 'Quer tenha 1 ou 100 dispositivos, nossa rede de técnicos mantém todos funcionando perfeitamente.',
                color: 'from-yellow-500 to-orange-600'
              },
              {
                icon: Database,
                title: 'Diagnóstico Avançado',
                description: 'Utilizamos tecnologia avançada para identificar problemas rapidamente e com precisão.',
                color: 'from-blue-500 to-indigo-600'
              },
              {
                icon: Layers,
                title: 'Peças Originais',
                description: 'Trabalhamos apenas com peças originais e de alta qualidade para garantir durabilidade.',
                color: 'from-green-500 to-emerald-600'
              },
              {
                icon: Shield,
                title: 'Gestão de Garantia',
                description: 'Sistema completo de gestão de garantias com cobertura de 6 meses em todas as reparações.',
                color: 'from-purple-500 to-pink-600'
              },
              {
                icon: CheckCircle,
                title: 'Conformidade Total',
                description: 'Cumprimos todas as normas europeias de qualidade e segurança em reparações.',
                color: 'from-indigo-500 to-purple-600'
              },
              {
                icon: Users,
                title: 'Gestão de Técnicos',
                description: 'Rede certificada de técnicos especializados com formação contínua e avaliação de qualidade.',
                color: 'from-pink-500 to-rose-600'
              }
            ].map((solution, index) => (
              <div key={index} className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
                <div className={`w-16 h-16 bg-gradient-to-br ${solution.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                  <solution.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">{solution.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{solution.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-black/10"></div>
          <div className="absolute top-20 right-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Diga adeus aos problemas dos seus dispositivos
          </h2>
          <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
            Gerencie todas as suas reparações com automação inteligente e técnicos especializados.
          </p>
          <button
            onClick={handleBookRepair}
            className="inline-flex items-center space-x-3 bg-white text-indigo-600 px-8 py-4 rounded-xl hover:bg-gray-50 transition-all font-medium text-lg shadow-lg hover:shadow-xl group"
          >
            <Search className="w-6 h-6" />
            <span>Começar reparação gratuita</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </button>
          <p className="text-indigo-200 text-sm mt-4">Sem cartão de crédito necessário!</p>
        </div>
      </section>

      {/* Pricing Section - Lexend Style */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800 relative">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Preços simples e transparentes
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Sem custos extra. Sem taxas ocultas.
            </p>
          </div>

          {/* Pricing Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[
              'Acompanhamento em tempo real',
              'Análises em tempo real',
              'Integrações com CRM',
              'Upload de ficheiros',
              'Performance do utilizador',
              'Espaços de trabalho ilimitados',
              'Suporte SSO e funções personalizadas',
              'Fluxos de aprovação',
              'Integração Salesforce',
              'Envio em massa e formulários'
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-2 text-gray-600 dark:text-gray-300">
                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Free Plan */}
            <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-600">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Gratuito</h3>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  €0
                </div>
                <p className="text-gray-600 dark:text-gray-300">Gratuito para sempre</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">1 reparação</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">1 utilizador</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">Suporte básico</span>
                </li>
              </ul>
              <button className="w-full bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white py-3 px-6 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-500 transition-all font-medium">
                Começar gratuitamente
              </button>
              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">Gratuito para sempre!</p>
            </div>

            {/* Pro Plan */}
            <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-xl border-2 border-indigo-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Melhor valor!
                </span>
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Pro</h3>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  €9
                </div>
                <p className="text-gray-600 dark:text-gray-300">Por mês, faturação anual</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">10 reparações</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">10 utilizadores</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">Suporte prioritário</span>
                </li>
              </ul>
              <button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all font-medium">
                Começar agora
              </button>
              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">Faturado €108 por ano.</p>
            </div>

            {/* Business Plan */}
            <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-600">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Business</h3>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  €29
                </div>
                <p className="text-gray-600 dark:text-gray-300">Por mês, faturação anual</p>
              </div>
              <ul className="space-y-4 mb-8">
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">Reparações ilimitadas</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">Utilizadores ilimitados</span>
                </li>
                <li className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-300">Suporte dedicado</span>
                </li>
              </ul>
              <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all font-medium">
                Começar agora
              </button>
              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">Faturado €348 por ano.</p>
            </div>
          </div>
        </div>
      </section>
      {/* Testimonials Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Veja o que os nossos clientes satisfeitos partilham sobre nós!
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Confiado por mais de 50.000 clientes em toda a Europa
            </p>
          </div>

          {/* Trust Brands */}
          <div className="flex items-center justify-center space-x-12 mb-16 opacity-60">
            <div className="text-2xl font-bold text-gray-400">Samsung</div>
            <div className="text-2xl font-bold text-gray-400">Apple</div>
            <div className="text-2xl font-bold text-gray-400">Xiaomi</div>
            <div className="text-2xl font-bold text-gray-400">Huawei</div>
            <div className="text-2xl font-bold text-gray-400">OnePlus</div>
          </div>

          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                text: "Procurávamos pessoas que partilhassem a nossa visão! A maior parte do nosso tempo costumava ser ocupado por trabalho administrativo alternativo, enquanto agora podemos focar-nos em ajudar os nossos clientes.",
                author: "Carlos Silva"
              },
              {
                text: "Esta ferramenta poderosa elimina a necessidade de sair da plataforma para fazer as coisas, pois posso criar um orçamento personalizado com tabelas de preços dinâmicas e obter aprovação em 36 minutos.",
                author: "Ana Rodrigues"
              },
              {
                text: "Estamos baseados na Europa e o mais recente Regulamento de Proteção de Dados força-nos a procurar fornecedores de serviços que cumpram este regulamento e esta plataforma é simplesmente excelente!",
                author: "Miguel Santos"
              },
              {
                text: "Procurávamos pessoas que partilhassem a nossa visão! A maior parte do nosso tempo costumava ser ocupado por trabalho administrativo, enquanto agora podemos focar-nos em construir para ajudar os nossos clientes.",
                author: "Sofia Costa"
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed text-sm">
                  "{testimonial.text}"
                </p>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {testimonial.author.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{testimonial.author}</div>
                    <div className="text-gray-500 dark:text-gray-400 text-xs">Cliente Revify</div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/testemunhos"
              className="inline-flex items-center space-x-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium"
            >
              <span>Ver todos os testemunhos</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Perguntas frequentes!
            </h2>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "Preciso de saber programar?",
                answer: "Não, não precisa de conhecimentos técnicos. A nossa plataforma é intuitiva e fácil de usar para qualquer pessoa."
              },
              {
                question: "Posso usar para projetos comerciais?",
                answer: "Sim, pode usar a Revify para o seu negócio. Oferecemos planos específicos para empresas e lojistas."
              },
              {
                question: "Posso usar para múltiplos projetos?",
                answer: "Definitivamente! Use quantas vezes quiser; não limitamos o uso da plataforma."
              },
              {
                question: "Qual é a vossa política de reembolso?",
                answer: "Compreendemos a importância da satisfação do cliente. Oferecemos garantia de satisfação em todas as reparações realizadas através da nossa plataforma."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">{faq.question}</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{faq.answer}</p>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-600">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                Ainda tem dúvidas?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Não consegue encontrar a resposta que procura? Entre em contacto com a nossa equipa amigável.
              </p>
              <div className="flex items-center justify-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full"></div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full"></div>
              </div>
              <Link
                href="/contacto"
                className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all font-medium"
              >
                <span>Entrar em contacto</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-black/10"></div>
          <div className="absolute -top-20 -right-20 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="flex space-x-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Soluções de reparação com IA!
          </h2>
          <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
            Veja como ajudamos a sua equipa a resolver os maiores desafios de hoje.
          </p>
          <button
            onClick={handleBookRepair}
            className="inline-flex items-center space-x-3 bg-white text-indigo-600 px-8 py-4 rounded-xl hover:bg-gray-50 transition-all font-medium text-lg shadow-lg hover:shadow-xl group"
          >
            <Mail className="w-6 h-6" />
            <span>Começar reparação gratuita com email</span>
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </button>
          <p className="text-indigo-200 text-sm mt-4">Sem cartão de crédito necessário!</p>
        </div>
      </section>





      {/* Recommended Shops Section */}
      {recommendedShops.length > 0 && (
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                <AutoTranslate text="Técnicos Recomendados" />
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                <AutoTranslate text="Os melhores profissionais da sua região" />
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recommendedShops.slice(0, 6).map((shop) => (
                <div key={shop.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700 overflow-hidden group">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                        {shop.profile?.companyName || shop.name}
                      </h3>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{shop.rating || 5.0}</span>
                      </div>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                      {shop.profile?.description || 'Especialista em reparações de dispositivos eletrónicos'}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <MapPin className="w-4 h-4" />
                        <span>{shop.distance ? shop.distance.toFixed(1) : '0.0'} km</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <Clock className="w-4 h-4" />
                        <span>{shop.estimatedTime || 24}h</span>
                      </div>
                    </div>

                    <Link
                      href={`/loja/${shop.id}`}
                      className="mt-4 w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all font-medium text-center block text-sm"
                    >
                      <AutoTranslate text="Ver Perfil" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </LexendLayout>
  )
}
