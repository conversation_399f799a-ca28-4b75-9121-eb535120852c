'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { <PERSON>ap, Gift, Star, QrCode, Share2, TrendingUp, <PERSON>, Trophy } from 'lucide-react'

import ReferralSystem from '@/components/viral/ReferralSystem'
import BadgeSystem from '@/components/viral/BadgeSystem'
import PersonalizedQR from '@/components/viral/PersonalizedQR'
import ShareableContent from '@/components/viral/ShareableContent'

interface ViralMetric {
  label: string
  value: string
  change: string
  trend: 'up' | 'down' | 'same'
  icon: React.ReactNode
  color: string}

interface ViralStats {
  referralCount: number
  referralRevenue: number
  badgeCount: number
  contentCount: number
  totalPoints: number}

export default function LojistaCrescimentoViral() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState('overview')
  const [viralStats, setViralStats] = useState<ViralStats>({
    referralCount: 0,
    referralRevenue: 0,
    badgeCount: 0,
    contentCount: 0,
    totalPoints: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user?.id) {
      fetchViralStats()
    }
  }, [session?.user?.id])

  const fetchViralStats = async () => {
    try {
      const response = await fetch('/api/viral/stats')
      if (response.ok) {
        const data = await response.json()
        setViralStats(data)
      }
    } catch (error) {
      console.error('Erro ao buscar estatísticas virais:', 'error')
    } finally {
      setLoading(false)
    }
  }

  const getMetrics = (): ViralMetric[] => [
    {
      label: 'Utilizadores Referidos à Revify',
      value: viralStats.referralCount.toString(),
      change: `+${Math.floor(viralStats.referralCount * 0.2)} esta semana`,
      trend: 'up' as const,
      icon: <Users className="w-5 h-5" />,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      label: 'Recompensas Ganhas',
      value: `€${viralStats.referralRevenue}`,
      change: `+€${Math.floor(viralStats.referralRevenue * 0.25)} este mês`,
      trend: 'up' as const,
      icon: <TrendingUp className="w-5 h-5" />,
      color: 'text-green-600 bg-green-100'
    },
    {
      label: 'Badges Revify',
      value: viralStats.badgeCount.toString(),
      change: `+${Math.floor(viralStats.badgeCount * 0.3)} este mês`,
      trend: 'up' as const,
      icon: <Trophy className="w-5 h-5" />,
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      label: 'Partilhas da Revify',
      value: viralStats.contentCount.toString(),
      change: `+${Math.floor(viralStats.contentCount * 0.4)} esta semana`,
      trend: 'up' as const,
      icon: <Share2 className="w-5 h-5" />,
      color: 'text-purple-600 bg-purple-100'
    }
  ]

  const tabs = [
    { id: overview, name: 'Visão Geral', icon: <Zap className="w-4 h-4" /> },
    { id: referrals, name: 'Convidar para Revify', icon: <Gift className="w-4 h-4" /> },
    { id: badges, name: 'Badges Revify', icon: <Trophy className="w-4 h-4" /> },
    { id: qr, name: 'QR da Revify', icon: <QrCode className="w-4 h-4" /> },
    { id: content, name: 'Partilhar Revify', icon: <Share2 className="w-4 h-4" /> }
  ]

  return (
    <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Revify Rewards
              </h1>
              <p className="text-gray-600">
                Ganhe recompensas por divulgar a Revify e trazer novos utilizadores para a plataforma
              </p>
            </div>
          </div>
        </div>

        {/* Metrics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border p-6 animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                  <div className="w-16 h-4 bg-gray-200 rounded"></div>
                </div>
                <div className="w-12 h-8 bg-gray-200 rounded mb-1"></div>
                <div className="w-24 h-4 bg-gray-200 rounded"></div>
              </div>
            ))
          ) : (
            getMetrics().map((metric, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm border p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${metric.color}`}>
                  {metric.icon}
                </div>
                <div className={`text-sm font-medium ${
                  metric.trend === up ? 'text-green-600' : 
                  metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                
}`}>
                  {metric.change}
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {metric.value}
              </div>
              <div className="text-sm text-gray-600">
              </div>
            </div>
          )))}
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-sm border mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <Gift className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900">
                        Programa de Referrals
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Ganha €5 por cada novo lojista que referires
                    </p>
                    <button
                      onClick={() => setActiveTab('referrals')}
                      className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
                    >
                      Ver Referrals
                    </button>
                  </div>

                  <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <Trophy className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900">
                        Sistema de Badges
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Conquista badges pela tua excelência
                    </p>
                    <button
                      onClick={() => setActiveTab('badges')}
                      className="w-full bg-yellow-500 text-white py-2 px-4 rounded-lg hover:bg-yellow-600 transition-colors"
                    >
                      Ver Badges
                    </button>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                        <QrCode className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="font-semibold text-gray-900">
                        QR Codes Personalizados
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      Cria QR codes com descontos para clientes
                    </p>
                    <button
                      onClick={() => setActiveTab('qr')}
                      className="w-full bg-purple-500 text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors"
                    >
                      Criar QR Code
                    </button>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-white rounded-xl border p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Atividade Recente
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <Gift className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          Novo cliente referido!
                        </p>
                        <p className="text-xs text-gray-600">
                          João Silva usou o teu código de referral
                        </p>
                      </div>
                      <div className="text-sm font-medium text-green-600">
                        +€5
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <Star className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          Badge conquistado!
                        </p>
                        <p className="text-xs text-gray-600">
                          Especialista Samsung - 50+ reparações Samsung
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Share2 className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          Conteúdo partilhado!
                        </p>
                        <p className="text-xs text-gray-600">
                          A tua transformação iPhone 13 teve 45 likes
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tips */}
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Dicas para Crescimento
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">1</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Partilha o teu código de referral
                        </p>
                        <p className="text-xs text-gray-600">
                          Envia para outros lojistas e ganha €5 por cada um
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">2</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Cria conteúdo antes/depois
                        </p>
                        <p className="text-xs text-gray-600">
                          Mostra as transformações incríveis que fazes
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">3</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Usa QR codes com desconto
                        </p>
                        <p className="text-xs text-gray-600">
                          Atrai novos clientes com ofertas especiais
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">4</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Mantém alta qualidade
                        </p>
                        <p className="text-xs text-gray-600">
                          Conquista badges de excelência e especialização
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'referrals' && (
              <div className="max-w-4xl mx-auto">
                <ReferralSystem />
              </div>
            )}

            {activeTab === 'badges' && (
              <div className="max-w-6xl mx-auto">
                <BadgeSystem />
              </div>
            )}

            {activeTab === 'qr' && (
              <div className="max-w-6xl mx-auto">
                <PersonalizedQR />
              </div>
            )}

            {activeTab === 'content' && (
              <div className="max-w-6xl mx-auto">
                <ShareableContent />
              </div>
            )}
          </div>
      </div>
    </div>
  )
}
