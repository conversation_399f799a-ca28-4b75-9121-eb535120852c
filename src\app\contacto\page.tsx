'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  ArrowRight, 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  MessageCircle,
  Users,
  Headphones,
  Globe,
  CheckCircle
} from 'lucide-react'
import LexendLayout from '@/components/LexendLayout'
import AutoTranslate from '@/components/ui/AutoTranslate'

export default function ContactoPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'support'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  const contactMethods = [
    {
      icon: Mail,
      title: 'Email',
      description: 'Envie-nos um email e responderemos em 24h',
      contact: '<EMAIL>',
      action: 'Enviar Email'
    },
    {
      icon: Phone,
      title: 'Telefone',
      description: 'Ligue-nos durante o horário comercial',
      contact: '+351 210 123 456',
      action: 'Ligar Agora'
    },
    {
      icon: MessageCircle,
      title: 'Chat ao Vivo',
      description: 'Fale connosco em tempo real',
      contact: 'Disponível 24/7',
      action: 'Iniciar Chat'
    }
  ]

  const offices = [
    {
      city: 'Lisboa',
      address: 'Avenida da Liberdade, 123\n1250-096 Lisboa',
      phone: '+351 210 123 456',
      email: '<EMAIL>'
    },
    {
      city: 'Porto',
      address: 'Rua de Santa Catarina, 456\n4000-447 Porto',
      phone: '+351 220 123 456',
      email: '<EMAIL>'
    },
    {
      city: 'Madrid',
      address: 'Gran Vía, 789\n28013 Madrid',
      phone: '+34 910 123 456',
      email: '<EMAIL>'
    }
  ]

  const faqs = [
    {
      question: 'Qual o tempo de resposta do suporte?',
      answer: 'Respondemos a todos os emails em até 24 horas. Para questões urgentes, utilize o chat ao vivo.'
    },
    {
      question: 'Posso agendar uma demonstração?',
      answer: 'Sim! Entre em contacto connosco e agendaremos uma demonstração personalizada da plataforma.'
    },
    {
      question: 'Têm suporte em outras línguas?',
      answer: 'Oferecemos suporte em português, espanhol, inglês e francês.'
    },
    {
      question: 'Como posso tornar-me parceiro técnico?',
      answer: 'Visite a nossa página de parceiros ou contacte-nos diretamente para conhecer os requisitos.'
    }
  ]

  if (isSubmitted) {
    return (
      <LexendLayout>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full mx-auto text-center">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Mensagem Enviada!" />
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              <AutoTranslate text="Obrigado pelo seu contacto. Responderemos em breve." />
            </p>
            <Link
              href="/"
              className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all"
            >
              <span><AutoTranslate text="Voltar ao Início" /></span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </LexendLayout>
    )
  }

  return (
    <LexendLayout>
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gray-50 dark:bg-gray-900">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-indigo-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6">
              <span className="text-gray-900 dark:text-white">Entre em </span>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Contacto
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              <AutoTranslate text="Estamos aqui para ajudar. Contacte-nos através do método que preferir." />
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon
              return (
                <div key={index} className="text-center p-8 bg-gray-50 dark:bg-gray-700 rounded-2xl hover:shadow-lg transition-all">
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    <AutoTranslate text={method.title} />
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    <AutoTranslate text={method.description} />
                  </p>
                  <div className="font-medium text-gray-900 dark:text-white mb-6">
                    {method.contact}
                  </div>
                  <button className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all">
                    <AutoTranslate text={method.action} />
                  </button>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Envie-nos uma Mensagem" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              <AutoTranslate text="Preencha o formulário e entraremos em contacto consigo" />
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    <AutoTranslate text="Nome Completo" />
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="O seu nome"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    <AutoTranslate text="Email" />
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    <AutoTranslate text="Tipo de Contacto" />
                  </label>
                  <select
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="support">Suporte Técnico</option>
                    <option value="sales">Vendas</option>
                    <option value="partnership">Parcerias</option>
                    <option value="general">Geral</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    <AutoTranslate text="Assunto" />
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    required
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="Resumo da sua questão"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  <AutoTranslate text="Mensagem" />
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={6}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all resize-none"
                  placeholder="Descreva a sua questão ou pedido em detalhe..."
                />
              </div>

              <div className="text-center">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg hover:shadow-xl"
                >
                  {isSubmitting ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      <span><AutoTranslate text="Enviar Mensagem" /></span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Offices */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Os Nossos Escritórios" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              <AutoTranslate text="Visite-nos pessoalmente em qualquer uma das nossas localizações" />
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {offices.map((office, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-2xl p-8 hover:shadow-lg transition-all">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                    <MapPin className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    {office.city}
                  </h3>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white mb-1">
                      <AutoTranslate text="Endereço" />
                    </div>
                    <div className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                      {office.address}
                    </div>
                  </div>

                  <div>
                    <div className="font-medium text-gray-900 dark:text-white mb-1">
                      <AutoTranslate text="Telefone" />
                    </div>
                    <div className="text-gray-600 dark:text-gray-300">
                      {office.phone}
                    </div>
                  </div>

                  <div>
                    <div className="font-medium text-gray-900 dark:text-white mb-1">
                      <AutoTranslate text="Email" />
                    </div>
                    <div className="text-gray-600 dark:text-gray-300">
                      {office.email}
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <Clock className="w-4 h-4 mr-2" />
                    <AutoTranslate text="Seg-Sex: 9h-18h" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <AutoTranslate text="Perguntas Frequentes" />
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              <AutoTranslate text="Respostas às questões mais comuns sobre contacto e suporte" />
            </p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  <AutoTranslate text={faq.question} />
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  <AutoTranslate text={faq.answer} />
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            <AutoTranslate text="Ainda tem dúvidas?" />
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            <AutoTranslate text="A nossa equipa está sempre disponível para ajudar. Não hesite em contactar-nos!" />
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
            >
              <span><AutoTranslate text="Explorar Plataforma" /></span>
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/sobre"
              className="inline-flex items-center justify-center space-x-2 px-8 py-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-all"
            >
              <span><AutoTranslate text="Saber Mais" /></span>
            </Link>
          </div>
        </div>
      </section>
    </LexendLayout>
  )
}
