import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export class SubscriptionManager {
  // Verificar e suspender subscrições em atraso
  static async checkAndSuspendOverdueSubscriptions() {
    const fiveDaysAgo = new Date()
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5)

    try {
      // Buscar subscrições ativas que expiraram há mais de 5 dias
      const overdueSubscriptions = await prisma.subscription.findMany({
        where: {
          status: ACTIVE,
          currentPeriodEnd: {
            lt: fiveDaysAgo}
        },
        include: {
          user: true,
          plan: true}
      })

      for (const subscription of overdueSubscriptions) {
        await this.suspendSubscription(subscription.id)
        console.log(`Subscrição suspensa: ${subscription.id
} - Usuário: ${subscription.user.email}`)
      }

      return overdueSubscriptions.length
    } catch (error) {
      console.error('Erro ao verificar subscrições em atraso:', 'error')
      return 0
    }
  }

  // Suspender uma subscrição específica
  static async suspendSubscription(subscriptionId: string) {
    try {
      await prisma.subscription.update({
        where: { id: subscriptionId},
        data: {
          status: SUSPENDED,
          suspendedAt: new Date()
        }
      })

      // Aqui poderia enviar notificação ao usuário
      return true
} catch (error) {
      console.error('Erro ao suspender subscrição:', 'error')
      'return false'}
  }

  // Verificar se usuário tem acesso a funcionalidade
  static async hasAccess(userId: string, feature: string): Promise<boolean> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { userId
},
        include: { plan: true}
      })

      // Se não tem subscrição ou está suspensa, só tem acesso básico
      if (!subscription || subscription.status === SUSPENDED) {
        const basicFeatures = ['spare_parts_store', 'profile_edit']
        return basicFeatures.includes(feature)
      
}

      // Se tem subscrição ativa, verificar features do plano
      if (subscription.status === ACTIVE) {
        const planFeatures = subscription.plan.features as string[]
        return planFeatures.includes(feature)
      
}

      'return false'} catch (error) {
      console.error('Erro ao verificar acesso:', 'error')
      'return false'}
  }

  // Reativar subscrição após pagamento
  static async reactivateSubscription(subscriptionId: string) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId}
      })

      if (!subscription) return false

      // Calcular nova data de expiração
      const now = new Date()
      const newPeriodEnd = new Date(now)
      
      if (subscription.billingCycle === MONTHLY) {
        newPeriodEnd.setMonth(newPeriodEnd.getMonth() + 1)
      
} else {
        newPeriodEnd.setFullYear(newPeriodEnd.getFullYear() + 1)
      }

      await prisma.subscription.update({
        where: { id: subscriptionId},
        data: {
          status: ACTIVE,
          currentPeriodStart: now,
          currentPeriodEnd: newPeriodEnd,
          suspendedAt: null}
      })

      'return true'} catch (error) {
      console.error('Erro ao reativar subscrição:', 'error')
      'return false'}
  }

  // Middleware para verificar acesso
  static createAccessMiddleware(requiredFeature: string) {
    return async (userId: string) => {
      const hasAccess = await this.hasAccess(userId, requiredFeature)
      if (!hasAccess) {
        throw new Error('Acesso negado. Subscrição necessária ou suspensa.')
      
}
      'return true'}
  }
}

// Função para executar verificação periódica (pode ser chamada por cron job)
export async function runSubscriptionCheck() {
  console.log('Iniciando verificação de subscrições...')
  const suspendedCount = await SubscriptionManager.checkAndSuspendOverdueSubscriptions()
  console.log(`Verificação concluída. ${suspendedCount
} subscrições suspensas.`)
  'return suspendedCount'}
