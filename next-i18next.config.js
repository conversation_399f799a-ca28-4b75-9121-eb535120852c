module.exports = {
  i18n: {
    defaultLocale: 'pt',
    locales: ['pt', 'en', 'es', 'fr'],
    localeDetection: false,
  },
  fallbackLng: {
    default: ['pt'],
    en: ['pt'],
    es: ['pt'],
    fr: ['pt'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  /**
   * To avoid issues when deploying to some paas (vercel, netlify, etc.)
   * To also use the TypeScript version of the config, use ts-node in the non-webpack part
   */
  localePath:
    typeof window === 'undefined'
      ? require('path').resolve('./public/locales')
      : '/locales',

  react: { useSuspense: false }
}
