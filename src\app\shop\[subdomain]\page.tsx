'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import ShopLayout from '@/components/shop/ShopLayout'
import AutoTranslate from '@/components/ui/AutoTranslate'
import {
  ShoppingCart,
  Star,
  MapPin,
  Phone,
  Clock,
  Wrench,
  Package,
  Shield,
  ArrowRight,
  Smartphone,
  Laptop,
  Tablet
} from 'lucide-react'

interface ShopConfig {
  id: string
  name: string
  companyName: string
  description: string
  logo: string
  phone: string
  address: string
  city: string
  postalCode: string
  businessHours: any
  isActive: boolean
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  category: {
    name: string
  }
  condition: string
  stock: number
}

interface Category {
  id: string
  name: string
  description?: string
  productCount?: number
  isCustom?: boolean
  icon?: string
  sortOrder?: number
}

export default function ShopPage() {
  const params = useParams()
  const subdomain = params.subdomain as string

  const [shopConfig, setShopConfig] = useState<ShopConfig | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchShopData = async () => {
      try {
        // Buscar configuração da loja
        const shopResponse = await fetch(`/api/shop/${subdomain}/config`)
        if (!shopResponse.ok) {
          throw new Error(<AutoTranslate text="Loja não encontrada" />)
        }
        const shopData = await shopResponse.json()
        setShopConfig(shopData)

        // Buscar produtos da loja
        const productsResponse = await fetch(`/api/shop/${subdomain}/products`)
        if (productsResponse.ok) {
          const productsData = await productsResponse.json()
          setProducts(productsData)
          // Pegar os primeiros 4 produtos como featured
          setFeaturedProducts(productsData.slice(0, 4))
        }

        // Buscar categorias da loja
        const categoriesResponse = await fetch(`/api/shop/${subdomain}/categories`)
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          setCategories(categoriesData.categories || [])
        }
      } catch (error) {
        console.error('Erro ao carregar loja:', error)
        setError("Loja não encontrada ou indisponível")
      } finally {
        setIsLoading(false)
      }
    }

    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !shopConfig) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4"><AutoTranslate text="Loja não encontrada" /></h1>
          <p className="text-gray-600 mb-8">{error}</p>
          <Link 
            href="/"
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          ><AutoTranslate text="Voltar ao início" /></Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shopConfig.companyName || shopConfig.name}
      subdomain={subdomain}
      logoUrl={shopConfig.logo}
      shopInfo={{
        phone: shopConfig.phone,
        address: shopConfig.address,
        city: shopConfig.city,
        postalCode: shopConfig.postalCode
      }}
    >
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-gray-900 via-black to-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6"><AutoTranslate text="Reparações de Qualidade" /></h1>
              <p className="text-xl mb-8 text-gray-300"><AutoTranslate text="Especialistas em reparação de dispositivos eletrónicos. Diagnóstico gratuito e garantia em todos os serviços." /></p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={`/shop/${subdomain}/nova-reparacao`}
                  className="bg-white text-black px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2 font-semibold"
                >
                  <Wrench className="w-5 h-5" />
                  <span><AutoTranslate text="Solicitar Reparação" /></span>
                </Link>
                <Link
                  href={`/shop/${subdomain}/produtos`}
                  className="border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-black transition-colors flex items-center justify-center space-x-2 font-semibold"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span><AutoTranslate text="Ver Produtos" /></span>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <div className="grid grid-cols-3 gap-6 text-center">
                  <div>
                    <Smartphone className="w-12 h-12 mx-auto mb-4" />
                    <h3 className="font-semibold">Smartphones</h3>
                  </div>
                  <div>
                    <Tablet className="w-12 h-12 mx-auto mb-4" />
                    <h3 className="font-semibold">Tablets</h3>
                  </div>
                  <div>
                    <Laptop className="w-12 h-12 mx-auto mb-4" />
                    <h3 className="font-semibold">Laptops</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4"><AutoTranslate text="Os Nossos Serviços" /></h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto"><AutoTranslate text="Oferecemos uma gama completa de serviços de reparação com qualidade garantida" /></p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-8 h-8 text-gray-800" />
              </div>
              <h3 className="text-lg font-semibold mb-2"><AutoTranslate text="Reparação de Ecrãs" /></h3>
              <p className="text-gray-600 text-sm"><AutoTranslate text="Substituição de ecrãs partidos com peças originais" /></p>
            </div>

            <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="w-8 h-8 text-gray-800" />
              </div>
              <h3 className="text-lg font-semibold mb-2"><AutoTranslate text="Substituição de Baterias" /></h3>
              <p className="text-gray-600 text-sm"><AutoTranslate text="Baterias de alta qualidade para maior duração" /></p>
            </div>

            <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-gray-800" />
              </div>
              <h3 className="text-lg font-semibold mb-2"><AutoTranslate text="Diagnóstico Gratuito" /></h3>
              <p className="text-gray-600 text-sm"><AutoTranslate text="Avaliação completa sem custos adicionais" /></p>
            </div>

            <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-gray-800" />
              </div>
              <h3 className="text-lg font-semibold mb-2"><AutoTranslate text="Reparação Rápida" /></h3>
              <p className="text-gray-600 text-sm"><AutoTranslate text="Maioria das reparações em 24h" /></p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      {categories.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Categorias
              </h2>
              <p className="text-lg text-gray-600"><AutoTranslate text="Explore os nossos produtos por categoria" /></p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/shop/${subdomain}/categoria/${category.id}`}
                  className="group"
                >
                  <div className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-200 group-hover:border-indigo-300">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-indigo-200 group-hover:to-purple-200 transition-colors">
                      {category.icon ? (
                        <span className="text-2xl">{category.icon}</span>
                      ) : category.isCustom ? (
                        <Package className="w-8 h-8 text-indigo-600" />
                      ) : (
                        getCategoryIcon(category.name)
                      )}
                    </div>
                    <h3 className="text-lg font-semibold mb-2 group-hover:text-indigo-600 transition-colors">
                      {category.name}
                    </h3>
                    {category.description && (
                      <p className="text-gray-600 text-sm mb-2">{category.description}</p>
                    )}
                    {category.productCount !== undefined && category.productCount > 0 && (
                      <p className="text-sm text-gray-500">
                        {category.productCount} produtos
                      </p>
                    )}
                    {category.isCustom && (
                      <span className="inline-block mt-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                        Personalizada
                      </span>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4"><AutoTranslate text="Produtos em Destaque" /></h2>
                <p className="text-lg text-gray-600"><AutoTranslate text="Peças e acessórios de qualidade para o seu dispositivo" /></p>
              </div>
              <Link
                href={`/shop/${subdomain}/produtos`}
                className="text-gray-900 hover:text-black font-semibold flex items-center space-x-2"
              >
                <span>Ver Todos</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                  <div className="aspect-square bg-gray-100 relative">
                    {product.images[0] ? (
                      <Image
                        src={product.images[0]}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Package className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                    {product.originalPrice && product.originalPrice > product.price && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                        -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">€{product.price.toFixed(2)}</span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-sm text-gray-500 line-through">€{product.originalPrice.toFixed(2)}</span>
                        )}
                      </div>
                      <button className="bg-gradient-to-r from-gray-900 to-black text-white p-2 rounded-lg hover:from-black hover:to-gray-900 transition-colors">
                        <ShoppingCart className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Contact & Hours Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Como Nos Encontrar</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-gray-800" />
                  <span>{shopConfig.address}, {shopConfig.city}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-gray-800" />
                  <span>{shopConfig.phone}</span>
                </div>
              </div>

              <div className="mt-8">
                <Link
                  href={`/shop/${subdomain}/nova-reparacao`}
                  className="inline-flex items-center space-x-2 bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
                >
                  <Wrench className="w-5 h-5<AutoTranslate text="/> <span><AutoTranslate text="Solicitar Reparação" /></span>
                </Link>
              </div>
            </div>

            <div>
              <h3 className=" />text-2xl font-bold text-gray-900 mb-6"><AutoTranslate text="Opções de Entrega" /></h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-green-600 mt-1" />
                  <div>
                    <h4 className="font-semibold"><AutoTranslate text="Entrega em Loja" /></h4>
                    <p className="text-gray-600 text-sm"><AutoTranslate text="Venha buscar diretamente à nossa loja" /></p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Package className="w-5 h-5 text-gray-800 mt-1" />
                  <div>
                    <h4 className="font-semibold">Envio por Correio</h4>
                    <p className="text-gray-600 text-sm"><AutoTranslate text="Enviamos para todo o país com seguro incluído" /></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </ShopLayout>
  )
}
