'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ShoppingCart, 
  User, 
  Search, 
  Star, 
  ArrowRight, 
  Truck, 
  Shield, 
  Phone,
  Mail,
  MapPin,
  Menu,
  X,
  Heart,
  Eye
} from 'lucide-react'

interface ShopData {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  logo?: string
  description?: string
  hasIndividualRepairs: boolean
  customSubdomain?: string
}

interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  images: string[]
  category?: {
    name: string
  }
  brand?: {
    name: string
  }
}

interface Category {
  id: string
  name: string
  productCount: number
}

export default function ShopHomePage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [cart, setCart] = useState<{[key: string]: number}>({})
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const subdomain = params.subdomain as string

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
      fetchFeaturedProducts()
      fetchCategories()
      loadCart()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}`)
      if (response.ok) {
        const data = await response.json()
        setShop(data.shop)
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', error)
    }
  }

  const fetchFeaturedProducts = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/produtos`)
      if (response.ok) {
        const data = await response.json()
        setFeaturedProducts(data.products.slice(0, 8))
      }
    } catch (error) {
      console.error('Erro ao carregar produtos:', error)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/categorias`)
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      }
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadCart = () => {
    const savedCart = localStorage.getItem(`cart_${subdomain}`)
    if (savedCart) {
      setCart(JSON.parse(savedCart))
    }
  }

  const getCartItemCount = () => {
    return Object.values(cart).reduce((sum, count) => sum + count, 0)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: 'EUR'
    }).format(price)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <p className="text-gray-600">O subdomínio solicitado não existe ou não está ativo.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href={`/shop/${subdomain}`} className="flex items-center">
                {shop.logo ? (
                  <img src={shop.logo} alt={shop.name} className="h-8 w-auto" />
                ) : (
                  <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {shop.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <span className="ml-3 text-xl font-bold text-gray-900">{shop.name}</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href={`/shop/${subdomain}`} className="text-gray-700 hover:text-blue-600 font-medium">
                Início
              </Link>
              <Link href={`/shop/${subdomain}/produtos`} className="text-gray-700 hover:text-blue-600 font-medium">
                Produtos
              </Link>
              {categories.slice(0, 3).map((category) => (
                <Link 
                  key={category.id}
                  href={`/shop/${subdomain}/produtos?categoria=${category.name}`} 
                  className="text-gray-700 hover:text-blue-600 font-medium"
                >
                  {category.name}
                </Link>
              ))}
              <Link href={`/shop/${subdomain}/nova-reparacao`} className="text-gray-700 hover:text-blue-600 font-medium">
                Reparações
              </Link>
            </nav>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden lg:block">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar produtos..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                  />
                </div>
              </div>

              {/* Account */}
              <Link
                href={`/shop/${subdomain}/cliente`}
                className="p-2 text-gray-600 hover:text-gray-900"
              >
                <User className="w-6 h-6" />
              </Link>

              {/* Cart */}
              <Link
                href={`/shop/${subdomain}/carrinho`}
                className="relative p-2 text-gray-600 hover:text-gray-900"
              >
                <ShoppingCart className="w-6 h-6" />
                {getCartItemCount() > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {getCartItemCount()}
                  </span>
                )}
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 text-gray-600 hover:text-gray-900"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-gray-100 py-4">
              <div className="space-y-4">
                <Link href={`/shop/${subdomain}`} className="block text-gray-700 hover:text-blue-600 font-medium">
                  Início
                </Link>
                <Link href={`/shop/${subdomain}/produtos`} className="block text-gray-700 hover:text-blue-600 font-medium">
                  Produtos
                </Link>
                {categories.slice(0, 3).map((category) => (
                  <Link 
                    key={category.id}
                    href={`/shop/${subdomain}/produtos?categoria=${category.name}`} 
                    className="block text-gray-700 hover:text-blue-600 font-medium"
                  >
                    {category.name}
                  </Link>
                ))}
                <Link href={`/shop/${subdomain}/nova-reparacao`} className="block text-gray-700 hover:text-blue-600 font-medium">
                  Reparações
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Banner */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-6xl font-bold mb-6">
                Bem-vindo à {shop.name}
              </h1>
              <p className="text-xl text-blue-100 mb-8">
                {shop.description || 'Descubra produtos de qualidade com os melhores preços e atendimento especializado.'}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={`/shop/${subdomain}/produtos`}
                  className="inline-flex items-center px-8 py-4 bg-white text-blue-600 rounded-lg hover:bg-gray-100 font-semibold text-lg"
                >
                  Comprar Agora
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href={`/shop/${subdomain}/nova-reparacao`}
                  className="inline-flex items-center px-8 py-4 border-2 border-white text-white rounded-lg hover:bg-white hover:text-blue-600 font-semibold text-lg"
                >
                  Solicitar Reparação
                </Link>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="relative">
                <div className="absolute inset-0 bg-white/10 rounded-2xl transform rotate-6"></div>
                <div className="relative bg-white/20 backdrop-blur-sm rounded-2xl p-8">
                  <div className="grid grid-cols-2 gap-4">
                    {featuredProducts.slice(0, 4).map((product, index) => (
                      <div key={product.id} className="bg-white/30 rounded-lg p-4">
                        <img
                          src={product.images[0] || '/images/placeholder-product.jpg'}
                          alt={product.name}
                          className="w-full h-20 object-cover rounded mb-2"
                        />
                        <p className="text-sm font-medium truncate">{product.name}</p>
                        <p className="text-xs text-blue-100">{formatPrice(product.price)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
