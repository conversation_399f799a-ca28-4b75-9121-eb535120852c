'use client'

import { useState, useEffect } from 'react'
import { Clock, Users, Star, TrendingUp, AlertCircle, CheckCircle, Zap } from 'lucide-react'
interface UrgencyNotification {
  id: string
  type: 'booking_activity' | 'limited_slots' | 'high_rating' | 'recent_repair' | 'trending_shop'
  message: string
  count?: number
  percentage?: number
  timeframe?: string
  priority: 'low' | 'medium' | 'high'
  shopName?: string
  location?: string}

interface ShopAvailability {
  shopId: string
  shopName: string
  location: string
  availableSlots: number
  totalSlots: number
  rating: number
  recentBookings: number
  isPopular: boolean}

export default function UrgencyElements() {
  const [notifications, setNotifications] = useState<UrgencyNotification[]>([])
  const [shopAvailability, setShopAvailability] = useState<ShopAvailability[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUrgencyData()
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchUrgencyData, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchUrgencyData = async () => {
    try {
      const response = await fetch('/api/viral/urgency')

      if (!response.ok) {
        throw new Error('Erro ao carregar dados de urgência')
      
}

      const data = await response.json()
      setNotifications(data.notifications)
      setShopAvailability(data.shopAvailability)
    } catch (error) {
      console.error('Erro ao buscar dados de urgência:', 'error')

      // Fallback para dados mock
      const mockNotifications: UrgencyNotification[] = [
        {
          id: 1,
          type: booking_activity,
          message: '12 pessoas agendaram reparação de iPhone hoje',
          count: 12,
          timeframe: hoje,
          priority: high
},
        {
          id: '2',
          type: limited_slots,
          message: 'Apenas 3 vagas disponíveis para hoje em Lisboa',
          count: 3,
          priority: high,
          location: Lisboa},
        {
          id: '3',
          type: high_rating,
          message: 'TechFix Lisboa mantém 4.9⭐ de rating',
          percentage: 98,
          priority: medium,
          shopName: 'TechFix Lisboa',
          location: Lisboa}
      ]

      const mockShopAvailability: ShopAvailability[] = [
        {
          shopId: '1',
          shopName: 'TechFix Lisboa',
          location: Lisboa,
          availableSlots: 3,
          totalSlots: 20,
          rating: 4.9,
          recentBookings: 12,
          isPopular: true},
        {
          shopId: '2',
          shopName: 'RepairPro Porto',
          location: Porto,
          availableSlots: 7,
          totalSlots: 15,
          rating: 4.8,
          recentBookings: 8,
          isPopular: false},
        {
          shopId: '3',
          shopName: 'QuickFix Coimbra',
          location: Coimbra,
          availableSlots: 1,
          totalSlots: 10,
          rating: 4.7,
          recentBookings: 5,
          isPopular: true}
      ]

      setNotifications(mockNotifications)
      setShopAvailability(mockShopAvailability)
    } finally {
      setLoading(false)
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking_activity':
        return <Users className="w-4 h-4" />
      case 'limited_slots':
        return <Clock className="w-4 h-4" />
      case 'high_rating':
        return <Star className="w-4 h-4" />
      case 'recent_repair':
        return <CheckCircle className="w-4 h-4" />
      case 'trending_shop':
        return <TrendingUp className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const getNotificationColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAvailabilityStatus = (available: number, total: number) => {
    const percentage = (available / 'total') * 100
    if (percentage <= 20) return { status: critical, color: 'text-red-600', bg: 'bg-red-100' }
    if (percentage <= 50) return { status: low, color: 'text-yellow-600', bg: 'bg-yellow-100' }
    return { status: good, color: 'text-green-600', bg: 'bg-green-100' }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Real-time Notifications */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Alertas de Urgência
              </h3>
              <p className="text-sm text-gray-600">
                Notificações em tempo real para criar urgência
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-1 text-red-600">
            <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
            <span className="text-xs font-medium">
              Ao vivo
            </span>
          </div>
        </div>

        <div className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`flex items-center space-x-3 p-4 rounded-lg border ${getNotificationColor(notification.priority)}`}
            >
              <div className="flex-shrink-0">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  {notification.count && (
                    <span className="text-lg font-bold">
                      {notification.count}
                    </span>
                  )}
                  {notification.percentage && (
                    <span className="text-lg font-bold">
                      {notification.percentage}%
                    </span>
                  )}
                  <span className="text-sm">
                  </span>
                </div>
                {notification.shopName && (
                  <div className="text-xs opacity-75 mt-1">
                    {notification.shopName}
                    {notification.location && ` • ${notification.location}`}
                  </div>
                )}
              </div>
              {notification.priority === 'high' && (
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Shop Availability Status */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
            <Clock className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Disponibilidade das Lojas
            </h3>
            <p className="text-sm text-gray-600">
              Status em tempo real da disponibilidade
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {shopAvailability.map((shop) => {
            const availability = getAvailabilityStatus(shop.availableSlots, shop.totalSlots)
            return (
              <div key={shop.shopId} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                        <span>{shop.shopName}</span>
                        {shop.isPopular && (
                          <div className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                            Popular
                          </div>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600">{shop.location}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="font-medium">{shop.rating}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className={`text-lg font-bold ${availability.color}`}>
                      {shop.availableSlots}
                    </div>
                    <div className="text-xs text-gray-600">
                      Vagas disponíveis
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {shop.recentBookings}
                    </div>
                    <div className="text-xs text-gray-600">
                      Agendamentos hoje
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {Math.round((shop.availableSlots / shop.totalSlots) * 100)}%
                    </div>
                    <div className="text-xs text-gray-600">
                      Disponibilidade
                    </div>
                  </div>
                </div>

                {/* Availability Bar */}
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-600">
                      Ocupação
                    </span>
                    <span className="text-xs text-gray-600">
                      {shop.totalSlots - shop.availableSlots}/{shop.totalSlots}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        availability.status === 'critical' ? 'bg-red-500' :
                        availability.status === 'low' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ 
                        width: `${((shop.totalSlots - shop.availableSlots) / shop.totalSlots) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>

                {/* Urgency Message */}
                {shop.availableSlots <= 3 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2 text-red-800">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">
                        Poucas vagas disponíveis! Agenda já.
                      </span>
                    </div>
                  </div>
                )}

                {shop.isPopular && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mt-2">
                    <div className="flex items-center space-x-2 text-orange-800">
                      <TrendingUp className="w-4 h-4" />
                      <span className="text-sm font-medium">
                        Esta loja está em alta demanda hoje!
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Social Proof Ticker */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <CheckCircle className="w-4 h-4 text-white" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900">
              Atividade recente da plataforma
            </div>
            <div className="text-xs text-gray-600 mt-1">
              João M. reparou iPhone 13 há 5 min • Maria S. agendou reparação há 12 min • TechFix Lisboa completou 3 reparações hoje
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
