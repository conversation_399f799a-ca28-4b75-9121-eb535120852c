import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendEmail } from "@/lib/email"

interface OrderItem {
  partId: string
  quantity: number
  price: number
}

interface Customer {
  email: string
  name: string
  phone: string
  address?: string | null
  city?: string | null
  postalCode?: string | null
}

interface OrderData {
  items: OrderItem[]
  customer: Customer
  deliveryMethod: 'delivery' | 'pickup'
  paymentMethod: 'card' | 'multibanco' | 'paypal'
  total: number
  shopSubdomain: string
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params
    const orderData: OrderData = await request.json()

    try {
      // Find the shop by subdomain
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain
          }
        },
        include: {
          profile: true
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: "Loja não encontrada" },
          { status: 404 }
        )
      }

      // Validate parts exist and have enough stock
      const partIds = orderData.items.map(item => item.partId)
      const parts = await prisma.part.findMany({
        where: {
          id: { in: partIds }
        }
      })

      if (parts.length !== partIds.length) {
        return NextResponse.json(
          { error: "Algumas peças não foram encontradas" },
          { status: 400 }
        )
      }

      // Check stock availability
      for (const item of orderData.items) {
        const part = parts.find((p: { id: string }) => p.id === item.partId)
        if (!part) {
          return NextResponse.json(
            { error: `Peça ${item.partId} não encontrada` },
            { status: 400 }
          )
        }

        if (part.stock < item.quantity) {
          return NextResponse.json(
            { error: `Stock insuficiente para a peça ${part.name}` },
            { status: 400 }
          )
        }
      }

      // Create order in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the order
        const order = await tx.order.create({
          data: {
            customerId: shop.id,
            shippingName: orderData.customer.name,
            shippingStreet: orderData.customer.address || '',
            shippingCity: orderData.customer.city || '',
            shippingPostalCode: orderData.customer.postalCode || '',
            paymentMethod: orderData.paymentMethod,
            total: orderData.total,
            status: 'PENDING'
          }
        })

        // Create order items
        for (const item of orderData.items) {
          await tx.orderItem.create({
            data: {
              orderId: order.id,
              partId: item.partId,
              quantity: item.quantity,
              price: item.price
            }
          })
        }

        return order

        // Update part stock
        for (const item of orderData.items) {
          await tx.part.update({
            where: { id: item.partId },
            data: {
              stock: {
                decrement: item.quantity
              }
            }
          })
        }

        return order
      })

      // Generate order number
      const orderNumber = `ORD-${result.id.slice(-8).toUpperCase()}`

      // Note: Order model doesn't have orderNumber field, so we skip this update

      // Send confirmation email
      try {
        await sendEmail(
          orderData.customer.email,
          'orderConfirmation',
          {
            customerName: orderData.customer.name,
            orderNumber,
            items: orderData.items.map(item => ({
              name: `Produto ${item.productId}`, // In real app, you'd fetch product name
              quantity: item.quantity,
              price: item.price.toFixed(2)
            })),
            total: orderData.total.toFixed(2),
            deliveryMethod: orderData.deliveryMethod,
            customerAddress: orderData.customer.address,
            customerCity: orderData.customer.city,
            customerPostalCode: orderData.customer.postalCode,
            shopName: shop.profile?.companyName || 'Loja'
          }
        )
      } catch (emailError) {
        console.error('Erro ao enviar email de confirmação:', emailError)
        // Don't fail the order creation if email fails
      }

      return NextResponse.json({
        success: true,
        orderId: result.id,
        orderNumber,
        message: "Encomenda criada com sucesso"
      })

    } catch (dbError) {
      console.error('Erro de base de dados:', dbError)
      throw dbError
    }

  } catch (error) {
    console.error("Erro ao criar encomenda:", error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params



    try {
      // Find the shop by subdomain
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain
          }
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: "Loja não encontrada" },
          { status: 404 }
        )
      }

      // Build where clause
      const whereClause: { customerId: string } = {
        customerId: shop.id
      }

      // Get orders
      const orders = await prisma.order.findMany({
        where: whereClause,
        include: {
          orderItems: {
            include: {
              part: {
                select: {
                  id: true,
                  name: true,
                  images: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return NextResponse.json({
        success: true,
        orders
      })

    } catch (dbError) {
      console.error('Erro de base de dados na busca:', dbError)
      throw dbError
    }

  } catch (error) {
    console.error("Erro ao buscar encomendas:", error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
