import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const userId = searchParams.get('userId')

    const whereClause: Record<string, any> = {}

    // Filtrar por role do utilizador
    if (session.user.role === 'CUSTOMER') {
      whereClause.customerId = session.user.id
    } else if (session.user.role === 'REPAIR_SHOP') {
      whereClause.repairShopId = session.user.id
    } else if (session.user.role === 'COURIER') {
      whereClause.courierId = session.user.id
    }
    // ADMIN pode ver todas as reparações

    // Filtrar por status se especificado
    if (status) {
      whereClause.status = status
    }

    // Filtrar por utilizador específico (apenas para admin)
    if (userId && session.user.role === 'ADMIN') {
      whereClause.customerId = userId
    }

    const repairs = await prisma.repair.findMany({
      where: whereClause,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        device: {
          include: {
            brand: true,
            category: true
          }
        },

        items: {
          include: {
            part: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(repairs)

  } catch (error) {
    console.error('Erro ao buscar reparações:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const {
      deviceId,
      problemTypeId,
      description,
      urgency,
      scheduledDate,
      pickupAddress,
      deliveryAddress,
      images,
      repairShopId
    } = await request.json()

    if (!deviceId || !description) {
      return NextResponse.json(
        { message: 'Dispositivo e descrição são obrigatórios' },
        { status: 400 }
      )
    }

    // Criar endereço temporário (será melhorado depois)
    const address = await prisma.address.create({
      data: {
        userId: session.user.id,
        name: 'Endereço de reparação',
        street: pickupAddress || 'Endereço não especificado',
        city: 'Lisboa',
        postalCode: '1000-000',
        country: 'Portugal',
        isDefault: false
      }
    })

    // Verificar se o dispositivo existe
    const device = await prisma.deviceModel.findUnique({
      where: { id: deviceId }
    })

    if (!device) {
      return NextResponse.json(
        { message: 'Dispositivo não encontrado' },
        { status: 404 }
      )
    }

    const repair = await prisma.repair.create({
      data: {
        customerId: session.user.id,
        deviceId,
        problemTypeId: problemTypeId || null,
        addressId: address.id,
        issue: description,
        description: description,
        images: images || [],
        status: 'RECEIVED',
        scheduledDate: scheduledDate ? new Date(scheduledDate) : null,
        estimatedPrice: 0,
        finalPrice: 0
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        device: {
          include: {
            brand: true,
            category: true
          }
        },
        problemType: true,
        address: true
      }
    })

    return NextResponse.json(repair, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
