'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { CreditCard, TrendingUp, Calendar, DollarSign, ArrowLeft, Plus, Edit } from 'lucide-react'
import Link from 'next/link'

interface Transaction {
  id: string
  type: string
  status: string
  amount: number
  commission: number
  netAmount: number
  description: string
  payoutDate: string | null
  paidAt: string | null
  createdAt: string
}

interface BankDetails {
  id: string
  iban: string
  accountName: string
  bankName: string
  verified: boolean
}

export default function FinanceiroPage() {
  const { data: session } = useSession()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [bankDetails, setBankDetails] = useState<BankDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('transactions')
  const [showBankForm, setShowBankForm] = useState(false)
  const [bankForm, setBankForm] = useState({
    iban: '',
    accountName: '',
    bankName: ''
  })

  useEffect(() => {
    fetchFinancialData()
  }, [])

  const fetchFinancialData = async () => {
    try {
      const [transactionsRes, bankRes] = await Promise.all([
        fetch('/api/lojista/transactions'),
        fetch('/api/lojista/bank-details')
      ])

      if (transactionsRes.ok) {
        const transactionsData = await transactionsRes.json()
        setTransactions(transactionsData.transactions)
      }

      if (bankRes.ok) {
        const bankData = await bankRes.json()
        setBankDetails(bankData.bankDetails)
        if (bankData.bankDetails) {
          setBankForm({
            iban: bankData.bankDetails.iban,
            accountName: bankData.bankDetails.accountName,
            bankName: bankData.bankDetails.bankName || ''
          })
        }
      }
    } catch (error) {
      console.error('Erro ao buscar dados financeiros:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const saveBankDetails = async () => {
    try {
      const response = await fetch('/api/lojista/bank-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bankForm)
      })

      if (response.ok) {
        alert('Dados bancários salvos com sucesso!')
        setShowBankForm(false)
        fetchFinancialData()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar dados bancários')
      }
    } catch (error) {
      console.error('Erro ao salvar dados bancários:', error)
      alert('Erro ao salvar dados bancários')
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'PROCESSING': 'bg-blue-100 text-blue-800',
      'COMPLETED': 'bg-indigo-100 text-indigo-800',
      'FAILED': 'bg-red-100 text-red-800',
      'CANCELLED': 'bg-gray-100 text-gray-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getTypeLabel = (type: string) => {
    const labels = {
      'MARKETPLACE_SALE': 'Venda Marketplace',
      'REPAIR_PAYMENT': 'Pagamento Reparação',
      'PARTS_PURCHASE': 'Compra de Peças',
      'COMMISSION': 'Comissão',
      'PAYOUT': 'Transferência',
      'REFUND': 'Reembolso'
    }
    return labels[type as keyof typeof labels] || type
  }

  const totalPending = transactions
    .filter(t => t.status === 'PENDING' || t.status === 'PROCESSING')
    .reduce((sum, t) => sum + t.netAmount, 0)

  const totalPaid = transactions
    .filter(t => t.status === 'COMPLETED')
    .reduce((sum, t) => sum + t.netAmount, 0)

  const totalCommission = transactions
    .reduce((sum, t) => sum + (t.commission || 0), 0)

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <h1 className="text-2xl font-bold text-black">Financeiro</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">A Receber</p>
                <p className="text-2xl font-bold text-gray-900">€{totalPending.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Recebido</p>
                <p className="text-2xl font-bold text-gray-900">€{totalPaid.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Comissões Pagas</p>
                <p className="text-2xl font-bold text-gray-900">€{totalCommission.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('transactions')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'transactions'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Transações
              </button>
              <button
                onClick={() => setActiveTab('bank')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'bank'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Dados Bancários
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'transactions' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Histórico de Transações</h3>
                </div>

                {transactions.length === 0 ? (
                  <div className="text-center py-12">
                    <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma transação</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      As suas transações aparecerão aqui quando tiver vendas ou reparações.
                    </p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tipo
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Descrição
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Valor Bruto
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Comissão
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Valor Líquido
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Data Prevista
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {transactions.map((transaction) => (
                          <tr key={transaction.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {getTypeLabel(transaction.type)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {transaction.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              €{transaction.amount.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                              -€{(transaction.commission || 0).toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                              €{transaction.netAmount.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                                {transaction.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {transaction.payoutDate 
                                ? new Date(transaction.payoutDate).toLocaleDateString('pt-PT')
                                : '-'
                              }
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'bank' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Dados Bancários</h3>
                  <button
                    onClick={() => setShowBankForm(!showBankForm)}
                    className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                  >
                    {bankDetails ? <Edit className="w-4 h-4 mr-2" /> : <Plus className="w-4 h-4 mr-2" />}
                    {bankDetails ? 'Editar' : 'Adicionar'} Dados
                  </button>
                </div>

                {bankDetails && !showBankForm ? (
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          IBAN
                        </label>
                        <p className="text-sm text-gray-900">{bankDetails.iban}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nome do Titular
                        </label>
                        <p className="text-sm text-gray-900">{bankDetails.accountName}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Banco
                        </label>
                        <p className="text-sm text-gray-900">{bankDetails.bankName || 'Não especificado'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          bankDetails.verified 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {bankDetails.verified ? 'Verificado' : 'Pendente Verificação'}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : showBankForm ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          IBAN *
                        </label>
                        <input
                          type="text"
                          value={bankForm.iban}
                          onChange={(e) => setBankForm({...bankForm, iban: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                          placeholder="PT50 0000 0000 0000 0000 0000 0"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nome do Titular *
                        </label>
                        <input
                          type="text"
                          value={bankForm.accountName}
                          onChange={(e) => setBankForm({...bankForm, accountName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                          placeholder="Nome completo do titular"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Banco
                        </label>
                        <input
                          type="text"
                          value={bankForm.bankName}
                          onChange={(e) => setBankForm({...bankForm, bankName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                          placeholder="Nome do banco"
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => setShowBankForm(false)}
                        className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                      >
                        Cancelar
                      </button>
                      <button
                        onClick={saveBankDetails}
                        className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                      >
                        Salvar
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum dado bancário</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Adicione os seus dados bancários para receber pagamentos.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
