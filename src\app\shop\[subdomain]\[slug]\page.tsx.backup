'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import ShopLayout from '@/components/shop/ShopLayout'
import { useShopCart } from '@/hooks/useShopCart'

interface CustomPage {
  id: string
  slug: string
  title: string
  content: string
  metaDescription?: string
}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string
  logoUrl?: string
}

export default function CustomPageView() {
  const params = useParams()
  const subdomain = params.subdomain as string
  const slug = params.slug as string
  
  const [page, setPage] = useState<CustomPage | null>(null)
  const [shopData, setShopData] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { getCartItemCount } = useShopCart()

  useEffect(() => {
    fetchPageData()
    fetchShopData()
  }, [subdomain, slug])

  const fetchPageData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/custom-pages/${slug}`)
      
      if (response.ok) {
        const data = await response.json()
        setPage(data.page)
      } else if (response.status === 404) {
        setError('Página não encontrada')
      } else {
        setError('Erro ao carregar página')
      }
    } catch (error) {
      console.error('Erro ao buscar página:', error)
      setError('Erro ao carregar página')
    } finally {
      setIsLoading(false)
    }
  }

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      
      if (response.ok) {
        const data = await response.json()
        setShopData({
          id: data.id,
          name: data.name,
          companyName: data.companyName,
          phone: data.phone,
          address: data.address,
          city: data.city,
          postalCode: data.postalCode,
          logoUrl: data.logo
        })
      }
    } catch (error) {
      console.error('Erro ao buscar dados da loja:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (error || !page || !shopData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Página não encontrada'}
          </h1>
          <p className="text-gray-600 mb-8">
            A página que procura não existe ou não está disponível.
          </p>
          <a 
            href={`/shop/${subdomain}`}
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors"
          >
            Voltar à loja
          </a>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shopData.companyName || shopData.name}
      subdomain={subdomain}
      logoUrl={shopData.logoUrl}
      cartItemsCount={getCartItemCount()}
      shopInfo={{
        phone: shopData.phone,
        address: shopData.address,
        city: shopData.city,
        postalCode: shopData.postalCode
      }}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {page.title}
          </h1>
          {page.metaDescription && (
            <p className="text-xl text-gray-600">
              {page.metaDescription}
            </p>
          )}
        </div>

        {/* Page Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: page.content }}
          />
        </div>
      </div>
    </ShopLayout>
  )
}
