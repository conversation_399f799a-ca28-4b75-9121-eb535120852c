import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { getToken } from 'next-auth/jwt'
import { authOptions } from '@/lib/auth'

export async function GET(request: Request) {
  try {
    // Verificar sessão do servidor
    const session = await getServerSession(authOptions)
    
    // Verificar token JWT
    const token = await getToken({ 
      req: request as any, 
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === 'production'
    })
    
    return NextResponse.json({
      success: true,
      session: session ? {
        user: session.user,
        expires: session.expires
      } : null,
      token: token ? {
        sub: token.sub,
        email: token.email,
        role: token.role,
        exp: token.exp
      } : null,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        hasSecret: !!process.env.NEXTAUTH_SECRET
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Erro no debug da sessão:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro no debug da sessão',
      details: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
