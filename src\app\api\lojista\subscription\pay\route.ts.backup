import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { paymentId } = await request.json()

    if (!paymentId) {
      return NextResponse.json(
        { message: 'ID do pagamento é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar pagamento
    const payment = await prisma.$queryRaw`
      SELECT 
        sp.*,
        s."userId" as subscription_user_id,
        plan.name as plan_name
      FROM subscription_payments sp
      JOIN subscriptions s ON sp."subscriptionId" = s.id
      JOIN subscription_plans plan ON s."planId" = plan.id
      WHERE sp.id = ${paymentId} 
      AND s."userId" = ${session.user.id}
      AND sp.status = 'PENDING'
    ` as any[]

    if (!payment || payment.length === 0) {
      return NextResponse.json(
        { message: 'Pagamento não encontrado ou já processado' },
        { status: 404 }
      )
    }

    const paymentData = payment[0]

    // Criar URL de checkout Stripe (simulado para demo)
    const checkoutUrl = `${process.env.NEXTAUTH_URL}/checkout?payment=${paymentId}&amount=${paymentData.amount}&plan=${paymentData.plan_name}`

    return NextResponse.json({
      message: 'Redirecionando para pagamento...',
      checkoutUrl,
      payment: {
        id: paymentData.id,
        amount: Number(paymentData.amount),
        currency: paymentData.currency,
        planName: paymentData.plan_name
      }
    })

  } catch (error) {
    console.error('Erro ao processar pagamento:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
