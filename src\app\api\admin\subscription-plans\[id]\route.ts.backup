import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const plan = await prisma.$queryRaw`
      SELECT * FROM subscription_plans
      WHERE id = ${params.id}
    `

    if (!plan || plan.length === 0) {
      return NextResponse.json(
        { message: 'Plano não encontrado' },
        { status: 404 }
      )
    }

    // Converter BigInt para Number
    const serializedPlan = {
      ...plan[0],
      monthlyPrice: Number(plan[0].monthlyPrice),
      yearlyPrice: Number(plan[0].yearlyPrice),
      marketplaceCommission: Number(plan[0].marketplaceCommission),
      repairCommission: Number(plan[0].repairCommission),
      paymentDelayDays: Number(plan[0].paymentDelayDays),
      sparePartsDiscount: Number(plan[0].sparePartsDiscount),
      priority: Number(plan[0].priority)
    }

    return NextResponse.json({ plan: serializedPlan })

  } catch (error) {
    console.error('Erro ao buscar plano:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()



    const plan = await prisma.subscriptionPlan.update({
      where: { id: params.id },
      data
    })

    return NextResponse.json({
      message: 'Plano atualizado com sucesso',
      plan
    })

  } catch (error) {
    console.error('Erro ao atualizar plano:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }



    // Verificar se há subscrições ativas para este plano
    const activeSubscriptions = await prisma.subscription.count({
      where: {
        planId: params.id,
        status: 'ACTIVE'
      }
    })

    if (activeSubscriptions > 0) {
      return NextResponse.json(
        { message: 'Não é possível remover plano com subscrições ativas' },
        { status: 400 }
      )
    }

    await prisma.subscriptionPlan.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Plano removido com sucesso'
    })

  } catch (error) {
    console.error('Erro ao remover plano:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
