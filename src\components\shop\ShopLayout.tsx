import { ReactNode } from 'react'
import ShopHeader from './ShopHeader'
import ShopFooter from './ShopFooter'
import { ToastProvider } from '@/components/ui/Toast'

interface ShopLayoutProps {
  children: ReactNode
  shopName: string
  subdomain: string
  shopInfo?: {
    phone?: string
    email?: string
    address?: string
    city?: string
    postalCode?: string}
  cartItemsCount?: number
  logoUrl?: string}

export default function ShopLayout({
  children,
  shopName,
  subdomain,
  shopInfo,
  cartItemsCount = 0, logoUrl }: ShopLayoutProps) {
  return (
    <ToastProvider>
      <div className="min-h-screen flex flex-col">
        <ShopHeader
          shopName={shopName}
          subdomain={subdomain}
          cartItemsCount={cartItemsCount}
          logoUrl={logoUrl}
        />

        <main className="flex-1">
          {children}
        </main>

        <ShopFooter
          shopName={shopName}
          subdomain={subdomain}
          shopInfo={shopInfo}
        />
      </div>
    </ToastProvider>
  )
}
