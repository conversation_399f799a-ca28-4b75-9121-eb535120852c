import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'
import { parse } from 'csv-parse/sync'

interface ProductImportData {
  name: string
  description?: string
  price: number
  stock: number
  category?: string
  brand?: string
  model?: string
  condition?: string
  images?: string[]
  sku?: string
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const contentType = request.headers.get('content-type')
    
    if (contentType?.includes('multipart/form-data')) {
      // File upload (CSV/XLSX)
      const formData = await request.formData()
      const file = formData.get('file') as File
      const method = formData.get('method') as string
      const isPreview = formData.get('preview') === 'true'

      if (!file) {
        return NextResponse.json(
          { message: 'Nenhum arquivo enviado' },
          { status: 400 }
        )
      }

      const buffer = Buffer.from(await file.arrayBuffer())
      let products: ProductImportData[] = []

      try {
        if (method === 'csv') {
          const csvContent = buffer.toString('utf-8')
          const records = parse(csvContent, {
            columns: true,
            skip_empty_lines: true,
            delimiter: ','
          })
          products = parseProductData(records)
        } else if (method === 'xlsx') {
          const workbook = XLSX.read(buffer, { type: 'buffer' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const records = XLSX.utils.sheet_to_json(worksheet)
          products = parseProductData(records)
        }
      } catch (error) {
        return NextResponse.json(
          { 
            message: 'Erro ao processar arquivo',
            errors: [error instanceof Error ? error.message : 'Erro desconhecido']
          },
          { status: 400 }
        )
      }

      if (isPreview) {
        return NextResponse.json({
          success: true,
          preview: products.slice(0, 10), // Mostrar apenas os primeiros 10 para preview
          total: products.length
        })
      }

      // Importar produtos
      const result = await importProducts(session.user.id, products)
      return NextResponse.json(result)

    } else {
      // API integration (WooCommerce/Shopify)
      const body = await request.json()
      const { method, config } = body

      let products: ProductImportData[] = []

      if (method === 'woocommerce') {
        products = await importFromWooCommerce(config)
      } else if (method === 'shopify') {
        products = await importFromShopify(config)
      }

      const result = await importProducts(session.user.id, products)
      return NextResponse.json(result)
    }

  } catch (error) {
    console.error('Erro na importação:', error)
    return NextResponse.json(
      { 
        message: 'Erro interno do servidor',
        errors: [error instanceof Error ? error.message : 'Erro desconhecido']
      },
      { status: 500 }
    )
  }
}

function parseProductData(records: any[]): ProductImportData[] {
  return records.map((record, index) => {
    try {
      return {
        name: record.name || record.Nome || record.produto || '',
        description: record.description || record.Descrição || record.descricao || '',
        price: parseFloat(record.price || record.Preço || record.preco || '0'),
        stock: parseInt(record.stock || record.Stock || record.estoque || '0'),
        category: record.category || record.Categoria || record.categoria || '',
        brand: record.brand || record.Marca || record.marca || '',
        model: record.model || record.Modelo || record.modelo || '',
        condition: record.condition || record.Condição || record.condicao || 'NOVO',
        sku: record.sku || record.SKU || record.codigo || '',
        images: record.images ? record.images.split(',').map((url: string) => url.trim()) : []
      }
    } catch (error) {
      throw new Error(`Erro na linha ${index + 2}: ${error instanceof Error ? error.message : 'Dados inválidos'}`)
    }
  })
}

async function importFromWooCommerce(config: any): Promise<ProductImportData[]> {
  try {
    const { url, consumerKey, consumerSecret } = config
    
    if (!url || !consumerKey || !consumerSecret) {
      throw new Error('Configuração do WooCommerce incompleta')
    }

    const apiUrl = `${url}/wp-json/wc/v3/products`
    const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString('base64')

    const response = await fetch(`${apiUrl}?per_page=100`, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Erro na API do WooCommerce: ${response.statusText}`)
    }

    const products = await response.json()

    return products.map((product: any) => ({
      name: product.name,
      description: product.description || product.short_description || '',
      price: parseFloat(product.price || '0'),
      stock: product.stock_quantity || 0,
      category: product.categories?.[0]?.name || '',
      sku: product.sku || '',
      condition: 'NOVO',
      images: product.images?.map((img: any) => img.src) || []
    }))
  } catch (error) {
    throw new Error(`Erro ao importar do WooCommerce: ${error instanceof Error ? error.message : 'Erro desconhecido'}`)
  }
}

async function importFromShopify(config: any): Promise<ProductImportData[]> {
  try {
    const { shopUrl, accessToken } = config
    
    if (!shopUrl || !accessToken) {
      throw new Error('Configuração do Shopify incompleta')
    }

    const apiUrl = `https://${shopUrl}/admin/api/2023-10/products.json`

    const response = await fetch(`${apiUrl}?limit=250`, {
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Erro na API do Shopify: ${response.statusText}`)
    }

    const data = await response.json()
    const products = data.products || []

    return products.flatMap((product: any) => 
      product.variants?.map((variant: any) => ({
        name: `${product.title}${variant.title !== 'Default Title' ? ` - ${variant.title}` : ''}`,
        description: product.body_html || '',
        price: parseFloat(variant.price || '0'),
        stock: variant.inventory_quantity || 0,
        category: product.product_type || '',
        sku: variant.sku || '',
        condition: 'NOVO',
        images: product.images?.map((img: any) => img.src) || []
      })) || []
    )
  } catch (error) {
    throw new Error(`Erro ao importar do Shopify: ${error instanceof Error ? error.message : 'Erro desconhecido'}`)
  }
}

async function importProducts(sellerId: string, products: ProductImportData[]) {
  const errors: string[] = []
  let imported = 0

  for (const [index, productData] of products.entries()) {
    try {
      if (!productData.name || productData.price <= 0) {
        errors.push(`Linha ${index + 1}: Nome e preço são obrigatórios`)
        continue
      }

      // Verificar se já existe produto com mesmo nome
      const existingProduct = await prisma.marketplaceProduct.findFirst({
        where: {
          sellerId,
          name: productData.name
        }
      })

      if (existingProduct) {
        errors.push(`Linha ${index + 1}: Produto "${productData.name}" já existe`)
        continue
      }

      await prisma.marketplaceProduct.create({
        data: {
          sellerId,
          name: productData.name,
          description: productData.description || '',
          price: productData.price,
          stock: productData.stock,
          category: productData.category || 'Outros',
          brand: productData.brand || '',
          model: productData.model || '',
          condition: productData.condition as any || 'NOVO',
          sku: productData.sku || '',
          images: productData.images || [],
          isActive: true
        }
      })

      imported++
    } catch (error) {
      errors.push(`Linha ${index + 1}: ${error instanceof Error ? error.message : 'Erro ao criar produto'}`)
    }
  }

  return {
    success: imported > 0,
    message: imported > 0 
      ? `${imported} produtos importados com sucesso${errors.length > 0 ? ` (${errors.length} erros)` : ''}`
      : 'Nenhum produto foi importado',
    imported,
    errors
  }
}
