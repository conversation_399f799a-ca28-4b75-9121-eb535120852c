import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const model = await prisma.deviceModel.findUnique({
      where: { id: resolvedParams.id },
      include: {
        brand: true}
    })

    if (!model) {
      return NextResponse.json(
        { message: "Modelo não encontrado" },
        { status: 404 }
      )
    }

    return NextResponse.json(model)

  } catch (error) {
    console.error('Erro ao buscar modelo:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, brandId, category, releaseYear, image, specifications, isActive } = await request.json()

    if (!name || !brandId || !category) {
      return NextResponse.json(
        { message: "Nome, marca e categoria são obrigatórios" },
        { status: 400 }
      )
    }

    // Verificar se o modelo existe
    const existingModel = await prisma.deviceModel.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!existingModel) {
      return NextResponse.json(
        { message: "Modelo não encontrado" },
        { status: 404 }
      )
    }

    // Verificar se a marca existe
    const brand = await prisma.brand.findUnique({
      where: { id: brandId}
    })

    if (!brand) {
      return NextResponse.json(
        { message: "Marca não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se outro modelo já tem este nome para esta marca
    const duplicateModel = await prisma.deviceModel.findFirst({
      where: {
        name,
        brandId,
        id: { not: resolvedParams.id }
      }
    })

    if (duplicateModel) {
      return NextResponse.json(
        { message: "Já existe um modelo com este nome para esta marca" },
        { status: 400 }
      )
    }

    const model = await prisma.deviceModel.update({
      where: { id: resolvedParams.id },
      data: {
        name,
        brandId,
        category,
        releaseYear: releaseYear ? parseInt(releaseYear) : null,
        image: image || null,
        specifications: specifications || null,
        isActive: isActive !== undefined ? isActive : true
},
      include: {
        brand: true}
    })

    return NextResponse.json(model)

  } catch (error) {
    console.error("Erro ao atualizar modelo:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o modelo existe
    const model = await prisma.deviceModel.findUnique({
      where: { id: resolvedParams.id }
    })

    if (!model) {
      return NextResponse.json(
        { message: "Modelo não encontrado" },
        { status: 404 }
      )
    }

    await prisma.deviceModel.delete({
      where: { id: resolvedParams.id }
    })

    return NextResponse.json({ message: "Modelo eliminado com sucesso" })

  } catch (error) {
    console.error("Erro ao eliminar modelo:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
