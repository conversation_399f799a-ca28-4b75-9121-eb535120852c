import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    
    const product = await prisma.marketplaceProduct.findUnique({
      where: {
        id: resolvedParams.id,
        isActive: true
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        },
        deviceModel: {
          select: {
            name: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            createdAt: true,
            isVerified: true,
            profile: {
              select: {
                companyName: true
              }
            }
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    // Buscar rating do vendedor através das avaliações
    const sellerReviews = await prisma.review.findMany({
      where: {
        repairShopId: product.sellerId
      },
      select: {
        rating: true
      }
    })

    const sellerRating = sellerReviews.length > 0
      ? sellerReviews.reduce((sum, review) => sum + review.rating, 0) / sellerReviews.length
      : 4.2 // Rating padrão

    const sellerReviewCount = sellerReviews.length

    // Garantir que os preços sejam números e adicionar dados do vendedor
    const productWithNumbers = {
      ...product,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      seller: {
        ...product.seller,
        rating: sellerRating,
        reviewCount: sellerReviewCount
      }
    }

    return NextResponse.json(productWithNumbers)

  } catch (error) {
    console.error('Erro ao buscar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
