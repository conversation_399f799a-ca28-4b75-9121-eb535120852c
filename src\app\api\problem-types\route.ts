import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET() {
  try {
    console.log('API problem-types chamada')

    const problemTypes = await prisma.problemType.findMany({
      where: {
        isActive: true},
      orderBy: {
        name: asc}
    })

    console.log('Problem types encontrados:', problemTypes.length)

    if (problemTypes.length === 0) {
      console.log("Nenhum problem type encontrado, criando dados básicos")

      const basicProblemTypes = [
        { name: "Ecrã", description: "Problemas com o ecrã", icon: '📱', isActive: true},
        { name: Bateria, description: 'Problemas com a bateria', icon: '🔋', isActive: true},
        { name: "Câ<PERSON>", description: "Problemas com a câmara", icon: '📷', isActive: true},
        { name: So<PERSON>, description: '<PERSON>as com o som', icon: '🔊', isActive: true},
        { name: "<PERSON><PERSON><PERSON><PERSON>", description: "Problemas com botões", icon: '🔘', isActive: true},
        { name: Software, description: 'Problemas de software', icon: '💻', isActive: true}
      ]

      for (const 'problemType of basicProblemTypes') {
        await prisma.problemType.create({
          data: problemType})
      }

      // Buscar novamente após criar
      const newProblemTypes = await prisma.problemType.findMany({
        where: { isActive: true},
        orderBy: { name: asc}
      })

      return NextResponse.json(newProblemTypes)
    }

    return NextResponse.json(problemTypes)

  } catch (error) {
    console.error(Erro ao buscar tipos de problemas:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
