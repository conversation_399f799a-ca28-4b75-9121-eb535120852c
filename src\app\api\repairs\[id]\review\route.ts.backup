import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { rating, comment } = await request.json()

    console.log('Dados da review:', { rating, comment })

    if (!rating || rating < 1 || rating > 5) {
      return NextResponse.json(
        { message: 'Classificação inválida (1-5)' },
        { status: 400 }
      )
    }

    // Verificar se a reparação pertence ao cliente e está concluída
    const repair = await prisma.repair.findFirst({
      where: {
        id: resolvedParams.id,
        customerId: session.user.id,
        status: {
          in: ['COMPLETED', 'DELIVERED']
        }
      },
      include: {
        repairShop: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: 'Reparação não encontrada ou não elegível para avaliação' },
        { status: 404 }
      )
    }

    // Verificar se já existe uma avaliação
    const existingReview = await prisma.review.findFirst({
      where: {
        repairId: resolvedParams.id
      }
    })

    if (existingReview) {
      return NextResponse.json(
        { message: 'Esta reparação já foi avaliada' },
        { status: 400 }
      )
    }

    // Criar a avaliação
    const review = await prisma.review.create({
      data: {
        repairId: resolvedParams.id,
        customerId: session.user.id,
        rating,
        comment: comment?.trim() || null
      }
    })

    // Criar notificação para o lojista
    await prisma.notification.create({
      data: {
        userId: repair.repairShop.id,
        title: 'Nova Avaliação Recebida',
        message: `Recebeu uma avaliação de ${rating} estrelas para a reparação #${resolvedParams.id.slice(-8)}${comment ? ': "' + comment.substring(0, 50) + (comment.length > 50 ? '..."' : '"') : ''}`,
        type: 'REVIEW_RECEIVED',
        metadata: {
          reviewId: review.id,
          repairId: resolvedParams.id,
          rating,
          comment
        }
      }
    })

    return NextResponse.json({
      message: 'Avaliação enviada com sucesso',
      review: {
        id: review.id,
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt
      }
    })

  } catch (error) {
    console.error('Erro ao criar avaliação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const review = await prisma.review.findFirst({
      where: {
        repairId: resolvedParams.id
      },
      include: {
        customer: {
          select: {
            name: true
          }
        }
      }
    })

    if (!review) {
      return NextResponse.json(
        { message: 'Avaliação não encontrada' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      customerName: review.customer.name,
      createdAt: review.createdAt
    })

  } catch (error) {
    console.error('Erro ao buscar avaliação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
