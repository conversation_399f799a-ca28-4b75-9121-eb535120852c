import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: { subdomain: string} }
) {
  try {
    const subdomain = params.subdomain

    if (!subdomain) {
      return NextResponse.json(
        { message: "Subdomínio é obrigatório" },
        { status: 400 }
      )
    }

    // Buscar loja pelo subdomínio
    const shop = await prisma.user.findFirst({
      where: {
        role: REPAIR_SHOP,
        profile: {
          customSubdomain: subdomain}
      },
      include: {
        subscription: {
          include: {
            plan: {
              select: {
                miniStore: true}
            }
          }
        }
      }
    })

    if (!shop) {
      return NextResponse.json(
        { message: "Loja não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se a loja tem acesso à funcionalidade de mini-loja
    if (!shop.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: "Loja online não disponível para esta loja" },
        { status: 403 }
      )
    }

    // Buscar categorias dos produtos da loja
    const categories = await prisma.category.findMany({
      where: {
        marketplaceProducts: {
          some: {
            sellerId: shop.id,
            isActive: true}
        }
      },
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            marketplaceProducts: {
              where: {
                sellerId: shop.id,
                isActive: true}
            }
          }
        }
      },
      orderBy: {
        name: asc}
    })

    // Formatar dados para o frontend
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      productCount: category._count.marketplaceProducts
    }))

    return NextResponse.json({
      success: true,
      categories: formattedCategories})

  } catch (error) {
    console.error(Erro ao buscar categorias da loja:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
