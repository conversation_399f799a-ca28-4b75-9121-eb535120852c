#!/usr/bin/env node

/**
 * Script para restaurar arquivos corrompidos e aplicar tradução seletiva
 */

const fs = require('fs');
const path = require('path');

// Arquivos que podem estar corrompidos
const criticalFiles = [
  'src/components/chat/LeMarChat.tsx',
  'src/app/auth/signin/page.tsx',
  'src/app/auth/signup/page.tsx',
  'src/components/ModernFooter.tsx',
  'src/app/page.tsx'
];

function restoreFromBackup(filePath) {
  const backupPath = `${filePath}.backup`;
  
  if (fs.existsSync(backupPath)) {
    console.log(`🔄 Restaurando ${filePath} do backup...`);
    const backupContent = fs.readFileSync(backupPath, 'utf8');
    fs.writeFileSync(filePath, backupContent);
    return true;
  }
  
  return false;
}

function applySelectiveTranslation(filePath) {
  if (!fs.existsSync(filePath)) return;
  
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;
  
  // Adicionar import se necessário
  if (!content.includes("import AutoTranslate from '@/components/ui/AutoTranslate'")) {
    const lines = content.split('\n');
    let importIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes("import") && lines[i].includes("'@/")) {
        importIndex = i;
      }
    }
    
    if (importIndex !== -1) {
      lines.splice(importIndex + 1, 0, "import AutoTranslate from '@/components/ui/AutoTranslate'");
      content = lines.join('\n');
      changes++;
    }
  }
  
  // Textos específicos para traduzir (apenas em JSX, não em atributos)
  const textsToTranslate = [
    // Textos comuns de interface
    { pattern: />Entrar</g, replacement: '><AutoTranslate text="Entrar" /><' },
    { pattern: />Registar</g, replacement: '><AutoTranslate text="Registar" /><' },
    { pattern: />Sair</g, replacement: '><AutoTranslate text="Sair" /><' },
    { pattern: />Dashboard</g, replacement: '><AutoTranslate text="Dashboard" /><' },
    { pattern: />Perfil</g, replacement: '><AutoTranslate text="Perfil" /><' },
    { pattern: />Configurações</g, replacement: '><AutoTranslate text="Configurações" /><' },
    { pattern: />Produtos</g, replacement: '><AutoTranslate text="Produtos" /><' },
    { pattern: />Categorias</g, replacement: '><AutoTranslate text="Categorias" /><' },
    { pattern: />Marcas</g, replacement: '><AutoTranslate text="Marcas" /><' },
    { pattern: />Clientes</g, replacement: '><AutoTranslate text="Clientes" /><' },
    { pattern: />Reparações</g, replacement: '><AutoTranslate text="Reparações" /><' },
    { pattern: />Marketplace</g, replacement: '><AutoTranslate text="Marketplace" /><' },
    { pattern: />Central de Ajuda</g, replacement: '><AutoTranslate text="Central de Ajuda" /><' },
    { pattern: />Contactos</g, replacement: '><AutoTranslate text="Contactos" /><' },
    { pattern: />Sobre Nós</g, replacement: '><AutoTranslate text="Sobre Nós" /><' },
    
    // Botões e ações
    { pattern: />Guardar</g, replacement: '><AutoTranslate text="Guardar" /><' },
    { pattern: />Cancelar</g, replacement: '><AutoTranslate text="Cancelar" /><' },
    { pattern: />Editar</g, replacement: '><AutoTranslate text="Editar" /><' },
    { pattern: />Eliminar</g, replacement: '><AutoTranslate text="Eliminar" /><' },
    { pattern: />Adicionar</g, replacement: '><AutoTranslate text="Adicionar" /><' },
    { pattern: />Remover</g, replacement: '><AutoTranslate text="Remover" /><' },
    { pattern: />Pesquisar</g, replacement: '><AutoTranslate text="Pesquisar" /><' },
    { pattern: />Filtrar</g, replacement: '><AutoTranslate text="Filtrar" /><' },
    
    // Títulos e labels comuns
    { pattern: />Nome</g, replacement: '><AutoTranslate text="Nome" /><' },
    { pattern: />Email</g, replacement: '><AutoTranslate text="Email" /><' },
    { pattern: />Descrição</g, replacement: '><AutoTranslate text="Descrição" /><' },
    { pattern: />Preço</g, replacement: '><AutoTranslate text="Preço" /><' },
    { pattern: />Quantidade</g, replacement: '><AutoTranslate text="Quantidade" /><' },
    { pattern: />Estado</g, replacement: '><AutoTranslate text="Estado" /><' },
    { pattern: />Data</g, replacement: '><AutoTranslate text="Data" /><' },
    { pattern: />Ações</g, replacement: '><AutoTranslate text="Ações" /><' }
  ];
  
  textsToTranslate.forEach(({ pattern, replacement }) => {
    const originalContent = content;
    content = content.replace(pattern, replacement);
    if (content !== originalContent) {
      changes++;
    }
  });
  
  if (changes > 0) {
    console.log(`✅ ${filePath} - ${changes} traduções seletivas aplicadas`);
    fs.writeFileSync(filePath, content);
  }
}

function checkSyntaxErrors(filePath) {
  if (!fs.existsSync(filePath)) return [];
  
  const content = fs.readFileSync(filePath, 'utf8');
  const errors = [];
  
  // Verificar problemas comuns
  if (content.includes('<AutoTranslate text=<AutoTranslate')) {
    errors.push('AutoTranslate aninhado detectado');
  }
  
  if (content.includes('=<AutoTranslate text="') && !content.includes('placeholder=') && !content.includes('title=')) {
    errors.push('AutoTranslate em atributo HTML detectado');
  }
  
  if (content.includes('url(<AutoTranslate')) {
    errors.push('AutoTranslate em URL detectado');
  }
  
  // Verificar parênteses e chaves balanceadas
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  if (openBraces !== closeBraces) {
    errors.push(`Chaves desbalanceadas: ${openBraces} abertas, ${closeBraces} fechadas`);
  }
  
  return errors;
}

// Função principal
function main() {
  console.log('🔧 Restaurando arquivos corrompidos e aplicando tradução seletiva...\n');
  
  criticalFiles.forEach(filePath => {
    console.log(`\n📁 Processando: ${filePath}`);
    
    // Verificar erros de sintaxe
    const errors = checkSyntaxErrors(filePath);
    if (errors.length > 0) {
      console.log(`❌ Erros detectados: ${errors.join(', ')}`);
      
      // Tentar restaurar do backup
      if (restoreFromBackup(filePath)) {
        console.log(`✅ Arquivo restaurado do backup`);
      }
    }
    
    // Aplicar tradução seletiva
    applySelectiveTranslation(filePath);
  });
  
  console.log('\n✅ Processo finalizado!');
  console.log('📋 Próximos passos:');
  console.log('1. Verificar se o servidor compila sem erros');
  console.log('2. Testar a tradução em diferentes páginas');
  console.log('3. Ajustar manualmente casos específicos se necessário');
}

main();
