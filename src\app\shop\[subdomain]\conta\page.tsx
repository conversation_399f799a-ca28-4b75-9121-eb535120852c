'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import ShopLayout from '@/components/shop/ShopLayout'
import { User, Package, ShoppingBag, Settings, LogOut, Eye, Calendar, MapPin, Phone, Mail } from 'lucide-react'

interface UserData {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string}

interface Order {
  id: string
  date: string
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  total: number
  items: {
    id: string
    name: string
    quantity: number
    price: number}[]
}

interface Repair {
  id: string
  repairNumber: string
  createdAt: string
  deviceBrand: string
  deviceModel: string
  problemDescription: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  estimatedPrice?: number
  notes?: string}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  address?: string
  city?: string
  postalCode?: string}

export default function ContaPage() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<UserData | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [orders, setOrders] = useState<Order[]>([])
  const [repairs, setRepairs] = useState<Repair[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const subdomain = params.subdomain as string

  // Mock shop data
  const shop: ShopData = {
    id: 1,
    name: 'iTech Mobile',
    companyName: 'iTech Mobile',
    phone: '+*********** 789',
    address: 'Rua da Tecnologia, 123',
    city: Lisboa,
    postalCode: '1000-001'
  
}

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem(user)
    if (!userData) {
      router.push(`/shop/${subdomain
}/login`)
      return
    }

    setUser(JSON.parse(userData))
    fetchUserData()
  }, [subdomain, router])

  const fetchUserData = async () => {
    try {
      const userData = localStorage.getItem('user')
      if (!userData) return

      const user = JSON.parse(userData)

      // Fetch user orders
      const ordersResponse = await fetch(`/api/shop/${subdomain}/orders?customerEmail=${user.email}`)
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setOrders(ordersData.orders || [])
      }

      // Fetch user repairs
      const repairsResponse = await fetch(`/api/shop/${subdomain}/repairs?customerEmail=${user.email}`)
      if (repairsResponse.ok) {
        const repairsData = await repairsResponse.json()
        setRepairs(repairsData.repairs || [])
      } else {
        // Fallback to mock data if API fails
        setRepairs([
          {
            id: 1,
            date: '2024-01-10',
            device: 'iPhone 13 Pro',
            problem: 'Ecrã partido',
            status: completed,
            price: 180.00
          
},
          {
            id: '2',
            date: '2024-01-22',
            device: 'Samsung Galaxy S23',
            problem: 'Bateria não carrega',
            status: in_progress}
        ])
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user')
    router.push(`/shop/${subdomain}`)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: currency,
      currency: EUR}).format(price)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'processing': case 'in_progress': return 'bg-blue-100 text-blue-800'
      case 'shipped': return 'bg-purple-100 text-purple-800'
      case 'delivered': case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Pendente'
      case 'processing': return 'A processar'
      case 'in_progress': return 'Em progresso'
      case 'shipped': return 'Enviado'
      case 'delivered': return 'Entregue'
      case 'completed': return 'Concluído'
      case 'cancelled': return 'Cancelado'
      default: 'return status'}
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!user) {
    'return null'}

  return (
    <ShopLayout 
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <div className="min-h-screen bg-gray-50">
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link href={`/shop/${subdomain}`} className="text-gray-500 hover:text-gray-700">Início</Link>
                </li>
                <li>
                  <span className="text-gray-400">/</span>
                </li>
                <li>
                  <span className="text-gray-900 font-medium">Minha Conta</span>
                </li>
              </ol>
            </nav>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-gray-900 to-black rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{user.name}</h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left ${
                      activeTab === 'overview' 
                        ? 'bg-gray-100 text-gray-900' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <User className="w-5 h-5" />
                    <span>Visão Geral</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('orders')}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left ${
                      activeTab === 'orders' 
                        ? 'bg-gray-100 text-gray-900' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <ShoppingBag className="w-5 h-5" />
                    <span>Encomendas</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('repairs')}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left ${
                      activeTab === 'repairs' 
                        ? 'bg-gray-100 text-gray-900' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <Package className="w-5 h-5" />
                    <span>Reparações</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('settings')}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left ${
                      activeTab === 'settings' 
                        ? 'bg-gray-100 text-gray-900' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <Settings className="w-5 h-5" />
                    <span>Definições</span>
                  </button>
                  
                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left text-red-600 hover:bg-red-50"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>Sair</span>
                  </button>
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Bem-vindo, {user.name}!</h2>
                    <p className="text-gray-600 mb-6">Aqui pode gerir as suas encomendas, reparações e dados pessoais.</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <ShoppingBag className="w-8 h-8 text-gray-600" />
                          <div>
                            <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
                            <p className="text-sm text-gray-600">Encomendas</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <Package className="w-8 h-8 text-gray-600" />
                          <div>
                            <p className="text-2xl font-bold text-gray-900">{repairs.length}</p>
                            <p className="text-sm text-gray-600">Reparações</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <Calendar className="w-8 h-8 text-gray-600" />
                          <div>
                            <p className="text-2xl font-bold text-gray-900">
                              {new Date().toLocaleDateString('pt-PT')}
                            </p>
                            <p className="text-sm text-gray-600">Hoje</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Atividade Recente</h3>
                    <div className="space-y-4">
                      {orders.slice(0, 2).map((order) => (
                        <div key={order.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                          <div className="flex items-center space-x-3">
                            <ShoppingBag className="w-5 h-5 text-gray-400" />
                            <div>
                              <p className="font-medium text-gray-900">Encomenda #{order.id}</p>
                              <p className="text-sm text-gray-500">{new Date(order.date).toLocaleDateString('pt-PT')}</p>
                            </div>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                            {getStatusText(order.status)}
                          </span>
                        </div>
                      ))}
                      
                      {repairs.slice(0, 2).map((repair) => (
                        <div key={repair.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                          <div className="flex items-center space-x-3">
                            <Package className="w-5 h-5 text-gray-400" />
                            <div>
                              <p className="font-medium text-gray-900">{repair.device}</p>
                              <p className="text-sm text-gray-500">{repair.problem}</p>
                            </div>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(repair.status)}`}>
                            {getStatusText(repair.status)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'orders' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Minhas Encomendas</h2>
                  
                  {orders.length === 0 ? (
                    <div className="text-center py-8">
                      <ShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma encomenda</h3>
                      <p className="text-gray-500 mb-6">Ainda não fez nenhuma encomenda.</p>
                      <Link
                        href={`/shop/${subdomain}/produtos`}
                        className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
                      >Ver Produtos</Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {orders.map((order) => (
                        <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium text-gray-900">Encomenda #{order.id}</h3>
                              <p className="text-sm text-gray-500">
                                {new Date(order.date).toLocaleDateString('pt-PT')}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                                {getStatusText(order.status)}
                              </span>
                              <p className="text-lg font-semibold text-gray-900 mt-1">
                                {formatPrice(order.total)}
                              </p>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            {order.items.map((item) => (
                              <div key={item.id} className="flex justify-between text-sm">
                                <span className="text-gray-600">
                                  {item.quantity}x {item.name}
                                </span>
                                <span className="text-gray-900">{formatPrice(item.price)}</span>
                              </div>
                            ))}
                          </div>
                          
                          <div className="mt-4 flex justify-end">
                            <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                              <Eye className="w-4 h-4" />
                              <span>Ver Detalhes</span>
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'repairs' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Minhas Reparações</h2>
                    <Link
                      href={`/shop/${subdomain}/nova-reparacao`}
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
                    >Nova Reparação</Link>
                  </div>
                  
                  {repairs.length === 0 ? (
                    <div className="text-center py-8">
                      <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma reparação</h3>
                      <p className="text-gray-500 mb-6">Ainda não solicitou nenhuma reparação.</p>
                      <Link
                        href={`/shop/${subdomain}/nova-reparacao`}
                        className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
                      >Solicitar Reparação</Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {repairs.map((repair) => (
                        <div key={repair.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium text-gray-900">
                                {repair.deviceBrand} {repair.deviceModel}
                              </h3>
                              <p className="text-sm text-gray-500">{repair.problemDescription}</p>
                              <p className="text-sm text-gray-500">
                                {repair.repairNumber} • {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(repair.status)}`}>
                                {getStatusText(repair.status)}
                              </span>
                              {repair.estimatedPrice && (
                                <p className="text-lg font-semibold text-gray-900 mt-1">
                                  {formatPrice(repair.estimatedPrice)}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          <div className="mt-4 flex justify-end">
                            <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                              <Eye className="w-4 h-4" />
                              <span>Ver Detalhes</span>
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'settings' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Definições da Conta</h2>
                  
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Nome completo</label>
                        <input
                          type="text"
                          defaultValue={user.name}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input
                          type="email"
                          defaultValue={user.email}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Telefone</label>
                        <input
                          type="tel"
                          defaultValue={user.phone}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Código Postal</label>
                        <input
                          type="text"
                          defaultValue={user.postalCode}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Morada</label>
                        <input
                          type="text"
                          defaultValue={user.address}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Cidade</label>
                        <input
                          type="text"
                          defaultValue={user.city}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button
                        type="submit"
                        className="px-6 py-2 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
                      >Guardar Alterações</button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ShopLayout>
  )
}
