import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar estatísticas de instalações por app
    const installStats = await prisma.$queryRaw`
      SELECT
        "appId",
        COUNT(*) as total_installs,
        COUNT(CASE WHEN "isActive" = true THEN 1 END) as active_installs,
        0 as trial_installs,
        COUNT(CASE WHEN "isActive" = true THEN 1 END) as paid_installs
      FROM installed_apps
      GROUP BY "appId"
    ` as any[]

    // Converter para objeto
    const stats: Record<string, any> = {}
    installStats.forEach((stat: any) => {
      stats[stat.appId] = {
        totalInstalls: Number(stat.total_installs || 0),
        activeInstalls: Number(stat.active_installs || 0),
        trialInstalls: Number(stat.trial_installs || 0),
        paidInstalls: Number(stat.paid_installs || 0)
      }
    })

    return NextResponse.json({
      stats
    })

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
