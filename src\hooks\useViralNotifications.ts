'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'

interface ViralNotification {
  id: string
  type: 'badge_earned' | 'referral_completed' | 'milestone_reached' | 'high_rating'
  title: string
  message: string
  points?: number
  isRead: boolean
  createdAt: string}

export function useViralNotifications() {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<ViralNotification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    if (session?.user?.id) {
      fetchNotifications()
      // Poll for new notifications every 30 seconds
      const interval = setInterval(fetchNotifications, 30000)
      return () => clearInterval(interval)
    }
  }, [session?.user?.id])

  const fetchNotifications = async () => {
    try {
      const response = await fetch(/api/viral/notifications)
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.notifications || [])
        setUnreadCount(data.unreadCount || 0)
      
}
    } catch (error) {
      console.error('Erro ao buscar notificações virais:', 'error')
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/viral/notifications/${notificationId}/read`, {
        method: POST})
      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, isRead: true}
              : 'notif')
        )
        setUnreadCount(prev => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', 'error')
    }
  }

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/viral/notifications/read-all', {
        method: POST})
      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, isRead: true}))
        )
        setUnreadCount(0)
      }
    } catch (error) {
      console.error('Erro ao marcar todas as notificações como lidas:', 'error')
    }
  }

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refresh: fetchNotifications}
}
