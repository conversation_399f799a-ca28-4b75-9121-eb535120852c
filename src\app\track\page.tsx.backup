'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Search, Package, Clock, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react'

interface RepairData {
  id: string
  trackingCode: string
  status: string
  customerName: string
  deviceBrand: string
  deviceModel: string
  problemType: string
  description: string
  estimatedPrice?: number
  finalPrice?: number
  estimatedCompletionDate?: string
  createdAt: string
  updatedAt: string
  repairShop: {
    name: string
    email: string
    phone?: string
  }
  notes?: string
}

const statusConfig = {
  PENDING: { label: 'Pendente', color: 'yellow', icon: Clock },
  IN_PROGRESS: { label: 'Em Progresso', color: 'blue', icon: Package },
  WAITING_PARTS: { label: 'Aguardando <PERSON>', color: 'orange', icon: AlertCircle },
  COMPLETED: { label: 'Con<PERSON><PERSON>ída', color: 'green', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'green', icon: CheckCircle },
  CANCELLED: { label: 'Cancelada', color: 'red', icon: AlertCircle }
}

export default function TrackPage() {
  const [trackingCode, setTrackingCode] = useState('')
  const [repair, setRepair] = useState<RepairData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!trackingCode.trim()) {
      setError('Por favor, insira um código de rastreamento')
      return
    }

    setIsLoading(true)
    setError('')
    setRepair(null)

    try {
      const response = await fetch(`/api/track/${trackingCode}`)
      
      if (response.ok) {
        const data = await response.json()
        setRepair(data.repair)
      } else if (response.status === 404) {
        setError('Código de rastreamento não encontrado')
      } else {
        setError('Erro ao buscar reparação')
      }
    } catch (error) {
      console.error('Erro ao buscar reparação:', error)
      setError('Erro ao buscar reparação')
    } finally {
      setIsLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: 'EUR'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusConfig = (status: string) => {
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Rastreamento de Reparações</span>
            </div>
            <Link
              href="/"
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              Voltar ao Início
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Rastrear Reparação
          </h1>
          <p className="text-lg text-gray-600">
            Insira o código de rastreamento para acompanhar o status da sua reparação
          </p>
        </div>

        {/* Search Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          <form onSubmit={handleSearch} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Código de Rastreamento
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={trackingCode}
                  onChange={(e) => setTrackingCode(e.target.value.toUpperCase())}
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  placeholder="Ex: REP123456"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-8 py-3 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Search className="w-5 h-5 mr-2" />
                      Buscar
                    </>
                  )}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                  <span className="text-red-700">{error}</span>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Repair Details */}
        {repair && (
          <div className="space-y-6">
            {/* Status Card */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Status da Reparação</h2>
                <span className="text-sm text-gray-500">
                  Atualizado em {formatDate(repair.updatedAt)}
                </span>
              </div>

              <div className="flex items-center">
                {(() => {
                  const statusInfo = getStatusConfig(repair.status)
                  const StatusIcon = statusInfo.icon
                  return (
                    <>
                      <div className={`p-3 rounded-full ${
                        statusInfo.color === 'green' ? 'bg-green-100' :
                        statusInfo.color === 'blue' ? 'bg-blue-100' :
                        statusInfo.color === 'yellow' ? 'bg-yellow-100' :
                        statusInfo.color === 'orange' ? 'bg-orange-100' :
                        'bg-red-100'
                      }`}>
                        <StatusIcon className={`w-6 h-6 ${
                          statusInfo.color === 'green' ? 'text-green-600' :
                          statusInfo.color === 'blue' ? 'text-blue-600' :
                          statusInfo.color === 'yellow' ? 'text-yellow-600' :
                          statusInfo.color === 'orange' ? 'text-orange-600' :
                          'text-red-600'
                        }`} />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">
                          {statusInfo.label}
                        </h3>
                        <p className="text-gray-600">
                          {repair.status === 'COMPLETED' && 'Sua reparação está pronta para retirada'}
                          {repair.status === 'IN_PROGRESS' && 'Estamos trabalhando na sua reparação'}
                          {repair.status === 'PENDING' && 'Sua reparação foi recebida e está na fila'}
                          {repair.status === 'WAITING_PARTS' && 'Aguardando chegada de peças'}
                          {repair.status === 'DELIVERED' && 'Reparação entregue com sucesso'}
                          {repair.status === 'CANCELLED' && 'Reparação foi cancelada'}
                        </p>
                      </div>
                    </>
                  )
                })()}
              </div>
            </div>

            {/* Repair Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Device Info */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações do Dispositivo</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Dispositivo:</span>
                    <p className="text-gray-900">{repair.deviceBrand} {repair.deviceModel}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Problema:</span>
                    <p className="text-gray-900">{repair.problemType}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Descrição:</span>
                    <p className="text-gray-900">{repair.description}</p>
                  </div>
                </div>
              </div>

              {/* Repair Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes da Reparação</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Código:</span>
                    <p className="text-gray-900 font-mono">{repair.trackingCode}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Data de Entrada:</span>
                    <p className="text-gray-900">{formatDate(repair.createdAt)}</p>
                  </div>
                  {repair.estimatedCompletionDate && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Previsão de Conclusão:</span>
                      <p className="text-gray-900">{formatDate(repair.estimatedCompletionDate)}</p>
                    </div>
                  )}
                  {repair.estimatedPrice && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Orçamento:</span>
                      <p className="text-gray-900">{formatPrice(repair.estimatedPrice)}</p>
                    </div>
                  )}
                  {repair.finalPrice && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Preço Final:</span>
                      <p className="text-gray-900 font-semibold">{formatPrice(repair.finalPrice)}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Shop Info */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações da Loja</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-500">Nome:</span>
                  <p className="text-gray-900">{repair.repairShop.name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Email:</span>
                  <p className="text-gray-900">{repair.repairShop.email}</p>
                </div>
                {repair.repairShop.phone && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Telefone:</span>
                    <p className="text-gray-900">{repair.repairShop.phone}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {repair.notes && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Observações</h3>
                <div className="text-gray-700 whitespace-pre-line">
                  {repair.notes}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Help Section */}
        <div className="mt-12 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Precisa de Ajuda?
          </h3>
          <p className="text-gray-600 mb-6">
            Se tiver dúvidas sobre sua reparação, entre em contacto diretamente com a loja
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar ao Início
          </Link>
        </div>
      </div>
    </div>
  )
}
