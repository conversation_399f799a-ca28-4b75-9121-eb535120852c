'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Search, Filter, Star, MapPin, Clock, ArrowRight, Wrench, Smartphone, Laptop, Tablet } from 'lucide-react'
import MainHeader from '@/components/MainHeader'

interface RepairShop {
  id: string
  name: string
  businessName: string
  rating: number
  reviewCount: number
  distance: number
  specialties: string[]
  isAvailable: boolean
  responseTime: string
  basePrice: number
  address: string
  profile?: {
    companyName?: string
    description?: string
    address?: string
  }
}

export default function MarketplaceLojasPage() {
  const { data: session } = useSession()
  const [repairShops, setRepairShops] = useState<RepairShop[]>([])
  const [filteredShops, setFilteredShops] = useState<RepairShop[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isLoading, setIsLoading] = useState(true)

  const categories = [
    { id: 'all', name: 'Todos', icon: Wrench },
    { id: 'smartphone', name: 'Smartphones', icon: Smartphone },
    { id: 'laptop', name: 'Laptops', icon: Laptop },
    { id: 'tablet', name: 'Tablets', icon: Tablet },
  ]

  useEffect(() => {
    fetchRepairShops()
  }, [])

  useEffect(() => {
    filterShops()
  }, [searchTerm, selectedCategory, repairShops])

  const fetchRepairShops = async () => {
    try {
      const response = await fetch('/api/repair-shops')
      if (response.ok) {
        const data = await response.json()
        setRepairShops(data.shops || [])
      }
    } catch (error) {
      console.error('Erro ao buscar lojas:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterShops = () => {
    let filtered = repairShops

    if (searchTerm) {
      filtered = filtered.filter(shop =>
        shop.businessName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.specialties?.some(specialty => 
          specialty.toLowerCase().includes(searchTerm.toLowerCase())
        )
      )
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(shop =>
        shop.specialties?.some(specialty =>
          specialty.toLowerCase().includes(selectedCategory)
        )
      )
    }

    setFilteredShops(filtered)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-gray-900 to-black text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Lojas Parceiras
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Encontre os melhores técnicos especializados perto de si
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Procurar por loja, especialidade ou localização..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-300"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categorias</h3>
              <div className="space-y-2">
                {categories.map((category) => {
                  const Icon = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-4 h-4 mr-3" />
                      {category.name}
                    </button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {filteredShops.length} lojas encontradas
              </h2>
              <div className="flex items-center space-x-4">
                <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                  <option>Ordenar por relevância</option>
                  <option>Melhor avaliação</option>
                  <option>Mais próximo</option>
                  <option>Menor preço</option>
                </select>
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 mt-4">A carregar lojas...</p>
              </div>
            )}

            {/* Repair Shops Grid */}
            {!isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredShops.map((shop) => (
                  <div key={shop.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-all hover:border-blue-300">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Wrench className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium text-gray-700">
                          {(shop.rating || 4.8).toFixed(1)}
                        </span>
                        <span className="text-xs text-gray-500 ml-1">
                          ({shop.reviewCount || 0})
                        </span>
                      </div>
                    </div>
                    
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {shop.profile?.companyName || shop.businessName || shop.name}
                    </h3>
                    
                    <div className="flex items-center text-gray-500 mb-3">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span className="text-sm">{shop.profile?.address || shop.address || 'Lisboa'}</span>
                    </div>
                    
                    {shop.profile?.description && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {shop.profile.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>{shop.responseTime || '2h'}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">desde</div>
                        <div className="text-lg font-bold text-green-600">€{shop.basePrice || 25}</div>
                      </div>
                    </div>
                    
                    {shop.specialties && shop.specialties.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {shop.specialties.slice(0, 2).map((specialty, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full"
                          >
                            {specialty}
                          </span>
                        ))}
                        {shop.specialties.length > 2 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            +{shop.specialties.length - 2}
                          </span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className={`px-2 py-1 text-xs rounded-full ${
                        shop.isAvailable 
                          ? 'bg-green-50 text-green-700' 
                          : 'bg-red-50 text-red-700'
                      }`}>
                        {shop.isAvailable ? 'Disponível' : 'Ocupado'}
                      </div>
                      
                      <Link
                        href={`/loja/${shop.id}`}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center"
                      >
                        Ver Loja
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && filteredShops.length === 0 && (
              <div className="text-center py-12">
                <Wrench className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Nenhuma loja encontrada
                </h3>
                <p className="text-gray-600 mb-6">
                  Tente ajustar os filtros ou termo de pesquisa
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                  }}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Limpar Filtros
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
