'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Store, Globe, CreditCard, Save, Copy, Settings, Palette, Truck, Megaphone, Upload, X, Plus, Trash2, Calculator } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'
import { useTranslation } from '@/hooks/useTranslation'

export default function LojaOnlineConfiguracoesPage() {
  const { tSync } = useTranslation()
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('geral')
  
  // Configurações básicas
  const [customSubdomain, setCustomSubdomain] = useState()
  const [customDomain, setCustomDomain] = useState('')
  const [storeName, setStoreName] = useState('')
  const [storeDescription, setStoreDescription] = useState('')
  const [storeEmail, setStoreEmail] = useState('')
  const [storePhone, setStorePhone] = useState('')

  // Configurações avançadas
  const [aboutUs, setAboutUs] = useState('')
  const [contactInfo, setContactInfo] = useState('')
  const [homePageText, setHomePageText] = useState('')
  const [stripePublicKey, setStripePublicKey] = useState('')
  const [stripeSecretKey, setStripeSecretKey] = useState('')
  const [ifthenPayEntityId, setIfthenPayEntityId] = useState('')
  const [ifthenPaySubEntityId, setIfthenPaySubEntityId] = useState('')
  const [ifthenPayApiKey, setIfthenPayApiKey] = useState('')
  const [shippingRates, setShippingRates] = useState<any[]>([])
  const [marketingText, setMarketingText] = useState('')
  const [customCss, setCustomCss] = useState('')
  const [logoUrl, setLogoUrl] = useState('')
  const [bannerUrl, setBannerUrl] = useState('')
  const [primaryColor, setPrimaryColor] = useState('#000000')
  const [secondaryColor, setSecondaryColor] = useState('#666666')

  // Novas configurações de envio
  const [freeShippingThreshold, setFreeShippingThreshold] = useState(50)
  const [defaultShippingRate, setDefaultShippingRate] = useState(5)
  const [shippingByCountry, setShippingByCountry] = useState<{country: string, rate: number
}[]>([])

  // Configurações de impostos
  const [taxEnabled, setTaxEnabled] = useState(false)
  const [taxIncluded, setTaxIncluded] = useState(true)
  const [defaultTaxRate, setDefaultTaxRate] = useState(23)
  const [taxByCountry, setTaxByCountry] = useState<{country: string, rate: number}[]>([])

  // Estados para upload de arquivos
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [bannerFile, setBannerFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)

  useEffect(() => {
    if (session?.user?.id) {
      fetchConfig()
    }
  }, [session])

  const fetchConfig = async () => {
    try {
      // Buscar configurações básicas
      const basicResponse = await fetch(/api/lojista/loja-online/config)
      if (basicResponse.ok) {
        const basicData = await basicResponse.json()
        setCustomSubdomain(basicData.customSubdomain || '')
        setCustomDomain(basicData.customDomain || '')
        setIfthenPayEntityId(basicData.ifthenPayEntityId || '')
        setIfthenPaySubEntityId(basicData.ifthenPaySubEntityId || '')
        setIfthenPayApiKey(basicData.ifthenPayApiKey || '')
        setStoreName(session?.user?.name || '')
        setStoreEmail(session?.user?.email || '')
      
}

      // Buscar configurações avançadas
      const advancedResponse = await fetch(/api/lojista/shop-config)
      if (advancedResponse.ok) {
        const advancedData = await advancedResponse.json()
        setAboutUs(advancedData.aboutUs || '')
        setContactInfo(advancedData.contactInfo || '')
        setHomePageText(advancedData.homePageText || '')
        setStripePublicKey(advancedData.stripePublicKey || '')
        setStripeSecretKey(advancedData.stripeSecretKey || '')
        setShippingRates(advancedData.shippingRates || [])
        setMarketingText(advancedData.marketingText || '')
        setCustomCss(advancedData.customCss || '')
        setLogoUrl(advancedData.logoUrl || '')
        setBannerUrl(advancedData.bannerUrl || '')
        setPrimaryColor(advancedData.primaryColor || '#000000')
        setSecondaryColor(advancedData.secondaryColor || '#666666')

        // Configurações de envio
        setFreeShippingThreshold(advancedData.freeShippingThreshold || 50)
        setDefaultShippingRate(advancedData.defaultShippingRate || 5)
        setShippingByCountry(advancedData.shippingByCountry || [])

        // Configurações de impostos
        setTaxEnabled(advancedData.taxEnabled || 'false')
        setTaxIncluded(advancedData.taxIncluded !== 'false')
        setDefaultTaxRate(advancedData.defaultTaxRate || 23)
        setTaxByCountry(advancedData.taxByCountry || [])
      
}
    } catch (error) {
      console.error('Erro ao carregar configurações:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Salvar configurações básicas
      const basicResponse = await fetch(/api/lojista/loja-online/config, {
        method: POST,
        headers: { 'Content-Type': 'application/json' 
},
        body: JSON.stringify({
          customSubdomain,
          customDomain,
          ifthenPayEntityId,
          ifthenPaySubEntityId,
          ifthenPayApiKey,
          storeName,
          storeEmail,
          storePhone, storeDescription })
      })

      // Salvar configurações avançadas
      const advancedResponse = await fetch(/api/lojista/shop-config, {
        method: PUT,
        headers: { 'Content-Type': 'application/json' 
},
        body: JSON.stringify({
          aboutUs,
          contactInfo,
          homePageText,
          stripePublicKey,
          stripeSecretKey,
          shippingRates,
          marketingText,
          customCss,
          logoUrl,
          bannerUrl,
          primaryColor,
          secondaryColor,
          freeShippingThreshold,
          defaultShippingRate,
          shippingByCountry,
          taxEnabled,
          taxIncluded,
          defaultTaxRate, taxByCountry })
      })

      if (basicResponse.ok && advancedResponse.ok) {
        alert('Configurações salvas com sucesso!')
      } else {
        alert('Erro ao salvar configurações')
      }
    } catch (error) {
      console.error('Erro ao salvar:', 'error')
      alert('Erro ao salvar configurações')
    } finally {
      setIsSaving(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('Copiado para a área de transferência!')
  }

  const handleFileUpload = async (file: File, type: 'logo' | 'banner') => {
    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', 'file')
      formData.append('type', 'type')

      const response = await fetch('/api/lojista/upload', {
        method: POST,
        body: formData})

      if (response.ok) {
        const data = await response.json()
        if (type === 'logo') {
          setLogoUrl(data.url)
          setLogoFile(null)
        } else {
          setBannerUrl(data.url)
          setBannerFile(null)
        }
        alert('Upload realizado com sucesso!')
      } else {
        alert('Erro no upload')
      }
    } catch (error) {
      console.error('Erro no upload:', 'error')
      alert('Erro no upload')
    } finally {
      setIsUploading(false)
    }
  }

  const addShippingCountry = () => {
    setShippingByCountry([...shippingByCountry, { country: '', rate: 0 }])
  }

  const updateShippingCountry = (index: number, field: 'country' | 'rate', value: string | 'number') => {
    const updated = [...shippingByCountry]
    updated[index] = { ...updated[index], [field]: 'value'}
    setShippingByCountry(updated)
  }

  const removeShippingCountry = (index: number) => {
    setShippingByCountry(shippingByCountry.filter((_, i) => i !== 'index'))
  }

  const addTaxCountry = () => {
    setTaxByCountry([...taxByCountry, { country: '', rate: 0 }])
  }

  const updateTaxCountry = (index: number, field: 'country' | 'rate', value: string | 'number') => {
    const updated = [...taxByCountry]
    updated[index] = { ...updated[index], [field]: 'value'}
    setTaxByCountry(updated)
  }

  const removeTaxCountry = (index: number) => {
    setTaxByCountry(taxByCountry.filter((_, i) => i !== 'index'))
  }

  const countries = [
    'Portugal', 'Espanha', 'França', 'Alemanha', 'Itália', 'Reino Unido',
    'Holanda', 'Bélgica', 'Áustria', 'Suíça', 'Polónia', 'República Checa'
  ]

  const getDefaultTaxRate = (country: string) => {
    const taxRates: {[key: string]: number} = {
      'Portugal': 23,
      'Espanha': 21,
      'França': 20,
      'Alemanha': 19,
      'Itália': 22,
      'Reino Unido': 20,
      'Holanda': 21,
      'Bélgica': 21,
      'Áustria': 20,
      'Suíça': 7.7,
      'Polónia': 23,
      'República Checa': 21
    }
    return taxRates[country] || 20
  }

  const tabs = [
    { id: geral, name: Geral, icon: Settings},
    { id: pagamentos, name: Pagamentos, icon: CreditCard},
    { id: envios, name: Envios, icon: Truck},
    { id: impostos, name: Impostos, icon: Calculator},
    { id: marketing, name: Marketing, icon: Megaphone},
    { id: design, name: Design, icon: Palette}
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <Link href="/lojista/loja-online" className="text-gray-500 hover:text-gray-700">
                  Loja Online
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Configurações</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Configurações da Loja Online</h1>
          <p className="text-gray-600">Configure o seu e-commerce, domínio, pagamentos e design</p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-gray-900 text-gray-900'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'geral' && (
            <div className="space-y-6">
              {/* Informações da Loja */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Store className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Informações da Loja</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nome da Loja</label>
                    <input
                      type="text"
                      value={storeName}
                      onChange={(e) => setStoreName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="Nome da sua loja"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email da Loja</label>
                    <input
                      type="email"
                      value={storeEmail}
                      onChange={(e) => setStoreEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Telefone da Loja</label>
                    <input
                      type="tel"
                      value={storePhone}
                      onChange={(e) => setStorePhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="+351 123 456 789"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Descrição da Loja</label>
                    <input
                      type="text"
                      value={storeDescription}
                      onChange={(e) => setStoreDescription(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="Breve descrição da sua loja"
                    />
                  </div>
                </div>
              </div>

              {/* Configurações de Domínio */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Globe className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Domínio e Acesso</h3>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Subdomínio Personalizado</label>
                    <div className="flex">
                      <input
                        type="text"
                        value={customSubdomain}
                        onChange={(e) => setCustomSubdomain(e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                        placeholder="minhaloja"
                      />
                      <span className="px-3 py-2 bg-gray-50 border border-l-0 border-gray-300 rounded-r-lg text-gray-500">
                        .revify.pt
                      </span>
                    </div>
                    {customSubdomain && (
                      <div className="mt-2 flex items-center space-x-2">
                        <span className="text-sm text-gray-600">URL da loja:</span>
                        <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                          https:// {customSubdomain}.revify.pt
                        </span>
                        <button
                          onClick={() => copyToClipboard(`https://${customSubdomain}.revify.pt`)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Domínio Próprio (Opcional)</label>
                    <input
                      type="text"
                      value={customDomain}
                      onChange={(e) => setCustomDomain(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="www.minhaloja.com"
                    />
                    <p className="mt-1 text-xs text-gray-500">Para usar um domínio próprio, contacte o suporte para configuração DNS</p>
                  </div>
                </div>
              </div>

              {/* Conteúdo Editável */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Conteúdo das Páginas</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Texto da Página Inicial</label>
                    <textarea
                      value={homePageText}
                      onChange={(e) => setHomePageText(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="Texto de boas-vindas para a página inicial..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sobre Nós</label>
                    <textarea
                      value={aboutUs}
                      onChange={(e) => setAboutUs(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="Conte a história da sua empresa..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Informações de Contacto</label>
                    <textarea
                      value={contactInfo}
                      onChange={(e) => setContactInfo(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="Morada, telefone, email, horários..."
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === pagamentos && (
            <div className="space-y-6">
              {/* Stripe */
}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Stripe</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Chave Pública Stripe</label>
                    <input
                      type="text"
                      value={stripePublicKey}
                      onChange={(e) => setStripePublicKey(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="pk_test_..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Chave Secreta Stripe
                    </label>
                    <input
                      type="password"
                      value={stripeSecretKey}
                      onChange={(e) => setStripeSecretKey(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="sk_test_..."
                    />
                  </div>
                </div>
              </div>

              {/* IfthenPay */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <CreditCard className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">IfthenPay (Multibanco)</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Entity ID
                    </label>
                    <input
                      type="text"
                      value={ifthenPayEntityId}
                      onChange={(e) => setIfthenPayEntityId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="11249"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sub Entity ID
                    </label>
                    <input
                      type="text"
                      value={ifthenPaySubEntityId}
                      onChange={(e) => setIfthenPaySubEntityId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="999"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API Key
                    </label>
                    <input
                      type="password"
                      value={ifthenPayApiKey}
                      onChange={(e) => setIfthenPayApiKey(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="sua-chave-api-ifthen-pay"
                    />
                  </div>
                </div>

                <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>Importante:</strong>Com estas configurações, os pagamentos da loja online serão processados diretamente para a sua conta IfthenPay.</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'envios' && (
            <div className="space-y-6">
              {/* Configurações Básicas de Envio */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Truck className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Configurações de Envio</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Taxa de Envio Padrão (€)</label>
                    <input
                      type="number"
                      value={defaultShippingRate}
                      onChange={(e) => setDefaultShippingRate(Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="5.00"
                      step="0.01"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Portes Grátis a partir de (€)</label>
                    <input
                      type="number"
                      value={freeShippingThreshold}
                      onChange={(e) => setFreeShippingThreshold(Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder="50.00"
                      step="0.01"
                      min="0"
                    />
                  </div>
                </div>
              </div>

              {/* Envio por País */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Taxas por País</h3>
                  <button
                    onClick={addShippingCountry}
                    className="flex items-center space-x-2 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Adicionar País</span>
                  </button>
                </div>

                <div className="space-y-3">
                  {shippingByCountry.map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <select
                        value={item.country}
                        onChange={(e) => updateShippingCountry(index, 'country', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      >
                        <option value="">Selecionar país</option>
                        {countries.map((country) => (
                          <option key={country} value={country}>{country}</option>
                        ))}
                      </select>
                      <input
                        type="number"
                        value={item.rate}
                        onChange={(e) => updateShippingCountry(index, 'rate', Number(e.target.value))}
                        className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                      />
                      <span className="text-gray-500">€</span>
                      <button
                        onClick={() => removeShippingCountry(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}

                  {shippingByCountry.length === 0 && (
                    <p className="text-gray-500 text-sm">Nenhuma taxa específica por país configurada. Será usada a taxa padrão.</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'impostos' && (
            <div className="space-y-6">
              {/* Configurações Básicas de Impostos */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Calculator className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Configurações de Impostos</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="taxEnabled"
                      checked={taxEnabled}
                      onChange={(e) => setTaxEnabled(e.target.checked)}
                      className="w-4 h-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500"
                    />
                    <label htmlFor="taxEnabled" className="text-sm font-medium text-gray-700">Ativar cobrança de impostos</label>
                  </div>

                  {taxEnabled && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Taxa de Imposto Padrão (%)</label>
                          <input
                            type="number"
                            value={defaultTaxRate}
                            onChange={(e) => setDefaultTaxRate(Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                            placeholder="23"
                            step="0.1"
                            min="0"
                            max="100"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Tipo de Cálculo</label>
                          <select
                            value={taxIncluded ? 'included' : 'excluded'}
                            onChange={(e) => setTaxIncluded(e.target.value === 'included')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                          >
                            <option value="included">Preço com imposto incluído</option>
                            <option value="excluded">Calcular imposto no final</option>
                          </select>
                        </div>
                      </div>

                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <p className="text-sm text-gray-700">
                          <strong>Preço com imposto incluído:</strong>O preço mostrado já inclui o imposto (ex: 100€ = 81.30€ + 18.70€ 'IVA')</p>
                        <p className="text-sm text-gray-700 mt-1">
                          <strong>Calcular imposto no final:</strong>O imposto é adicionado ao preço final (ex: 100€ + 23€ IVA = 123€)</p>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Impostos por País */}
              {taxEnabled && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Taxas por País</h3>
                    <button
                      onClick={addTaxCountry}
                      className="flex items-center space-x-2 px-3 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
                    >
                    </button>
                  </div>

                  <div className=" />space-y-3">
                    {taxByCountry.map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <select
                          value={item.country}
                          onChange={(e) => {
                            const country = e.target.value
                            const defaultRate = getDefaultTaxRate(country)
                            updateTaxCountry(index, 'country', 'country')
                            updateTaxCountry(index, 'rate', 'defaultRate')
                          }}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                          <option value="">Selecionar país</option>
                          {countries.map((country) => (
                            <option key={country} value={country}>{country}</option>
                          ))}
                        </select>
                        <input
                          type="number"
                          value={item.rate}
                          onChange={(e) => updateTaxCountry(index, 'rate', Number(e.target.value))}
                          className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                          placeholder="0"
                          step="0.1"
                          min="0"
                          max="100"
                        />
                        <span className="text-gray-500">%</span>
                        <button
                          onClick={() => removeTaxCountry(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}

                    {taxByCountry.length === 0 && (
                      <p className="text-gray-500 text-sm">Nenhuma taxa específica por país configurada. Será usada a taxa padrão.</p>
                    )}
                  </div>

                  <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-800">
                      <strong>Dica:</strong>As taxas são automaticamente preenchidas com os valores padrão de cada país quando selecionados.</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'marketing' && (
            <div className="space-y-6">
              {/* Banner Promocional */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Megaphone className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Banner Promocional</h3>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Texto do Banner Principal
                    </label>
                    <input
                      type="text"
                      value={marketingText}
                      onChange={(e) => setMarketingText(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder={tSync("Ex: Entregas grátis acima de 50€ | Reparações com garantia")}
                    />
                    <p className="mt-1 text-xs text-gray-500">Este texto aparecerá no topo da sua loja online</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Texto da Página Inicial</label>
                    <textarea
                      value={homePageText}
                      onChange={(e) => setHomePageText(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder={tSync("Bem-vindos à nossa loja! Aqui encontra os melhores produtos e serviços de reparação...")}
                    />
                    <p className="mt-1 text-xs text-gray-500">Descrição que aparece na página inicial da loja</p>
                  </div>
                </div>
              </div>

              {/* Informações da Empresa */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Informações da Empresa</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sobre Nós</label>
                    <textarea
                      value={aboutUs}
                      onChange={(e) => setAboutUs(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder={tSync("Conte a história da sua empresa, missão, valores...")}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Informações de Contacto</label>
                    <textarea
                      value={contactInfo}
                      onChange={(e) => setContactInfo(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      placeholder={tSync("Morada, horários de funcionamento, informações adicionais...")}
                    />
                    <p className="mt-1 text-xs text-gray-500">Informações adicionais para a página de contacto</p>
                  </div>
                </div>
              </div>

              {/* Dicas de Marketing */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 Dicas de Marketing</h3>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• <strong>Página inicial:</strong>Destaque os seus serviços principais e diferenciais</p>
                  <p>• <strong>Sobre nós:</strong>Conte a sua história e construa confiança com os clientes</p>
                  <p>• <strong>Contacto:</strong>Inclua horários, localização e formas de contacto</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'design' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <Palette className="w-5 h-5 text-gray-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Design e Aparência</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Cor Primária</label>
                    <input
                      type="color"
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Cor Secundária</label>
                    <input
                      type="color"
                      value={secondaryColor}
                      onChange={(e) => setSecondaryColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Logo da Loja
                    </label>
                    <div className="space-y-3">
                      {logoUrl && (
                        <div className="flex items-center space-x-3">
                          <img src={logoUrl} alt="Logo atual" className="w-16 h-16 object-contain border border-gray-200 rounded" />
                          <button
                            onClick={() => setLogoUrl('')}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                      <div className="flex items-center space-x-3">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) {
                              setLogoFile(file)
                              handleFileUpload(file, 'logo')
                            }
                          }}
                          className="hidden"
                          id="logo-upload"
                        />
                        <label
                          htmlFor="logo-upload"
                          className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 cursor-pointer transition-colors"
                        >
                          <Upload className="w-4 h-4" />
                          <span>Escolher Arquivo</span>
                        </label>
                        {isUploading && <span className="text-sm text-gray-500">Uploading...</span>}
                      </div>
                      <p className="text-xs text-gray-500">Formatos aceitos: JPG, PNG, SVG. Tamanho máximo: 2MB</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Banner da Loja
                    </label>
                    <div className="space-y-3">
                      {bannerUrl && (
                        <div className="flex items-center space-x-3">
                          <img src={bannerUrl} alt="Banner atual" className="w-32 h-16 object-cover border border-gray-200 rounded" />
                          <button
                            onClick={() => setBannerUrl('')}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                      <div className="flex items-center space-x-3">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) {
                              setBannerFile(file)
                              handleFileUpload(file, 'banner')
                            }
                          }}
                          className="hidden"
                          id="banner-upload"
                        />
                        <label
                          htmlFor="banner-upload"
                          className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 cursor-pointer transition-colors"
                        >
                          <Upload className="w-4 h-4" />
                          <span>Escolher Arquivo</span>
                        </label>
                        {isUploading && <span className="text-sm text-gray-500">Uploading...</span>}
                      </div>
                      <p className="text-xs text-gray-500">Formatos aceitos: JPG, PNG. Tamanho recomendado: 1200x400px</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CSS Personalizado
                  </label>
                  <textarea
                    value={customCss}
                    onChange={(e) => setCustomCss(e.target.value)}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono text-sm"
                    placeholder="/* CSS personalizado para a sua loja */"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Botão Salvar */}
        <div className="mt-8 flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {isSaving ? 'Salvando...' : 'Salvar Configurações'}
          </button>
        </div>
      </div>
    </div>
  )
}
