import Stripe from 'stripe'

export function createStripeInstance(secretKey: string): Stripe {
  return new Stripe(secretKey, {
    apiVersion: '2024-06-20',
  })
}

export function generateMultibancoReference(): {
  entity: string
  reference: string
  amount: number} {
  // Gerar referência Multibanco simulada
  const entity = 12345
  const reference = Math.floor(100000000 + Math.random() * 900000000).toString()
  
  return {
    entity,
    reference,
    amount: 0 // 'Será definido pelo valor da encomenda'
}
}
