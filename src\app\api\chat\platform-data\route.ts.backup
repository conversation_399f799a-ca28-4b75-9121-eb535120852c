import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const type = searchParams.get('type')
    const query = searchParams.get('query')

    switch (type) {
      case 'categories':
        const categories = await prisma.category.findMany({
          select: {
            id: true,
            name: true,
            description: true
          }
        })
        return NextResponse.json(categories)

      case 'brands':
        const brands = await prisma.brand.findMany({
          select: {
            id: true,
            name: true,
            category: {
              select: {
                name: true
              }
            }
          }
        })
        return NextResponse.json(brands)

      case 'devices':
        const devices = await prisma.deviceModel.findMany({
          where: query ? {
            name: {
              contains: query,
              mode: 'insensitive'
            }
          } : undefined,
          select: {
            id: true,
            name: true,
            brand: {
              select: {
                name: true,
                category: {
                  select: {
                    name: true
                  }
                }
              }
            }
          },
          take: 20
        })
        return NextResponse.json(devices)

      case 'problem-types':
        const problemTypes = await prisma.problemType.findMany({
          select: {
            id: true,
            name: true,
            description: true,
            category: {
              select: {
                name: true
              }
            }
          }
        })
        return NextResponse.json(problemTypes)

      case 'shops':
        const shops = await prisma.user.findMany({
          where: {
            role: 'LOJISTA'
          },
          select: {
            id: true,
            name: true,
            email: true,
            profile: {
              select: {
                companyName: true,
                phone: true,
                address: true,
                city: true,
                description: true
              }
            }
          },
          take: 10
        })
        return NextResponse.json(shops)

      case 'faq':
        // Dados de FAQ estáticos para o Lemar
        const faqData = [
          {
            category: 'Reparações',
            questions: [
              {
                question: 'Quanto tempo demora uma reparação?',
                answer: 'O tempo varia conforme o tipo de reparação. Reparações simples como troca de ecrã podem demorar 1-2 horas, enquanto reparações mais complexas podem demorar 1-3 dias úteis.'
              },
              {
                question: 'Como posso acompanhar a minha reparação?',
                answer: 'Pode acompanhar o estado da sua reparação na área de cliente ou através do link enviado por email. Receberá notificações em cada etapa do processo.'
              },
              {
                question: 'Que garantia têm as reparações?',
                answer: 'Todas as reparações têm garantia mínima de 3 meses. A garantia específica depende do tipo de reparação e da loja escolhida.'
              }
            ]
          },
          {
            category: 'Plataforma',
            questions: [
              {
                question: 'Como funciona a Revify?',
                answer: 'A Revify conecta clientes a lojas de reparação especializadas. Pode simular reparações, comparar preços e escolher a melhor opção na sua área.'
              },
              {
                question: 'É seguro usar a plataforma?',
                answer: 'Sim, todas as lojas são verificadas e avaliadas pelos clientes. Oferecemos proteção ao cliente e suporte completo durante todo o processo.'
              },
              {
                question: 'Como me torno lojista parceiro?',
                answer: 'Pode candidatar-se através da página "Para Lojistas". Analisamos a candidatura e, se aprovada, ajudamos com a configuração da loja online.'
              }
            ]
          },
          {
            category: 'Preços e Pagamentos',
            questions: [
              {
                question: 'Como são calculados os preços?',
                answer: 'Cada loja define os seus preços baseados no tipo de reparação, peças necessárias e complexidade. Pode comparar preços antes de escolher.'
              },
              {
                question: 'Que métodos de pagamento aceitam?',
                answer: 'Aceitamos cartão de crédito/débito, Multibanco, MB WAY e pagamento na loja (conforme disponibilidade da loja escolhida).'
              }
            ]
          }
        ]
        return NextResponse.json(faqData)

      case 'platform-info':
        const platformInfo = {
          name: 'Revify',
          description: 'Maior plataforma Tech repair da Europa',
          features: [
            'Simulação de reparações online',
            'Comparação de preços entre lojas',
            'Acompanhamento em tempo real',
            'Marketplace de produtos tech',
            'Sistema de avaliações',
            'Suporte ao cliente 24/7'
          ],
          userTypes: [
            {
              type: 'Cliente',
              description: 'Pessoas que precisam de reparações para os seus dispositivos',
              benefits: ['Preços competitivos', 'Lojas verificadas', 'Garantia de qualidade']
            },
            {
              type: 'Lojista',
              description: 'Lojas de reparação que querem expandir o seu negócio',
              benefits: ['Mais clientes', 'Gestão digital', 'Ferramentas de marketing']
            },
            {
              type: 'Estafeta',
              description: 'Profissionais que fazem entregas e recolhas',
              benefits: ['Trabalho flexível', 'Rotas otimizadas', 'Pagamento garantido']
            }
          ]
        }
        return NextResponse.json(platformInfo)

      default:
        return NextResponse.json({ error: 'Tipo de dados não suportado' }, { status: 400 })
    }
  } catch (error) {
    console.error('Erro ao buscar dados da plataforma:', error)
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 })
  }
}
