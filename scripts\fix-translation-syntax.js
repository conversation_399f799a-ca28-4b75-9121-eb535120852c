#!/usr/bin/env node

/**
 * Script para corrigir problemas de sintaxe após conversão automática
 */

const fs = require('fs');
const path = require('path');

// Diretórios para processar
const dirsToProcess = [
  'src/app',
  'src/components'
];

// Extensões de arquivo para processar
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!file.startsWith('.') && file !== 'node_modules') {
        getAllFiles(filePath, fileList);
      }
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function fixSyntaxIssues(content) {
  let fixed = content;
  let changes = 0;

  // 1. Corrigir quebras de linha dentro de AutoTranslate text=""
  const multilineTextRegex = /<AutoTranslate\s+text="([^"]*\n[^"]*)"[^>]*\/>/g;
  fixed = fixed.replace(multilineTextRegex, (match, text) => {
    const cleanText = text.replace(/\n\s*/g, ' ').trim();
    changes++;
    return match.replace(text, cleanText);
  });

  // 2. Corrigir placeholders que ficaram mal formatados
  const badPlaceholderRegex = /placeholder\s*=\s*\{tSync\("([^"]+)"\)\}/g;
  fixed = fixed.replace(badPlaceholderRegex, (match, text) => {
    changes++;
    return `placeholder={tSync("${text}")}`;
  });

  // 3. Corrigir AutoTranslate dentro de strings JSX
  const stringAutoTranslateRegex = /"\s*<AutoTranslate\s+text="([^"]+)"\s*\/>\s*"/g;
  fixed = fixed.replace(stringAutoTranslateRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  // 4. Corrigir AutoTranslate duplicado
  const duplicateAutoTranslateRegex = /<AutoTranslate\s+text="<AutoTranslate\s+text="([^"]+)"\s*\/>"[^>]*\/>/g;
  fixed = fixed.replace(duplicateAutoTranslateRegex, (match, text) => {
    changes++;
    return `<AutoTranslate text="${text}" />`;
  });

  // 5. Corrigir espaços extras em AutoTranslate
  const extraSpacesRegex = /<AutoTranslate\s+text="\s*([^"]+?)\s*"\s*\/>/g;
  fixed = fixed.replace(extraSpacesRegex, (match, text) => {
    if (text.trim() !== text) {
      changes++;
      return `<AutoTranslate text="${text.trim()}" />`;
    }
    return match;
  });

  // 6. Corrigir AutoTranslate em atributos HTML
  const attributeAutoTranslateRegex = /(title|alt|aria-label)\s*=\s*"<AutoTranslate\s+text="([^"]+)"\s*\/>"/g;
  fixed = fixed.replace(attributeAutoTranslateRegex, (match, attr, text) => {
    changes++;
    return `${attr}={tSync("${text}")}`;
  });

  // 7. Adicionar import useTranslation se tSync for usado mas não importado
  if (fixed.includes('tSync(') && !fixed.includes('useTranslation')) {
    const importRegex = /import.*from\s+['"]@\/hooks\/useTranslation['"]/;
    if (!importRegex.test(fixed)) {
      // Encontrar onde adicionar o import
      const lines = fixed.split('\n');
      let importIndex = -1;
      
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("import") && lines[i].includes("'@/")) {
          importIndex = i;
        }
      }
      
      if (importIndex !== -1) {
        lines.splice(importIndex + 1, 0, "import { useTranslation } from '@/hooks/useTranslation'");
        fixed = lines.join('\n');
        changes++;
      }
    }
  }

  // 8. Adicionar hook useTranslation se tSync for usado
  if (fixed.includes('tSync(') && !fixed.includes('const { tSync }') && !fixed.includes('const { t, tSync }')) {
    // Procurar por função de componente
    const componentRegex = /(export default function \w+\([^)]*\)\s*\{)/;
    const match = componentRegex.exec(fixed);
    
    if (match) {
      const insertIndex = match.index + match[0].length;
      const before = fixed.substring(0, insertIndex);
      const after = fixed.substring(insertIndex);
      
      // Verificar se já não tem o hook
      const nextLines = after.split('\n').slice(0, 10).join('\n');
      if (!nextLines.includes('useTranslation')) {
        fixed = before + '\n  const { tSync } = useTranslation()' + after;
        changes++;
      }
    }
  }

  return { content: fixed, changes };
}

function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return;
  }
  
  const originalContent = fs.readFileSync(filePath, 'utf8');
  const { content: fixedContent, changes } = fixSyntaxIssues(originalContent);
  
  if (changes > 0) {
    console.log(`🔧 ${filePath} - ${changes} correções aplicadas`);
    fs.writeFileSync(filePath, fixedContent);
  }
}

// Função principal
function main() {
  console.log('🔧 Corrigindo problemas de sintaxe...\n');
  
  let allFiles = [];
  
  // Coletar todos os arquivos
  dirsToProcess.forEach(dir => {
    const files = getAllFiles(dir);
    allFiles = allFiles.concat(files);
  });
  
  console.log(`📁 Processando ${allFiles.length} arquivos...\n`);
  
  // Processar cada arquivo
  allFiles.forEach(processFile);
  
  console.log('\n✅ Correções de sintaxe finalizadas!');
}

main();
