import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🛍️ Criando produtos do marketplace...')

  // Buscar dados necessários
  const lojista = await prisma.user.findFirst({
    where: { email: '<EMAIL>' }
  })

  const categories = await prisma.category.findMany()
  const brands = await prisma.brand.findMany()
  const deviceModels = await prisma.deviceModel.findMany()

  if (!lojista || categories.length === 0 || brands.length === 0) {
    console.log('❌ Dados básicos não encontrados. Execute o seed primeiro.')
    return
  }

  // Criar produtos
  const produtos = [
    {
      name: 'iPhone 14 128GB Azul - Como Novo',
      description: 'iPhone 14 em excelente estado, usado apenas 3 meses. Inclui caixa original e carregador.',
      price: 650.00,
      originalPrice: 799.00,
      condition: 'LIKE_NEW',
      images: [
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
        'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500'
      ],
      stock: 2
    },
    {
      name: 'Samsung Galaxy S23 256GB Preto',
      description: 'Galaxy S23 em perfeito estado de funcionamento. Sem riscos ou danos.',
      price: 580.00,
      condition: 'GOOD',
      images: [
        'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500'
      ],
      stock: 1
    },
    {
      name: 'iPad Air 64GB Wi-Fi - Bom Estado',
      description: 'iPad Air em bom estado, ideal para trabalho e entretenimento.',
      price: 350.00,
      originalPrice: 450.00,
      condition: 'GOOD',
      images: [
        'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500'
      ],
      stock: 0 // Esgotado para testar
    }
  ]

  for (const produto of produtos) {
    await prisma.marketplaceProduct.create({
      data: {
        ...produto,
        sellerId: lojista.id,
        categoryId: categories[0].id, // Primeira categoria
        brandId: brands[0].id, // Primeira marca
        deviceModelId: deviceModels[0]?.id || null
      }
    })
  }

  console.log('✅ Produtos criados com sucesso!')
  console.log(`📱 ${produtos.length} produtos adicionados ao marketplace`)
}

main()
  .catch((e) => {
    console.error('❌ Erro:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
