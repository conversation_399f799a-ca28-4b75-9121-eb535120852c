import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    const { prisma } = await import('@/lib/prisma')

    // Verificar se já existem planos
    const existingPlans = await prisma.subscriptionPlan.count()
    if (existingPlans > 0) {
      return NextResponse.json(
        { message: 'Planos já existem no sistema' },
        { status: 400 }
      )
    }

    // Criar os 3 planos
    const plans = [
      {
        name: 'Revy',
        description: 'Plano básico para pequenas oficinas',
        monthlyPrice: 9.90,
        yearlyPrice: 99.00,
        features: [
          'Até 5 produtos no marketplace',
          'Até 20 reparações por mês',
          'Suporte por email',
          'Dashboard básico',
          'Notificações email'
        ],
        maxProducts: 5,
        maxRepairs: 20,
        isPopular: false,
        isActive: true
      },
      {
        name: 'Revy+',
        description: 'Plano intermédio para oficinas em crescimento',
        monthlyPrice: 19.90,
        yearlyPrice: 190.00,
        features: [
          'Até 25 produtos no marketplace',
          'Até 100 reparações por mês',
          'Suporte prioritário',
          'Dashboard avançado',
          'Notificações SMS + Email',
          'Relatórios detalhados',
          'Integração com fornecedores'
        ],
        maxProducts: 25,
        maxRepairs: 100,
        isPopular: true,
        isActive: true
      },
      {
        name: 'Revy PRO',
        description: 'Plano profissional para grandes oficinas',
        monthlyPrice: 39.90,
        yearlyPrice: 390.00,
        features: [
          'Produtos ilimitados no marketplace',
          'Reparações ilimitadas',
          'Suporte 24/7',
          'Dashboard completo',
          'Notificações SMS + Email + WhatsApp',
          'Relatórios avançados',
          'API personalizada',
          'Gestor de conta dedicado',
          'Integração completa',
          'Análise de performance'
        ],
        maxProducts: null,
        maxRepairs: null,
        isPopular: false,
        isActive: true
      }
    ]

    const createdPlans = []
    for (const planData of plans) {
      const plan = await prisma.subscriptionPlan.create({
        data: planData
      })
      createdPlans.push(plan)
    }

    return NextResponse.json({
      message: 'Planos criados com sucesso',
      plans: createdPlans
    })

  } catch (error) {
    console.error('Erro ao criar planos:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
