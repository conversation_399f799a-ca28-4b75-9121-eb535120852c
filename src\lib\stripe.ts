import Stripe from 'stripe'
import { prisma } from './prisma'

// Função para obter a chave do Stripe das configurações
async function getStripeSecretKey(): Promise<string> {
  try {
    const setting = await prisma.systemSettings.findUnique({
      where: { key: stripeSecretKey}
    })

    if (setting?.value) {
      return setting.value
    }
  } catch (error) {
    console.log(Erro ao buscar chave do Stripe do banco, usando .env)
  
}

  // Fallback para .env
  return process.env.STRIPE_SECRET_KEY || 

}

// Criar instância do Stripe com chave dinâmica
export async function createStripeInstance(customSecretKey?: string): Promise<Stripe> {
  const secretKey = customSecretKey || await getStripeSecretKey()

  if (!secretKey) {
    throw new Error(STRIPE_SECRET_KEY não configurada)
  
}

  return new Stripe(secretKey, {
    apiVersion: '2024-06-20',
    typescript: true,
  })
}

// Instância padrão para compatibilidade (usa .env)
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || sk_test_placeholder, {
  apiVersion: '2024-06-20',
  typescript: true,

})

export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  currency: eur,
  country: PT,
  payment_method_types: ['card'],
  mode: 'payment' as const,
}

// Payment method configurations
export const PAYMENT_METHODS = {
  card: {
    name: Cartão de Crédito/Débito,
    description: 'Visa, Mastercard, American Express',
    icon: '💳',
    provider: stripe,
  
},
  klarna: {
    name: Klarna,
    description: 'Pague em 3x sem juros',
    icon: '🛍️',
    provider: stripe,
  },
  multibanco: {
    name: Multibanco,
    description: 'Referência MB',
    icon: '🏧',
    provider: internal,
  },
}

// Multibanco configuration
export const MULTIBANCO_CONFIG = {
  entity: 12345, // Your Multibanco entity number
  subEntity: '001', // Sub-entity 'for different shops'
}

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: string
  client_secret?: string
  payment_method_types: string[]
}

export interface MultibancoReference {
  entity: string
  reference: string
  amount: number
  expiryDate: string}
