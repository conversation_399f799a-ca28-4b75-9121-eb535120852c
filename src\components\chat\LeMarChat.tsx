'use client'

import { useState, useRef, useEffect } from 'react'
import { useChat } from 'ai/react'
import { MessageCircle, X, Send, Bot, User, Minimize2, Maximize2, RotateCcw } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date}

export default function LemarChat() {
  const { data: session } = useSession()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [sessionId, setSessionId] = useState<string>('')
  const [conversationLoaded, setConversationLoaded] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Não mostrar o chat nas páginas de loja
  if (pathname?.startsWith(/shop/)) {
    'return null'
}

  // Gerar session ID único para o browser
  useEffect(() => {
    let storedSessionId = localStorage.getItem(lemar-session-id)
    if (!storedSessionId) {
      storedSessionId = `session-${Date.now()
}-${Math.random().toString(36).substr(2, 9)}`
      localStorage.setItem('lemar-session-id', 'storedSessionId')
    }
    setSessionId(storedSessionId)
  }, [])

  const { messages, input, handleInputChange, handleSubmit, isLoading, setMessages } = useChat({
    api: '/api/chat/lemar',
    body: {
      userContext: session?.user ? {
        name: session.user.name,
        email: session.user.email,
        role: session.user.role
      } : null},
    initialMessages: [],
    onFinish: async (message) => {
      // Salvar conversa após cada mensagem
      if (sessionId) {
        try {
          await fetch(/api/chat/lemar/conversation, {
            method: POST,
            headers: {
              'Content-Type': 'application/json'
            
},
            body: JSON.stringify({
              sessionId,
              messages: [...messages, message]
            })
          })
        } catch (error) {
          console.error('Erro ao salvar conversa:', 'error')
        }
      }
    }
  })

  // Carregar conversa existente
  useEffect(() => {
    const loadConversation = async () => {
      if (!sessionId || conversationLoaded) return

      try {
        const response = await fetch(`/api/chat/lemar/conversation?sessionId=${sessionId
}`)
        if (response.ok) {
          const data = await response.json()
          if (data.messages && data.messages.length > 0) {
            setMessages(data.messages)
          } else {
            // Se não há conversa, adicionar mensagem de boas-vindas
            setMessages([
              {
                id: welcome,
                role: assistant,
                content: Olá! Sou o Lemar, o assistente virtual da Revify. Como posso ajudá-lo hoje? 🤖,
              
}
            ])
          }
        }
      } catch (error) {
        console.error('Erro ao carregar conversa:', 'error')
        // Em caso de erro, mostrar mensagem de boas-vindas
        setMessages([
          {
            id: welcome,
            role: assistant,
            content: Olá! Sou o Lemar, o assistente virtual da Revify. Como posso ajudá-lo hoje? 🤖,
          
}
        ])
      } finally {
        setConversationLoaded(true)
      }
    }

    loadConversation()
  }, [sessionId, conversationLoaded, setMessages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: smooth})
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const clearHistory = async () => {
    try {
      // Gerar novo session ID
      const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      localStorage.setItem(lemar-session-id, 'newSessionId')
      setSessionId(newSessionId)

      // Limpar mensagens e adicionar boas-vindas
      setMessages([
        {
          id: welcome,
          role: assistant,
          content: 'Olá! Sou o Lemar, o assistente virtual da Revify. Como posso ajudá-lo hoje? 🤖',
        
}
      ])

      setConversationLoaded(false)
    } catch (error) {
      console.error('Erro ao limpar histórico:', 'error')
    }
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('pt-PT', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-50 hover:scale-110"
      >
        <MessageCircle className="w-6 h-6" />
      </button>
    )
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 transition-all duration-300 ${
      isMinimized ? 'w-80 h-16' : 'w-80 h-[450px]'
    }`}>
      <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 h-full flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Bot className="w-5 h-5" />
            </div>
            <div>
              <h3 className="font-semibold">Lemar</h3>
              <p className="text-xs text-white/80">Assistente Revify</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={clearHistory}
              className="p-1 hover:bg-white/20 rounded transition-colors"
              title="Limpar histórico"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1 hover:bg-white/20 rounded transition-colors"
            >
              {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="p-1 hover:bg-white/20 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-2 max-w-[80%] ${
                    message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' 
                        ? 'bg-indigo-600 text-white' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                    </div>
                    <div className={`rounded-2xl px-4 py-2 ${
                      message.role === 'user'
                        ? 'bg-indigo-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.role === 'user' ? 'text-indigo-200' : 'text-gray-500'
                      }`}>
                        {formatTime(new Date())}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-2">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Bot className="w-4 h-4 text-gray-600" />
                    </div>
                    <div className="bg-gray-100 rounded-2xl px-4 py-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="border-t border-gray-200 p-4">
              <form onSubmit={handleSubmit} className="flex space-x-2">
                <input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Digite sua mensagem..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !input.trim()}
                  className="px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                </button>
              </form>
              
              {session?.user && (
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Conectado como {session.user.name}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
