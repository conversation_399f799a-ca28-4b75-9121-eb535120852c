import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: "Login necessário" },
        { status: 401 }
      )
    }

    const orderId = params.id

    // Buscar encomenda específica do cliente
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        customerId: session.user.id
      },
      include: {
        marketplaceOrderItems: {
          include: {
            product: {
              include: {
                seller: {
                  select: {
                    name: true,
                    profile: {
                      select: {
                        companyName: true}
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { message: "Encomenda não encontrada" },
        { status: 404 }
      )
    }

    // Formatar dados para o frontend
    const formattedOrder = {
      id: order.id,
      status: order.status,
      total: Number(order.total),
      createdAt: order.createdAt,
      shippingAddress: {
        name: order.shippingName,
        street: order.shippingStreet,
        city: order.shippingCity,
        postalCode: order.shippingPostalCode,
        country: order.shippingCountry
      },
      items: order.marketplaceOrderItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.price),
        product: {
          id: item.product.id,
          name: item.product.name,
          images: item.product.images,
          seller: {
            name: item.product.seller.profile?.companyName || item.product.seller.name
          }
        }
      }))
    }

    return NextResponse.json({
      order: formattedOrder})

  } catch (error) {
    console.error("Erro ao buscar detalhes da encomenda:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
