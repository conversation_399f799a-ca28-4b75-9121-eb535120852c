'use client'

import { useState, useRef } from 'react'
import { QrCode, Download, Share2, Copy, Palette, Setting<PERSON>, Gift } from 'lucide-react'
interface QRCodeConfig {
  id: string
  shopName: string
  discount: number
  qrUrl: string
  customMessage: string
  color: string
  logo?: string
  scans: number
  conversions: number
  createdAt: string}

export default function PersonalizedQR() {
  const [qrCodes, setQrCodes] = useState<QRCodeConfig[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedColor, setSelectedColor] = useState('#000000')
  const [discount, setDiscount] = useState(10)
  const [customMessage, setCustomMessage] = useState('')
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const mockQRCodes: QRCodeConfig[] = [
    {
      id: '1',
      shopName: 'TechFix Lisboa',
      discount: 15,
      qrUrl: 'https://revify.pt/shop/techfix-lisboa?ref=QR001&discount=15',
      customMessage: 'Reparação com 15% desconto!',
      color: '#3B82F6',
      scans: 234,
      conversions: 45,
      createdAt: '2024-01-20'
    
},
    {
      id: '2',
      shopName: 'RepairPro Porto',
      discount: 20,
      qrUrl: 'https://revify.pt/shop/repairpro-porto?ref=QR002&discount=20',
      customMessage: 'Especialistas em Samsung - 20% OFF',
      color: '#10B981',
      scans: 156,
      conversions: 32,
      createdAt: '2024-01-19'
    
}
  ]

  const colorOptions = [
    { name: Preto, value: '#000000' },
    { name: Azul, value: '#3B82F6' },
    { name: Verde, value: '#10B981' },
    { name: Roxo, value: '#8B5CF6' },
    { name: Vermelho, value: '#EF4444' },
    { name: Laranja, value: '#F97316' }
  ]

  const generateQRCode = (text: string, color: string = '#000000') => {
    // Simulação de geração de QR Code
    // Em produção, usaria uma biblioteca como qrcode.js
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Limpar canvas
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, 200, 200)

    // Desenhar padrão QR simplificado
    ctx.fillStyle = color
    
    // Padrão de exemplo (não 'é um QR real')
    for (let i = 0; i < 20; i++) {
      for (let j = 0; j < 20; j++) {
        if (Math.random() > 0.5) {
          ctx.fillRect(i * 10, j * 10, 10, 10)
        }
      }
    }

    // Cantos de posicionamento
    ctx.fillRect(0, 0, 70, 70)
    ctx.fillRect(130, 0, 70, 70)
    ctx.fillRect(0, 130, 70, 70)
    
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(10, 10, 50, 50)
    ctx.fillRect(140, 10, 50, 50)
    ctx.fillRect(10, 140, 50, 50)
    
    ctx.fillStyle = color
    ctx.fillRect(20, 20, 30, 30)
    ctx.fillRect(150, 20, 30, 30)
    ctx.fillRect(20, 150, 30, 30)
  
}

  const handleCreateQR = () => {
    const newQR: QRCodeConfig = {
      id: Date.now().toString(),
      shopName: 'Minha Loja',
      discount: discount,
      qrUrl: `https:// revify.pt/shop/minha-loja?ref=QR${Date.now()}&discount=${discount}`,
      customMessage: customMessage || `Desconto de ${discount}% na reparação!`,
      color: selectedColor,
      scans: 0,
      conversions: 0,
      createdAt: new Date().toISOString().split(T)[0]
    
}
    
    setQrCodes([...qrCodes, newQR])
    setShowCreateModal(false)
    setCustomMessage('')
    setDiscount(10)
  }

  const handleDownload = (qrConfig: QRCodeConfig) => {
    // Gerar QR code no canvas
    generateQRCode(qrConfig.qrUrl, qrConfig.color)
    
    // Download do canvas
    const canvas = canvasRef.current
    if (canvas) {
      const link = document.createElement(a)
      link.download = `qr-${qrConfig.shopName.toLowerCase().replace(/\s+/g, '-')
}.png`
      link.href = canvas.toDataURL()
      link.click()
    }
  }

  const handleShare = async (qrConfig: QRCodeConfig) => {
    const shareData = {
      title: `QR Code - ${qrConfig.shopName}`,
      text: qrConfig.customMessage,
      url: qrConfig.qrUrl
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(qrConfig.qrUrl)
        alert('Link copiado para a área de transferência!')
      }
    } catch (error) {
      console.error('Erro ao partilhar:', 'error')
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      alert('Link copiado!')
    } catch (error) {
      console.error('Erro ao copiar:', 'error')
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-lg flex items-center justify-center">
            <QrCode className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              QR Codes da Revify
            </h3>
            <p className="text-sm text-gray-600">
              Cria QR codes personalizados para divulgar a Revify
            </p>
          </div>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-lg hover:from-indigo-600 hover:to-blue-600 transition-colors"
        >
          <QrCode className="w-4 h-4" />
          <span>Criar QR Code</span>
        </button>
      </div>

      {/* QR Codes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockQRCodes.map((qr) => (
          <div key={qr.id} className="bg-gray-50 rounded-lg p-4">
            {/* QR Code Preview */}
            <div className="bg-white rounded-lg p-4 mb-4 flex items-center justify-center">
              <div 
                className="w-32 h-32 border-2 border-gray-200 rounded-lg flex items-center justify-center"
                style={{ borderColor: qr.color }}
              >
                <QrCode className="w-16 h-16" style={{ color: qr.color }} />
              </div>
            </div>

            {/* QR Info */}
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-gray-900">{qr.shopName}</h4>
                <p className="text-sm text-gray-600">{qr.customMessage}</p>
              </div>

              {/* Discount Badge */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  <Gift className="w-3 h-3" />
                  <span>{qr.discount}% OFF</span>
                </div>
                <div className="text-xs text-gray-500">
                  Criado em {qr.createdAt}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{qr.scans}</div>
                  <div className="text-gray-600">
                    Scans
                  </div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{qr.conversions}</div>
                  <div className="text-gray-600">
                    Conversões
                  </div>
                </div>
              </div>

              {/* Conversion Rate */}
              <div className="bg-white rounded-lg p-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-600">
                    Taxa de Conversão
                  </span>
                  <span className="text-xs font-medium text-gray-900">
                    {qr.scans > 0 ? Math.round((qr.conversions / qr.scans) * 100) : 0}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${qr.scans > 0 ? (qr.conversions / qr.scans) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => handleDownload(qr)}
                  className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </button>
                <button
                  onClick={() => handleShare(qr)}
                  className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                >
                  <Share2 className="w-4 h-4" />
                  <span>Partilhar</span>
                </button>
              </div>

              {/* URL */}
              <div className="bg-white rounded-lg p-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={qr.qrUrl}
                    readOnly
                    className="flex-1 text-xs text-gray-600 bg-transparent border-none outline-none"
                  />
                  <button
                    onClick={() => copyToClipboard(qr.qrUrl)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Hidden Canvas for QR Generation */}
      <canvas
        ref={canvasRef}
        width={200}
        height={200}
        className="hidden"
      />

      {/* Create QR Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Criar Novo QR Code
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Desconto (%)
                </label>
                <input
                  type="number"
                  value={discount}
                  onChange={(e) => setDiscount(Number(e.target.value))}
                  min="1"
                  max="50"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mensagem Personalizada
                </label>
                <input
                  type="text"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder={`Desconto de ${discount}% na reparação!`}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cor do QR Code
                </label>
                <div className="grid grid-cols-6 gap-2">
                  {colorOptions.map((color) => (
                    <button
                      key={color.value}
                      onClick={() => setSelectedColor(color.value)}
                      className={`w-8 h-8 rounded-full border-2 ${
                        selectedColor === color.value ? 'border-gray-400' : 'border-gray-200'
                      }`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>

              {/* Preview */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-2">
                  Pré-visualização
                </div>
                <div className="bg-white rounded-lg p-3 flex items-center justify-center">
                  <QrCode className="w-16 h-16" style={{ color: selectedColor}} />
                </div>
                <div className="mt-2 text-center">
                  <div className="text-sm font-medium text-gray-900">
                    {customMessage || `Desconto de ${discount}% na reparação!`}
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {discount}% OFF
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateQR}
                className="flex-1 py-2 px-4 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-lg hover:from-indigo-600 hover:to-blue-600 transition-colors"
              >
                Criar QR Code
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
