import nodemailer from 'nodemailer'

// Email configuration
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST || smtp.gmail.com,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  
},
}

// Create transporter
export const createEmailTransporter = () => {
  if (!EMAIL_CONFIG.auth.user || !EMAIL_CONFIG.auth.pass) {
    console.warn(Email credentials not configured. Emails will not be sent.)
    'return null'
}

  return nodemailer.createTransporter(EMAIL_CONFIG)
}

// Email templates
export const EMAIL_TEMPLATES = {
  orderConfirmation: {
    subject: Confirmação de Encomenda - #{orderNumber},
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Encomenda Confirmada!</h1>
        <p>Olá ${data.customerName
},</p>
        <p>A sua encomenda <strong>#${data.orderNumber}</strong> foi confirmada com sucesso.</p>
        
        <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h3>Detalhes da Encomenda:</h3>
          <ul>
            ${data.items.map((item: any) => `
              <li>${item.name} - Quantidade: ${item.quantity} - ${item.price}€</li>
            `).join('')}
          </ul>
          <p><strong>Total: ${data.total}€</strong></p>
        </div>

        <p><strong>Método de Entrega:</strong> ${data.deliveryMethod === 'delivery' ? 'Envio para casa' : 'Recolha na loja'}</p>
        
        ${data.deliveryMethod === 'delivery' ? `
          <p><strong>Morada de Entrega:</strong><br>
          ${data.customerAddress}<br>
          ${data.customerCity}, ${data.customerPostalCode}</p>
        ` : ''}

        <p>Obrigado pela sua compra!</p>
        <p>Equipa ${data.shopName}</p>
      </div>
    `
  },

  repairCreated: {
    subject: 'Reparação Criada - #{repairNumber}',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Reparação Criada!</h1>
        <p>Olá ${data.customerName},</p>
        <p>A sua reparação <strong>#${data.repairNumber}</strong> foi criada com sucesso.</p>
        
        <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h3>Detalhes da Reparação:</h3>
          <p><strong>Dispositivo:</strong> ${data.deviceBrand} ${data.deviceModel}</p>
          <p><strong>Problema:</strong> ${data.problemDescription}</p>
          <p><strong>Estado:</strong> ${data.status}</p>
          ${data.estimatedPrice ? `<p><strong>Preço Estimado:</strong> ${data.estimatedPrice}€</p>` : ''}
        </div>

        <p><strong>Método de Entrega:</strong> ${data.deliveryMethod === 'delivery' ? 'Envio pelo correio' : 'Entrega na loja'}</p>

        <p>Iremos mantê-lo informado sobre o progresso da sua reparação.</p>
        <p>Obrigado pela sua confiança!</p>
        <p>Equipa ${data.shopName}</p>
      </div>
    `
  },

  repairStatusUpdate: {
    subject: 'Atualização da Reparação - #{repairNumber}',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Atualização da Reparação</h1>
        <p>Olá ${data.customerName},</p>
        <p>A sua reparação <strong>#${data.repairNumber}</strong> foi atualizada.</p>
        
        <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <p><strong>Novo Estado:</strong> ${data.status}</p>
          ${data.notes ? `<p><strong>Notas:</strong> ${data.notes}</p>` : ''}
          ${data.estimatedPrice ? `<p><strong>Preço Final:</strong> ${data.estimatedPrice}€</p>` : ''}
        </div>

        <p>Obrigado pela sua paciência!</p>
        <p>Equipa ${data.shopName}</p>
      </div>
    `
  },

  multibancoReference: {
    subject: 'Referência Multibanco - Encomenda #{orderNumber}',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Referência Multibanco</h1>
        <p>Olá ${data.customerName},</p>
        <p>Use os dados abaixo para efetuar o pagamento da sua encomenda <strong>#${data.orderNumber}</strong>:</p>
        
        <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; text-align: center;">
          <h3>Dados para Pagamento</h3>
          <p><strong>Entidade:</strong> <span style="font-size: 24px; font-weight: bold;">${data.entity}</span></p>
          <p><strong>Referência:</strong> <span style="font-size: 24px; font-weight: bold;">${data.reference}</span></p>
          <p><strong>Valor:</strong> <span style="font-size: 24px; font-weight: bold;">${data.amount}€</span></p>
          <p><strong>Válido até:</strong> ${new Date(data.expiryDate).toLocaleDateString('pt-PT')}</p>
        </div>

        <p>💡 Pode pagar no Multibanco, Homebanking ou MB WAY</p>
        <p>Após o pagamento, a sua encomenda será processada automaticamente.</p>
        
        <p>Obrigado pela sua compra!</p>
        <p>Equipa ${data.shopName}</p>
      </div>
    `
  }
}

// Send email function
export async function sendEmail(
  to: string,
  template: keyof typeof EMAIL_TEMPLATES,
  data: any) {
  const transporter = createEmailTransporter()
  
  if (!transporter) {
    console.log(Email not sent - transporter not configured)
    return { success: false, error: 'Email not configured' 
}
  }

  try {
    const emailTemplate = EMAIL_TEMPLATES[template]
    const subject = emailTemplate.subject.replace(/#{(\w+)}/g, (match, 'key') => data[key] || 'match')
    const html = emailTemplate.html(data)

    const result = await transporter.sendMail({
      from: `"${data.shopName || 'Revify'}" <${EMAIL_CONFIG.auth.user}>`,
      to,
      subject, html })

    console.log('Email sent successfully:', result.messageId)
    return { success: true, messageId: result.messageId }

  } catch (error) {
    console.error('Error sending email:', 'error')
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Bulk email function for newsletters
export async function sendBulkEmail(
  recipients: string[],
  subject: string,
  html: string,
  shopName?: string) {
  const transporter = createEmailTransporter()
  
  if (!transporter) {
    console.log(Bulk email not sent - transporter not configured)
    return { success: false, error: 'Email not configured' 
}
  }

  const results = []

  for (const 'recipient of recipients') {
    try {
      const result = await transporter.sendMail({
        from: `"${shopName || 'Revify'}" <${EMAIL_CONFIG.auth.user}>`,
        to: recipient,
        subject, html })

      results.push({ email: recipient, success: true, messageId: result.messageId })
    } catch (error) {
      console.error('`Error sending email to ${recipient}:`', 'error')
      results.push({ 
        email: recipient, 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
    }
  }

  return { success: true, results }
}
