'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Smartphone, CheckCircle, Star, Clock,
  Shield, Wrench, Battery, Droplets, Volume2, Wifi,
  ArrowRight, Phone
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'

export default function SmartphoneRepairPage() {
  const { t } = useTranslation()
  const [selectedBrand, setSelectedBrand] = useState('')

  const brands = [
    { name: 'iPhone', logo: '🍎', popular: true },
    { name: 'Samsung', logo: '📱', popular: true },
    { name: '<PERSON><PERSON><PERSON>', logo: '📱', popular: true },
    { name: '<PERSON><PERSON>', logo: '📱', popular: false },
    { name: 'OnePlus', logo: '📱', popular: false },
    { name: 'Google Pixel', logo: '📱', popular: false },
  ]

  const commonProblems = [
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: 'Ecrã Partido',
      description: 'Substituição completa do ecrã e touch',
      price: 'desde €45',
      time: '30min'
    },
    {
      icon: <Battery className="w-6 h-6" />,
      title: 'Bateria',
      description: 'Substituição de bateria original',
      price: 'desde €35',
      time: '20min'
    },
    {
      icon: <Droplets className="w-6 h-6" />,
      title: 'Danos por Água',
      description: 'Limpeza e reparação de componentes',
      price: 'desde €60',
      time: '2h'
    },
    {
      icon: <Volume2 className="w-6 h-6" />,
      title: 'Problemas de Som',
      description: 'Reparação de altifalantes e microfone',
      price: 'desde €40',
      time: '45min'
    },
    {
      icon: <Wifi className="w-6 h-6" />,
      title: 'Conectividade',
      description: 'WiFi, Bluetooth e dados móveis',
      price: 'desde €50',
      time: '1h'
    },
    {
      icon: <Wrench className="w-6 h-6" />,
      title: 'Não Liga',
      description: 'Diagnóstico e reparação completa',
      price: 'desde €55',
      time: '1.5h'
    }
  ]

  const features = [
    'Peças originais ou compatíveis de qualidade',
    'Garantia de 6 meses em todas as reparações',
    'Técnicos certificados e experientes',
    'Diagnóstico gratuito em 15 minutos',
    'Reparação no mesmo dia (maioria dos casos)',
    'Preços transparentes sem surpresas'
  ]

  return (
    <ModernLayout>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Smartphone className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('smartphoneRepairTitle')}
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
              {t('smartphoneRepairSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/simular-reparacao"
                className="bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Wrench className="w-5 h-5" />
                <span>Simular Reparação</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/servicos/diagnostico-gratuito"
                className="border-2 border-white/20 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <CheckCircle className="w-5 h-5" />
                <span>Diagnóstico Gratuito</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Brands Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Reparamos Todas as Marcas
            </h2>
            <p className="text-xl text-gray-600">
              Especialistas certificados para cada marca e modelo
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {brands.map((brand) => (
              <div
                key={brand.name}
                className={`relative bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-all cursor-pointer ${
                  selectedBrand === brand.name ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => setSelectedBrand(brand.name)}
              >
                {brand.popular && (
                  <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                    Popular
                  </div>
                )}
                <div className="text-4xl mb-3">{brand.logo}</div>
                <h3 className="font-semibold text-gray-900">{brand.name}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Common Problems */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Problemas Mais Comuns
            </h2>
            <p className="text-xl text-gray-600">
              Soluções rápidas e eficazes para os problemas mais frequentes
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {commonProblems.map((problem, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-4 text-blue-600">
                  {problem.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {problem.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {problem.description}
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-green-600 font-bold">
                    {problem.price}
                  </div>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    {problem.time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Porque Escolher a Revify?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8">
                <Link
                  href="/simular-reparacao"
                  className="bg-blue-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-blue-700 transition-colors inline-flex items-center space-x-2"
                >
                  <Wrench className="w-5 h-5" />
                  <span>Começar Agora</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Shield className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Garantia de 6 Meses
                </h3>
                <p className="text-gray-600 mb-6">
                  Todas as nossas reparações incluem garantia completa de 6 meses. 
                  Se algo correr mal, reparamos novamente sem custos adicionais.
                </p>
                <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span>4.9/5 avaliação</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>15k+ reparações</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-gray-900 to-black text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Pronto para Reparar o Seu Smartphone?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Junte-se a milhares de clientes satisfeitos
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/simular-reparacao"
              className="bg-white text-black px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Smartphone className="w-5 h-5" />
              <span>Simular Reparação</span>
            </Link>
            <Link
              href="/contactos"
              className="border-2 border-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Falar Connosco</span>
            </Link>
          </div>
        </div>
      </section>
    </ModernLayout>
  )
}
