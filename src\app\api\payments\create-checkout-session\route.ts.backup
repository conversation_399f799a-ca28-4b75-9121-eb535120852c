import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createStripeInstance } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Não autenticado' },
        { status: 401 }
      )
    }

    const { amount, plan, paymentId } = await request.json()

    if (!amount || !plan) {
      return NextResponse.json(
        { message: 'Dados inválidos' },
        { status: 400 }
      )
    }

    // Buscar chave do Stripe das configurações do admin
    const stripeSecretSetting = await prisma.systemSettings.findUnique({
      where: { key: 'stripeSecretKey' }
    })

    const stripeSecretKey = stripeSecretSetting?.value || process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey || stripeSecretKey.includes('placeholder') || stripeSecretKey.includes('xyz')) {
      return NextResponse.json(
        { message: 'Stripe não configurado. Configure as chaves do Stripe no admin.' },
        { status: 400 }
      )
    }

    // Criar instância do Stripe
    const stripe = await createStripeInstance(stripeSecretKey)

    // Criar sessão de checkout
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'payment',
      customer_email: session.user.email || undefined,
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `Subscrição ${plan}`,
              description: `Upgrade para o plano ${plan}`,
            },
            unit_amount: Math.round(amount * 100), // Stripe usa centavos
          },
          quantity: 1,
        },
      ],
      metadata: {
        userId: session.user.id,
        plan: plan,
        paymentId: paymentId || '',
        type: 'subscription_upgrade'
      },
      success_url: `${process.env.NEXTAUTH_URL}/checkout?session_id={CHECKOUT_SESSION_ID}&payment=${paymentId}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/checkout?payment=${paymentId}&amount=${amount}&plan=${encodeURIComponent(plan)}&error=cancelled`,
    })

    // Salvar informações do pagamento temporário
    if (paymentId) {
      await prisma.pendingUpgrade.upsert({
        where: { id: paymentId },
        update: {
          status: 'PENDING'
        },
        create: {
          id: paymentId,
          userId: session.user.id,
          newPlanId: plan,
          billingCycle: 'MONTHLY',
          amount: amount,
          currency: 'EUR',
          status: 'PENDING'
        }
      })
    }

    return NextResponse.json({
      url: checkoutSession.url
    })

  } catch (error) {
    console.error('Erro ao criar sessão de checkout:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
