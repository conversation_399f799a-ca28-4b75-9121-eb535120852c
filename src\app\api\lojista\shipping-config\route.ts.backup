import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: { userId: session.user.id },
      select: {
        shippingEnabled: true,
        freeShippingThreshold: true,
        freeShippingCountries: true,
        shippingRates: true
      }
    })

    return NextResponse.json({
      shippingEnabled: profile?.shippingEnabled || false,
      freeShippingThreshold: profile?.freeShippingThreshold ? Number(profile.freeShippingThreshold) : null,
      freeShippingCountries: profile?.freeShippingCountries || [],
      shippingRates: profile?.shippingRates || []
    })

  } catch (error) {
    console.error('Erro ao buscar configurações de envio:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const {
      shippingEnabled,
      freeShippingThreshold,
      freeShippingCountries,
      shippingRates
    } = await request.json()

    // Validar dados
    if (typeof shippingEnabled !== 'boolean') {
      return NextResponse.json(
        { message: 'shippingEnabled deve ser boolean' },
        { status: 400 }
      )
    }

    if (freeShippingThreshold !== null && (typeof freeShippingThreshold !== 'number' || freeShippingThreshold < 0)) {
      return NextResponse.json(
        { message: 'freeShippingThreshold deve ser um número positivo ou null' },
        { status: 400 }
      )
    }

    if (!Array.isArray(freeShippingCountries)) {
      return NextResponse.json(
        { message: 'freeShippingCountries deve ser um array' },
        { status: 400 }
      )
    }

    if (!Array.isArray(shippingRates)) {
      return NextResponse.json(
        { message: 'shippingRates deve ser um array' },
        { status: 400 }
      )
    }

    // Validar shippingRates
    for (const rate of shippingRates) {
      if (!rate.country || !rate.type) {
        return NextResponse.json(
          { message: 'Cada tarifa deve ter country e type' },
          { status: 400 }
        )
      }

      if (rate.type === 'FIXED' && (typeof rate.fixedRate !== 'number' || rate.fixedRate < 0)) {
        return NextResponse.json(
          { message: 'Taxa fixa deve ser um número positivo' },
          { status: 400 }
        )
      }

      if (rate.type === 'WEIGHT_BASED') {
        if (!Array.isArray(rate.weightRates) || rate.weightRates.length === 0) {
          return NextResponse.json(
            { message: 'Tarifas por peso devem ter pelo menos uma faixa' },
            { status: 400 }
          )
        }

        for (const weightRate of rate.weightRates) {
          if (typeof weightRate.maxWeight !== 'number' || weightRate.maxWeight <= 0) {
            return NextResponse.json(
              { message: 'Peso máximo deve ser um número positivo' },
              { status: 400 }
            )
          }

          if (typeof weightRate.rate !== 'number' || weightRate.rate < 0) {
            return NextResponse.json(
              { message: 'Taxa por peso deve ser um número positivo' },
              { status: 400 }
            )
          }
        }
      }
    }

    // Atualizar perfil
    await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        shippingEnabled,
        freeShippingThreshold: freeShippingThreshold,
        freeShippingCountries,
        shippingRates
      },
      create: {
        userId: session.user.id,
        phone: '',
        address: '',
        city: '',
        postalCode: '',
        country: 'PT',
        shippingEnabled,
        freeShippingThreshold: freeShippingThreshold,
        freeShippingCountries,
        shippingRates
      }
    })

    return NextResponse.json({
      message: 'Configurações de envio atualizadas com sucesso'
    })

  } catch (error) {
    console.error('Erro ao salvar configurações de envio:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
