'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Gift, Share2, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
interface ReferralStats {
  totalReferrals: number
  pendingRewards: number
  completedRewards: number
  referralCode: string
  nextRewardAt: number}

export default function ClientReferralWidget() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<ReferralStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showShareModal, setShowShareModal] = useState(false)

  useEffect(() => {
    if (session?.user?.id) {
      fetchReferralStats()
    }
  }, [session])

  const fetchReferralStats = async () => {
    try {
      // Mock data para demonstração
      const mockStats: ReferralStats = {
        totalReferrals: 3,
        pendingRewards: 1,
        completedRewards: 2,
        referralCode: CLIENTE2024,
        nextRewardAt: 5
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Erro ao carregar stats de referral:', error)
    
} finally {
      setLoading(false)
    }
  }

  const handleShare = async () => {
    if (!stats) return

    const shareData = {
      title: 'Revify - Reparações de Confiança',
      text: `Usa o meu código ${stats.referralCode} e ganha 10% de desconto na tua primeira reparação!`,
      url: `${window.location.origin}?ref=${stats.referralCode}`
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(`${shareData.text}\n${shareData.url}`)
        alert('Link copiado para a área de transferência!')
      }
    } catch (error) {
      console.error('Erro ao partilhar:', 'error')
    }
  }

  const copyReferralCode = async () => {
    if (!stats) return
    
    try {
      await navigator.clipboard.writeText(stats.referralCode)
      alert('Código copiado!')
    } catch (error) {
      console.error('Erro ao copiar:', 'error')
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!stats) return null

  return (
    <>
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl shadow-sm border border-blue-200 p-6">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
            <Gift className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Programa de Referrals
            </h3>
            <p className="text-sm text-gray-600">
              Convida amigos e ganha descontos!
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {stats.totalReferrals}
            </div>
            <div className="text-xs text-gray-600">
              Amigos referidos
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {stats.completedRewards}
            </div>
            <div className="text-xs text-gray-600">
              Descontos ganhos
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {stats.pendingRewards}
            </div>
            <div className="text-xs text-gray-600">
              Pendentes
            </div>
          </div>
        </div>

        {/* Referral Code */}
        <div className="bg-white rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-700 mb-1">
                O teu código:
              </div>
              <div className="text-lg font-bold text-gray-900 font-mono">
                {stats.referralCode}
              </div>
            </div>
            <button
              onClick={copyReferralCode}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Copy className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Progress to Next Reward */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Próximo desconto em
            </span>
            <span className="text-sm text-gray-600">
              {stats.totalReferrals}/{stats.nextRewardAt}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(stats.totalReferrals / stats.nextRewardAt) * 100}%` }}
            ></div>
          </div>
          <div className="text-xs text-gray-600 mt-1">
            Mais {stats.nextRewardAt - stats.totalReferrals} referrals para 15% de desconto
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleShare}
            className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-colors"
          >
            <Share2 className="w-4 h-4" />
            <span>Partilhar</span>
          </button>
          <button
            onClick={() => setShowShareModal(true)}
            className="flex items-center justify-center px-4 py-3 border border-blue-300 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <Users className="w-4 h-4" />
          </button>
        </div>

        {/* Benefits */}
        <div className="mt-4 pt-4 border-t border-blue-200">
          <div className="text-sm font-medium text-gray-700 mb-2">
            Como funciona:
          </div>
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex items-center space-x-2">
              <Percent className="w-3 h-3 text-blue-500" />
              <span>Tu e o teu amigo ganham 10% de desconto</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="w-3 h-3 text-yellow-500" />
              <span>Descontos maiores com mais referrals</span>
            </div>
            <div className="flex items-center space-x-2">
              <Trophy className="w-3 h-3 text-purple-500" />
              <span>Badges especiais para top referrers</span>
            </div>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Partilhar com Amigos
            </h3>
            
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-2">
                  Mensagem para partilhar:
                </div>
                <div className="text-sm text-gray-600 italic">
                  "Descobri a Revify - a melhor plataforma para reparações! Usa o meu código <strong>{stats.referralCode}</strong> e ganha 10% de desconto na tua primeira reparação. 📱✨"
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => {
                    const text = `Descobri a Revify - a melhor plataforma para reparações! Usa o meu código ${stats.referralCode} e ganha 10% de desconto na tua primeira reparação. 📱✨ ${window.location.origin}?ref=${stats.referralCode}`
                    window.open(`https:// wa.me/?text=${encodeURIComponent(text)}`, _blank)
                  
}}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  <span>WhatsApp</span>
                </button>
                
                <button
                  onClick={() => {
                    const text = `Descobri a Revify - a melhor plataforma para reparações! Usa o meu código ${stats.referralCode} e ganha 10% de desconto na tua primeira reparação. 📱✨ ${window.location.origin}?ref=${stats.referralCode}`
                    window.open(`https:// www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.origin)}&quote=${encodeURIComponent(text)}`, _blank)
                  
}}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <span>Facebook</span>
                </button>
                
                <button
                  onClick={() => {
                    const text = `Descobri a Revify! Usa o código ${stats.referralCode} para 10% desconto 📱✨`
                    window.open(`https:// twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(window.location.origin)}`, _blank)
                  
}}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors"
                >
                  <span>Twitter</span>
                </button>
                
                <button
                  onClick={async () => {
                    const text = `Descobri a Revify - a melhor plataforma para reparações! Usa o meu código ${stats.referralCode} e ganha 10% de desconto na tua primeira reparação. 📱✨ ${window.location.origin}?ref=${stats.referralCode}`
                    await navigator.clipboard.writeText(text)
                    alert('Mensagem copiada!')
                  }}
                  className="flex items-center justify-center space-x-2 py-3 px-4 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copiar</span>
                </button>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowShareModal(false)}
                className="py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
