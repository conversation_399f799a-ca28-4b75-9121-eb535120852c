'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { ArrowLeft, Phone, Mail, MapPin, Clock, Euro, Edit, MessageCircle } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface RepairDetail {
  id: string
  status: string
  customerName: string
  customerPhone: string
  customerNif?: string
  description: string
  estimatedPrice: number
  finalPrice?: number
  deliveryMethod: string
  pickupAddress?: string
  deliveryAddress?: string
  createdAt: string
  scheduledDate?: string
  completedDate?: string
  paidAt?: string
  confirmedAt?: string
  deviceModel?: {
    name: string
    brand: { name: string }
    category: { name: string }
  }
  problemType?: {
    name: string
    icon: string
  }
  repairShop?: {
    id: string
    email: string
    profile?: {
      companyName: string
      phone: string
      description: string
    }
  }
  customer?: {
    name: string
    email: string
  }
  payments?: Array<{
    amount: number
    status: string
    createdAt: string
  }>
}

export default function AdminRepairDetailPage({ params }: { params: { id: string } }) {
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [repair, setRepair] = useState<RepairDetail | null>(null)

  useEffect(() => {
    fetchRepairDetail()
  }, [params.id])

  const fetchRepairDetail = async () => {
    try {
      const response = await fetch(`/api/admin/repairs/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data)
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes da reparação:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING_PAYMENT': return 'Aguarda Pagamento'
      case 'PAID': return 'Pago'
      case 'CONFIRMED': return 'Confirmado'
      case 'RECEIVED': return 'Recebido'
      case 'DIAGNOSIS': return 'Diagnóstico'
      case 'WAITING_PARTS': return 'Aguarda Peças'
      case 'IN_REPAIR': return 'Em Reparação'
      case 'TESTING': return 'Teste'
      case 'COMPLETED': return 'Concluído'
      case 'DELIVERED': return 'Entregue'
      case 'CANCELLED': return 'Cancelado'
      default: return status
    }
  }

  const getDeliveryMethodLabel = (method: string) => {
    switch (method) {
      case 'STORE_PICKUP': return 'Entregar na loja'
      case 'COURIER_PICKUP': return 'Recolha por estafeta'
      case 'MAIL_SEND': return 'Envio pelos correios'
      default: return method
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!repair) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Reparação não encontrada</h2>
          <Link href="/admin/repairs" className="text-blue-600 hover:text-blue-800">
            Voltar às reparações
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-black">Revify Admin</Link>
              <span className="ml-3 text-xs text-gray-500">Detalhes da Reparação</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link href="/admin/repairs" className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Voltar às Reparações
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Coluna Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Informações da Reparação */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    Reparação #{repair.id.slice(-8)}
                  </h1>
                  <div className="flex items-center space-x-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800`}>
                      {getStatusLabel(repair.status)}
                    </span>
                    <span className="text-sm text-gray-500">
                      Criada em {new Date(repair.createdAt).toLocaleDateString('pt-PT')}
                    </span>
                  </div>
                </div>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                  <Edit className="w-4 h-4 mr-2" />
                  Editar Status
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Dispositivo</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Categoria:</span>
                      <span className="ml-2 text-gray-600">{repair.deviceModel?.category?.name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Marca:</span>
                      <span className="ml-2 text-gray-600">{repair.deviceModel?.brand?.name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Modelo:</span>
                      <span className="ml-2 text-gray-600">{repair.deviceModel?.name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Problema:</span>
                      <span className="ml-2 text-gray-600">
                        {repair.problemType?.icon} {repair.problemType?.name}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Valores</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Preço Estimado:</span>
                      <span className="ml-2 text-gray-600">€{repair.estimatedPrice}</span>
                    </div>
                    {repair.finalPrice && (
                      <div>
                        <span className="font-medium text-gray-700">Preço Final:</span>
                        <span className="ml-2 text-gray-600">€{repair.finalPrice}</span>
                      </div>
                    )}
                    <div>
                      <span className="font-medium text-gray-700">Método de Entrega:</span>
                      <span className="ml-2 text-gray-600">{getDeliveryMethodLabel(repair.deliveryMethod)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Descrição do Problema</h3>
                <p className="text-gray-600 bg-gray-50 p-4 rounded-lg">{repair.description}</p>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Reparação criada</div>
                    <div className="text-xs text-gray-500">{new Date(repair.createdAt).toLocaleString('pt-PT')}</div>
                  </div>
                </div>
                {repair.paidAt && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">Pagamento processado</div>
                      <div className="text-xs text-gray-500">{new Date(repair.paidAt).toLocaleString('pt-PT')}</div>
                    </div>
                  </div>
                )}
                {repair.scheduledDate && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">Agendamento marcado</div>
                      <div className="text-xs text-gray-500">{new Date(repair.scheduledDate).toLocaleString('pt-PT')}</div>
                    </div>
                  </div>
                )}
                {repair.completedDate && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-emerald-600 rounded-full"></div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">Reparação concluída</div>
                      <div className="text-xs text-gray-500">{new Date(repair.completedDate).toLocaleString('pt-PT')}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Informações do Cliente */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cliente</h3>
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-gray-900">{repair.customerName}</div>
                  <div className="text-sm text-gray-500 flex items-center mt-1">
                    <Phone className="w-4 h-4 mr-1" />
                    {repair.customerPhone}
                  </div>
                  {repair.customer?.email && (
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Mail className="w-4 h-4 mr-1" />
                      {repair.customer.email}
                    </div>
                  )}
                  {repair.customerNif && (
                    <div className="text-sm text-gray-500 mt-1">
                      NIF: {repair.customerNif}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Informações da Loja */}
            {repair.repairShop && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Loja de Reparação</h3>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {repair.repairShop.profile?.companyName || 'Nome não disponível'}
                    </div>
                    {repair.repairShop.profile?.phone && (
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <Phone className="w-4 h-4 mr-1" />
                        {repair.repairShop.profile.phone}
                      </div>
                    )}
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Mail className="w-4 h-4 mr-1" />
                      {repair.repairShop.email}
                    </div>
                  </div>
                  <button className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Contactar Loja
                  </button>
                </div>
              </div>
            )}

            {/* Endereços */}
            {(repair.pickupAddress || repair.deliveryAddress) && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Endereços</h3>
                <div className="space-y-3">
                  {repair.pickupAddress && (
                    <div>
                      <div className="text-sm font-medium text-gray-700">Recolha:</div>
                      <div className="text-sm text-gray-600 flex items-start mt-1">
                        <MapPin className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                        {repair.pickupAddress}
                      </div>
                    </div>
                  )}
                  {repair.deliveryAddress && (
                    <div>
                      <div className="text-sm font-medium text-gray-700">Entrega:</div>
                      <div className="text-sm text-gray-600 flex items-start mt-1">
                        <MapPin className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                        {repair.deliveryAddress}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Pagamentos */}
            {repair.payments && repair.payments.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pagamentos</h3>
                <div className="space-y-3">
                  {repair.payments.map((payment, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">€{payment.amount}</div>
                        <div className="text-xs text-gray-500">{payment.status}</div>
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(payment.createdAt).toLocaleDateString('pt-PT')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
