'use client'

import Link from 'next/link'
import { ArrowLeft, Search, MapPin, Calendar, CreditCard, CheckCircle } from 'lucide-react'

export default function FazerReparacaoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="mr-8">
                <Link href="/" className="font-bold text-black text-xl">
                  Revify
                </Link>
                <div className="text-xs text-gray-600 font-light">Central de Ajuda</div>
              </div>
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />Voltar à Central de Ajuda</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <span className=" />mx-2">›</span>
              <Link href="/ajuda?category=clientes" className="hover:text-gray-700">Para Clientes</Link>
              <span className="mx-2">›</span>
              <span>Como fazer uma reparação</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Como fazer uma reparação</h1>
            <p className="text-lg text-gray-600">Guia completo para solicitar uma reparação na plataforma Revify.</p>
          </div>

          <div className="prose max-w-none">
            <h2>Processo de reparação</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Search className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Passo 1: Encontrar uma oficina</h3>
                  <p className="text-blue-800 mb-3">Use a nossa ferramenta de pesquisa para encontrar oficinas na sua área:</p>
                  <ul className="list-disc list-inside text-blue-800 space-y-1">
                    <li>Introduza a sua localização ou código postal</li>
                    <li>Selecione o tipo de reparação necessária</li>
                    <li>Compare preços e avaliações</li>
                    <li>Verifique a disponibilidade</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <MapPin className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Passo 2: Descrever o problema</h3>
                  <p className="text-green-800 mb-3">Forneça informações detalhadas sobre:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Tipo de equipamento (smartphone, tablet, laptop, etc.)</li>
                    <li>Marca e modelo</li>
                    <li>Descrição do problema</li>
                    <li>Quando o problema começou</li>
                    <li>Fotos do equipamento (se 'aplicável')</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Calendar className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900 mb-2">Passo 3: Agendar a reparação</h3>
                  <p className="text-purple-800 mb-3">Escolha a opção que melhor se adequa:</p>
                  <ul className="list-disc list-inside text-purple-800 space-y-1">
                    <li><strong>Entrega na oficina:</strong> Leve o equipamento diretamente</li>
                    <li><strong>Recolha ao domicílio:</strong> Agendamos a recolha</li>
                    <li><strong>Reparação ao domicílio:</strong>Para alguns tipos de reparação</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CreditCard className="w-6 h-6 text-yellow-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-yellow-900 mb-2">Passo 4: Orçamento e pagamento</h3>
                  <p className="text-yellow-800 mb-3">Processo transparente:</p>
                  <ul className="list-disc list-inside text-yellow-800 space-y-1">
                    <li>Diagnóstico gratuito na maioria dos casos</li>
                    <li>Orçamento detalhado antes da reparação</li>
                    <li>Aprovação necessária para prosseguir</li>
                    <li>Pagamento seguro online ou na entrega</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Passo 5: Acompanhar o progresso</h3>
                  <p className="text-green-800 mb-3">Mantenha-se informado:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Notificações por email e SMS</li>
                    <li>Acompanhamento em tempo real na plataforma</li>
                    <li>Comunicação direta com a oficina</li>
                    <li>Estimativa de tempo de conclusão</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Tipos de reparação disponíveis</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Smartphones</h3>
                <ul className="text-sm space-y-1">
                  <li>• Substituição de ecrã</li>
                  <li>• Troca de bateria</li>
                  <li>• Reparação de botões</li>
                  <li>• Problemas de software</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">Computadores</h3>
                <ul className="text-sm space-y-1">
                  <li>• Limpeza de vírus</li>
                  <li>• Upgrade de hardware</li>
                  <li>• Instalação de software</li>
                  <li>• Reparação de componentes</li>
                </ul>
              </div>
            </div>

            <h2>Garantias e políticas</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <ul className="space-y-2">
                <li><strong>Garantia:</strong>Todas as reparações incluem garantia mínima de 3 meses</li>
                <li><strong>Peças originais:</strong>Utilizamos apenas peças certificadas</li>
                <li><strong>Diagnóstico:</strong>Gratuito na maioria dos casos</li>
                <li><strong>Cancelamento:</strong>Pode cancelar antes da aprovação do orçamento</li>
              </ul>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda/clientes/criar-conta"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior: Como criar uma conta
              </Link>
              <Link
                href="/ajuda/clientes/acompanhar-reparacao"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >Próximo: Acompanhar reparação<ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
