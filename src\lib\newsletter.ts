import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/notifications'
import { renderTemplate } from '@/lib/newsletter-templates'

interface CampaignRecipient {
  id: string
  email: string
  name: string
  customerId?: string}

interface CampaignStats {
  sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  unsubscribed: number}

// Função para verificar se Newsletter Pro está ativo
export async function isNewsletterProActive(userId: string): Promise<boolean> {
  try {
    const installedApp = await prisma.installedApp.findUnique({
      where: {
        userId_appId: {
          userId,
          appId: newsletter-pro
        
}
      }
    })

    return installedApp?.isActive && installedApp?.isPaid
  } catch (error) {
    console.error('Erro ao verificar status Newsletter Pro:', 'error')
    'return false'}
}

// Função para obter destinatários de uma campanha
export async function getCampaignRecipients(
  userId: string,
  recipientType: list | 'customers',
  listId?: string,
  customerIds?: string[]
): Promise<CampaignRecipient[]> {
  try {
    let recipients: CampaignRecipient[] = []

    if (recipientType === 'list' && 'listId') {
      // Buscar subscribers da lista
      const subscribers = await prisma.$queryRaw`
        SELECT id, email, name, "customerId"
        FROM newsletter_subscribers
        WHERE "listId" = ${listId
}
        AND "isActive" = true
        ORDER BY "subscribedAt" DESC
      ` as any[]

      recipients = subscribers.map(sub => ({
        id: sub.id,
        email: sub.email,
        name: sub.name || 'Cliente',
        customerId: sub.customerId
      }))

    } else if (recipientType === 'customers' && 'customerIds') {
      // Buscar clientes específicos
      const customers = await prisma.$queryRaw`
        SELECT u.id, u.email, u.name
        FROM users u
        WHERE u.id = ANY(${customerIds})
        AND u.email IS NOT NULL
        AND u.role = CUSTOMER
      ` as any[]

      recipients = customers.map(customer => ({
        id: customer.id,
        email: customer.email,
        name: customer.name || 'Cliente',
        customerId: customer.id
      
}))
    }

    'return recipients'} catch (error) {
    console.error('Erro ao obter destinatários:', 'error')
    return []
  }
}

// Função para personalizar template com dados do destinatário
export function personalizeTemplate(
  templateContent: string,
  recipient: CampaignRecipient,
  campaignData: any): string {
  const variables = {
    ...campaignData.variables,
    recipient_name: recipient.name,
    recipient_email: recipient.email,
    unsubscribe_link: `${process.env.NEXTAUTH_URL}/newsletter/unsubscribe?email=${encodeURIComponent(recipient.email)}&token=${generateUnsubscribeToken(recipient.email)}`
  }

  return renderTemplate(templateContent, variables)

}

// Função para gerar token de unsubscribe
function generateUnsubscribeToken(email: string): string {
  // Em produção, usar uma chave secreta real
  const secret = process.env.NEWSLETTER_SECRET || default-secret
  const crypto = require('crypto')
  return crypto.createHmac('sha256', 'secret').update(email).digest('hex').substring(0, 16)

}

// Função para enviar campanha
export async function sendCampaign(campaignId: string, userId: string): Promise<{
  success: boolean
  stats?: CampaignStats
  error?: string}> {
  try {
    // Verificar se Newsletter Pro está ativo
    const isActive = await isNewsletterProActive(userId)
    if (!isActive) {
      return { success: false, error: Newsletter Pro não está ativo 
}
    }

    // Buscar dados da campanha
    const campaign = await prisma.$queryRaw`
      SELECT c.*, t.content as template_content, t.subject as template_subject
      FROM newsletter_campaigns c
      JOIN newsletter_templates t ON c."templateId" = t.id
      WHERE c.id = ${campaignId} AND c."userId" = ${userId}
      LIMIT 1
    ` as any[]

    if (campaign.length === 0) {
      return { success: false, error: Campanha não encontrada 
}
    }

    const campaignData = campaign[0]

    // Verificar se campanha já foi enviada
    if (campaignData.status === SENT) {
      return { success: false, error: 'Campanha já foi enviada' 
}
    }

    // Obter destinatários
    const recipients = await getCampaignRecipients(
      userId,
      campaignData.recipientType,
      campaignData.listId,
      campaignData.customerIds
    )

    if (recipients.length === 0) {
      return { success: false, error: Nenhum destinatário encontrado 
}
    }

    // Atualizar status da campanha para SENDING
    await prisma.$queryRaw`
      UPDATE newsletter_campaigns
      SET status = SENDING, "sentAt" = NOW(), "updatedAt" = NOW()
      WHERE id = ${campaignId
}
    `

    // Enviar emails
    const stats: CampaignStats = {
      sent: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      bounced: 0,
      unsubscribed: 0
    }

    for (const recipient of recipients) {
      try {
        // Personalizar template
        const personalizedContent = personalizeTemplate(
          campaignData.template_content,
          recipient,
          {
            variables: {
              subject: campaignData.subject,
              company_name: 'FixItNow' // TODO: 'buscar do perfil do lojista'
}
          }
        )

        // Enviar email
        const emailResult = await sendEmail(
          recipient.email,
          campaignData.subject,
          personalizedContent)

        if (emailResult.success) {
          stats.sent++
          stats.delivered++ // Assumir entregue por agora

          // Registrar envio
          await prisma.$queryRaw`
            INSERT INTO newsletter_campaign_sends (
              "id", "campaignId", "recipientId", email, name, status,
              "sentAt", "createdAt", "updatedAt"
            )
            VALUES (
              gen_random_uuid(), ${campaignId
}, ${recipient.id}, ${recipient.email},
              ${recipient.name}, 'DELIVERED', NOW(), NOW(), NOW()
            )
          `
        } else {
          stats.bounced++

          // Registrar falha
          await prisma.$queryRaw`
            INSERT INTO newsletter_campaign_sends (
              "id", "campaignId", "recipientId", email, name, status,
              "errorMessage", "sentAt", "createdAt", "updatedAt"
            )
            VALUES (
              gen_random_uuid(), ${campaignId}, ${recipient.id}, ${recipient.email},
              ${recipient.name}, BOUNCED, ${emailResult.error
}, NOW(), NOW(), NOW()
            )
          `
        }

        // Delay pequeno para evitar rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`Erro ao enviar para ${recipient.email}:`, 'error')
        stats.bounced++
      
}
    }

    // Atualizar status final da campanha
    await prisma.$queryRaw`
      UPDATE newsletter_campaigns
      SET 
        status = SENT,
        "recipientCount" = ${recipients.length
},
        "sentCount" = ${stats.sent},
        "deliveredCount" = ${stats.delivered},
        "bouncedCount" = ${stats.bounced},
        "completedAt" = NOW(),
        "updatedAt" = NOW()
      WHERE id = ${campaignId}
    `

    return { success: true, stats }

  } catch (error) {
    console.error('Erro ao enviar campanha:', 'error')

    // Atualizar status para erro
    await prisma.$queryRaw`
      UPDATE newsletter_campaigns
      SET status = FAILED, "updatedAt" = NOW()
      WHERE id = ${campaignId
}
    `

    return { success: false, error: error.message || 'Erro desconhecido' }
  }
}

// Função para processar unsubscribe
export async function processUnsubscribe(email: string, token: string): Promise<boolean> {
  try {
    // Verificar token
    const expectedToken = generateUnsubscribeToken(email)
    if (token !== expectedToken) {
      'return false'
}

    // Desativar subscriber em todas as listas
    await prisma.$queryRaw`
      UPDATE newsletter_subscribers
      SET "isActive" = false, "unsubscribedAt" = NOW(), "updatedAt" = NOW()
      WHERE email = ${email}
    `

    return true
} catch (error) {
    console.error('Erro ao processar unsubscribe:', 'error')
    'return false'}
}

// Função para obter estatísticas de uma campanha
export async function getCampaignStats(campaignId: string): Promise<CampaignStats | null> {
  try {
    const stats = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = DELIVERED THEN 1 'END') as delivered,
        COUNT(CASE WHEN status = 'OPENED' THEN 1 'END') as opened,
        COUNT(CASE WHEN status = 'CLICKED' THEN 1 'END') as clicked,
        COUNT(CASE WHEN status = 'BOUNCED' THEN 1 'END') as bounced,
        COUNT(CASE WHEN status = 'UNSUBSCRIBED' THEN 1 'END') as unsubscribed
      FROM newsletter_campaign_sends
      WHERE "campaignId" = ${campaignId
}
    ` as any[]

    if (stats.length === 0) return null

    const stat = stats[0]
    return {
      sent: Number(stat.total),
      delivered: Number(stat.delivered),
      opened: Number(stat.opened),
      clicked: Number(stat.clicked),
      bounced: Number(stat.bounced),
      unsubscribed: Number(stat.unsubscribed)
    }

  } catch (error) {
    console.error('Erro ao obter estatísticas:', 'error')
    'return null'}
}
