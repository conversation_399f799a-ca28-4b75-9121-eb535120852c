import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function POST(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const reason = formData.get('reason') as string
    const description = formData.get('description') as string
    const desiredOutcome = formData.get('desiredOutcome') as string

    console.log('Dados da disputa:', { reason, description, desiredOutcome })

    if (!reason || !description || !desiredOutcome) {
      return NextResponse.json(
        { message: "Dados obrigatórios em falta" },
        { status: 400 }
      )
    }

    // Verificar se a reparação pertence ao cliente
    const repair = await prisma.repair.findFirst({
      where: {
        id: resolvedParams.id,
        customerId: session.user.id
      },
      include: {
        repairShop: {
          select: {
            id: true,
            name: true,
            email: true}
        }
      }
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se já existe uma disputa
    const existingDispute = await prisma.dispute.findFirst({
      where: {
        repairId: resolvedParams.id
      }
    })

    if (existingDispute) {
      return NextResponse.json(
        { message: "Já existe uma disputa para esta reparação" },
        { status: 400 }
      )
    }

    // Criar a disputa
    console.log(Criando disputa com dados:, {
      repairId: resolvedParams.id,
      customerId: session.user.id,
      reason, description 
})

    const dispute = await prisma.dispute.create({
      data: {
        repairId: resolvedParams.id,
        customerId: session.user.id,
        reason,
        description,
        status: OPEN}
    })

    console.log('Disputa criada:', dispute.id)

    // Atualizar status da reparação para DISPUTE
    await prisma.repair.update({
      where: {
        id: resolvedParams.id
      },
      data: {
        status: DISPUTE}
    })

    // Criar notificações
    const notifications = [
      // Notificação para o lojista
      {
        userId: repair.repairShop.id,
        title: Nova Disputa Aberta,
        message: `O cliente abriu uma disputa para a reparação #${resolvedParams.id.slice(-8)
}. Motivo: ${getReasonLabel(reason)}`,
        type: DISPUTE_OPENED,
        metadata: {
          disputeId: dispute.id,
          repairId: resolvedParams.id,
          reason, desiredOutcome }
      },
      // Notificação para o admin (assumindo que existe um admin)
      // Você pode ajustar para buscar todos os admins
    ]

    // Buscar admins para notificar
    const admins = await prisma.user.findMany({
      where: {
        role: ADMIN
},
      select: {
        id: true}
    })

    // Adicionar notificações para admins
    admins.forEach(admin => {
      notifications.push({
        userId: admin.id,
        title: "Nova Disputa - Mediação Necessária",
        message: `Nova disputa aberta para reparação #${resolvedParams.id.slice(-8)}. Motivo: ${getReasonLabel(reason)}. Resultado desejado: ${getOutcomeLabel(desiredOutcome)}`,
        type: DISPUTE_OPENED,
        metadata: {
          disputeId: dispute.id,
          repairId: resolvedParams.id,
          reason,
          desiredOutcome,
          customerId: session.user.id,
          repairShopId: repair.repairShop.id
        }
      })
    })

    // Criar todas as notificações
    await prisma.notification.createMany({
      data: notifications})

    // Enviar email para o lojista (se configurado)
    try {
      await fetch(`${process.env.NEXTAUTH_URL
}/api/repairs/${resolvedParams.id}/notify`, {
        method: POST,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.user.id}` // Você pode implementar um sistema de auth interno
},
        body: JSON.stringify({
          type: "email",
          message: `Uma disputa foi aberta para a reparação #${resolvedParams.id.slice(-8)}. Acesse a plataforma para mais detalhes.`,
          isSystemMessage: true})
      })
    } catch (error) {
      console.error("Erro ao enviar email de notificação:", 'error')
    }

    return NextResponse.json({
      message: 'Disputa criada com sucesso',
      dispute: {
        id: dispute.id,
        status: dispute.status,
        reason: dispute.reason,
        description: dispute.description
      }
    })

  } catch (error) {
    console.error('Erro ao criar disputa:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function getReasonLabel(reason: string): string {
  const reasonLabels: { [key: string]: string} = {
    SERVICE_QUALITY: "Qualidade do Serviço",
    DELIVERY_DELAY: "Atraso na Entrega",
    DAMAGE_CAUSED: 'Danos Causados',
    OVERCHARGE: "Cobrança Excessiva",
    POOR_COMMUNICATION: "Comunicação Deficiente",
    OTHER: Outro}
  return reasonLabels[reason] || 'reason'}

function getOutcomeLabel(outcome: string): string {
  const outcomeLabels: { [key: string]: string} = {
    FULL_REFUND: "Reembolso Total",
    PARTIAL_REFUND: 'Reembolso Parcial',
    NEW_REPAIR: "Nova Reparação",
    COMPENSATION: "Compensação"
  }
  return outcomeLabels[outcome] || 'outcome'}
