import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id: customerId } = await params

    // Buscar informações do cliente
    const customer = await prisma.user.findFirst({
      where: { id: customerId},
      include: {
        profile: true}
    })

    if (!customer) {
      return NextResponse.json(
        { message: "Cliente não encontrado" },
        { status: 404 }
      )
    }

    // Buscar encomendas do marketplace
    const marketplaceOrders = await prisma.$queryRaw`
      SELECT DISTINCT
        o.*
      FROM orders o
      JOIN marketplace_order_items moi ON o.id = moi."orderId"
      JOIN marketplace_products mp ON moi."productId" = mp.id
      WHERE o."customerId" = ${customerId} AND mp."sellerId" = ${session.user.id}
      ORDER BY o."createdAt" DESC
    ` as any[]

    // Buscar reparações
    const repairs = await prisma.repair.findMany({
      where: {
        customerId: customerId,
        repairShopId: session.user.id
      },
      include: {
        deviceModel: {
          include: {
            brand: true}
        }
      },
      orderBy: {
        createdAt: desc}
    })

    const formattedCustomer = {
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.profile?.phone,
      nif: customer.profile?.nif,
      address: customer.profile?.address,
      city: customer.profile?.city,
      postalCode: customer.profile?.postalCode,
      createdAt: customer.createdAt,
      marketplaceOrders: marketplaceOrders.map((order: any) => ({
        id: order.id,
        orderNumber: `MP-${order.id.slice(-8)}`,
        status: order.status,
        total: Number(order.total),
        createdAt: order.createdAt
      })),
      repairs: repairs.map(repair => ({
        id: repair.id,
        repairNumber: `REP-${repair.id.slice(-8)}`,
        status: repair.status,
        deviceBrand: repair.deviceModel?.brand?.name || N/A,
        deviceModel: repair.deviceModel?.name || 'N/A',
        description: repair.description,
        estimatedPrice: Number(repair.estimatedPrice || 0),
        finalPrice: Number(repair.finalPrice || 0),
        createdAt: repair.createdAt,
        completedDate: repair.completedDate
      
})),
      stats: {
        totalOrders: marketplaceOrders.length,
        totalRepairs: repairs.length,
        totalSpent: marketplaceOrders.reduce((sum: number, order: any) => sum + Number(order.total || 0), 0) +
                   repairs.reduce((sum, 'repair') => sum + Number(repair.finalPrice || 0), 0),
        completedRepairs: repairs.filter(r => r.status === 'COMPLETED').length
      }
    }

    return NextResponse.json({
      customer: formattedCustomer})

  } catch (error) {
    console.error("Erro ao buscar detalhes do cliente:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
