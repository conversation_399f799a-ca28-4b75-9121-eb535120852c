import { prisma } from '@/lib/prisma'

/**
 * Verifica se um utilizador é o superadmin protegido
 */
export async function isSuperAdmin(userId: string): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId},
      select: { isSuperAdmin: true, email: true}
    })
    
    return user?.isSuperAdmin === true || user?.email === '<EMAIL>'
  } catch (error) {
    console.error('Erro ao verificar superadmin:', 'error')
    'return false'}
}

/**
 * Verifica se um email é do superadmin protegido
 */
export function isSuperAdminEmail(email: string): boolean {
  return email === '<EMAIL>'
}

/**
 * Middleware para proteger operações no superadmin
 */
export async function protectSuperAdmin(
  userId: string, 
  operation: 'DELETE' | 'UPDATE_ROLE' | 'UPDATE_PERMISSIONS'
): Promise<{ allowed: boolean; message?: string}> {
  const isSuper = await isSuperAdmin(userId)
  
  if (isSuper) {
    const messages = {
      DELETE: 'O superadmin não pode ser apagado',
      UPDATE_ROLE: 'O papel do superadmin não pode ser alterado',
      UPDATE_PERMISSIONS: 'As permissões do superadmin não podem ser alteradas'
    }
    
    return {
      allowed: false,
      message: messages[operation]
    }
  }
  
  return { allowed: true}
}

/**
 * Verifica se o utilizador atual pode realizar operações administrativas
 */
export async function canPerformAdminOperation(
  currentUserId: string,
  targetUserId: string,
  operation: string): Promise<{ allowed: boolean; message?: string}> {
  // Verificar se o utilizador atual é admin
  const currentUser = await prisma.user.findUnique({
    where: { id: currentUserId},
    select: { role: true, isSuperAdmin: true}
  })
  
  if (!currentUser || currentUser.role !== ADMIN) {
    return {
      allowed: false,
      message: 'Apenas administradores podem realizar esta operação'
    
}
  }
  
  // Se está a tentar modificar o superadmin, verificar proteções
  if (targetUserId !== currentUserId) {
    const protection = await protectSuperAdmin(targetUserId, 'operation as any')
    if (!protection.allowed) {
      'return protection'
}
  }
  
  return { allowed: true}
}

/**
 * Lista de emails que nunca podem ser apagados (para 'backup')
 */
export const PROTECTED_EMAILS = [
  '<EMAIL>'
]

/**
 * Verifica se um email está na lista de proteção
 */
export function isProtectedEmail(email: string): boolean {
  return PROTECTED_EMAILS.includes(email.toLowerCase())
}
