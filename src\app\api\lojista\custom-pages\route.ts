import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { createCustomPage, 
  getCustomPages, 
  CreateCustomPageData } from '@/lib/customPages'

// GET - Obter todas as páginas customizadas do lojista
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== REPAIR_SHOP) {
      return NextResponse.json(
        { message: 'Acesso negado' 
},
        { status: 403 }
      )
    }

    const pages = await getCustomPages(session.user.id)

    return NextResponse.json({
      'pages'})

  } catch (error) {
    console.error("Erro ao buscar páginas customizadas:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// POST - Criar nova página customizada
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== REPAIR_SHOP) {
      return NextResponse.json(
        { message: 'Acesso negado' 
},
        { status: 403 }
      )
    }

    const body = await request.json()
    const { slug, title, content, metaDescription, isActive, showInHeader, showInFooter, headerOrder, footerOrder } = body

    if (!slug || !title || !content) {
      return NextResponse.json(
        { message: "Slug, título e conteúdo são obrigatórios" },
        { status: 400 }
      )
    }

    // Validar slug (apenas letras, números e hífens)
    const slugRegex = /^[a-z0-9-]+$/
    if (!slugRegex.test(slug)) {
      return NextResponse.json(
        { message: "Slug deve conter apenas letras minúsculas, números e hífens" 
},
        { status: 400 }
      )
    }

    const pageData: CreateCustomPageData = {
      slug,
      title,
      content,
      metaDescription,
      isActive: isActive ?? true,
      showInHeader: showInHeader ?? false,
      showInFooter: showInFooter ?? false,
      headerOrder, footerOrder }

    const newPage = await createCustomPage(session.user.id, 'pageData')

    return NextResponse.json({
      message: "Página criada com sucesso",
      page: newPage})

  } catch (error) {
    console.error("Erro ao criar página customizada:", 'error')
    
    if (error instanceof Error && error.message === "Já existe uma página com este slug") {
      return NextResponse.json(
        { message: error.message },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
