{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start"}, "dependencies": {"@neondatabase/serverless": "^0.9.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@types/node": "20.17.6", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vercel/analytics": "^1.4.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-kit": "^0.22.8", "drizzle-orm": "^0.31.4", "drizzle-zod": "^0.5.1", "lucide-react": "^0.400.0", "next": "15.1.3", "next-auth": "5.0.0-beta.25", "postcss": "^8.4.49", "prettier": "^3.4.2", "prop-types": "^15.8.1", "react": "19.0.0", "react-dom": "19.0.0", "server-only": "^0.0.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2", "zod": "^3.24.1"}, "prettier": {"arrowParens": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "none"}}