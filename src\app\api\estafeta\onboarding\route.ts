import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'COURIER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { phone, 
      address, 
      city, 
      postalCode, 
      vehicleType, 
      vehiclePlate, 
      drivingLicense, 
      availability } = await request.json()

    // Atualizar perfil do usuário
    await prisma.profile.upsert({
      where: {
        userId: session.user.id
      },
      update: {
        phone,
        address: `${address}, ${city}, ${postalCode}`.trim(),
        vehicleType,
        vehiclePlate,
        drivingLicense, availability },
      create: {
        userId: session.user.id,
        phone,
        address: `${address}, ${city}, ${postalCode}`.trim(),
        vehicleType,
        vehiclePlate,
        drivingLicense, availability }
    })

    // Criar notificação de boas-vindas
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        title: "Perfil de Estafeta Configurado!",
        message: "Bem-vindo à equipa Revify! Seu perfil foi configurado com sucesso.",
        type: WELCOME}
    })

    return NextResponse.json({
      message: "Perfil de estafeta atualizado com sucesso"
    })

  } catch (error) {
    console.error("Erro no onboarding do estafeta:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
