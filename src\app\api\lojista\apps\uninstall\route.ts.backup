import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { appId } = await request.json()

    if (!appId) {
      return NextResponse.json(
        { message: 'ID da app é obrigatório' },
        { status: 400 }
      )
    }

    // Desinstalar a app (marcar como inativa)
    const result = await prisma.$queryRaw`
      UPDATE installed_apps
      SET "isActive" = false, "uninstalledAt" = NOW()
      WHERE "userId" = ${session.user.id}
      AND "appId" = ${appId}
      AND "isActive" = true
    `

    // Verificar se alguma linha foi afetada
    const check = await prisma.$queryRaw`
      SELECT COUNT(*) as count FROM installed_apps
      WHERE "userId" = ${session.user.id}
      AND "appId" = ${appId}
      AND "isActive" = false
      AND "uninstalledAt" IS NOT NULL
    `

    if (!check || check[0]?.count === 0) {
      return NextResponse.json(
        { message: 'App não encontrada ou já desinstalada' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'App desinstalada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao desinstalar app:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
