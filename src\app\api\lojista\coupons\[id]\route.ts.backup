import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    // Verificar se o cupão existe e pertence ao lojista
    const existingCoupon = await prisma.coupon.findFirst({
      where: {
        id: resolvedParams.id,
        sellerId: session.user.id
      }
    })

    if (!existingCoupon) {
      return NextResponse.json(
        { message: 'Cupão não encontrado' },
        { status: 404 }
      )
    }

    // Validações
    if (data.type === 'PERCENTAGE' && (data.value < 0 || data.value > 100)) {
      return NextResponse.json(
        { message: 'Percentagem deve estar entre 0 e 100' },
        { status: 400 }
      )
    }

    if (data.type === 'FIXED_AMOUNT' && data.value < 0) {
      return NextResponse.json(
        { message: 'Valor fixo deve ser positivo' },
        { status: 400 }
      )
    }

    // Verificar se o código já existe (exceto o próprio cupão)
    if (data.code !== existingCoupon.code) {
      const codeExists = await prisma.coupon.findUnique({
        where: { code: data.code }
      })

      if (codeExists) {
        return NextResponse.json(
          { message: 'Código de cupão já existe' },
          { status: 400 }
        )
      }
    }

    // Atualizar cupão
    const updatedCoupon = await prisma.coupon.update({
      where: { id: resolvedParams.id },
      data: {
        code: data.code.toUpperCase(),
        name: data.name,
        description: data.description || null,
        type: data.type,
        value: data.value,
        minOrderValue: data.minOrderValue || null,
        maxOrderValue: data.maxOrderValue || null,
        maxDiscountValue: data.maxDiscountValue || null,
        allowedCountries: data.allowedCountries || ['PT'],
        usageLimit: data.usageLimit || null,
        userUsageLimit: data.userUsageLimit || null,
        startsAt: data.startsAt ? new Date(data.startsAt) : null,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        isActive: data.isActive !== false
      }
    })

    return NextResponse.json(updatedCoupon)

  } catch (error) {
    console.error('Erro ao atualizar cupão:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o cupão existe e pertence ao lojista
    const existingCoupon = await prisma.coupon.findFirst({
      where: {
        id: resolvedParams.id,
        sellerId: session.user.id
      }
    })

    if (!existingCoupon) {
      return NextResponse.json(
        { message: 'Cupão não encontrado' },
        { status: 404 }
      )
    }

    // Verificar se o cupão foi usado
    if (existingCoupon.usageCount > 0) {
      return NextResponse.json(
        { message: 'Não é possível eliminar cupão que já foi usado' },
        { status: 400 }
      )
    }

    // Eliminar cupão
    await prisma.coupon.delete({
      where: { id: resolvedParams.id }
    })

    return NextResponse.json({ message: 'Cupão eliminado com sucesso' })

  } catch (error) {
    console.error('Erro ao eliminar cupão:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
