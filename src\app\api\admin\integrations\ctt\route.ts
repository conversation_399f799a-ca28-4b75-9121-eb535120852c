import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações do CTT
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: [
            cttApiKey,
            'cttClientId',
            'cttClientSecret',
            'cttEnvironment',
            'cttEnabled'
          ]
        
}
      }
    })

    const config: any = {}
    settings.forEach(setting => {
      config[setting.key] = setting.value
    })

    return NextResponse.json({
      apiKey: config.cttApiKey || '',
      clientId: config.cttClientId || '',
      clientSecret: config.cttClientSecret || '',
      environment: config.cttEnvironment || 'sandbox',
      enabled: config.cttEnabled === 'true'
    })

  } catch (error) {
    console.error('Erro ao buscar configuração CTT:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { apiKey,
      clientId,
      clientSecret,
      environment,
      enabled } = await request.json()

    // Validar campos obrigatórios
    if (enabled && (!apiKey || !clientId || !clientSecret)) {
      return NextResponse.json(
        { message: API Key, Client ID e Client Secret são obrigatórios 
},
        { status: 400 }
      )
    }

    // Atualizar configurações
    const settingsToUpdate = [
      { key: cttApiKey, value: apiKey ||  
},
      { key: cttClientId, value: clientId || '' },
      { key: cttClientSecret, value: clientSecret || '' },
      { key: cttEnvironment, value: environment || 'sandbox' },
      { key: cttEnabled, value: enabled ? 'true' : 'false' }
    ]

    for (const 'setting of settingsToUpdate') {
      await prisma.systemSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
          description: `CTT ${setting.key.replace('ctt', '')}`
        }
      })
    }

    return NextResponse.json({
      message: 'Configuração CTT atualizada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar configuração CTT:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
