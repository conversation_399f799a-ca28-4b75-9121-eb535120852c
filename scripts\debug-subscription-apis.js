const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugSubscriptionAPIs() {
  try {
    console.log('🔍 Debugando APIs de subscription...\n')

    const userId = 'cmdoeycm10000tbpshto54h0x' // ID do lojista teste

    console.log(`👤 Testando para usuário: ${userId}\n`)

    // 1. Testar API /api/lojista/subscription/status
    console.log('1️⃣ API: /api/lojista/subscription/status')
    console.log('   Query usada pela função checkSubscriptionAccess:')
    
    const statusQuery = await prisma.$queryRaw`
      SELECT 
        s.*,
        sp.name as plan_name,
        sp.features,
        sp."maxProducts",
        sp."maxRepairs"
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    console.log(`   Resultado: ${statusQuery.length} registros`)
    if (statusQuery.length > 0) {
      const sub = statusQuery[0]
      console.log(`   ✅ Status: ${sub.status}, Plano: ${sub.plan_name}`)
      console.log(`   ✅ Retornaria: hasActiveSubscription: ${sub.status === 'ACTIVE'}`)
    } else {
      console.log('   ❌ Nenhum registro encontrado')
    }

    console.log('\n2️⃣ API: /api/lojista/subscription')
    console.log('   Query usada pela página de subscriptions:')
    
    const subscriptionQuery = await prisma.$queryRaw`
      SELECT
        s.*,
        sp.name as plan_name,
        sp.features as plan_features,
        sp."monthlyPrice" as plan_monthly_price,
        sp."yearlyPrice" as plan_yearly_price
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      AND s.status IN ('ACTIVE', 'INCOMPLETE')
      ORDER BY s."createdAt" DESC
      LIMIT 1
    `

    console.log(`   Resultado: ${subscriptionQuery.length} registros`)
    if (subscriptionQuery.length > 0) {
      const sub = subscriptionQuery[0]
      console.log(`   ✅ Status: ${sub.status}, Plano: ${sub.plan_name}`)
      console.log(`   ✅ Preço mensal: €${sub.plan_monthly_price}`)
      
      // Buscar pagamentos
      const payments = await prisma.subscriptionPayment.findMany({
        where: { subscriptionId: sub.id },
        orderBy: { createdAt: 'desc' }
      })
      
      console.log(`   💳 Pagamentos encontrados: ${payments.length}`)
      payments.forEach((payment, index) => {
        console.log(`      ${index + 1}. Status: ${payment.status}, Valor: €${payment.amount}`)
      })
    } else {
      console.log('   ❌ Nenhum registro encontrado')
    }

    console.log('\n3️⃣ API: /api/lojista/subscription (para upgrade page)')
    console.log('   Esta API deveria retornar a subscription atual para mostrar qual plano está ativo')
    
    if (subscriptionQuery.length > 0) {
      const sub = subscriptionQuery[0]
      console.log(`   ✅ Plano atual: ${sub.plan_name} (ID: ${sub.planId})`)
      console.log(`   ✅ A página de upgrade deveria marcar este plano como "Plano Atual"`)
    }

    console.log('\n4️⃣ Verificando se há problemas na estrutura dos dados...')
    
    // Verificar se há subscriptions com outros status
    const allSubscriptions = await prisma.$queryRaw`
      SELECT s.*, sp.name as plan_name
      FROM subscriptions s
      JOIN subscription_plans sp ON s."planId" = sp.id
      WHERE s."userId" = ${userId}
      ORDER BY s."createdAt" DESC
    `
    
    console.log(`   📊 Total de subscriptions do usuário: ${allSubscriptions.length}`)
    allSubscriptions.forEach((sub, index) => {
      console.log(`      ${index + 1}. Status: ${sub.status}, Plano: ${sub.plan_name}, Criado: ${sub.createdAt}`)
    })

    // Verificar se o plano existe e está ativo
    if (subscriptionQuery.length > 0) {
      const sub = subscriptionQuery[0]
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: sub.planId }
      })
      
      console.log(`\n5️⃣ Verificando plano ${sub.planId}:`)
      if (plan) {
        console.log(`   ✅ Plano encontrado: ${plan.name}`)
        console.log(`   ✅ Ativo: ${plan.isActive}`)
        console.log(`   ✅ Preço mensal: €${plan.monthlyPrice}`)
      } else {
        console.log(`   ❌ Plano não encontrado!`)
      }
    }

  } catch (error) {
    console.error('❌ Erro no debug:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugSubscriptionAPIs()
