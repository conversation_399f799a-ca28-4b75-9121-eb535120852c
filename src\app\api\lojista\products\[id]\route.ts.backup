import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    const resolvedParams = await params

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const product = await prisma.marketplaceProduct.findUnique({
      where: {
        id: resolvedParams.id,
        sellerId: session.user.id
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        },
        deviceModel: {
          select: {
            name: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)

  } catch (error) {
    console.error('Erro ao buscar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()
    const {
      name,
      description,
      price,
      originalPrice,
      condition,
      categoryId,
      brandId,
      deviceModelId,
      stock,
      isActive
    } = data

    // Verificar se o produto pertence ao lojista
    const existingProduct = await prisma.marketplaceProduct.findFirst({
      where: {
        id: resolvedParams.id,
        sellerId: session.user.id
      }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    const updatedProduct = await prisma.marketplaceProduct.update({
      where: {
        id: resolvedParams.id
      },
      data: {
        name,
        description,
        price: price ? parseFloat(price) : undefined,
        originalPrice: originalPrice ? parseFloat(originalPrice) : undefined,
        condition,
        categoryId,
        brandId,
        deviceModelId: deviceModelId || null,
        stock: stock ? parseInt(stock) : undefined,
        isActive: Boolean(isActive)
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        brand: {
          select: {
            name: true
          }
        },
        deviceModel: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Produto atualizado com sucesso',
      product: {
        id: updatedProduct.id,
        name: updatedProduct.name,
        price: Number(updatedProduct.price),
        originalPrice: updatedProduct.originalPrice ? Number(updatedProduct.originalPrice) : null,
        condition: updatedProduct.condition,
        stock: updatedProduct.stock,
        isActive: updatedProduct.isActive,
        images: updatedProduct.images,
        category: updatedProduct.category,
        brand: updatedProduct.brand,
        deviceModel: updatedProduct.deviceModel
      }
    })

  } catch (error) {
    console.error('Erro ao atualizar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o produto pertence ao lojista
    const existingProduct = await prisma.marketplaceProduct.findFirst({
      where: {
        id: resolvedParams.id,
        sellerId: session.user.id
      }
    })

    if (!existingProduct) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    await prisma.marketplaceProduct.delete({
      where: {
        id: resolvedParams.id
      }
    })

    return NextResponse.json({
      message: 'Produto eliminado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao eliminar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
