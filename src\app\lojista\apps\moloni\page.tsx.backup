'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Settings, FileText, CheckCircle, AlertCircle, Loader } from 'lucide-react'
import Link from 'next/link'

interface MoloniConfig {
  clientId: string
  clientSecret: string
  username: string
  password: string
  companyId: string
  documentSetId: string
  autoIssue: boolean
  isConnected: boolean
}

interface Invoice {
  id: string
  number: string
  customerName: string
  amount: number
  status: 'PENDING' | 'ISSUED' | 'SENT' | 'PAID'
  type: 'SALE' | 'REPAIR'
  createdAt: string
  moloniId?: string
}

export default function MoloniAppPage() {
  const { data: session } = useSession()
  const [config, setConfig] = useState<MoloniConfig>({
    clientId: '',
    clientSecret: '',
    username: '',
    password: '',
    companyId: '',
    documentSetId: '',
    autoIssue: false,
    isConnected: false
  })
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'config' | 'invoices'>('config')

  useEffect(() => {
    fetchConfig()
    fetchInvoices()
  }, [])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/lojista/apps/moloni/config')
      if (response.ok) {
        const data = await response.json()
        setConfig(data.config)
      }
    } catch (error) {
      console.error('Erro ao buscar configuração:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchInvoices = async () => {
    try {
      const response = await fetch('/api/lojista/apps/moloni/invoices')
      if (response.ok) {
        const data = await response.json()
        setInvoices(data.invoices)
      }
    } catch (error) {
      console.error('Erro ao buscar faturas:', error)
    }
  }

  const saveConfig = async () => {
    setIsSaving(true)
    try {
      const response = await fetch('/api/lojista/apps/moloni/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        const data = await response.json()
        setConfig(data.config)
        alert('Configuração salva com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar configuração')
      }
    } catch (error) {
      console.error('Erro ao salvar configuração:', error)
      alert('Erro ao salvar configuração')
    } finally {
      setIsSaving(false)
    }
  }

  const testConnection = async () => {
    try {
      const response = await fetch('/api/lojista/apps/moloni/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: config.clientId,
          clientSecret: config.clientSecret,
          username: config.username,
          password: config.password
        })
      })

      if (response.ok) {
        alert('Conexão testada com sucesso!')
        setConfig(prev => ({ ...prev, isConnected: true }))
      } else {
        const error = await response.json()
        alert(error.message || 'Erro na conexão')
        setConfig(prev => ({ ...prev, isConnected: false }))
      }
    } catch (error) {
      console.error('Erro ao testar conexão:', error)
      alert('Erro ao testar conexão')
    }
  }

  const issueInvoice = async (invoiceId: string) => {
    try {
      const response = await fetch('/api/lojista/apps/moloni/issue-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ invoiceId })
      })

      if (response.ok) {
        alert('Fatura emitida com sucesso!')
        fetchInvoices()
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao emitir fatura')
      }
    } catch (error) {
      console.error('Erro ao emitir fatura:', error)
      alert('Erro ao emitir fatura')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/appstore"
                className="inline-flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Voltar
              </Link>
              <div className="flex items-center">
                <FileText className="w-6 h-6 mr-2 text-blue-600" />
                <h1 className="text-2xl font-bold text-black">Moloni</h1>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {config.isConnected ? (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Conectado
                </div>
              ) : (
                <div className="flex items-center text-red-600">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  Desconectado
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('config')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'config'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Settings className="w-4 h-4 inline mr-2" />
                Configuração
              </button>
              <button
                onClick={() => setActiveTab('invoices')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'invoices'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FileText className="w-4 h-4 inline mr-2" />
                Faturas
              </button>
            </nav>
          </div>
        </div>

        {/* Configuration Tab */}
        {activeTab === 'config' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuração Moloni</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Client ID
                </label>
                <input
                  type="text"
                  value={config.clientId}
                  onChange={(e) => setConfig({...config, clientId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="Seu Client ID da API Moloni"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Client Secret
                </label>
                <input
                  type="password"
                  value={config.clientSecret}
                  onChange={(e) => setConfig({...config, clientSecret: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="••••••••••••••••"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Username Moloni
                </label>
                <input
                  type="text"
                  value={config.username}
                  onChange={(e) => setConfig({...config, username: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password Moloni
                </label>
                <input
                  type="password"
                  value={config.password}
                  onChange={(e) => setConfig({...config, password: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="••••••••"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ID da Empresa
                </label>
                <input
                  type="text"
                  value={config.companyId}
                  onChange={(e) => setConfig({...config, companyId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="12345"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ID da Série de Documentos
                </label>
                <input
                  type="text"
                  value={config.documentSetId}
                  onChange={(e) => setConfig({...config, documentSetId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  placeholder="12345"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.autoIssue}
                  onChange={(e) => setConfig({...config, autoIssue: e.target.checked})}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">
                  Emitir faturas automaticamente
                </span>
              </label>
            </div>

            <div className="mt-6 flex space-x-4">
              <button
                onClick={testConnection}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Testar Conexão
              </button>
              <button
                onClick={saveConfig}
                disabled={isSaving}
                className="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
              >
                {isSaving ? 'Salvando...' : 'Salvar Configuração'}
              </button>
            </div>
          </div>
        )}

        {/* Invoices Tab */}
        {activeTab === 'invoices' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Faturas Pendentes</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Número
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoices.map((invoice) => (
                    <tr key={invoice.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {invoice.number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.customerName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        €{invoice.amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.type === 'SALE' ? 'Venda' : 'Reparação'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          invoice.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                          invoice.status === 'ISSUED' ? 'bg-blue-100 text-blue-800' :
                          invoice.status === 'SENT' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {invoice.status === 'PENDING' ? 'Pendente' :
                           invoice.status === 'ISSUED' ? 'Emitida' :
                           invoice.status === 'SENT' ? 'Enviada' : 'Paga'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {invoice.status === 'PENDING' && (
                          <button
                            onClick={() => issueInvoice(invoice.id)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Emitir
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {invoices.length === 0 && (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nenhuma fatura pendente
                  </h3>
                  <p className="text-gray-500">
                    As faturas aparecerão aqui quando houver vendas ou reparações concluídas.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
