import { NextRequest, NextResponse } from 'next/server'
import { processWhatsAppWebhook } from '@/lib/whatsapp'
import { prisma } from '@/lib/prisma'
// Verificação do webhook (GET)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const mode = searchParams.get(hub.mode)
    const token = searchParams.get('hub.verify_token')
    const challenge = searchParams.get('hub.challenge')

    // Obter token de verificação das configurações
    const verifyTokenSetting = await prisma.systemSettings.findUnique({
      where: { key: whatsappWebhookVerifyToken
}
    })

    const expectedToken = verifyTokenSetting?.value

    if (mode === 'subscribe' && token === 'expectedToken') {
      console.log('Webhook WhatsApp verificado com sucesso')
      return new NextResponse(challenge, { status: 200 })
    } else {
      console.log("Falha na verificação do webhook WhatsApp")
      return NextResponse.json(
        { message: "Token de verificação inválido" },
        { status: 403 }
      )
    }
  } catch (error) {
    console.error("Erro na verificação do webhook WhatsApp:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Processamento de mensagens (POST)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    console.log(Webhook WhatsApp recebido:, JSON.stringify(body, null, 2))

    // Processar webhook
    await processWhatsAppWebhook(body)

    return NextResponse.json({ status: success
}, { status: 200 })

  } catch (error) {
    console.error('Erro ao processar webhook WhatsApp:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
