'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import AutoTranslate, { AutoTranslateList } from '@/components/ui/AutoTranslate'
import LanguageSelector from '@/components/LanguageSelector'
import AutoTranslate from '@/components/ui/AutoTranslate'

// Exemplo de dados de produtos
const produtosExemplo = [
  {
    id: '1<AutoTranslate text=", nome:" />Reparação de Ecrã iPhone',
    descricao: 'Substituição completa do ecrã danificado do seu iPhone',
    preco: 89.99,
    categoria: 'Smartphones'
  },
  {
    id: '2<AutoTranslate text=",  nome:" />Limpeza de Vírus PC',
    descricao: 'Remoção completa de vírus e malware do seu computador<AutoTranslate text=", preco: 45.00, categoria:" />Computadores'
  },
  {
    id: '3<AutoTranslate text=", nome:" />Substituição Bateria Portátil',
    descricao: 'Troca da bateria do portátil por uma nova de alta qualidade<AutoTranslate text=", preco: 120.00, categoria:" />Portáteis'
  }
]

/**
 * Exemplo completo de como usar o sistema de traduções otimizado
 * Mostra diferentes formas de implementar traduções automáticas
 */
export default function TranslationExample() {
  const { currentLanguage, changeLanguage, t, tSync, isTranslating } = useTranslation()
  const [textoTraduzido, setTextoTraduzido] = useState('')

  // Exemplo de tradução manual
  useEffect(() => {
    const traduzirTexto = async () => {
      const resultado = await t('Bem-vindo à nossa plataforma de reparações!')
      setTextoTraduzido(resultado)
    }
    traduzirTexto()
  }, [currentLanguage, t])

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header com seletor de idioma */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">
          <AutoTranslate text=<AutoTranslate text="Exemplos de Tradução Automática" /> as="span" />
        </h1>
        <LanguageSelector />
      </div>

      {/* Indicador de tradução ativa */}
      {isTranslating && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-blue-800">
              <AutoTranslate text="Traduzindo conteúdo..." />
            </span>
          </div>
        </div>
      )}

      {/* Informações do idioma atual */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-2">
          <AutoTranslate text="Idioma Atual" />
        </h2>
        <p>
          <AutoTranslate text="Idioma selecionado" />: <strong>{currentLanguage.toUpperCase()}</strong>
        </p>
        <p className="text-sm text-gray-600 mt-1">
          <AutoTranslate text="Todas as traduções são feitas automaticamente via DeepL API" />
        </p>
      </div>

      {/* Exemplo 1: Tradução simples com componente */}
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          <AutoTranslate text="1. Tradução Simples com Componente" />
        </h2>
        <div className="space-y-2">
          <p>
            <AutoTranslate text="Este texto é traduzido automaticamente!" className="text-blue-600" />
          </p>
          <p>
            <AutoTranslate 
              text="Texto com fallback" 
              fallback="Fallback se a tradução falhar"
              className="text-green-600" 
            />
          </p>
        </div>
      </div>

      {/* Exemplo 2: Tradução manual com hook */}
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          <AutoTranslate text="2. Tradução Manual com Hook" />
        </h2>
        <div className="bg-gray-100 p-4 rounded">
          <p className="font-medium">
            <AutoTranslate text="Texto traduzido manualmente" />:
          </p>
          <p className="text-gray-700 mt-2">{textoTraduzido}</p>
        </div>
      </div>

      {/* Exemplo 3: Lista de produtos com tradução automática */}
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          <AutoTranslate text="3. Lista de Produtos com Tradução Automática" />
        </h2>
        
        <AutoTranslateList
          items={produtosExemplo}
          textExtractor={(produto) => produto.nome}
          renderItem={(produto, nomeTrauzido, index) => (
            <div key={produto.id} className="border-b border-gray-200 py-4 last:border-b-0">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{nomeTrauzido}</h3>
                  <p className="text-gray-600 mt-1">
                    <AutoTranslate text={produto.descricao} />
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    <AutoTranslate text="Categoria" />: <AutoTranslate text={produto.categoria} />
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-green-600">€{produto.preco}</p>
                  <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    <AutoTranslate text="Solicitar" />
                  </button>
                </div>
              </div>
            </div>
          )}
          className="space-y-0"
          loadingComponent={
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">
                <AutoTranslate text="Traduzindo produtos..." />
              </p>
            </div>
          }
        />
      </div>

      {/* Exemplo 4: Diferentes elementos HTML */}
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          <AutoTranslate text="4. Diferentes Elementos HTML" />
        </h2>
        <div className="space-y-4">
          <AutoTranslate 
            text="Título Principal" 
            as="h1" 
            className="text-2xl font-bold text-gray-800" 
          />
          <AutoTranslate 
            text=<AutoTranslate text="Subtítulo importante" /> 
            as="h2" 
            className="text-lg font-semibold text-gray-700" 
          />
          <AutoTranslate 
            text=<AutoTranslate text="Parágrafo de texto normal que pode ser mais longo e detalhado." /> 
            as="p" 
            className="text-gray-600 leading-relaxed" 
          />
          <AutoTranslate 
            text="Texto em destaque" 
            as="strong" 
            className="text-blue-600 font-bold" 
          />
        </div>
      </div>

      {/* Informações técnicas */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold text-yellow-800 mb-2">
          <AutoTranslate text="Informações Técnicas" />
        </h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <AutoTranslate text="Todas as traduções usam a API DeepL" /></li>
          <li>• <AutoTranslate text="Cache inteligente evita traduções desnecessárias" /></li>
          <li>• <AutoTranslate text="Suporte para tradução em lote para melhor performance" /></li>
          <li>• <AutoTranslate text="Fallback automático em caso de erro" /></li>
          <li>• <AutoTranslate text="Detecção automática de idioma por browser e IP" /></li>
        </ul>
      </div>
    </div>
  )
}
