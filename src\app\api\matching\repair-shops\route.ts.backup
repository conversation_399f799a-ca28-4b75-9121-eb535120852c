import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const {
      deviceModelId,
      categoryId,
      problemTypeId,
      customerLocation, // { lat, lng }
      maxDistance = 50 // km
    } = await request.json()

    if (!problemTypeId) {
      return NextResponse.json(
        { message: 'Tipo de problema é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar lojistas que trabalham com este tipo de problema
    const repairShops = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP',
        profile: {
          workingProblems: {
            has: problemTypeId
          },
          // Se categoria especificada, verificar se trabalha com ela
          ...(categoryId && {
            workingCategories: {
              has: categoryId
            }
          })
        }
      },
      include: {
        profile: true,
        repairShopPrices: {
          where: {
            problemTypeId,
            isActive: true,
            OR: [
              { deviceModelId },
              { categoryId }
            ]
          },
          include: {
            deviceModel: {
              include: {
                brand: true,
                category: true
              }
            },
            category: true,
            problemType: true
          }
        }
      }
    })

    // Calcular score para cada lojista
    const rankedShops = repairShops.map(shop => {
      let score = 0
      let price = null
      let estimatedTime = null

      // 1. Verificar se tem preço definido (prioridade para modelo específico)
      const specificPrice = shop.repairShopPrices.find(p => p.deviceModelId === deviceModelId)
      const categoryPrice = shop.repairShopPrices.find(p => p.categoryId === categoryId)
      
      if (specificPrice) {
        price = specificPrice.price
        estimatedTime = specificPrice.estimatedTime
        score += 30 // Bonus por ter preço específico
      } else if (categoryPrice) {
        price = categoryPrice.price
        estimatedTime = categoryPrice.estimatedTime
        score += 20 // Bonus por ter preço da categoria
      } else {
        score -= 10 // Penalização por não ter preço definido
      }

      // 2. Rating (simulado por enquanto - será implementado depois)
      const rating = 4 + Math.random() // Rating entre 4-5
      score += rating * 10

      // 3. Tempo de resposta médio (simulado)
      const avgResponseTime = shop.profile?.averageRepairTime || 120
      if (avgResponseTime <= 60) score += 15
      else if (avgResponseTime <= 120) score += 10
      else if (avgResponseTime <= 240) score += 5

      // 4. Distância (simulada por enquanto)
      const distance = Math.random() * maxDistance
      if (distance <= 5) score += 20
      else if (distance <= 15) score += 15
      else if (distance <= 30) score += 10
      else if (distance <= 50) score += 5

      // 5. Disponibilidade (simulada)
      const isAvailable = Math.random() > 0.2 // 80% disponível
      if (isAvailable) score += 10

      return {
        id: shop.id,
        name: shop.name,
        email: shop.email,
        profile: shop.profile,
        price,
        estimatedTime,
        rating: Math.round(rating * 10) / 10,
        distance: Math.round(distance * 10) / 10,
        isAvailable,
        score: Math.round(score),
        responseTime: avgResponseTime
      }
    })

    // Ordenar por score (maior primeiro)
    rankedShops.sort((a, b) => b.score - a.score)

    // Retornar top 10
    const topShops = rankedShops.slice(0, 10)

    return NextResponse.json({
      shops: topShops,
      totalFound: rankedShops.length,
      searchCriteria: {
        deviceModelId,
        categoryId,
        problemTypeId,
        maxDistance
      }
    })

  } catch (error) {
    console.error('Erro no matching de lojistas:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
