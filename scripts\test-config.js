const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testConfig() {
  try {
    console.log('🔍 Testando configuração do sistema...')
    
    const config = await prisma.systemConfig.findFirst()
    
    if (!config) {
      console.log('❌ Nenhuma configuração encontrada')
      return
    }
    
    console.log('✅ Configuração encontrada:')
    console.log('- Platform Name:', config.platformName)
    console.log('- Platform Logo:', config.platformLogo || 'Não definido')
    console.log('- Platform Icon:', config.platformIcon || 'Não definido')
    console.log('- AWS Access Key ID:', config.awsAccessKeyId ? 'Definido' : 'Não definido')
    console.log('- AWS Secret Access Key:', config.awsSecretAccessKey ? 'Definido' : 'Não definido')
    console.log('- AWS S3 Bucket:', config.awsS3Bucket || 'Não definido')
    console.log('- AWS Region:', config.awsRegion)
    
    // Verificar variáveis de ambiente
    console.log('\n🔍 Verificando variáveis de ambiente...')
    console.log('- AWS_ACCESS_KEY_ID:', process.env.AWS_ACCESS_KEY_ID ? 'Definido' : 'Não definido')
    console.log('- AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? 'Definido' : 'Não definido')
    console.log('- AWS_S3_BUCKET:', process.env.AWS_S3_BUCKET || 'Não definido')
    console.log('- AWS_REGION:', process.env.AWS_REGION || 'Não definido')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testConfig()
