const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestLojista() {
  try {
    console.log('🏪 Criando lojista de teste...')

    const hashedPassword = await bcrypt.hash('teste123', 12)

    // Verificar se já existe
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    let lojista
    if (existingUser) {
      console.log('⚠️ Lojista já existe, usando existente...')
      lojista = existingUser
    } else {
      // Criar lojista
      lojista = await prisma.user.create({
        data: {
          name: 'TechFix Porto',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'REPAIR_SHOP',
          isVerified: true
        }
      })

      // Criar perfil
      await prisma.profile.create({
        data: {
          userId: lojista.id,
          phone: '+*********** 678',
          companyName: 'TechFix Porto',
          description: 'Especialistas em reparação de dispositivos móveis e eletrónicos',
          address: 'Rua de Santa Catarina, 123',
          city: 'Porto',
          postalCode: '4000-447',
          customSubdomain: 'techfix-porto',
          workingCategories: ['Smartphones', 'Tablets', 'Laptops'],
          workingBrands: ['Apple', 'Samsung', 'Xiaomi', 'Huawei'],
          workingProblems: ['Ecrã partido', 'Bateria', 'Problemas de software', 'Água'],
          serviceRadius: 25,
          averageRepairTime: 120,
          monthlyRepairs: 50,
          expectedGrowth: 20,
          businessHours: {
            monday: { open: '09:00', close: '18:00', closed: false },
            tuesday: { open: '09:00', close: '18:00', closed: false },
            wednesday: { open: '09:00', close: '18:00', closed: false },
            thursday: { open: '09:00', close: '18:00', closed: false },
            friday: { open: '09:00', close: '18:00', closed: false },
            saturday: { open: '09:00', close: '17:00', closed: false },
            sunday: { open: '10:00', close: '16:00', closed: true }
          }
        }
      })

      console.log('✅ Lojista criado!')
    }

    // Criar subscrição ativa
    const plan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'Pro' }
    })

    if (plan) {
      await prisma.subscription.upsert({
        where: { userId: lojista.id },
        update: {
          planId: plan.id,
          status: 'ACTIVE',
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 dias
        },
        create: {
          userId: lojista.id,
          planId: plan.id,
          status: 'ACTIVE',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      })
    }

    console.log('🛍️ Criando produtos no marketplace...')

    // Buscar categorias e marcas
    const smartphoneCategory = await prisma.category.findUnique({ where: { name: 'Smartphones' } })
    const tabletCategory = await prisma.category.findUnique({ where: { name: 'Tablets' } })
    const accessoryCategory = await prisma.category.findUnique({ where: { name: 'Acessórios' } })

    const appleBrand = await prisma.brand.findUnique({ where: { name: 'Apple' } })
    const samsungBrand = await prisma.brand.findUnique({ where: { name: 'Samsung' } })

    // Produtos de teste
    const products = [
      {
        name: 'iPhone 14 Pro 128GB - Recondicionado',
        description: 'iPhone 14 Pro em excelente estado, totalmente testado e com garantia de 6 meses. Inclui carregador original.',
        price: 899.99,
        stock: 3,
        categoryId: smartphoneCategory?.id,
        brandId: appleBrand?.id,
        condition: 'LIKE_NEW',
        images: ['/placeholder-iphone.jpg']
      },
      {
        name: 'Samsung Galaxy S23 256GB - Novo',
        description: 'Samsung Galaxy S23 novo, selado, com garantia oficial Samsung de 2 anos.',
        price: 749.99,
        stock: 2,
        categoryId: smartphoneCategory?.id,
        brandId: samsungBrand?.id,
        condition: 'NEW',
        images: ['/placeholder-samsung.jpg']
      },
      {
        name: 'iPad Air 5ª Geração 64GB - Recondicionado',
        description: 'iPad Air em óptimo estado, ideal para trabalho e entretenimento. Testado e garantido.',
        price: 549.99,
        stock: 1,
        categoryId: tabletCategory?.id,
        brandId: appleBrand?.id,
        condition: 'LIKE_NEW',
        images: ['/placeholder-ipad.jpg']
      },
      {
        name: 'Carregador Original Apple USB-C 20W',
        description: 'Carregador original Apple USB-C de 20W, compatível com iPhone e iPad.',
        price: 29.99,
        stock: 10,
        categoryId: accessoryCategory?.id,
        brandId: appleBrand?.id,
        condition: 'NEW',
        images: ['/placeholder-charger.jpg']
      },
      {
        name: 'Capa Protetora Samsung Galaxy S23',
        description: 'Capa protetora transparente para Samsung Galaxy S23, proteção total contra quedas.',
        price: 19.99,
        stock: 15,
        categoryId: accessoryCategory?.id,
        brandId: samsungBrand?.id,
        condition: 'NEW',
        images: ['/placeholder-case.jpg']
      },
      {
        name: 'iPhone 13 128GB - Usado (Bom Estado)',
        description: 'iPhone 13 em bom estado de conservação, pequenos sinais de uso. Bateria em 87%. Garantia de 3 meses.',
        price: 599.99,
        stock: 2,
        categoryId: smartphoneCategory?.id,
        brandId: appleBrand?.id,
        condition: 'GOOD',
        images: ['/placeholder-iphone13.jpg']
      }
    ]

    for (const productData of products) {
      if (productData.categoryId && productData.brandId) {
        await prisma.marketplaceProduct.create({
          data: {
            ...productData,
            sellerId: lojista.id,
            isActive: true
          }
        })
      }
    }

    console.log('✅ Lojista de teste criado com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: teste123')
    console.log('🌐 Loja: https://techfix-porto.revify.pt')
    console.log('📦 Produtos criados: 6')

  } catch (error) {
    console.error('❌ Erro ao criar lojista de teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestLojista()
