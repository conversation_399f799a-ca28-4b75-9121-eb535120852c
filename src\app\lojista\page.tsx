'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Activity,
  DollarSign,
  Download,
  Package,
  TrendingUp,
  Users,
  Wrench,
  Euro,
  Plus } from 'lucide-react'

interface TopProduct {
  id: string
  name: string
  totalSales: number
  totalRevenue: number
  orderCount: number}

interface RepairCategory {
  id: string
  name: string
  count: number
  percentage: number
  color: string}

export default function LojistaDashboard() {
  const { data: session } = useSession()
  const [dashboardStats, setDashboardStats] = useState({
    totalProducts: 0,
    totalRepairs: 0,
    monthlyRevenue: 0,
    pendingOrders: 0,
    totalCustomers: 0,
    revenueGrowth: 0,
    customersGrowth: 0,
    repairsGrowth: 0,
    weeklyRevenue: 0,
    completedRepairs: 0,
    averageRepairValue: 0,
    customerSatisfaction: 0
  })
  const [loading, setLoading] = useState(true)
  const [topProducts, setTopProducts] = useState<TopProduct[]>([])
  const [repairsByCategory, setRepairsByCategory] = useState<RepairCategory[]>([])
  const [analyticsLoading, setAnalyticsLoading] = useState(true)
  const [subscriptionData, setSubscriptionData] = useState({
    planName: 'Sem Plano',
    daysUntilRenewal: null})
  const [recentActivities, setRecentActivities] = useState<any[]>([])

  useEffect(() => {
    if (session?.user) {
      fetchDashboardStats()
      fetchSubscriptionData()
      fetchRecentActivities()
      fetchAnalyticsData()
    }
  }, [session])

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/lojista/dashboard-stats')
      if (response.ok) {
        const data = await response.json()
        setDashboardStats(data.stats)
      }
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', 'error')
    } finally {
      setLoading(false)
    }
  }

  const fetchAnalyticsData = async () => {
    try {
      setAnalyticsLoading(true)

      // Buscar produtos mais vendidos
      const topProductsResponse = await fetch(/api/lojista/analytics/top-products)
      if (topProductsResponse.ok) {
        const topProductsData = await topProductsResponse.json()
        setTopProducts(topProductsData.topProducts || [])
      
}

      // Buscar reparações por categoria
      const repairsCategoryResponse = await fetch(/api/lojista/analytics/repairs-by-category)
      if (repairsCategoryResponse.ok) {
        const repairsCategoryData = await repairsCategoryResponse.json()
        setRepairsByCategory(repairsCategoryData.repairsByCategory || [])
      
}

    } catch (error) {
      console.error('Erro ao carregar dados de análises:', 'error')
    } finally {
      setAnalyticsLoading(false)
    }
  }

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/lojista/subscription')
      if (response.ok) {
        const data = await response.json()
        setSubscriptionData({
          planName: data.planName || 'Sem Plano',
          daysUntilRenewal: data.daysUntilRenewal
        })
      }
    } catch (error) {
      console.error('Erro ao buscar dados da subscrição:', 'error')
    }
  }

  const fetchRecentActivities = async () => {
    try {
      const response = await fetch('/api/lojista/recent-activity')
      if (response.ok) {
        const data = await response.json()
        setRecentActivities(data.activities || [])
      }
    } catch (error) {
      console.error('Erro ao buscar atividade recente:', 'error')
    }
  }

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Bom dia'
    if (hour < 18) return 'Boa tarde'
    return 'Boa noite'
  }

  const getUserName = () => {
    return session?.user?.name || 'Lojista'
  }

  const downloadReport = async (reportType: string) => {
    try {
      const response = await fetch(`/api/lojista/reports/${reportType}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `${reportType}-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Erro ao gerar relatório')
      }
    } catch (error) {
      console.error('Erro ao descarregar relatório:', 'error')
      alert('Erro ao descarregar relatório')
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="animate-pulse">
          <div className="h-20 bg-gray-200 rounded-lg mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* Banner de Boas-vindas */}
      <div className="relative overflow-hidden rounded-lg bg-gradient-to-r from-gray-900 to-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col space-y-1">
            <h1 className="text-lg font-medium text-white">
              {getGreeting()}, {getUserName()}!
            </h1>
            <p className="text-sm text-gray-300">
              Bem-vindo ao seu dashboard
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-xs text-gray-300">
                Plano Atual
              </p>
              <p className="text-sm font-medium text-white">{subscriptionData.planName}</p>
              <p className="text-xs text-gray-400">
                {subscriptionData.daysUntilRenewal ? (
                  subscriptionData.daysUntilRenewal > 0 ? (
                  ) : (
                    'Renovação vencida')
                ) : (
                  'Sem renovação')}
              </p>
            </div>
            <Button size="sm" className="bg-white text-gray-900 hover:bg-gray-100 text-xs px-3 py-1" asChild>
              <Link href="/lojista/upgrade">
                Upgrade
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">
          Dashboard
        </h2>
        <div className="flex items-center space-x-2">
          <Button asChild size="sm">
            <Link href="/lojista/produtos/novo">
              <Plus className="mr-2 h-4 w-4" />
              Novo Produto
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">
            Visão Geral
          </TabsTrigger>
          <TabsTrigger value="analytics">
            Análises
          </TabsTrigger>
          <TabsTrigger value="reports">
            Relatórios
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Receita Total
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">€{dashboardStats.monthlyRevenue?.toFixed(2) || '0.00'}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={(dashboardStats.revenueGrowth || 0) >= 0 ? "text-green-600" : "text-red-600"}>
                    {(dashboardStats.revenueGrowth || 0) >= 0 ? "+" : ""}{dashboardStats.revenueGrowth || 0}%
                  </span> vs mês anterior
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Reparações
                </CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.totalRepairs}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={dashboardStats.repairsGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.repairsGrowth >= 0 ? "+" : ""}{dashboardStats.repairsGrowth}%
                  </span> vs mês anterior
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Produtos
                </CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.totalProducts}</div>
                <p className="text-xs text-muted-foreground">
                  Produtos ativos
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Clientes
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.totalCustomers}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={dashboardStats.customersGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                    {dashboardStats.customersGrowth >= 0 ? "+" : ""}{dashboardStats.customersGrowth}%
                  </span> vs mês anterior
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>
                  Visão Geral
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground mb-2">
                      Receita desta semana
                    </p>
                    <p className="text-3xl font-bold text-primary">
                      €{dashboardStats.weeklyRevenue?.toFixed(2) || '0.00'}
                    </p>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium text-muted-foreground mb-3">
                      Distribuição por categoria
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-2 rounded-lg bg-blue-50">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            Reparações
                          </span>
                        </div>
                        <span className="text-sm font-bold text-blue-700">
                          €{((dashboardStats.monthlyRevenue || 0) * 0.6).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-2 rounded-lg bg-green-50">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            Produtos
                          </span>
                        </div>
                        <span className="text-sm font-bold text-green-700">
                          €{((dashboardStats.monthlyRevenue || 0) * 0.4).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>
                  Atividade Recente
                </CardTitle>
                <CardDescription>
                  Últimas ações na sua loja
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {recentActivities.length > 0 ? (
                    recentActivities.map((activity, index) => (
                      <div key={activity.id || 'index'} className="flex items-center">
                        <div className={`w-9 h-9 bg-${activity.color}-100 rounded-full flex items-center justify-center`}>
                          {activity.icon === 'Wrench' && <Wrench className={`w-4 h-4 text-${activity.color}-600`} />}
                          {activity.icon === 'Package' && <Package className={`w-4 h-4 text-${activity.color}-600`} />}
                          {activity.icon === 'Users' && <Users className={`w-4 h-4 text-${activity.color}-600`} />}
                        </div>
                        <div className="ml-4 space-y-1">
                          <p className="text-sm font-medium leading-none">
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {activity.description}
                          </p>
                        </div>
                        {activity.amount && (
                          <div className="ml-auto font-medium">€{activity.amount.toFixed(2)}</div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      Nenhuma atividade recente
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Valor Médio Reparação
                </CardTitle>
                <Euro className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">€{dashboardStats.averageRepairValue?.toFixed(2) || '0.00'}</div>
                <p className="text-xs text-muted-foreground">
                  Média dos últimos 30 dias
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Taxa de Satisfação
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.customerSatisfaction}%</div>
                <p className="text-xs text-muted-foreground">
                  Baseado em avaliações
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Reparações Concluídas
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.completedRepairs}</div>
                <p className="text-xs text-muted-foreground">
                  Este mês
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Encomendas Pendentes
                </CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardStats.pendingOrders}</div>
                <p className="text-xs text-muted-foreground">
                  Requer atenção
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>
                  Produtos Mais Vendidos
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="space-y-4">
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {topProducts.length > 0 ? (
                      topProducts.slice(0, 3).map((product) => (
                        <div key={product.id} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {product.totalSales} {product.totalSales === 1 ? 'venda' : 'vendas'}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">€{product.totalRevenue.toFixed(2)}</p>
                            <p className="text-sm text-muted-foreground">Total</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-muted-foreground py-4">
                        <p>Nenhuma venda registada ainda</p>
                        <p className="text-sm">Comece a vender produtos para ver as estatísticas</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>
                  Reparações por Categoria
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="space-y-4">
                    <div className="animate-pulse flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-10"></div>
                    </div>
                    <div className="animate-pulse flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-10"></div>
                    </div>
                    <div className="animate-pulse flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-gray-200 rounded-full"></div>
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </div>
                      <div className="h-4 bg-gray-200 rounded w-10"></div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {repairsByCategory.length > 0 ? (
                      repairsByCategory.slice(0, 4).map((category) => (
                        <div key={category.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: category.color }}
                            ></div>
                            <span>{category.name}</span>
                          </div>
                          <span className="font-medium">{category.percentage}%</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-muted-foreground py-4">
                        <p>Nenhuma reparação registada ainda</p>
                        <p className="text-sm">Comece a registar reparações para ver as estatísticas</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>
                Relatórios Disponíveis
              </CardTitle>
              <CardDescription>
                Descarregue relatórios detalhados do seu negócio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      Relatório Mensal de Vendas
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Vendas detalhadas do último mês
                    </p>
                  </div>
                  <Button size="sm" onClick={() => downloadReport('vendas-mensais')}>
                    <Download className="mr-2 h-4 w-4" />
                    Descarregar
                  </Button>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      Relatório de Reparações
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Histórico completo de reparações
                    </p>
                  </div>
                  <Button size="sm" onClick={() => downloadReport('reparacoes')}>
                    <Download className="mr-2 h-4 w-4" />
                    Descarregar
                  </Button>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      Relatório de Clientes
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Base de dados de clientes
                    </p>
                  </div>
                  <Button size="sm" onClick={() => downloadReport('clientes')}>
                    <Download className="mr-2 h-4 w-4" />
                    Descarregar
                  </Button>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      Relatório Financeiro
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Receitas e despesas detalhadas
                    </p>
                  </div>
                  <Button size="sm" onClick={() => downloadReport('financeiro')}>
                    <Download className="mr-2 h-4 w-4" />
                    Descarregar
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
