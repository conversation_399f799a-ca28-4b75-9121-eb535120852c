'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { CheckCircle, Package, ArrowRight, Home } from 'lucide-react'

function SuccessPageContent() {
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session_id')
  const isDemo = searchParams.get('demo') === 'true'
  const paymentMethod = searchParams.get('payment_method') || 'card'
  const isMultibanco = searchParams.get('multibanco') === 'true'
  const [isLoading, setIsLoading] = useState(true)
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [multibancoRef, setMultibancoRef] = useState<any>(null)

  useEffect(() => {
    if (isMultibanco) {
      // Dados do Multibanco vêm da URL
      setMultibancoRef({
        entity: searchParams.get('entity'),
        reference: searchParams.get('reference'),
        amount: searchParams.get('amount')
      })
      setOrderDetails({
        orderNumber: 'MB' + Date.now(),
        total: parseFloat(searchParams.get('amount') || '0'),
        paymentMethod: 'multibanco'
      })
      setIsLoading(false)
    } else if (sessionId) {
      if (isDemo) {
        // Modo demo - simular dados
        setTimeout(() => {
          setOrderDetails({
            id: sessionId,
            orderNumber: `DEMO-${sessionId?.slice(-8)}`,
            status: 'confirmed',
            total: 99.99,
            demo: true,
            items: [
              {
                id: '1',
                quantity: 1,
                price: 99.99,
                product: {
                  name: 'Produto Demo',
                  images: ['/placeholder-product.jpg']
                }
              }
            ]
          })
          setIsLoading(false)
        }, 1000)
      } else {
        fetchOrderDetails()
        // Se for Multibanco, buscar referência
        if (paymentMethod === 'multibanco') {
          fetchMultibancoReference()
        }
      }
    }
  }, [sessionId, isDemo, paymentMethod, isMultibanco])

  const fetchMultibancoReference = async () => {
    try {
      const response = await fetch(`/api/marketplace/multibanco-ref?session_id=${sessionId}`)
      if (response.ok) {
        const data = await response.json()
        setMultibancoRef(data)
      }
    } catch (error) {
      console.error('Erro ao buscar referência Multibanco:', error)
    }
  }

  const fetchOrderDetails = async () => {
    try {
      const response = await fetch(`/api/marketplace/order-details?session_id=${sessionId}`)
      if (response.ok) {
        const data = await response.json()
        setOrderDetails(data)
      }
    } catch (error) {
      console.error('Erro ao buscar detalhes da encomenda:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          {/* Header with gradient */}
          <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-8 py-12 text-center text-white">
            {/* Success Icon */}
            <div className="mx-auto flex items-center justify-center w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm mb-6">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>

            {/* Success Message */}
            <h1 className="text-3xl font-bold mb-4">
              {isDemo ? 'Demonstração Concluída!' : 'Compra Realizada com Sucesso!'}
            </h1>

            <p className="text-green-100 text-lg">
              {isDemo
                ? 'Fluxo de pagamento simulado com sucesso'
                : 'Obrigado pela sua compra!'
              }
            </p>
          </div>

          <div className="p-8">
            {isDemo && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    <strong>Modo Demonstração:</strong> Esta é uma simulação. Configure chaves reais do Stripe no admin para pagamentos reais.
                  </p>
                </div>
              </div>
            </div>
          )}

            <p className="text-lg text-gray-600 mb-8">
            {isDemo
              ? 'Esta é uma demonstração do fluxo de pagamento. Em produção, seria processado um pagamento real.'
              : 'Obrigado pela sua compra. Receberá um email de confirmação em breve.'
            }
          </p>

            {/* Order Details */}
            {orderDetails && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8 text-left">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Detalhes da Encomenda
              </h2>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Número da Encomenda:</span>
                  <span className="font-medium">{orderDetails.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Pago:</span>
                  <span className="font-medium">€{orderDetails.total ? Number(orderDetails.total).toFixed(2) : '0.00'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Em Processamento
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Data da Compra:</span>
                  <span className="font-medium">
                    {new Date(orderDetails.createdAt).toLocaleDateString('pt-PT')}
                  </span>
                </div>
              </div>

              {/* Items */}
              {orderDetails.items && orderDetails.items.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Itens Comprados</h3>
                  <div className="space-y-3">
                    {orderDetails.items.map((item: any, index: number) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          {item.product?.images?.length > 0 ? (
                            <img
                              src={item.product.images[0]}
                              alt={item.product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                              Sem Imagem
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {item.product?.name || 'Produto'}
                          </p>
                          <p className="text-sm text-gray-600">
                            Quantidade: {item.quantity} × €{item.price ? Number(item.price).toFixed(2) : '0.00'}
                          </p>
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          €{item.price ? (Number(item.price) * item.quantity).toFixed(2) : '0.00'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

            {/* Multibanco Reference */}
            {(paymentMethod === 'multibanco' || isMultibanco) && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center mr-3">
                    <span className="text-white text-sm font-bold">MB</span>
                  </div>
                  <h3 className="text-lg font-semibold text-red-900">
                    Referência Multibanco
                  </h3>
                </div>

                {multibancoRef ? (
                  <div className="bg-white rounded-lg p-4 border border-red-200">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Entidade</p>
                        <p className="text-2xl font-bold text-gray-900">{multibancoRef.entity}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Referência</p>
                        <p className="text-2xl font-bold text-gray-900">{multibancoRef.reference}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Valor</p>
                        <p className="text-2xl font-bold text-gray-900">€{multibancoRef.amount}</p>
                      </div>
                    </div>
                    <div className="mt-4 text-center">
                      <p className="text-sm text-red-700">
                        <strong>Importante:</strong> Guarde esta referência para efetuar o pagamento.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg p-4 border border-red-200 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-2"></div>
                    <p className="text-red-700">A gerar referência Multibanco...</p>
                  </div>
                )}
              </div>
            )}

            {/* Next Steps */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              Próximos Passos
            </h3>
            <div className="text-left space-y-2 text-blue-800">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span>Receberá um email de confirmação</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span>Os vendedores irão processar os seus pedidos</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span>Receberá atualizações sobre o envio</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span>Pode acompanhar o progresso nas suas encomendas</span>
              </div>
            </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 justify-center">
            <Link
              href="/cliente/encomendas"
              className="inline-flex items-center px-4 py-2 bg-black text-white text-sm rounded-xl hover:bg-gray-800 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <Package className="w-4 h-4 mr-2" />
              Ver Encomendas
            </Link>

            <Link
              href="/marketplace"
              className="inline-flex items-center px-4 py-2 bg-white text-gray-700 text-sm border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 hover:scale-105 shadow-sm"
            >
              Continuar Comprando
            </Link>

            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-white text-gray-700 text-sm border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 hover:scale-105 shadow-sm"
            >
              <Home className="w-4 h-4 mr-2" />
              Início
            </Link>
            </div>

            {/* Support */}
            <div className="mt-12 text-center">
            <p className="text-sm text-gray-600">
              Precisa de ajuda? {' '}
              <Link href="/contacto" className="text-black hover:underline">
                Entre em contacto connosco
              </Link>
            </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function SuccessPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>}>
      <SuccessPageContent />
    </Suspense>
  )
}
