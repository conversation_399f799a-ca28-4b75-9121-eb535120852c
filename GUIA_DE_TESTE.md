# 🧪 G<PERSON>a <PERSON> de Teste - Revify

## 📋 **Índice**
1. [Configuração Inicial](#configuração-inicial)
2. [Testando o Dashboard do Lojista](#testando-o-dashboard-do-lojista)
3. [Testando Reparações Independentes](#testando-reparações-independentes)
4. [Testando Loja Online (Backend)](#testando-loja-online-backend)
5. [Testando Frontend da Loja E-commerce](#testando-frontend-da-loja-e-commerce)
6. [Testand<PERSON> Sistema de Emails](#testando-sistema-de-emails)
7. [Testando Gestão de Cupões](#testando-gestão-de-cupões)

---

## 🚀 **Configuração Inicial**

### 1. **Configurar Variáveis de Ambiente**
Crie/atualize o arquivo `.env.local`:

```env
# Database
DATABASE_URL="sua-database-url"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="seu-secret"

# Email (Opcional - para testar notificações)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=sua-senha-app
SMTP_FROM=<EMAIL>
```

### 2. **Iniciar o Servidor**
```bash
npm run dev
```

### 3. **Criar Usuário de Teste**
1. Acesse: `http://localhost:3000/auth/signin`
2. Registre-se como lojista
3. No admin, atribua o plano "Revy Pro" ao usuário

---

## 🏪 **Testando o Dashboard do Lojista**

### **URL**: `http://localhost:3000/lojista`

### **O que verificar:**
- ✅ Seção "Marketplace" com ações básicas
- ✅ Seção "Premium Features" com:
  - Reparações Independentes
  - Loja Online
- ✅ Estatísticas do dashboard
- ✅ Links funcionais

---

## 🔧 **Testando Reparações Independentes**

### **1. Configurar Preços**
- **URL**: `http://localhost:3000/lojista/precos`
- Configure preços para diferentes marcas e tipos de problema

### **2. Criar Reparação Independente**
- **URL**: `http://localhost:3000/lojista/reparacoes/independente`
- **Teste**:
  - ✅ Dropdowns de categoria, marca e modelo funcionam
  - ✅ Preços são preenchidos automaticamente
  - ✅ Email de notificação é enviado (se configurado)
  - ✅ Código de tracking é gerado

### **3. Listar Reparações**
- **URL**: `http://localhost:3000/lojista/reparacoes`
- **Teste**:
  - ✅ Badges "Independente" e "Revify" aparecem
  - ✅ Dados corretos são exibidos
  - ✅ Filtros funcionam

### **4. Página Pública para Clientes**
- **URL**: `http://localhost:3000/shop/[subdomain]/nova-reparacao`
- **Como testar**: Ver seção "Frontend da Loja"

---

## 🛒 **Testando Loja Online (Backend)**

### **1. Configurações da Loja**
- **URL**: `http://localhost:3000/lojista/loja-online/configuracoes`
- **Teste**:
  - ✅ Configurar subdomínio (ex: "minhaloja")
  - ✅ Configurar dados da loja
  - ✅ Configurar IfthenPay (opcional)
  - ✅ Salvar configurações

### **2. Dashboard da Loja Online**
- **URL**: `http://localhost:3000/lojista/loja-online`
- **Teste**:
  - ✅ Estatísticas são exibidas
  - ✅ Status da loja (ativa/inativa)
  - ✅ Links para gestão funcionam

### **3. Gestão de Produtos**
- **URL**: `http://localhost:3000/lojista/loja-online/produtos`
- **Teste**:
  - ✅ Lista produtos do marketplace
  - ✅ Filtros funcionam
  - ✅ Toggle ativo/inativo funciona

### **4. Gestão de Encomendas**
- **URL**: `http://localhost:3000/lojista/loja-online/encomendas`
- **Teste**:
  - ✅ Lista encomendas (quando houver)
  - ✅ Badges de origem funcionam
  - ✅ Filtros funcionam

---

## 🛍️ **Testando Frontend da Loja E-commerce**

### **Como Acessar no Localhost:**

#### **Método 1: Parâmetro URL (Recomendado)**
```
http://localhost:3000?shop=SUBDOMAIN
```
**Exemplo**: `http://localhost:3000?shop=minhaloja`

#### **Método 2: URLs Diretas**
```
http://localhost:3000/shop/SUBDOMAIN
```
**Exemplo**: `http://localhost:3000/shop/minhaloja`

### **Páginas para Testar:**

#### **1. Homepage da Loja**
- **URL**: `http://localhost:3000?shop=minhaloja`
- **Teste**:
  - ✅ Nome da loja aparece
  - ✅ Informações da loja são exibidas
  - ✅ Links funcionam

#### **2. Catálogo de Produtos**
- **URL**: `http://localhost:3000/shop/minhaloja/produtos`
- **Teste**:
  - ✅ Grid de produtos responsivo
  - ✅ Filtros de busca e categoria
  - ✅ Botão "Adicionar ao carrinho"
  - ✅ Contador do carrinho no header

#### **3. Detalhes do Produto**
- **URL**: `http://localhost:3000/shop/minhaloja/produto/[id]`
- **Teste**:
  - ✅ Imagens do produto
  - ✅ Informações detalhadas
  - ✅ Seletor de quantidade
  - ✅ Adicionar ao carrinho

#### **4. Carrinho de Compras**
- **URL**: `http://localhost:3000/shop/minhaloja/carrinho`
- **Teste**:
  - ✅ Lista itens do carrinho
  - ✅ Alterar quantidades
  - ✅ Remover itens
  - ✅ Cálculo de portes (grátis acima €50)
  - ✅ Resumo do pedido

#### **5. Checkout**
- **URL**: `http://localhost:3000/shop/minhaloja/checkout`
- **Teste**:
  - ✅ Formulário de dados pessoais
  - ✅ Endereço de entrega
  - ✅ Métodos de pagamento
  - ✅ Resumo do pedido
  - ✅ Finalizar compra

#### **6. Área do Cliente**
- **URL**: `http://localhost:3000/shop/minhaloja/cliente`
- **Teste**:
  - ✅ Login simples por email
  - ✅ Visualizar pedidos
  - ✅ Dados pessoais
  - ✅ Logout

#### **7. Solicitar Reparação**
- **URL**: `http://localhost:3000/shop/minhaloja/nova-reparacao`
- **Teste**:
  - ✅ Formulário completo
  - ✅ Dropdowns funcionam
  - ✅ Código de tracking gerado
  - ✅ Link para acompanhamento

---

## 📧 **Testando Sistema de Emails**

### **Pré-requisitos:**
1. Configurar variáveis SMTP no `.env.local`
2. Usar email real para testes

### **Testes:**

#### **1. Email de Criação de Reparação**
- Criar reparação independente com email do cliente
- **Verificar**: Email HTML bem formatado é recebido

#### **2. Email de Atualização de Status**
- **API**: `PATCH /api/lojista/reparacoes/[id]/status`
- **Body**: 
```json
{
  "status": "COMPLETED",
  "notes": "Reparação concluída com sucesso",
  "sendNotification": true
}
```

#### **3. Notificação Manual**
- **API**: `POST /api/lojista/reparacoes/[id]/notify`
- **Body**:
```json
{
  "message": "Sua reparação está pronta para retirada"
}
```

---

## 🎫 **Testando Gestão de Cupões**

### **URL**: `http://localhost:3000/lojista/loja-online/cupoes`

### **Testes:**

#### **1. Criar Cupão**
- ✅ Formulário de criação
- ✅ Gerador automático de código
- ✅ Tipos: percentual e valor fixo
- ✅ Configurações avançadas

#### **2. Gestão de Cupões**
- ✅ Listar cupões
- ✅ Ativar/desativar
- ✅ Copiar código
- ✅ Excluir cupão

#### **3. Aplicar Cupão (Futuro)**
- No checkout da loja online
- Validação de condições

---

## 🔍 **Checklist de Teste Completo**

### **Backend (Gestão)**
- [ ] Dashboard reorganizado com Premium Features
- [ ] Reparações independentes com dropdowns
- [ ] Listagem unificada com badges
- [ ] Configurações da loja online
- [ ] Gestão de produtos da loja
- [ ] Gestão de cupões
- [ ] Sistema de notificações por email

### **Frontend (Loja E-commerce)**
- [ ] Homepage da loja
- [ ] Catálogo de produtos com filtros
- [ ] Detalhes do produto
- [ ] Carrinho de compras funcional
- [ ] Checkout completo
- [ ] Área do cliente
- [ ] Solicitar reparação pública
- [ ] Tracking de reparações

### **Integrações**
- [ ] Middleware de subdomínios (localhost + produção)
- [ ] Sistema de emails (SMTP)
- [ ] Armazenamento local do carrinho
- [ ] APIs de produtos e encomendas

---

## 🚨 **Problemas Comuns**

### **1. Subdomínio não funciona**
- **Solução**: Use `?shop=subdomain` no localhost
- **Exemplo**: `http://localhost:3000?shop=minhaloja`

### **2. Emails não são enviados**
- **Verificar**: Configuração SMTP no `.env.local`
- **Teste**: Usar Gmail com senha de app

### **3. Carrinho não persiste**
- **Verificar**: localStorage do navegador
- **Limpar**: Dados do site se necessário

### **4. Produtos não aparecem**
- **Verificar**: Lojista tem produtos ativos no marketplace
- **Criar**: Produtos de teste primeiro

---

## 🎯 **Status de Implementação: 100% COMPLETO**

✅ **Frontend da loja e-commerce**: Completo
✅ **Sistema de carrinho e checkout**: Completo  
✅ **Área do cliente isolada**: Completo
✅ **Gestão de cupões**: Completo
✅ **Sistema de notificações**: Completo
✅ **Middleware para localhost**: Completo

**🎉 Todas as funcionalidades estão implementadas e prontas para teste!**
