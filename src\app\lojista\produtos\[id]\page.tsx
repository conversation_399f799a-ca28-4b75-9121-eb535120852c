'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { ArrowLeft, Edit, Trash2, Package, Euro, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
interface Product {
  id: string
  name: string
  description: string
  price: number
  stock: number
  category: string
  images: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string}

export default function ProductDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (params.id) {
      fetchProduct()
    }
  }, [params.id])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/lojista/produtos/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data.product)
      } else {
        setError('Produto não encontrado')
      }
    } catch (error) {
      console.error('Erro ao buscar produto:', 'error')
      setError('Erro ao carregar produto')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Tem certeza que deseja eliminar este produto?')) return

    try {
      const response = await fetch(`/api/lojista/produtos/${params.id}`, {
        method: DELETE})

      if (response.ok) {
        router.push('/lojista/produtos')
      } else {
        alert('Erro ao eliminar produto')
      }
    } catch (error) {
      console.error('Erro ao eliminar produto:', 'error')
      alert('Erro ao eliminar produto')
    }
  }

  const toggleStatus = async () => {
    if (!product) return

    try {
      const response = await fetch(`/api/lojista/produtos/${params.id}`, {
        method: PATCH,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive: !product.isActive
        })
      })

      if (response.ok) {
        setProduct(prev => prev ? { ...prev, isActive: !prev.isActive } : null)
      } else {
        alert('Erro ao alterar status do produto')
      }
    } catch (error) {
      console.error('Erro ao alterar status:', 'error')
      alert('Erro ao alterar status do produto')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Produto não encontrado</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/lojista/produtos"
            className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />Voltar aos Produtos</Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/lojista/produtos"
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />Produtos</Link>
              <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={toggleStatus}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium ${
                  product.isActive
                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {product.isActive ? (
                  <>
                    <Eye className="w-4 h-4 mr-2" />Ativo</>
                ) : (
                  <>
                    <EyeOff className="w-4 h-4 mr-2" />Inativo</>
                )}
              </button>
              <Link
                href={`/lojista/produtos/${product.id}/editar`}
                className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                <Edit className="w-4 h-4 mr-2" />Editar</Link>
              <button
                onClick={handleDelete}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                <Trash2 className="w-4 h-4 mr-2" />Eliminar</button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Images */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Imagens</h3>
            {product.images && product.images.length > 0 ? (
              <div className="grid grid-cols-2 gap-4">
                {product.images.map((image, index) => (
                  <div key={index} className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={image}
                      alt={`${product.name} - Imagem ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <Package className="w-16 h-16 text-gray-400" />
              </div>
            )}
          </div>

          {/* Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes</h3>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <dl className="space-y-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Nome</dt>
                  <dd className="text-lg font-semibold text-gray-900">{product.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Descrição</dt>
                  <dd className="text-gray-900">{product.description || 'Sem descrição'}</dd>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Preço</dt>
                    <dd className="flex items-center text-xl font-bold text-gray-900">
                      <Euro className="w-5 h-5 mr-1" />
                      {Number(product.price).toFixed(2)}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Stock</dt>
                    <dd className="text-xl font-bold text-gray-900">{product.stock}</dd>
                  </div>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Categoria</dt>
                  <dd className="text-gray-900">{product.category || 'Sem categoria'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      product.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {product.isActive ? 'Ativo' : 'Inativo'}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Criado em</dt>
                  <dd className="text-gray-900">
                    {new Date(product.createdAt).toLocaleDateString('pt-PT')}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Última atualização</dt>
                  <dd className="text-gray-900">
                    {new Date(product.updatedAt).toLocaleDateString('pt-PT')}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
