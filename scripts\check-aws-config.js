const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkAWSConfig() {
  try {
    console.log('🔍 Verificando configuração AWS...')
    
    // Verificar variáveis de ambiente
    console.log('\n📋 Variáveis de ambiente:')
    console.log(`AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID ? '✅ Definida' : '❌ Não definida'}`)
    console.log(`AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? '✅ Definida' : '❌ Não definida'}`)
    console.log(`AWS_S3_BUCKET: ${process.env.AWS_S3_BUCKET ? '✅ Definida' : '❌ Não definida'}`)
    console.log(`AWS_REGION: ${process.env.AWS_REGION || 'eu-west-1 (padr<PERSON>)'}`)
    
    // Verificar configuração no banco de dados
    console.log('\n🗄️ Configuração no banco de dados:')
    const config = await prisma.systemConfig.findFirst({
      select: {
        awsAccessKeyId: true,
        awsSecretAccessKey: true,
        awsS3Bucket: true,
        awsRegion: true
      }
    })
    
    if (config) {
      console.log(`AWS Access Key ID: ${config.awsAccessKeyId ? '✅ Definida' : '❌ Não definida'}`)
      console.log(`AWS Secret Access Key: ${config.awsSecretAccessKey ? '✅ Definida' : '❌ Não definida'}`)
      console.log(`AWS S3 Bucket: ${config.awsS3Bucket ? '✅ Definida' : '❌ Não definida'}`)
      console.log(`AWS Region: ${config.awsRegion || 'eu-west-1 (padrão)'}`)
    } else {
      console.log('❌ Nenhuma configuração encontrada no banco de dados')
    }
    
    // Verificar se há pelo menos uma configuração válida
    const hasEnvConfig = process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY && process.env.AWS_S3_BUCKET
    const hasDbConfig = config && config.awsAccessKeyId && config.awsSecretAccessKey && config.awsS3Bucket
    
    console.log('\n🎯 Status geral:')
    if (hasEnvConfig) {
      console.log('✅ Configuração AWS válida encontrada nas variáveis de ambiente')
    } else if (hasDbConfig) {
      console.log('✅ Configuração AWS válida encontrada no banco de dados')
    } else {
      console.log('❌ Nenhuma configuração AWS válida encontrada')
      console.log('\n💡 Para resolver:')
      console.log('1. Definir variáveis de ambiente AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_S3_BUCKET')
      console.log('2. OU configurar AWS nas configurações do admin')
    }

  } catch (error) {
    console.error('❌ Erro ao verificar configuração AWS:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAWSConfig()
