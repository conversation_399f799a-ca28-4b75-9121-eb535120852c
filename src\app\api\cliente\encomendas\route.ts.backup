import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Login necessário' },
        { status: 401 }
      )
    }

    // Buscar encomendas do cliente
    const orders = await prisma.order.findMany({
      where: {
        customerId: session.user.id
      },
      include: {
        marketplaceOrderItems: {
          include: {
            product: {
              include: {
                seller: {
                  select: {
                    name: true,
                    profile: {
                      select: {
                        companyName: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Formatar dados para o frontend
    const formattedOrders = orders.map(order => ({
      id: order.id,
      status: order.status,
      total: Number(order.total),
      createdAt: order.createdAt,
      shippingAddress: {
        name: order.shippingName,
        street: order.shippingStreet,
        city: order.shippingCity,
        postalCode: order.shippingPostalCode,
        country: order.shippingCountry
      },
      items: order.marketplaceOrderItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: Number(item.price),
        product: {
          id: item.product.id,
          name: item.product.name,
          images: item.product.images,
          seller: {
            name: item.product.seller.profile?.companyName || item.product.seller.name
          }
        }
      }))
    }))

    return NextResponse.json({
      orders: formattedOrders
    })

  } catch (error) {
    console.error('Erro ao buscar encomendas:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
