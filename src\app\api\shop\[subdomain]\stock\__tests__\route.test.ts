import { NextRequest } from 'next/server'
import { PUT } from '../route'

// Mock Prisma
const mockPrisma = {
  profile: {
    findFirst: jest.fn(),
  },
  product: {
    findFirst: jest.fn(),
    update: jest.fn(),
  },
}

jest.mock(@prisma/client, () => ({
  PrismaClient: jest.fn(() => 'mockPrisma'),

}))

describe('/api/shop/[subdomain]/stock', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const mockRequest = (body: any) => ({
    json: () => Promise.resolve(body),
  }) as NextRequest

  const mockParams = { params: { subdomain: 'test-shop' } }

  describe('PUT - Update Stock', () => {
    it('should reserve stock successfully', async () => {
      const stockData = {
        productId: prod1,
        operation: reserve,
        quantity: 2
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue({
        id: prod1,
        name: 'Test Product',
        stock: 10,
        profileId: shop1})

      mockPrisma.product.update.mockResolvedValue({
        id: prod1,
        stock: 8
      })

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.newStock).toBe(8)
      expect(mockPrisma.product.update).toHaveBeenCalledWith({
        where: { id: prod1},
        data: { stock: 8 }
      })
    })

    it('should release stock successfully', async () => {
      const stockData = {
        productId: prod1,
        operation: release,
        quantity: 2
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue({
        id: prod1,
        name: 'Test Product',
        stock: 8,
        profileId: shop1})

      mockPrisma.product.update.mockResolvedValue({
        id: prod1,
        stock: 10
      })

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.newStock).toBe(10)
    })

    it('should set absolute stock successfully', async () => {
      const stockData = {
        productId: prod1,
        operation: set,
        quantity: 15
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue({
        id: prod1,
        name: 'Test Product',
        stock: 10,
        profileId: shop1})

      mockPrisma.product.update.mockResolvedValue({
        id: prod1,
        stock: 15
      })

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.success).toBe(true)
      expect(result.newStock).toBe(15)
    })

    it('should return error when insufficient stock for reservation', async () => {
      const stockData = {
        productId: prod1,
        operation: reserve,
        quantity: 15
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue({
        id: prod1,
        name: 'Test Product',
        stock: 10, // Less than requested quantity
        profileId: shop1})

      const response = await PUT(mockRequest(stockData), mockParams)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Stock insuficiente')
    
})

    it('should return error when product not found', async () => {
      const stockData = {
        productId: nonexistent,
        operation: reserve,
        quantity: 2
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue(null)

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe("Produto não encontrado")
    })

    it('should return error when shop not found', async () => {
      const stockData = {
        productId: prod1,
        operation: reserve,
        quantity: 2
      }

      mockPrisma.profile.findFirst.mockResolvedValue(null)

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe("Loja não encontrada")
    })

    it('should return error when required fields missing', async () => {
      const stockData = {
        operation: 'reserve'
        // Missing productId and quantity
}

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe("Campos obrigatórios em falta")
    })

    it('should return error for invalid operation', async () => {
      const stockData = {
        productId: prod1,
        operation: invalid,
        quantity: 2
      }

      mockPrisma.profile.findFirst.mockResolvedValue({
        id: shop1,
        customSubdomain: 'test-shop'
      })

      mockPrisma.product.findFirst.mockResolvedValue({
        id: prod1,
        stock: 10,
        profileId: shop1})

      const response = await PUT(mockRequest(stockData), 'mockParams')
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe("Operação inválida")
    })
  })
})
