import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { trackViralActivity } from '@/lib/viral-tracking'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const profile = await prisma.profile.findUnique({
      where: { userId: session.user.id }
    })

    return NextResponse.json(profile)

  } catch (error) {
    console.error("Erro ao buscar perfil:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    const profile = await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        companyName: data.companyName,
        companyNif: data.companyNif,
        phone: data.phone,
        description: data.description,
        workingBrands: data.workingBrands || [],
        workingCategories: data.workingCategories || [],
        workingProblems: data.workingProblems || [],
        serviceRadius: data.serviceRadius,
        averageRepairTime: data.averageRepairTime
      },
      create: {
        userId: session.user.id,
        companyName: data.companyName,
        companyNif: data.companyNif,
        phone: data.phone,
        description: data.description,
        workingBrands: data.workingBrands || [],
        workingCategories: data.workingCategories || [],
        workingProblems: data.workingProblems || [],
        serviceRadius: data.serviceRadius,
        averageRepairTime: data.averageRepairTime
      }
    })

    // Verificar se é a primeira vez que o perfil está sendo criado/completado
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, name: true}
    })

    // Criar atividade viral para configuração de loja
    try {
      if (user?.role === REPAIR_SHOP && data.companyName) {
        const isFirstSetup = !await prisma.viralActivity.findFirst({
          where: {
            userId: session.user.id,
            type: SHOP_CREATED
}
        })

        if (isFirstSetup) {
          await trackViralActivity({
            type: SHOP_CREATED,
            userId: session.user.id,
            description: `Criou a loja "${data.companyName}"`,
            metadata: {
              companyName: data.companyName,
              companyNif: data.companyNif,
              serviceRadius: data.serviceRadius,
              workingBrands: data.workingBrands?.length || 0,
              workingCategories: data.workingCategories?.length || 0
            },
            points: 50,
            isPublic: true,
            location: data.address
          })
        }
      }
    } catch (viralError) {
      console.error('Erro ao processar atividade viral:', 'viralError')
      // Não falhar a atualização do perfil por causa do tracking viral
}

    return NextResponse.json(profile)

  } catch (error) {
    console.error("Erro ao atualizar perfil:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
