'use client'

import Link from 'next/link'
import MainHeader from '@/components/MainHeader'
import { ArrowLeft, User, Mail, Phone, MapPin, Shield, Bell } from 'lucide-react'

export default function GestaoPerfilPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <MainHeader subtitle="Central de Ajuda" />

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=clientes" className="hover:text-gray-700">Para Clientes</Link>
              <span className="mx-2">›</span>
              <span>Gestão de perfil</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Gestão de perfil</h1>
            <p className="text-lg text-gray-600">Como gerir e atualizar as informações do seu perfil na plataforma Revify.</p>
          </div>

          <div className="prose max-w-none">
            <h2>Aceder ao seu perfil</h2>
            <p>
              Para aceder às configurações do seu perfil, faça login na plataforma e clique no seu nome 
              no canto superior direito, depois selecione Perfil.
            </p>

            <h2>Informações pessoais</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <User className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Dados básicos</h3>
                  <p className="text-blue-800 mb-3">Informações que pode editar:</p>
                  <ul className="list-disc list-inside text-blue-800 space-y-1">
                    <li>Nome completo</li>
                    <li>Data de nascimento</li>
                    <li>Género</li>
                    <li>Foto de perfil</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Mail className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Contactos</h3>
                  <p className="text-green-800 mb-3">Mantenha os seus contactos atualizados:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li><strong>Email principal:</strong>Para login e notificações</li>
                    <li><strong>Email secundário:</strong>Backup para recuperação</li>
                    <li><strong>Telefone:</strong>Para SMS e contacto direto</li>
                    <li><strong>WhatsApp:</strong>Para comunicação rápida</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <MapPin className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900 mb-2">Moradas</h3>
                  <p className="text-purple-800 mb-3">Pode adicionar múltiplas moradas:</p>
                  <ul className="list-disc list-inside text-purple-800 space-y-1">
                    <li><strong>Morada principal:</strong> Para entregas e recolhas</li>
                    <li><strong>Morada de trabalho:</strong> Alternativa para entregas</li>
                    <li><strong>Outras moradas:</strong> Familiares ou amigos</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Segurança da conta</h2>
            
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Shield className="w-6 h-6 text-red-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-red-900 mb-2">Palavra-passe</h3>
                  <p className="text-red-800 mb-3">Dicas para uma palavra-passe segura:</p>
                  <ul className="list-disc list-inside text-red-800 space-y-1">
                    <li>Mínimo 8 caracteres</li>
                    <li>Combine letras, números e símbolos</li>
                    <li>Evite informações pessoais</li>
                    <li>Altere regularmente</li>
                    <li>Use palavras-passe únicas</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Preferências de notificação</h2>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Bell className="w-6 h-6 text-yellow-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-yellow-900 mb-2">Tipos de notificação</h3>
                  <p className="text-yellow-800 mb-3">Configure como quer ser contactado:</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-800">
                    <div>
                      <h4 className="font-semibold mb-2">Email</h4>
                      <ul className="text-sm space-y-1">
                        <li>• Atualizações de reparação</li>
                        <li>• Orçamentos</li>
                        <li>• Promoções</li>
                        <li>• Newsletter</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">SMS</h4>
                      <ul className="text-sm space-y-1">
                        <li>• Estados importantes</li>
                        <li>• Lembretes de recolha</li>
                        <li>• Códigos de verificação</li>
                        <li>• Emergências</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <h2>Dados fiscais</h2>
            <div className="bg-gray-50 rounded-lg p-6 my-6">
              <h3 className="font-semibold mb-3">Para faturação</h3>
              <p className="mb-3">Se precisar de faturas com IVA, adicione:</p>
              <ul className="space-y-2">
                <li><strong>NIF:</strong>Número de identificação fiscal</li>
                <li><strong>Nome/Empresa:</strong> Para aparecer na fatura</li>
                <li><strong>Morada fiscal:</strong>Morada oficial</li>
              </ul>
            </div>

            <h2>Privacidade</h2>
            <div className="bg-blue-50 rounded-lg p-6 my-6">
              <h3 className="font-semibold mb-3">Controlo de dados</h3>
              <ul className="space-y-2">
                <li><strong>Visibilidade do perfil:</strong>Quem pode ver as suas informações</li>
                <li><strong>Histórico de reparações:</strong> Partilhar com oficinas</li>
                <li><strong>Marketing:</strong> Receber ofertas personalizadas</li>
                <li><strong>Analytics:</strong> Ajudar a melhorar a plataforma</li>
              </ul>
            </div>

            <h2>Como editar informações</h2>
            <div className="space-y-4 my-6">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">1. Aceder ao perfil</h3>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">2. Selecionar secção</h3>
                <p className="text-sm">Escolha a categoria que quer editar</p>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">3. Fazer alterações</h3>
                <p className="text-sm">Edite os campos necessários</p>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">4. Guardar</h3>
                <p className="text-sm">Clique em Guardar alterações</p>
              </div>
            </div>

            <h2>Eliminar conta</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 my-6">
              <h3 className="font-semibold text-red-900 mb-3">⚠️ Ação irreversível</h3>
              <p className="text-red-800 mb-3">Se pretender eliminar a sua conta, contacte o suporte. Esta ação:</p>
              <ul className="list-disc list-inside text-red-800 space-y-1">
                <li>Remove todos os seus dados permanentemente</li>
                <li>Cancela reparações em curso</li>
                <li>Elimina histórico de compras</li>
                <li>Não pode ser desfeita</li>
              </ul>
            </div>

            <h2>Problemas comuns</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Não consigo alterar email</h3>
              <p className="mb-4">O email principal só pode ser alterado após verificação. Contacte o suporte se tiver problemas.</p>
              
              <h3 className="font-semibold mb-3">Esqueci-me da palavra-passe</h3>
              
              <h3 className="font-semibold mb-3">Não recebo notificações</h3>
              <p>Verifique as configurações de notificação e a pasta de spam do email.</p>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda/clientes/marketplace-comprar"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />Anterior: Marketplace - Como comprar</Link>
              <Link
                href="/ajuda"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >Voltar à Central de Ajuda<ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
