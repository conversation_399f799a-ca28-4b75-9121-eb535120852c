import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a subscrição pertence ao utilizador
    const subscription = await prisma.subscription.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!subscription) {
      return NextResponse.json(
        { message: 'Subscrição não encontrada' },
        { status: 404 }
      )
    }

    // Cancelar a subscrição
    const updatedSubscription = await prisma.subscription.update({
      where: { id: params.id },
      data: {
        cancelAtPeriodEnd: true,
        canceledAt: new Date()
      }
    })

    return NextResponse.json({
      message: 'Subscrição cancelada com sucesso',
      subscription: updatedSubscription
    })

  } catch (error) {
    console.error('Erro ao cancelar subscrição:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
