'use client'

import { useState, useEffect, use } from 'react'
import Link from 'next/link'
import { ArrowLeft, Clock, CheckCircle, AlertCircle, Package, Phone, Mail, MapPin } from 'lucide-react'
import RepairChat from '@/components/RepairChat'
import PriceApproval from '@/components/PriceApproval'
import RepairReviewModal from '@/components/RepairReviewModal'
import RepairDisputeModal from '@/components/RepairDisputeModal'

interface Repair {
  id: string
  referenceNumber: string
  status: string
  description: string
  diagnosis?: string
  estimatedCost: number
  finalCost: number
  notes?: string
  createdAt: string
  estimatedCompletionDate?: string
  pickupAddress?: string
  deliveryAddress?: string
  device: {
    name: string
    brand: {
      name: string
    }
    category: {
      name: string
    }
  }
  repairShop?: {
    name: string
    email: string
    profile?: {
      companyName: string
      phone: string
    }
  }
  courier?: {
    name: string
    email: string
  }
  statusHistory?: Array<{
    id: string
    status: string
    notes: string
    createdAt: string
  }>
}

const STATUS_CONFIG = {
  CONFIRMED: { label: 'Confirmado', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  RECEIVED: { label: 'Recebido', color: 'bg-blue-100 text-blue-800', icon: Package },
  DIAGNOSIS: { label: 'Diagnóstico', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
  WAITING_PARTS: { label: 'Aguarda Peças', color: 'bg-orange-100 text-orange-800', icon: Clock },
  IN_REPAIR: { label: 'Em Reparação', color: 'bg-purple-100 text-purple-800', icon: Clock },
  TESTING: { label: 'Teste', color: 'bg-indigo-100 text-indigo-800', icon: Clock },
  COMPLETED: { label: 'Concluído', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
  CANCELLED: { label: 'Cancelado', color: 'bg-red-100 text-red-800', icon: AlertCircle }
}

const STATUS_STEPS = ['CONFIRMED', 'RECEIVED', 'DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING', 'COMPLETED', 'DELIVERED']

export default function RepairDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const [repair, setRepair] = useState<Repair | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [showPriceApproval, setShowPriceApproval] = useState(false)
  const [showReview, setShowReview] = useState(false)
  const [showDispute, setShowDispute] = useState(false)

  useEffect(() => {
    fetchRepair()
  }, [])

  useEffect(() => {
    if (repair) {
      // Mostrar aprovação de preço se necessário
      const needsPriceApproval = repair.finalCost &&
                                repair.estimatedCost &&
                                repair.finalCost !== repair.estimatedCost &&
                                repair.status !== 'COMPLETED' &&
                                repair.status !== 'DELIVERED'
      setShowPriceApproval(needsPriceApproval)

      // Mostrar review se reparação concluída
      const canReview = repair.status === 'COMPLETED' || repair.status === 'DELIVERED'
      setShowReview(canReview)
    }
  }, [repair])

  const fetchRepair = async () => {
    try {
      const response = await fetch(`/api/repairs/${resolvedParams.id}`)
      if (response.ok) {
        const data = await response.json()
        setRepair(data)
      } else {
        console.error('Reparação não encontrada')
      }
    } catch (error) {
      console.error('Erro ao carregar reparação:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getCurrentStepIndex = () => {
    if (!repair) return 0
    return STATUS_STEPS.indexOf(repair.status)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!repair) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Reparação não encontrada</h2>
          <Link
            href="/cliente/reparacoes"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar às Reparações
          </Link>
        </div>
      </div>
    )
  }

  const statusConfig = STATUS_CONFIG[repair.status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.CONFIRMED
  const StatusIcon = statusConfig.icon
  const currentStep = getCurrentStepIndex()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link
                href="/cliente/reparacoes"
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-700" />
              </Link>
              <h1 className="text-2xl font-bold text-black">{repair.referenceNumber}</h1>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.color}`}>
                <StatusIcon className="w-4 h-4 mr-2" />
                {statusConfig.label}
              </span>

              {/* Botão de Disputa */}
              {repair.status !== 'CANCELLED' && repair.status !== 'DELIVERED' && (
                <button
                  onClick={() => setShowDispute(!showDispute)}
                  className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm"
                >
                  Abrir Disputa
                </button>
              )}

              {/* Botão de Review */}
              {(repair.status === 'COMPLETED' || repair.status === 'DELIVERED') && (
                <button
                  onClick={() => setShowReview(!showReview)}
                  className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors text-sm"
                >
                  Avaliar Serviço
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Progress Timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Progresso da Reparação</h2>
            <div className="flex items-center justify-between">
              {STATUS_STEPS.map((step, index) => {
                const stepConfig = STATUS_CONFIG[step as keyof typeof STATUS_CONFIG]
                const StepIcon = stepConfig.icon
                const isCompleted = index <= currentStep
                const isCurrent = index === currentStep

                return (
                  <div key={step} className="flex flex-col items-center flex-1">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      isCompleted 
                        ? isCurrent 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-400'
                    }`}>
                      <StepIcon className="w-5 h-5" />
                    </div>
                    <div className="text-xs text-center mt-2 max-w-20">
                      <div className={`font-medium ${isCompleted ? 'text-gray-900' : 'text-gray-500'}`}>
                        {stepConfig.label}
                      </div>
                    </div>
                    {index < STATUS_STEPS.length - 1 && (
                      <div className={`absolute h-0.5 w-full mt-5 ${
                        index < currentStep ? 'bg-green-600' : 'bg-gray-200'
                      }`} style={{ left: '50%', right: '-50%', zIndex: -1 }} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Device Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações do Dispositivo</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">Dispositivo</div>
                <div className="font-medium text-gray-900">
                  {repair.deviceModel?.brand?.name} {repair.deviceModel?.name}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Categoria</div>
                <div className="font-medium text-gray-900">{repair.deviceModel?.category?.name}</div>
              </div>
              <div className="md:col-span-2">
                <div className="text-sm text-gray-600">Descrição do Problema</div>
                <div className="font-medium text-gray-900">{repair.description}</div>
              </div>
              {repair.diagnosis && (
                <div className="md:col-span-2">
                  <div className="text-sm text-gray-600">Diagnóstico</div>
                  <div className="font-medium text-gray-900">{repair.diagnosis}</div>
                </div>
              )}
            </div>
          </div>

          {/* Cost Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Informações de Custo</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {repair.estimatedCost > 0 && (
                <div>
                  <div className="text-sm text-gray-600">Custo Estimado</div>
                  <div className="text-lg font-semibold text-gray-900">€{repair.estimatedCost.toFixed(2)}</div>
                </div>
              )}
              {repair.finalCost > 0 && (
                <div>
                  <div className="text-sm text-gray-600">Custo Final</div>
                  <div className="text-lg font-semibold text-green-600">€{repair.finalCost.toFixed(2)}</div>
                </div>
              )}
            </div>
          </div>

          {/* Contact Info */}
          {repair.repairShop && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Loja de Reparação</h2>
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="font-medium text-gray-900">
                    {repair.repairShop.profile?.companyName || repair.repairShop.name}
                  </div>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="w-4 h-4 mr-2" />
                  {repair.repairShop.email}
                </div>
                {repair.repairShop.profile?.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="w-4 h-4 mr-2" />
                    {repair.repairShop.profile.phone}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Addresses */}
          {(repair.pickupAddress || repair.deliveryAddress) && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Endereços</h2>
              <div className="space-y-4">
                {repair.pickupAddress && (
                  <div>
                    <div className="text-sm text-gray-600 flex items-center mb-1">
                      <MapPin className="w-4 h-4 mr-1" />
                      Recolha
                    </div>
                    <div className="font-medium text-gray-900">{repair.pickupAddress}</div>
                  </div>
                )}
                {repair.deliveryAddress && (
                  <div>
                    <div className="text-sm text-gray-600 flex items-center mb-1">
                      <MapPin className="w-4 h-4 mr-1" />
                      Entrega
                    </div>
                    <div className="font-medium text-gray-900">{repair.deliveryAddress}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Histórico</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">Reparação criada</div>
                  <div className="text-sm text-gray-600">
                    {new Date(repair.createdAt).toLocaleString('pt-PT')}
                  </div>
                </div>
              </div>
              
              {repair.statusHistory?.map((history) => (
                <div key={history.id} className="flex items-start">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3"></div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {STATUS_CONFIG[history.status as keyof typeof STATUS_CONFIG]?.label || history.status}
                    </div>
                    <div className="text-sm text-gray-600">{history.notes}</div>
                    <div className="text-xs text-gray-500">
                      {new Date(history.createdAt).toLocaleString('pt-PT')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Price Approval */}
        {showPriceApproval && repair && (
          <div className="mt-6">
            <PriceApproval
              repairId={repair.id}
              originalPrice={repair.estimatedCost || 0}
              newPrice={repair.finalCost || 0}
              onApproval={() => {
                setShowPriceApproval(false)
                fetchRepairDetails()
              }}
            />
          </div>
        )}

        {/* Review Modal */}
        {repair && (
          <RepairReviewModal
            repairId={repair.id}
            repairShopName={repair.repairShop?.name || 'Loja'}
            isOpen={showReview}
            onClose={() => setShowReview(false)}
            onReviewSubmitted={() => {
              setShowReview(false)
              fetchRepairDetails()
            }}
          />
        )}

        {/* Dispute Modal */}
        {repair && (
          <RepairDisputeModal
            repairId={repair.id}
            isOpen={showDispute}
            onClose={() => setShowDispute(false)}
            onDisputeSubmitted={() => {
              setShowDispute(false)
              fetchRepairDetails()
            }}
          />
        )}
      </div>

      {/* Chat Component */}
      {repair && (
        <RepairChat
          repairId={repair.id}
          isOpen={isChatOpen}
          onToggle={() => setIsChatOpen(!isChatOpen)}
        />
      )}
    </div>
  )
}
