'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Plus, Search, Filter, Eye, Clock, CheckCircle, AlertCircle, Wrench } from 'lucide-react'
import NotificationDropdown from '@/components/NotificationDropdown'
import UserDropdown from '@/components/UserDropdown'

interface IndependentRepair {
  id: string
  trackingCode: string
  customerName: string
  customerPhone: string
  deviceBrand: string
  deviceModel: string
  problemType: string
  status: string
  estimatedPrice: number
  createdAt: string
  estimatedCompletionDate: string
}

const statusConfig = {
  PENDING: { label: 'Pendente', color: 'yellow', icon: Clock },
  IN_PROGRESS: { label: 'Em Progresso', color: 'blue', icon: Wrench },
  WAITING_PARTS: { label: 'Aguardando Peças', color: 'orange', icon: AlertCircle },
  COMPLETED: { label: 'Concluída', color: 'green', icon: CheckCircle },
  DELIVERED: { label: 'Entregue', color: 'green', icon: CheckCircle },
  CANCELLED: { label: 'Cancelada', color: 'red', icon: AlertCircle }
}

export default function ReparacoesIndependentesPage() {
  const { data: session } = useSession()
  const [repairs, setRepairs] = useState<IndependentRepair[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    if (session?.user?.id) {
      checkAccess()
      fetchRepairs()
    }
  }, [session])

  const checkAccess = async () => {
    try {
      const response = await fetch('/api/lojista/mini-loja-config')
      if (response.ok) {
        const data = await response.json()
        setHasAccess(data.hasAccess)
      }
    } catch (error) {
      console.error('Erro ao verificar acesso:', error)
    }
  }

  const fetchRepairs = async () => {
    try {
      const response = await fetch('/api/lojista/reparacoes/independentes')
      if (response.ok) {
        const data = await response.json()
        setRepairs(data.repairs || [])
      }
    } catch (error) {
      console.error('Erro ao carregar reparações:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch = repair.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         repair.trackingCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         repair.deviceModel.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = !statusFilter || repair.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getStatusConfig = (status: string) => {
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-14">
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold text-black">Revify</Link>
                <span className="ml-3 text-xs text-gray-500">Reparações Independentes</span>
              </div>
              <div className="flex items-center space-x-3">
                <NotificationDropdown />
                <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
                <UserDropdown user={session?.user || {}} />
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <Wrench className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Reparações Independentes Não Disponíveis</h1>
            <p className="text-gray-600 mb-8">
              Esta funcionalidade não está disponível no seu plano atual.
            </p>
            <Link
              href="/lojista/planos"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Ver Planos Disponíveis
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-black">Revify</Link>
              <span className="ml-3 text-xs text-gray-500">Reparações Independentes</span>
            </div>
            <div className="flex items-center space-x-3">
              <NotificationDropdown />
              <span className="text-xs text-gray-600">Olá, {session?.user?.name}</span>
              <UserDropdown user={session?.user || {}} />
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/lojista" className="text-gray-500 hover:text-gray-700">
                  Dashboard
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Reparações Independentes</span>
              </li>
            </ol>
          </nav>
        </div>

        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reparações Independentes</h1>
            <p className="text-gray-600">Gerencie reparações walk-in e clientes habituais</p>
          </div>
          <Link
            href="/lojista/reparacoes/independente"
            className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nova Reparação
          </Link>
        </div>

        {/* Filtros */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Buscar por cliente, código ou dispositivo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos os status</option>
                <option value="PENDING">Pendente</option>
                <option value="IN_PROGRESS">Em Progresso</option>
                <option value="WAITING_PARTS">Aguardando Peças</option>
                <option value="COMPLETED">Concluída</option>
                <option value="DELIVERED">Entregue</option>
                <option value="CANCELLED">Cancelada</option>
              </select>
            </div>
          </div>
        </div>

        {/* Lista de Reparações */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredRepairs.length === 0 ? (
            <div className="text-center py-12">
              <Wrench className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {repairs.length === 0 ? 'Nenhuma reparação independente' : 'Nenhuma reparação encontrada'}
              </h3>
              <p className="text-gray-600 mb-6">
                {repairs.length === 0 
                  ? 'Comece criando sua primeira reparação independente'
                  : 'Tente ajustar os filtros de busca'
                }
              </p>
              {repairs.length === 0 && (
                <Link
                  href="/lojista/reparacoes/independente"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Nova Reparação
                </Link>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Código
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dispositivo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Preço
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRepairs.map((repair) => {
                    const statusInfo = getStatusConfig(repair.status)
                    const StatusIcon = statusInfo.icon
                    
                    return (
                      <tr key={repair.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {repair.trackingCode}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {repair.customerName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {repair.customerPhone}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {repair.deviceBrand} {repair.deviceModel}
                          </div>
                          <div className="text-sm text-gray-500">
                            {repair.problemType}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                            statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                            statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            statusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            <StatusIcon className="w-3 h-3 mr-1" />
                            {statusInfo.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          €{repair.estimatedPrice.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(repair.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link
                            href={`/lojista/reparacoes/${repair.id}`}
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Ver
                          </Link>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
