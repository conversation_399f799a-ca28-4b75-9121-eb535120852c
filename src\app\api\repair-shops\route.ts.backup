import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// Função para calcular distância entre duas coordenadas (fórmula de Haversine)
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371 // Raio da Terra em km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const lat = searchParams.get('lat')
    const lng = searchParams.get('lng')
    const radius = parseInt(searchParams.get('radius') || '20') // 20km por padrão

    const repairShops = await prisma.user.findMany({
      where: {
        role: 'REPAIR_SHOP'
      },
      orderBy: [
        { isFeatured: 'desc' }, // Lojas em destaque primeiro
        { isVerified: 'desc' }, // Depois lojas verificadas
        { createdAt: 'desc' }   // Por último, mais recentes
      ],
      include: {
        profile: {
          select: {
            companyName: true,
            description: true,
            phone: true,
            street: true,
            city: true,
            postalCode: true,
            serviceRadius: true,
            averageRepairTime: true,
            workingCategories: true,
            workingBrands: true
          }
        },
        repairShopPrices: {
          select: {
            price: true,
            estimatedTime: true,
            problemType: {
              select: {
                name: true
              }
            }
          }
        },
        shopReviews: {
          select: {
            rating: true
          }
        }
      }
    })

    // Calcular dados reais para cada loja
    const repairShopsWithRealData = repairShops.map(shop => {
      // Calcular rating médio
      const ratings = shop.shopReviews.map(r => r.rating)
      const averageRating = ratings.length > 0
        ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
        : 4.5

      // Encontrar preço mais baixo
      const lowestPrice = shop.repairShopPrices.length > 0
        ? Math.min(...shop.repairShopPrices.map(p => Number(p.price)))
        : 25

      // Calcular tempo médio de reparação
      const averageTime = shop.profile?.averageRepairTime ||
        (shop.repairShopPrices.length > 0
          ? shop.repairShopPrices.reduce((sum, p) => sum + (p.estimatedTime || 120), 0) / shop.repairShopPrices.length
          : 120)

      // Por enquanto, não filtrar por localização até implementarmos coordenadas
      let distance = null

      return {
        id: shop.id,
        name: shop.name,
        businessName: shop.profile?.companyName || shop.name,
        rating: Number(averageRating.toFixed(1)),
        reviewCount: shop.shopReviews.length,
        basePrice: lowestPrice,
        estimatedTime: averageTime,
        responseTime: averageTime < 60 ? `${averageTime}min` : `${Math.round(averageTime / 60)}h`,
        address: shop.profile?.street || `${shop.profile?.city || 'Lisboa'}`,
        distance: distance ? Number(distance.toFixed(1)) : null,
        isAvailable: true, // TODO: implementar lógica de disponibilidade
        isFeatured: shop.isFeatured,
        isVerified: shop.isVerified,
        specialties: shop.profile?.workingCategories || ['Reparações Gerais'],
        profile: shop.profile
      }
    }).filter(Boolean) // Remove lojas fora do raio

    return NextResponse.json({
      shops: repairShopsWithRealData,
      total: repairShopsWithRealData.length
    })

  } catch (error) {
    console.error('Erro ao buscar lojas de reparação:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
