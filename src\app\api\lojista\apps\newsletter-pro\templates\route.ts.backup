import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { PREDEFINED_TEMPLATES } from '@/lib/newsletter-templates'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar templates do usuário
    const templates = await prisma.newsletterTemplate.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Combinar templates personalizados com templates pré-definidos
    const customTemplates = templates.map((template) => ({
      id: template.id,
      name: template.name,
      subject: template.subject,
      content: template.content,
      isActive: template.isActive,
      type: 'custom',
      createdAt: template.createdAt
    }))

    const predefinedTemplates = PREDEFINED_TEMPLATES.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      thumbnail: template.thumbnail,
      content: template.content,
      variables: template.variables,
      type: 'predefined',
      isActive: true
    }))

    return NextResponse.json({
      templates: [...predefinedTemplates, ...customTemplates]
    })

  } catch (error) {
    console.error('Erro ao buscar templates:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, subject, content } = await request.json()

    if (!name || !subject || !content) {
      return NextResponse.json(
        { message: 'Dados obrigatórios em falta' },
        { status: 400 }
      )
    }

    // Criar template
    const template = await prisma.newsletterTemplate.create({
      data: {
        userId: session.user.id,
        name,
        subject,
        content,
        isActive: true
      }
    })

    return NextResponse.json({
      message: 'Template criado com sucesso',
      template
    })

  } catch (error) {
    console.error('Erro ao criar template:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
