import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedPlans() {
  try {
    // Verificar se já existem planos
    const existingPlans = await prisma.subscriptionPlan.count()
    
    if (existingPlans > 0) {
      console.log('Planos já existem, pulando seed...')
      return
    }

    // Criar planos de subscrição
    const plans = [
      {
        name: 'Básico',
        description: 'Ideal para começar',
        monthlyPrice: 29.99,
        yearlyPrice: 299.99,
        features: [
          'Até 50 reparações/mês',
          'Gestão básica de clientes',
          'Relatórios simples',
          'Suporte por email'
        ],
        maxRepairs: 50,
        maxUsers: 1,
        hasAdvancedReports: false,
        hasApiAccess: false,
        hasPrioritySupport: false,
        isActive: true,
        priority: 1
      },
      {
        name: 'Profissional',
        description: 'Para negócios em crescimento',
        monthlyPrice: 59.99,
        yearlyPrice: 599.99,
        features: [
          'Até 200 reparações/mês',
          'Gestão avançada de clientes',
          'Relatórios detalhados',
          'Integração com CTT',
          'Suporte prioritário'
        ],
        maxRepairs: 200,
        maxUsers: 3,
        hasAdvancedReports: true,
        hasApiAccess: false,
        hasPrioritySupport: true,
        isActive: true,
        priority: 2
      },
      {
        name: 'Empresarial',
        description: 'Para grandes empresas',
        monthlyPrice: 99.99,
        yearlyPrice: 999.99,
        features: [
          'Reparações ilimitadas',
          'Múltiplos utilizadores',
          'API completa',
          'WhatsApp Business',
          'Faturas eletrónicas',
          'Suporte 24/7'
        ],
        maxRepairs: -1, // Ilimitado
        maxUsers: -1, // Ilimitado
        hasAdvancedReports: true,
        hasApiAccess: true,
        hasPrioritySupport: true,
        isActive: true,
        priority: 3
      }
    ]

    for (const plan of plans) {
      await prisma.subscriptionPlan.create({
        data: plan
      })
    }

    console.log('Planos de subscrição criados com sucesso!')

  } catch (error) {
    console.error('Erro ao criar planos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedPlans()
