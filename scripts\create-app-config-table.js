const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createAppConfigTable() {
  try {
    console.log('🗄️ Criando tabela app_configs...')
    
    // Criar tabela app_configs manualmente
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS app_configs (
        id TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL,
        "appId" TEXT NOT NULL,
        settings JSONB DEFAULT '{}',
        "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE("userId", "appId")
      );
    `

    console.log('✅ Tabela app_configs criada')

    // Verificar se foi criada
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'app_configs'
      );
    `
    
    console.log('🗄️ Tabela app_configs existe:', tableExists[0].exists)

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAppConfigTable()
