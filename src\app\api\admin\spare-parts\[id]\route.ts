import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(
  request: NextRequest,
  { 'params'}: { params: Promise<{ id: string}> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id } = await params
    const part = await prisma.sparePart.findUnique({
      where: { id },
      include: {
        category: true,
        brand: true,
        deviceModel: true}
    })

    if (!part) {
      return NextResponse.json(
        { message: "Peça não encontrada" },
        { status: 404 }
      )
    }

    return NextResponse.json({ 'part'})

  } catch (error) {
    console.error("Erro ao buscar peça:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const data = await request.json()

    const part = await prisma.sparePart.update({
      where: { id: params.id },
      'data'})

    return NextResponse.json({
      message: "Peça atualizada com sucesso",
      'part'})

  } catch (error) {
    console.error("Erro ao atualizar peça:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { 'params'}: { params: { id: string} }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se a peça tem encomendas associadas
    const ordersCount = await prisma.sparePartOrderItem.count({
      where: { partId: params.id }
    })

    if (ordersCount > 0) {
      return NextResponse.json(
        { message: Não é possível remover peça com encomendas associadas 
},
        { status: 400 }
      )
    }

    await prisma.sparePart.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Peça removida com sucesso'
    })

  } catch (error) {
    console.error('Erro ao remover peça:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
