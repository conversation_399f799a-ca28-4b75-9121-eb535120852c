# Lemar Chat - Assistente Virtual da Revify

## Visão Geral

O Lemar é o assistente virtual inteligente da Revify, baseado no AI Chatbot da Vercel com integração completa da plataforma. Ele tem acesso a toda a base de dados da Revify e pode responder a qualquer pergunta sobre reparações, dispositivos, lojas, preços e funcionalidades.

## Funcionalidades

### ✅ Implementadas
- **Chat Interface Moderna**: Design moderno com gradientes e animações
- **Minimizar/Maximizar**: Controlo de tamanho da janela do chat
- **Streaming de Respostas**: Respostas em tempo real usando AI SDK
- **Contexto do Utilizador**: Reconhece se o utilizador está autenticado
- **Personalidade Portuguesa**: Responde em português com conhecimento da Revify
- **Disponível Globalmente**: Aparece em todas as páginas (exceto shops)
- **Integração com Base de Dados**: Acesso completo aos dados da plataforma
- **Pesquisa de Dispositivos**: Pode pesquisar qualquer modelo na base de dados
- **Central de Ajuda**: Conhecimento completo de FAQ e procedimentos

### 🎯 Capacidades Avançadas do Lemar
- **Consulta de Dispositivos**: Pesquisa modelos específicos (iPhone 14, Samsung Galaxy, etc.)
- **Informações de Lojas**: Dados sobre lojas parceiras e especializações
- **Preços e Garantias**: Informações sobre custos e políticas
- **Troubleshooting**: Ajuda com problemas técnicos básicos
- **Navegação**: Orienta sobre como usar funcionalidades específicas
- **Suporte Personalizado**: Respostas baseadas no tipo de utilizador
- **FAQ Dinâmica**: Acesso a perguntas frequentes atualizadas

## Arquitetura Técnica

### Componentes
- **`LeMarChat.tsx`**: Componente principal do chat
- **`/api/chat/lemar/route.ts`**: API route para processamento das mensagens

### Dependências
- `ai` - Vercel AI SDK
- `@ai-sdk/openai` - Integração com OpenAI
- `nanoid` - Geração de IDs únicos
- `next-auth` - Contexto do utilizador

### Configuração
```env
OPENAI_API_KEY="your-openai-api-key-here"
```

## Como Usar

### Para Utilizadores
1. Clique no ícone de chat no canto inferior direito
2. Digite sua pergunta sobre a Revify
3. Receba respostas personalizadas do LeMar

### Para Desenvolvedores
O chat está automaticamente disponível em todas as páginas através do `layout.tsx`:

```tsx
import LeMarChat from '@/components/chat/LeMarChat'

// No layout principal
<LeMarChat />
```

## Personalização

### Modificar Personalidade
Edite o `systemPrompt` em `/api/chat/lemar/route.ts`:

```typescript
const systemPrompt = `Você é o LeMar, o assistente virtual...`
```

### Alterar Aparência
Modifique os estilos em `LeMarChat.tsx`:

```tsx
// Cores do gradiente
className="bg-gradient-to-r from-indigo-600 to-purple-600"

// Tamanho da janela
className="w-96 h-[600px]"
```

## Estados do Chat

1. **Fechado**: Apenas botão flutuante visível
2. **Aberto**: Janela completa do chat
3. **Minimizado**: Apenas header visível
4. **Carregando**: Animação de typing dots

## Integração com a Plataforma

O LeMar tem conhecimento sobre:
- **Clientes**: Como fazer reparações, encontrar lojas
- **Lojistas**: Como usar o dashboard, gerir produtos
- **Estafetas**: Como funciona o sistema de entregas
- **Administradores**: Funcionalidades de gestão

## Próximos Passos

### Funcionalidades Futuras
- [ ] Histórico de conversas persistente
- [ ] Integração com base de conhecimento
- [ ] Suporte a anexos (imagens de dispositivos)
- [ ] Agendamento direto de reparações
- [ ] Integração com sistema de tickets
- [ ] Suporte multilíngue automático
- [ ] Analytics de conversas
- [ ] Respostas sugeridas
- [ ] Integração com WhatsApp/Telegram

### Melhorias Técnicas
- [ ] Rate limiting
- [ ] Caching de respostas comuns
- [ ] Fallback para quando API está indisponível
- [ ] Métricas de satisfação
- [ ] A/B testing de prompts

## Monitorização

### Métricas Importantes
- Número de conversas iniciadas
- Taxa de resolução de problemas
- Tempo médio de resposta
- Satisfação do utilizador
- Tópicos mais perguntados

### Logs
As conversas são logadas para análise e melhoria contínua do sistema.
