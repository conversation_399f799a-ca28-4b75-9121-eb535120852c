'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  Users,
  Smartphone,
  Wrench,
  Package,
  BarChart3,
  Settings,
  Plus,
  Edit,
  Trash2,
  CreditCard
} from 'lucide-react'

export default function AdminDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalRepairs: 0,
    totalOrders: 0,
    totalRevenue: 0
  })

  useEffect(() => {
    // Fetch stats
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error)
    }
  }

  const menuItems = [
    {
      title: 'Gestão de Dispositivos',
      items: [
        { name: 'Categorias', href: '/admin/categories', icon: Package, description: 'Gerir categorias de dispositivos' },
        { name: '<PERSON><PERSON>', href: '/admin/brands', icon: Smartphone, description: 'Gerir marcas de dispositivos' },
        { name: 'Modelos', href: '/admin/models', icon: Smartphone, description: 'Gerir modelos de dispositivos' },
      ]
    },
    {
      title: 'Gestão de Utilizadores',
      items: [
        { name: 'Utilizadores', href: '/admin/users', icon: Users, description: 'Gerir todos os utilizadores' },
        { name: 'Lojistas', href: '/admin/repair-shops', icon: Wrench, description: 'Gerir oficinas de reparação' },
        { name: 'Planos', href: '/admin/planos', icon: Package, description: 'Gerir planos de subscrição' },
        { name: 'Subscrições', href: '/admin/subscricoes', icon: CreditCard, description: 'Gerir subscrições ativas' },
      ]
    },
    {
      title: 'Loja de Peças',
      items: [
        { name: 'Gestão de Peças', href: '/admin/spare-parts', icon: Package, description: 'Gerir catálogo de peças' },
        { name: 'Encomendas de Peças', href: '/admin/spare-part-orders', icon: Package, description: 'Gerir encomendas de peças' },
      ]
    },
    {
      title: 'Operações',
      items: [
        { name: 'Reparações', href: '/admin/repairs', icon: Wrench, description: 'Monitorizar reparações' },
        { name: 'Encomendas', href: '/admin/orders', icon: Package, description: 'Gerir encomendas' },
      ]
    },
    {
      title: 'App Store',
      items: [
        { name: 'Gestão de Apps', href: '/admin/apps', icon: Package, description: 'Gerir apps da loja' },
      ]
    },
    {
      title: 'Relatórios',
      items: [
        { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, description: 'Relatórios e estatísticas' },
        { name: 'Configurações', href: '/admin/settings', icon: Settings, description: 'Configurações do sistema' },
        { name: 'Integrações', href: '/admin/integracoes', icon: Settings, description: 'WhatsApp, CTT, Faturas Eletrónicas' },
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-black">Revify Admin</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Olá, {session?.user?.name}</span>
              <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-xs font-medium">
                {session?.user?.name?.charAt(0).toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Utilizadores</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Wrench className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reparações</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalRepairs}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Package className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Encomendas</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Receita</p>
                <p className="text-2xl font-bold text-gray-900">€{stats.totalRevenue}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Sections */}
        <div className="space-y-8">
          {menuItems.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{section.title}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                      className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow group"
                    >
                      <div className="flex items-center mb-3">
                        <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-black group-hover:text-white transition-colors">
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <h3 className="ml-3 text-lg font-medium text-gray-900">{item.name}</h3>
                      </div>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h2>
          <div className="flex flex-wrap gap-3">
            <Link
              href="/admin/categories"
              className="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Gerir Categorias
            </Link>
            <Link
              href="/admin/brands"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Gerir Marcas
            </Link>
            <Link
              href="/admin/models"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Gerir Modelos
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
