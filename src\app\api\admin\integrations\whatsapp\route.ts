import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações do WhatsApp
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: [
            whatsappAccessToken,
            'whatsappPhoneNumberId',
            'whatsappBusinessAccountId',
            'whatsappWebhookVerifyToken',
            'whatsappEnabled'
          ]
        
}
      }
    })

    const config: any = {}
    settings.forEach(setting => {
      config[setting.key] = setting.value
    })

    return NextResponse.json({
      accessToken: config.whatsappAccessToken || '',
      phoneNumberId: config.whatsappPhoneNumberId || '',
      businessAccountId: config.whatsappBusinessAccountId || '',
      webhookVerifyToken: config.whatsappWebhookVerifyToken || '',
      enabled: config.whatsappEnabled === 'true',
      webhookUrl: `${process.env.NEXTAUTH_URL}/api/webhooks/whatsapp`
    })

  } catch (error) {
    console.error('Erro ao buscar configuração WhatsApp:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { accessToken,
      phoneNumberId,
      businessAccountId,
      webhookVerifyToken,
      enabled } = await request.json()

    // Validar campos obrigatórios
    if (enabled && (!accessToken || !phoneNumberId)) {
      return NextResponse.json(
        { message: Access Token e Phone Number ID são obrigatórios 
},
        { status: 400 }
      )
    }

    // Atualizar configurações
    const settingsToUpdate = [
      { key: whatsappAccessToken, value: accessToken ||  
},
      { key: whatsappPhoneNumberId, value: phoneNumberId || '' },
      { key: whatsappBusinessAccountId, value: businessAccountId || '' },
      { key: whatsappWebhookVerifyToken, value: webhookVerifyToken || '' },
      { key: whatsappEnabled, value: enabled ? 'true' : 'false' }
    ]

    for (const 'setting of settingsToUpdate') {
      await prisma.systemSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
          description: `WhatsApp ${setting.key.replace('whatsapp', '')}`
        }
      })
    }

    return NextResponse.json({
      message: 'Configuração WhatsApp atualizada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao atualizar configuração WhatsApp:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
