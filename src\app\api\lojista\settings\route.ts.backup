import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Buscar configurações do usuário
    const settings = await prisma.$queryRaw`
      SELECT * FROM user_settings WHERE "userId" = ${session.user.id}
    `

    // Configurações padrão se não existir
    const defaultSettings = {
      emailNotifications: true,
      smsNotifications: false,
      marketingEmails: false,
      orderUpdates: true,
      repairUpdates: true,
      twoFactorAuth: false,
      publicProfile: true,
      showPhone: true,
      showEmail: false,
      autoAcceptRepairs: false,
      workingHoursNotifications: true,
      weekendNotifications: false,
      language: 'pt',
      timezone: 'Europe/Lisbon',
      currency: 'EUR'
    }

    return NextResponse.json({
      settings: settings.length > 0 ? {
        ...defaultSettings,
        ...settings[0].preferences
      } : defaultSettings
    })

  } catch (error) {
    console.error('Erro ao buscar configurações:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const settings = await request.json()

    // Validar configurações
    const validSettings = {
      emailNotifications: Boolean(settings.emailNotifications),
      smsNotifications: Boolean(settings.smsNotifications),
      marketingEmails: Boolean(settings.marketingEmails),
      orderUpdates: Boolean(settings.orderUpdates),
      repairUpdates: Boolean(settings.repairUpdates),
      twoFactorAuth: Boolean(settings.twoFactorAuth),
      publicProfile: Boolean(settings.publicProfile),
      showPhone: Boolean(settings.showPhone),
      showEmail: Boolean(settings.showEmail),
      autoAcceptRepairs: Boolean(settings.autoAcceptRepairs),
      workingHoursNotifications: Boolean(settings.workingHoursNotifications),
      weekendNotifications: Boolean(settings.weekendNotifications),
      language: settings.language || 'pt',
      timezone: settings.timezone || 'Europe/Lisbon',
      currency: settings.currency || 'EUR'
    }

    // Salvar ou atualizar configurações
    const userSettings = await prisma.$queryRaw`
      INSERT INTO user_settings ("id", "userId", preferences, "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), ${session.user.id}, ${JSON.stringify(validSettings)}::jsonb, NOW(), NOW())
      ON CONFLICT ("userId")
      DO UPDATE SET preferences = ${JSON.stringify(validSettings)}::jsonb, "updatedAt" = NOW()
      RETURNING *
    `

    // Se ativou 2FA, gerar código de setup
    let twoFactorSetup = null
    if (validSettings.twoFactorAuth && !settings.twoFactorAuth) {
      // Aqui implementaria a geração do QR code para 2FA
      twoFactorSetup = {
        qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        secret: 'JBSWY3DPEHPK3PXP',
        backupCodes: [
          '123456789',
          '987654321',
          '456789123',
          '789123456',
          '321654987'
        ]
      }
    }

    return NextResponse.json({
      message: 'Configurações salvas com sucesso',
      settings: validSettings,
      twoFactorSetup
    })

  } catch (error) {
    console.error('Erro ao salvar configurações:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
