'use client'

import { useState, useRef, useEffect } from 'react'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { LogOut, User, Settings, CreditCard, LayoutDashboard, Trophy } from 'lucide-react'

interface UserDropdownProps {
  user?: {
    name?: string | null
    email?: string | null
    role?: string} | null
  className?: string}

export default function UserDropdown({ user, className = '' }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', 'handleClickOutside')
    return () => {
      document.removeEventListener('mousedown', 'handleClickOutside')
    }
  }, [])

  // Se não há user, não renderizar o dropdown
  if (!user) {
    return null
}

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  const getAvatarColor = (role?: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-600'
      case 'REPAIR_SHOP':
        return 'bg-emerald-500'
      case 'COURIER':
        return 'bg-purple-600'
      case 'CUSTOMER':
        return 'bg-blue-600'
      default:
        return 'bg-gray-600'
    }
  }

  const getRoleLabel = (role?: string) => {
    switch (role) {
      case 'ADMIN':
        return 'Administrador'
      case 'REPAIR_SHOP':
        return 'Lojista'
      case 'COURIER':
        return 'Estafeta'
      case 'CUSTOMER':
        return 'Cliente'
      default:
        return 'Utilizador'
    }
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`w-7 h-7 ${getAvatarColor(user?.role)} text-white rounded-full flex items-center justify-center text-xs font-medium hover:opacity-80 transition-opacity`}
      >
        {user?.name?.charAt(0).toUpperCase() || 'U'}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* User Info */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 ${getAvatarColor(user?.role)} text-white rounded-full flex items-center justify-center font-medium`}>
                {user?.name?.charAt(0).toUpperCase() || 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.name || 'Utilizador'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {user?.email}
                </p>
                <p className="text-xs text-gray-400">
                  {getRoleLabel(user?.role)}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <button
              onClick={() => {
                setIsOpen(false)
                const dashboardPath = user?.role === 'ADMIN' ? '/admin' :
                                     user?.role === 'REPAIR_SHOP' ? '/lojista' :
                                     user?.role === 'COURIER' ? '/estafeta' :
                                     '/cliente'
                router.push(dashboardPath)
              }}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <LayoutDashboard className="w-4 h-4 mr-3" />
              Dashboard
            </button>

            <button
              onClick={() => {
                setIsOpen(false)
                const profilePath = user?.role === 'REPAIR_SHOP' ? '/lojista/perfil' :
                                   user?.role === 'ADMIN' ? '/admin/profile' : '/profile'
                router.push(profilePath)
              }}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <User className="w-4 h-4 mr-3" />Perfil</button>

            <button
              onClick={() => {
                setIsOpen(false)
                const settingsPath = user?.role === 'REPAIR_SHOP' ? '/lojista/configuracoes' :
                                    user?.role === 'ADMIN' ? '/admin/configuracoes' : '/configuracoes'
                router.push(settingsPath)
              }}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Settings className="w-4 h-4 mr-3" />Configurações</button>

            {/* Progress link for clients */}
            {(user?.role === 'cliente' || user?.role === 'customer' || user?.role === 'CUSTOMER') && (
              <button
                onClick={() => {
                  setIsOpen(false)
                  router.push('/cliente/progresso')
                }}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Trophy className="w-4 h-4 mr-3" />
                O Meu Progresso
              </button>
            )}

            {user?.role === 'REPAIR_SHOP' && (
              <button
                onClick={() => {
                  setIsOpen(false)
                  router.push('/lojista/revify-rewards')
                }}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Trophy className="w-4 h-4 mr-3" />
                Revify Rewards
              </button>
            )}

            {user?.role === 'REPAIR_SHOP' && (
              <button
                onClick={() => {
                  setIsOpen(false)
                  router.push('/lojista/appstore')
                }}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                App Store
              </button>
            )}

            {user?.role === 'REPAIR_SHOP' && (
              <button
                onClick={() => {
                  setIsOpen(false)
                  router.push('/lojista/subscricao')
                }}
                className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <CreditCard className="w-4 h-4 mr-3" />Gerir Subscrição</button>
            )}
          </div>

          {/* Logout */}
          <div className="border-t border-gray-200">
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-3" />Terminar Sessão</button>
          </div>
        </div>
      )}
    </div>
  )
}
