'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Edit, Trash2, Euro, Clock, Search } from 'lucide-react'

interface Category {
  id: string
  name: string
}

interface DeviceModel {
  id: string
  name: string
  brand: {
    name: string
  }
  category: {
    name: string
  }
}

interface ProblemType {
  id: string
  name: string
  icon: string
}

interface RepairShopPrice {
  id: string
  price: number
  estimatedTime: number
  deviceModel?: DeviceModel
  category?: Category
  problemType: ProblemType
}

export default function LojistaPrecos() {
  const [prices, setPrices] = useState<RepairShopPrice[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [models, setModels] = useState<DeviceModel[]>([])
  const [problemTypes, setProblemTypes] = useState<ProblemType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  
  const [newPrice, setNewPrice] = useState({
    deviceModelId: '',
    categoryId: '',
    problemTypeId: '',
    price: '',
    estimatedTime: '60'
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [pricesRes, categoriesRes, modelsRes, problemTypesRes] = await Promise.all([
        fetch('/api/lojista/prices'),
        fetch('/api/admin/categories'),
        fetch('/api/admin/models'),
        fetch('/api/problem-types')
      ])

      if (pricesRes.ok) setPrices(await pricesRes.json())
      if (categoriesRes.ok) setCategories(await categoriesRes.json())
      if (modelsRes.ok) setModels(await modelsRes.json())
      if (problemTypesRes.ok) setProblemTypes(await problemTypesRes.json())
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddPrice = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validação frontend
    if (!newPrice.problemTypeId) {
      alert('Por favor selecione um tipo de problema')
      return
    }

    if (!newPrice.price || parseFloat(newPrice.price) <= 0) {
      alert('Por favor insira um preço válido')
      return
    }

    if (!newPrice.estimatedTime || parseInt(newPrice.estimatedTime) <= 0) {
      alert('Por favor insira um tempo estimado válido')
      return
    }

    if (!newPrice.deviceModelId && !newPrice.categoryId) {
      alert('Por favor selecione um modelo específico ou categoria geral')
      return
    }

    try {
      const payload = {
        deviceModelId: newPrice.deviceModelId || null,
        categoryId: newPrice.categoryId || null,
        problemTypeId: newPrice.problemTypeId,
        price: parseFloat(newPrice.price),
        estimatedTime: parseInt(newPrice.estimatedTime)
      }

      console.log('Enviando dados:', payload)

      const response = await fetch('/api/lojista/prices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const price = await response.json()
        setPrices([...prices, price])
        setNewPrice({
          deviceModelId: '',
          categoryId: '',
          problemTypeId: '',
          price: '',
          estimatedTime: '60'
        })
        setShowAddForm(false)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao adicionar preço')
      }
    } catch (error) {
      console.error('Erro ao adicionar preço:', error)
      alert('Erro ao adicionar preço')
    }
  }

  const handleDeletePrice = async (id: string) => {
    if (!confirm('Tem certeza que deseja eliminar este preço?')) {
      return
    }

    try {
      const response = await fetch(`/api/lojista/prices/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setPrices(prices.filter(p => p.id !== id))
      } else {
        alert('Erro ao eliminar preço')
      }
    } catch (error) {
      console.error('Erro ao eliminar preço:', error)
      alert('Erro ao eliminar preço')
    }
  }

  const filteredPrices = prices.filter(price => {
    const searchLower = searchTerm.toLowerCase()
    return (
      price.problemType.name.toLowerCase().includes(searchLower) ||
      (price.deviceModel && price.deviceModel.name.toLowerCase().includes(searchLower)) ||
      (price.deviceModel && price.deviceModel.brand.name.toLowerCase().includes(searchLower)) ||
      (price.category && price.category.name.toLowerCase().includes(searchLower))
    )
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/lojista" className="text-2xl font-bold text-black">Revify</Link>
              <span className="ml-4 text-sm text-gray-500">Gestão de Preços</span>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Novo Preço
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-600" />
            </div>
            <input
              type="text"
              placeholder="Pesquisar preços..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent placeholder:text-gray-500"
            />
          </div>
        </div>

        {/* Prices Table */}
        {filteredPrices.length === 0 ? (
          <div className="text-center py-12">
            <Euro className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-700 text-lg mb-4 font-medium">
              {searchTerm ? 'Nenhum preço encontrado' : 'Ainda não definiu preços'}
            </div>
            {!searchTerm && (
              <button
                onClick={() => setShowAddForm(true)}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Definir primeiro preço
              </button>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dispositivo/Categoria
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipo de Problema
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Preço
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tempo Estimado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPrices.map((price) => (
                    <tr key={price.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          {price.deviceModel ? (
                            <>
                              <div className="text-sm font-medium text-gray-900">
                                {price.deviceModel.brand.name} {price.deviceModel.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {price.deviceModel.category.name}
                              </div>
                            </>
                          ) : (
                            <div className="text-sm font-medium text-gray-900">
                              {price.category?.name} (Geral)
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{price.problemType.icon}</span>
                          <span className="text-sm font-medium text-gray-900">
                            {price.problemType.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Euro className="w-4 h-4 text-green-600 mr-1" />
                          <span className="text-lg font-semibold text-green-600">
                            {Number(price.price).toFixed(2)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 text-blue-600 mr-1" />
                          <span className="text-sm text-gray-900">
                            {price.estimatedTime < 60 
                              ? `${price.estimatedTime}min` 
                              : `${Math.round(price.estimatedTime / 60)}h`
                            }
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDeletePrice(price.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Add Price Modal */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Novo Preço</h2>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Plus className="w-5 h-5 text-gray-600 rotate-45" />
                </button>
              </div>
              
              <form onSubmit={handleAddPrice} className="space-y-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Tipo de Problema *
                  </label>
                  <select
                    required
                    value={newPrice.problemTypeId}
                    onChange={(e) => setNewPrice({ ...newPrice, problemTypeId: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900 bg-white"
                  >
                    <option value="">Selecionar...</option>
                    {problemTypes.map((type) => (
                      <option key={type.id} value={type.id}>
                        {type.icon} {type.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Modelo Específico (opcional)
                  </label>
                  <select
                    value={newPrice.deviceModelId}
                    onChange={(e) => setNewPrice({ ...newPrice, deviceModelId: e.target.value, categoryId: '' })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900 bg-white"
                  >
                    <option value="">Selecionar modelo específico...</option>
                    {models.map((model) => (
                      <option key={model.id} value={model.id}>
                        {model.brand.name} {model.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Ou Categoria Geral
                  </label>
                  <select
                    value={newPrice.categoryId}
                    onChange={(e) => setNewPrice({ ...newPrice, categoryId: e.target.value, deviceModelId: '' })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900 bg-white"
                  >
                    <option value="">Selecionar categoria...</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2">
                      Preço (€) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      required
                      value={newPrice.price}
                      onChange={(e) => setNewPrice({ ...newPrice, price: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-900 mb-2">
                      Tempo (min) *
                    </label>
                    <input
                      type="number"
                      min="1"
                      required
                      value={newPrice.estimatedTime}
                      onChange={(e) => setNewPrice({ ...newPrice, estimatedTime: e.target.value })}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-600 focus:border-transparent text-gray-900"
                      placeholder="60"
                    />
                  </div>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-800 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="flex-1 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-colors font-medium"
                  >
                    Adicionar
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
