'use client'

import { useState, useEffect } from 'react'
import { AlertTriangle, CheckCircle, TrendingUp, Globe } from 'lucide-react'

interface UsageData {
  characterCount: number
  characterLimit: number
  usagePercentage: number
  remainingCharacters: number
  isNearLimit: boolean
  isCritical: boolean
}

/**
 * Componente para monitorar o uso da API DeepL
 * Mostra estatísticas de uso, alertas e recomendações
 */
export default function DeepLUsageMonitor() {
  const [usage, setUsage] = useState<UsageData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchUsage = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await fetch('/api/deepl/usage')
      
      if (!response.ok) {
        throw new Error('Erro ao obter dados de uso')
      }
      
      const data = await response.json()
      setUsage(data)
      setLastUpdated(new Date())
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchUsage()
    
    // Atualizar a cada 5 minutos
    const interval = setInterval(fetchUsage, 5 * 60 * 1000)
    
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = () => {
    if (!usage) return 'gray'
    if (usage.isCritical) return 'red'
    if (usage.isNearLimit) return 'yellow'
    return 'green'
  }

  const getStatusIcon = () => {
    if (!usage) return <Globe className="w-5 h-5" />
    if (usage.isCritical) return <AlertTriangle className="w-5 h-5 text-red-500" />
    if (usage.isNearLimit) return <AlertTriangle className="w-5 h-5 text-yellow-500" />
    return <CheckCircle className="w-5 h-5 text-green-500" />
  }

  const getStatusMessage = () => {
    if (!usage) return 'Carregando...'
    if (usage.isCritical) return 'Uso crítico! Próximo do limite mensal'
    if (usage.isNearLimit) return 'Atenção: Uso elevado da API'
    return 'Uso normal da API'
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('pt-PT').format(num)
  }

  if (isLoading && !usage) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-2 text-red-600 mb-4">
          <AlertTriangle className="w-5 h-5" />
          <h3 className="font-semibold">Erro no Monitoramento</h3>
        </div>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={fetchUsage}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <h3 className="text-lg font-semibold text-gray-900">
            Uso da API DeepL
          </h3>
        </div>
        <button
          onClick={fetchUsage}
          disabled={isLoading}
          className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors disabled:opacity-50"
        >
          {isLoading ? 'Atualizando...' : 'Atualizar'}
        </button>
      </div>

      {usage && (
        <>
          {/* Status Alert */}
          <div className={`p-4 rounded-lg mb-6 ${
            usage.isCritical ? 'bg-red-50 border border-red-200' :
            usage.isNearLimit ? 'bg-yellow-50 border border-yellow-200' :
            'bg-green-50 border border-green-200'
          }`}>
            <p className={`font-medium ${
              usage.isCritical ? 'text-red-800' :
              usage.isNearLimit ? 'text-yellow-800' :
              'text-green-800'
            }`}>
              {getStatusMessage()}
            </p>
          </div>

          {/* Usage Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatNumber(usage.characterCount)}
              </div>
              <div className="text-sm text-gray-600">Caracteres Usados</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatNumber(usage.remainingCharacters)}
              </div>
              <div className="text-sm text-gray-600">Caracteres Restantes</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {usage.usagePercentage}%
              </div>
              <div className="text-sm text-gray-600">Percentual Usado</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>0</span>
              <span>{formatNumber(usage.characterLimit)} caracteres/mês</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${
                  usage.isCritical ? 'bg-red-500' :
                  usage.isNearLimit ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${Math.min(usage.usagePercentage, 100)}%` }}
              />
            </div>
          </div>

          {/* Recommendations */}
          {usage.isNearLimit && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-semibold text-blue-800 mb-2">Recomendações:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Considere otimizar o cache de traduções</li>
                <li>• Evite traduzir textos muito longos desnecessariamente</li>
                <li>• Monitore o uso diário para evitar surpresas</li>
                {usage.isCritical && (
                  <li>• <strong>Considere upgrade para plano pago</strong></li>
                )}
              </ul>
            </div>
          )}

          {/* Last Updated */}
          {lastUpdated && (
            <div className="text-xs text-gray-500 text-center">
              Última atualização: {lastUpdated.toLocaleString('pt-PT')}
            </div>
          )}
        </>
      )}
    </div>
  )
}
