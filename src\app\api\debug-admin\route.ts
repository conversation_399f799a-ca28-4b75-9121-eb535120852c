import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    console.log('Debug admin endpoint called')
    
    const session = await getServerSession(authOptions)
    console.log('Session:', session)
    
    if (!session) {
      return NextResponse.json(
        { 
          error: 'No session found',
          debug: {
            headers: Object.fromEntries(request.headers.entries()),
            url: request.url
          }
        },
        { status: 401 }
      )
    }

    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { 
          error: 'Not admin',
          debug: {
            userRole: session.user.role,
            userId: session.user.id,
            userEmail: session.user.email
          }
        },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      session: {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role
      },
      debug: {
        timestamp: new Date().toISOString(),
        headers: Object.fromEntries(request.headers.entries())
      }
    })

  } catch (error) {
    console.error('Debug admin error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
