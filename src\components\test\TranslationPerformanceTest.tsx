'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import AutoTranslate, { AutoTranslateList } from '@/components/ui/AutoTranslate'
import { Play, Pause, RotateCcw, Clock, Zap, CheckCircle } from 'lucide-react'

interface TestResult {
  testName: string
  duration: number
  success: boolean
  cacheHit: boolean
  error?: string
}

/**
 * Componente para testar a performance do sistema de traduções
 * Útil para identificar gargalos e otimizar o cache
 */
export default function TranslationPerformanceTest() {
  const { t, tBatch, currentLanguage, changeLanguage } = useTranslation()
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])
  const [currentTest, setCurrentTest] = useState<string>('')

  // Textos de teste de diferentes tamanhos
  const testTexts = {
    short: [
      'O<PERSON><PERSON>',
      'Obri<PERSON>',
      'Por favor',
      '<PERSON><PERSON><PERSON><PERSON>',
      'Sim',
      'N<PERSON>'
    ],
    medium: [
      'Bem-vindo à nossa plataforma de reparações',
      'Selecione o tipo de dispositivo que pretende reparar',
      'Preencha os seus dados de contacto',
      'Escolha o método de pagamento preferido',
      'A sua reparação foi agendada com sucesso'
    ],
    long: [
      'A Revify é a maior plataforma de reparações de dispositivos eletrónicos da Europa, conectando utilizadores a técnicos especializados em todo o continente.',
      'O nosso sistema de traduções automáticas permite que utilizadores de diferentes países acedam aos nossos serviços na sua língua nativa, garantindo uma experiência personalizada.',
      'Utilizamos tecnologia de ponta, incluindo inteligência artificial e APIs de tradução profissionais, para oferecer um serviço rápido, confiável e de alta qualidade.',
      'Desde smartphones e tablets até computadores e consolas de jogos, reparamos todos os tipos de dispositivos com garantia de qualidade e preços competitivos.'
    ]
  }

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    setCurrentTest(testName)
    const startTime = performance.now()
    
    try {
      await testFunction()
      const duration = performance.now() - startTime
      
      setResults(prev => [...prev, {
        testName,
        duration,
        success: true,
        cacheHit: false // Seria necessário implementar detecção de cache
      }])
    } catch (error) {
      const duration = performance.now() - startTime
      
      setResults(prev => [...prev, {
        testName,
        duration,
        success: false,
        cacheHit: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }])
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setResults([])
    setCurrentTest('')

    try {
      // Teste 1: Traduções curtas individuais
      await runTest('Textos Curtos (Individual)', async () => {
        for (const text of testTexts.short) {
          await t(text)
        }
      })

      // Teste 2: Traduções curtas em lote
      await runTest('Textos Curtos (Lote)', async () => {
        await tBatch(testTexts.short)
      })

      // Teste 3: Traduções médias individuais
      await runTest('Textos Médios (Individual)', async () => {
        for (const text of testTexts.medium) {
          await t(text)
        }
      })

      // Teste 4: Traduções médias em lote
      await runTest('Textos Médios (Lote)', async () => {
        await tBatch(testTexts.medium)
      })

      // Teste 5: Traduções longas individuais
      await runTest('Textos Longos (Individual)', async () => {
        for (const text of testTexts.long) {
          await t(text)
        }
      })

      // Teste 6: Traduções longas em lote
      await runTest('Textos Longos (Lote)', async () => {
        await tBatch(testTexts.long)
      })

      // Teste 7: Cache hit (repetir traduções)
      await runTest('Cache Hit Test', async () => {
        // Repetir algumas traduções para testar cache
        await tBatch([...testTexts.short, ...testTexts.medium])
      })

    } finally {
      setIsRunning(false)
      setCurrentTest('')
    }
  }

  const clearResults = () => {
    setResults([])
  }

  const getAverageTime = () => {
    if (results.length === 0) return 0
    const total = results.reduce((sum, result) => sum + result.duration, 0)
    return Math.round(total / results.length)
  }

  const getSuccessRate = () => {
    if (results.length === 0) return 0
    const successful = results.filter(r => r.success).length
    return Math.round((successful / results.length) * 100)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              <AutoTranslate text="Teste de Performance - Traduções" />
            </h2>
            <AutoTranslate text="Avalie a performance do sistema de traduções em diferentes cenários" as="p" className="text-gray-600" />
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={currentLanguage}
              onChange={(e) => changeLanguage(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg"
              disabled={isRunning}
            >
              <option value="pt"><AutoTranslate text="Português" /></option>
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr"><AutoTranslate text="Français" /></option>
            </select>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={runAllTests}
              disabled={isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              <span>{isRunning ? 'Executando...' : 'Iniciar Testes'}</span>
            </button>
            
            <button
              onClick={clearResults}
              disabled={isRunning}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
            >
              <RotateCcw className="w-4 h-4" />
              <AutoTranslate text="Limpar" />
            </button>
          </div>

          {/* Current Test */}
          {currentTest && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm font-medium">{currentTest}</span>
            </div>
          )}
        </div>
      </div>

      {/* Summary Stats */}
      {results.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
            <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{getAverageTime()}ms</div>
            <AutoTranslate text="Tempo Médio" as="div" className="text-sm text-gray-600" />
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
            <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{getSuccessRate()}%</div>
            <AutoTranslate text="Taxa de Sucesso" as="div" className="text-sm text-gray-600" />
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
            <Zap className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{results.length}</div>
            <AutoTranslate text="Testes Executados" as="div" className="text-sm text-gray-600" />
          </div>
        </div>
      )}

      {/* Results Table */}
      {results.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              <AutoTranslate text="Resultados dos Testes" />
            </h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <AutoTranslate text="Teste" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <AutoTranslate text="Duração" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <AutoTranslate text="Status" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <AutoTranslate text="Cache" />
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {results.map((result, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {result.testName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {Math.round(result.duration)}ms
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        result.success 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {result.success ? 'Sucesso' : 'Erro'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.cacheHit ? 'Hit' : 'Miss'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

    </div>
  )
}
