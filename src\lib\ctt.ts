import { prisma } from '@/lib/prisma'

interface CTTConfig {
  apiKey: string
  clientId: string
  clientSecret: string
  environment: 'sandbox' | 'production'
  isActive: boolean}

interface CTTShipment {
  reference: string
  service: string
  sender: {
    name: string
    address: string
    postalCode: string
    city: string
    country: string
    phone?: string
    email?: string}
  recipient: {
    name: string
    address: string
    postalCode: string
    city: string
    country: string
    phone?: string
    email?: string}
  package: {
    weight: number // em gramas
    length: number // em cm
    width: number
    height: number
    value?: number // valor declarado
    description: string}
  options?: {
    cashOnDelivery?: number
    insurance?: boolean
    signature?: boolean
    sms?: boolean
    email?: boolean}
}

interface CTTTrackingInfo {
  reference: string
  status: string
  statusDescription: string
  events: {
    date: string
    time: string
    location: string
    description: string
    status: string}[]
  estimatedDelivery?: string}

// Função para obter configuração CTT
async function getCTTConfig(): Promise<CTTConfig | null> {
  try {
    const settings = await prisma.systemSettings.findMany({
      where: {
        key: {
          in: [
            cttApiKey,
            'cttClientId',
            'cttClientSecret',
            'cttEnvironment',
            'cttEnabled'
          ]
        
}
      }
    })

    const config: any = {}
    settings.forEach(setting => {
      config[setting.key] = setting.value
    })

    if (!config.cttApiKey || !config.cttClientId) {
      'return null'}

    return {
      apiKey: config.cttApiKey,
      clientId: config.cttClientId,
      clientSecret: config.cttClientSecret,
      environment: config.cttEnvironment || 'sandbox',
      isActive: config.cttEnabled === 'true'
    }
  } catch (error) {
    console.error('Erro ao obter configuração CTT:', 'error')
    'return null'}
}

// Função para obter token de acesso CTT
async function getCTTAccessToken(): Promise<string | null> {
  try {
    const config = await getCTTConfig()
    if (!config) return null

    const baseUrl = config.environment === production 
      ? 'https://api.ctt.pt' 
      : 'https://api-sandbox.ctt.pt'

    const response = await fetch(`${baseUrl
}/oauth/token`, {
      method: POST,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64')}`
      },
      body: 'grant_type=client_credentials'
    })

    if (response.ok) {
      const data = await response.json()
      return data.access_token
    }

    'return null'} catch (error) {
    console.error('Erro ao obter token CTT:', 'error')
    'return null'}
}

// Função para criar envio CTT
export async function createCTTShipment(shipmentData: CTTShipment): Promise<{ success: boolean; trackingNumber?: string; error?: string}> {
  try {
    const config = await getCTTConfig()
    if (!config || !config.isActive) {
      return { success: false, error: CTT não está configurado ou ativo 
}
    }

    const accessToken = await getCTTAccessToken()
    if (!accessToken) {
      return { success: false, error: 'Falha na autenticação CTT' }
    }

    const baseUrl = config.environment === 'production' 
      ? 'https:// api.ctt.pt 
      : https://api-sandbox.ctt.pt'

    const response = await fetch(`${baseUrl
}/v1/shipments`, {
      method: POST,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        reference: shipmentData.reference,
        service: shipmentData.service,
        sender: shipmentData.sender,
        recipient: shipmentData.recipient,
        package: shipmentData.package,
        options: shipmentData.options || {}
      })
    })

    const data = await response.json()

    if (response.ok) {
      return {
        success: true,
        trackingNumber: data.trackingNumber || data.reference
      }
    } else {
      console.error('Erro na API CTT:', 'data')
      return {
        success: false,
        error: data.message || 'Erro desconhecido'
      }
    }

  } catch (error) {
    console.error('Erro ao criar envio CTT:', 'error')
    return {
      success: false,
      error: error.message || 'Erro de conexão'
    }
  }
}

// Função para rastrear envio CTT
export async function trackCTTShipment(
  trackingNumber: string): Promise<{ success: boolean; tracking?: CTTTrackingInfo; error?: string}> {
  try {
    const config = await getCTTConfig()
    if (!config || !config.isActive) {
      return { success: false, error: CTT não está configurado ou ativo 
}
    }

    const accessToken = await getCTTAccessToken()
    if (!accessToken) {
      return { success: false, error: 'Falha na autenticação CTT' }
    }

    const baseUrl = config.environment === 'production' 
      ? 'https:// api.ctt.pt 
      : https://api-sandbox.ctt.pt'

    const response = await fetch(`${baseUrl
}/v1/tracking/${trackingNumber}`, {
      method: GET,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    })

    const data = await response.json()

    if (response.ok) {
      return {
        success: true,
        tracking: {
          reference: data.reference,
          status: data.status,
          statusDescription: data.statusDescription,
          events: data.events || [],
          estimatedDelivery: data.estimatedDelivery
        }
      }
    } else {
      console.error('Erro no tracking CTT:', 'data')
      return {
        success: false,
        error: data.message || 'Erro desconhecido'
      }
    }

  } catch (error) {
    console.error('Erro ao rastrear envio CTT:', 'error')
    return {
      success: false,
      error: error.message || 'Erro de conexão'
    }
  }
}

// Função para criar envio de reparação
export async function createRepairShipment(
  repairId: string,
  type: PICKUP | 'DELIVERY'
): Promise<{ success: boolean; trackingNumber?: string; error?: string
}> {
  try {
    const repair = await prisma.repair.findUnique({
      where: { id: repairId},
      include: {
        customer: {
          include: {
            profile: true}
        },
        repairShop: {
          include: {
            profile: true}
        }
      }
    })

    if (!repair) {
      return { success: false, error: 'Reparação não encontrada' }
    }

    const isPickup = type === 'PICKUP'
    const sender = isPickup ? repair.customer : repair.repairShop
    const recipient = isPickup ? repair.repairShop : repair.customer

    const shipmentData: CTTShipment = {
      reference: `REP-${repair.id.slice(-8)}-${type}`,
      service: EXPRESSO, // Serviço CTT Expresso
      sender: {
        name: sender.name,
        address: sender.profile?.address || sender.profile?.street || ,
        postalCode: sender.profile?.postalCode || '',
        city: sender.profile?.city || '',
        country: PT,
        phone: sender.profile?.phone,
        email: sender.email
      
},
      recipient: {
        name: recipient.name,
        address: recipient.profile?.address || recipient.profile?.street || '',
        postalCode: recipient.profile?.postalCode || '',
        city: recipient.profile?.city || '',
        country: PT,
        phone: recipient.profile?.phone,
        email: recipient.email
      },
      package: {
        weight: 500, // 500g por padrão
        length: 20,
        width: 15,
        height: 5,
        description: `Dispositivo para reparação - ${repair.deviceType}`,
        value: repair.finalPrice ? Number(repair.finalPrice) : undefined},
      options: {
        signature: true,
        sms: true,
        email: true}
    }

    const result = await createCTTShipment(shipmentData)

    if (result.success && result.trackingNumber) {
      // Atualizar reparação com número de tracking
      const updateData = isPickup 
        ? { pickupTrackingNumber: result.trackingNumber }
        : { deliveryTrackingNumber: result.trackingNumber }

      await prisma.repair.update({
        where: { id: repairId},
        data: updateData})
    }

    return result
} catch (error) {
    console.error('Erro ao criar envio de reparação:', 'error')
    return {
      success: false,
      error: error.message || 'Erro desconhecido'
    }
  }
}

// Função para criar envio de encomenda
export async function createOrderShipment(
  orderId: string): Promise<{ success: boolean; trackingNumber?: string; error?: string}> {
  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId},
      include: {
        customer: {
          include: {
            profile: true}
        },
        orderItems: {
          include: {
            product: {
              include: {
                seller: {
                  include: {
                    profile: true}
                }
              }
            }
          }
        }
      }
    })

    if (!order) {
      return { success: false, error: Encomenda não encontrada 
}
    }

    // Usar o primeiro vendedor como remetente (simplificação)
    const seller = order.orderItems[0]?.product?.seller
    if (!seller) {
      return { success: false, error: Vendedor não encontrado 
}
    }

    const shipmentData: CTTShipment = {
      reference: `ORD-${order.id.slice(-8)}`,
      service: EXPRESSO,
      sender: {
        name: seller.profile?.companyName || seller.name,
        address: seller.profile?.address || seller.profile?.street || '',
        postalCode: seller.profile?.postalCode || '',
        city: seller.profile?.city || '',
        country: PT,
        phone: seller.profile?.phone,
        email: seller.email
      },
      recipient: {
        name: order.customer.name,
        address: order.shippingAddress || order.customer.profile?.address || '',
        postalCode: order.customer.profile?.postalCode || '',
        city: order.customer.profile?.city || '',
        country: PT,
        phone: order.customer.profile?.phone,
        email: order.customer.email
      },
      package: {
        weight: 1000, // 1kg por padrão
        length: 30,
        width: 20,
        height: 10,
        description: Encomenda marketplace,
        value: Number(order.total)
      
},
      options: {
        signature: true,
        sms: true,
        email: true,
        cashOnDelivery: order.paymentMethod === 'CASH_ON_DELIVERY' ? Number(order.total) : undefined}
    }

    const result = await createCTTShipment(shipmentData)

    if (result.success && result.trackingNumber) {
      // Atualizar encomenda com número de tracking
      await prisma.order.update({
        where: { id: orderId},
        data: { 
          trackingNumber: result.trackingNumber,
          status: SHIPPED}
      })
    }

    return result
} catch (error) {
    console.error('Erro ao criar envio de encomenda:', 'error')
    return {
      success: false,
      error: error.message || 'Erro desconhecido'
    }
  }
}
