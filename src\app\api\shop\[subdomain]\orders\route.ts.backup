import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email'

interface OrderItem {
  productId: string
  quantity: number
  price: number
}

interface Customer {
  email: string
  name: string
  phone: string
  address?: string | null
  city?: string | null
  postalCode?: string | null
}

interface OrderData {
  items: OrderItem[]
  customer: Customer
  deliveryMethod: 'delivery' | 'pickup'
  paymentMethod: 'card' | 'multibanco' | 'paypal'
  total: number
  shopSubdomain: string
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params
    const orderData: OrderData = await request.json()

    try {
      // Find the shop by subdomain
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain
          }
        },
        include: {
          profile: true
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: 'Loja não encontrada' },
          { status: 404 }
        )
      }

      // Validate products exist and have enough stock
      const productIds = orderData.items.map(item => item.productId)
      const products = await prisma.product.findMany({
        where: {
          id: { in: productIds },
          userId: shop.id
        }
      })

      if (products.length !== productIds.length) {
        return NextResponse.json(
          { error: 'Alguns produtos não foram encontrados' },
          { status: 400 }
        )
      }

      // Check stock availability
      for (const item of orderData.items) {
        const product = products.find(p => p.id === item.productId)
        if (!product) {
          return NextResponse.json(
            { error: `Produto ${item.productId} não encontrado` },
            { status: 400 }
          )
        }
        
        if (product.stock < item.quantity) {
          return NextResponse.json(
            { error: `Stock insuficiente para ${product.name}` },
            { status: 400 }
          )
        }
      }

      // Create order in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the order
        const order = await tx.order.create({
          data: {
            shopId: shop.id,
            customerEmail: orderData.customer.email,
            customerName: orderData.customer.name,
            customerPhone: orderData.customer.phone,
            customerAddress: orderData.customer.address,
            customerCity: orderData.customer.city,
            customerPostalCode: orderData.customer.postalCode,
            deliveryMethod: orderData.deliveryMethod,
            paymentMethod: orderData.paymentMethod,
            total: orderData.total,
            status: 'pending',
            items: {
              create: orderData.items.map(item => ({
                productId: item.productId,
                quantity: item.quantity,
                price: item.price,
                total: item.price * item.quantity
              }))
            }
          },
          include: {
            items: {
              include: {
                product: true
              }
            }
          }
        })

        // Update product stock
        for (const item of orderData.items) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity
              }
            }
          })
        }

        return order
      })

      // Generate order number
      const orderNumber = `ORD-${result.id.slice(-8).toUpperCase()}`

      // Update order with order number
      await prisma.order.update({
        where: { id: result.id },
        data: { orderNumber }
      })

      // Send confirmation email
      try {
        await sendEmail(
          orderData.customer.email,
          'orderConfirmation',
          {
            customerName: orderData.customer.name,
            orderNumber,
            items: orderData.items.map(item => ({
              name: `Produto ${item.productId}`, // In real app, you'd fetch product name
              quantity: item.quantity,
              price: item.price.toFixed(2)
            })),
            total: orderData.total.toFixed(2),
            deliveryMethod: orderData.deliveryMethod,
            customerAddress: orderData.customer.address,
            customerCity: orderData.customer.city,
            customerPostalCode: orderData.customer.postalCode,
            shopName: shop.name || shop.companyName || 'Loja'
          }
        )
      } catch (emailError) {
        console.error('Erro ao enviar email de confirmação:', emailError)
        // Don't fail the order creation if email fails
      }

      return NextResponse.json({
        success: true,
        orderId: result.id,
        orderNumber,
        message: 'Encomenda criada com sucesso'
      })

    } catch (dbError) {
      console.error('Erro de base de dados:', dbError)
      throw dbError
    }

  } catch (error) {
    console.error('Erro ao criar encomenda:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params
    const { searchParams } = new URL(request.url)
    const customerEmail = searchParams.get('email')

    try {
      // Find the shop by subdomain
      const shop = await prisma.user.findFirst({
        where: {
          profile: {
            customSubdomain: subdomain
          }
        }
      })

      if (!shop) {
        return NextResponse.json(
          { error: 'Loja não encontrada' },
          { status: 404 }
        )
      }

      // Build where clause
      const whereClause: any = {
        shopId: shop.id
      }

      if (customerEmail) {
        whereClause.customerEmail = customerEmail
      }

      // Get orders
      const orders = await prisma.order.findMany({
        where: whereClause,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  images: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return NextResponse.json({
        success: true,
        orders
      })

    } catch (dbError) {
      console.error('Erro de base de dados na busca:', dbError)
      throw dbError
    }

  } catch (error) {
    console.error('Erro ao buscar encomendas:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
