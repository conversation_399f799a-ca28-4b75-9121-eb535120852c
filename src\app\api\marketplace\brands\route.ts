import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const brands = await prisma.brand.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            deviceModels: true}
        }
      },
      orderBy: {
        name: asc}
    })

    const formattedBrands = brands.map(brand => ({
      id: brand.id,
      name: brand.name,
      productCount: brand._count.deviceModels
    }))

    return NextResponse.json(formattedBrands)

  } catch (error) {
    console.error('Erro ao buscar marcas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
