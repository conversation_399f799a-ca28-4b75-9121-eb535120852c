'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Search, Filter, ShoppingCart, Star, Heart, Grid, List } from 'lucide-react'
import { AlertModal } from '@/components/ui/modal'
import { useTranslation } from '@/hooks/useTranslation'
import ModernLayout from '@/components/ModernLayout'

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  images: string[]
  condition: string
  rating: number
  reviewCount: number
  stock: number
  category: {
    name: string
  }
  brand: {
    name: string
  }
  deviceModel?: {
    name: string
  }
  seller: {
    name: string
    rating: number
  }
}

interface Category {
  id: string
  name: string
  productCount: number
}

interface Brand {
  id: string
  name: string
  productCount: number
}

export default function MarketplacePage() {
  const { data: session, status } = useSession()
  const { t } = useTranslation()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [brands, setBrands] = useState<Brand[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedBrand, setSelectedBrand] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [showFilters, setShowFilters] = useState(false)
  const [addingToCart, setAddingToCart] = useState<string | null>(null)
  const [cartCount, setCartCount] = useState(0)
  const [recommendedProducts, setRecommendedProducts] = useState<Product[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 12
  const [modal, setModal] = useState<{
    isOpen: boolean
    title: string
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'info'
  })

  useEffect(() => {
    fetchData()
  }, [selectedCategory, selectedBrand, sortBy, currentPage])

  useEffect(() => {
    if (status !== 'loading') {
      fetchCartCount()
      fetchRecommendedProducts()
    }
  }, [status, session])

  const fetchRecommendedProducts = async () => {
    try {
      const response = await fetch('/api/marketplace/products/recommended?limit=8')
      if (response.ok) {
        const data = await response.json()
        setRecommendedProducts(data.products)
      }
    } catch (error) {
      console.error('Erro ao buscar produtos recomendados:', error)
    }
  }

  const fetchCartCount = async () => {
    if (status !== 'authenticated' || !session?.user) {
      setCartCount(0)
      return
    }

    try {
      const response = await fetch('/api/marketplace/cart')
      if (response.ok) {
        const cartData = await response.json()
        setCartCount(cartData.items?.length || 0)
      } else if (response.status === 401) {
        setCartCount(0)
      }
    } catch (error) {
      console.error('Erro ao buscar carrinho:', error)
      setCartCount(0)
    }
  }

  const fetchData = async () => {
    setIsLoading(true)
    try {
      const [productsRes, categoriesRes, brandsRes] = await Promise.all([
        fetch(`/api/marketplace/products?category=${selectedCategory}&brand=${selectedBrand}&sort=${sortBy}&search=${searchTerm}&page=${currentPage}&limit=${itemsPerPage}`),
        fetch('/api/marketplace/categories'),
        fetch('/api/marketplace/brands')
      ])

      if (productsRes.ok) {
        const productsData = await productsRes.json()
        setProducts(productsData.products)
        setTotalPages(Math.ceil(productsData.total / itemsPerPage))
      }

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData)
      }

      if (brandsRes.ok) {
        const brandsData = await brandsRes.json()
        setBrands(brandsData)
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    fetchData()
  }

  const showModal = (title: string, message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setModal({
      isOpen: true,
      title,
      message,
      type
    })
  }

  const addToCart = async (productId: string, requestedQuantity: number = 1) => {
    setAddingToCart(productId)
    try {
      // Verificar stock antes de adicionar
      const product = products.find(p => p.id === productId)
      if (!product) {
        showModal('Erro', 'Produto não encontrado', 'error')
        return
      }

      if (product.stock <= 0) {
        showModal('Indisponível', 'Produto esgotado', 'error')
        return
      }

      if (requestedQuantity > product.stock) {
        showModal('Stock Limitado', `Apenas ${product.stock} unidade${product.stock > 1 ? 's' : ''} em stock`, 'warning')
        return
      }

      const response = await fetch('/api/marketplace/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId,
          quantity: requestedQuantity
        })
      })

      if (response.ok) {
        showModal('Sucesso', 'Produto adicionado ao carrinho!', 'success')
        fetchCartCount()
        // Atualizar contador no header se disponível
        if (window.updateCartCount) {
          window.updateCartCount()
        }
      } else if (response.status === 401) {
        window.location.href = '/auth/signin'
      } else {
        const error = await response.json()
        showModal('Erro', error.message || 'Não foi possível adicionar o produto ao carrinho', 'error')
      }
    } catch (error) {
      console.error('Erro ao adicionar ao carrinho:', error)
      showModal('Erro', 'Erro ao adicionar ao carrinho', 'error')
    } finally {
      setAddingToCart(null)
    }
  }

  return (
    <ModernLayout showCart={true}>
      <style jsx>{`
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
        
        .product-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .product-card:hover {
          transform: translateY(-2px);
        }
      `}</style>



      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Hero Banner */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-gray-900 via-gray-800 to-black mb-12">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>

          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 border border-white/20 rounded-full"></div>
            <div className="absolute top-20 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
            <div className="absolute bottom-10 left-1/4 w-16 h-16 border border-white/20 rounded-full"></div>
          </div>

          <div className="relative px-8 py-12 lg:px-16 lg:py-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div className="text-white">
                <h1 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Marketplace
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                    Tecnologia Premium
                  </span>
                </h1>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  Descubra dispositivos recondicionados, acessórios originais e peças de qualidade das melhores lojas certificadas da Europa.
                </p>
                <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    Garantia Incluída
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                    Lojas Verificadas
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                    Entrega Rápida
                  </div>
                </div>
              </div>

              {/* Device Images */}
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  {/* Apple */}
                  <div className="relative group">
                    <div className="bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl p-6 transform group-hover:scale-105 transition-transform duration-300">
                      <div className="w-16 h-16 bg-white rounded-xl mb-4 flex items-center justify-center">
                        <svg className="w-10 h-10 text-black" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                      </div>
                      <h3 className="text-white font-semibold mb-1">iPhone</h3>
                      <p className="text-gray-400 text-sm">Apple</p>
                    </div>
                  </div>

                  {/* Samsung */}
                  <div className="relative group mt-8">
                    <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl p-6 transform group-hover:scale-105 transition-transform duration-300">
                      <div className="w-16 h-16 bg-white rounded-xl mb-4 flex items-center justify-center">
                        <svg className="w-10 h-10 text-black" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                      </div>
                      <h3 className="text-white font-semibold mb-1">Galaxy</h3>
                      <p className="text-blue-200 text-sm">Samsung</p>
                    </div>
                  </div>

                  {/* Xiaomi */}
                  <div className="relative group -mt-4">
                    <div className="bg-gradient-to-br from-orange-600 to-red-600 rounded-2xl p-6 transform group-hover:scale-105 transition-transform duration-300">
                      <div className="w-16 h-16 bg-white rounded-xl mb-4 flex items-center justify-center">
                        <svg className="w-10 h-10 text-black" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19.96 20.04L12 12.08l-7.96 7.96c.78.78 2.05.78 2.83 0L12 14.91l5.13 5.13c.78.78 2.05.78 2.83 0zM4.04 4.04c-.78.78-.78 2.05 0 2.83L12 14.91l7.96-7.96c.78-.78.78-2.05 0-2.83L12 12.08 4.04 4.04z"/>
                        </svg>
                      </div>
                      <h3 className="text-white font-semibold mb-1">Mi Series</h3>
                      <p className="text-orange-200 text-sm">Xiaomi</p>
                    </div>
                  </div>

                  {/* Accessories */}
                  <div className="relative group">
                    <div className="bg-gradient-to-br from-purple-600 to-purple-700 rounded-2xl p-6 transform group-hover:scale-105 transition-transform duration-300">
                      <div className="w-16 h-16 bg-white rounded-xl mb-4 flex items-center justify-center">
                        <svg className="w-10 h-10 text-black" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                        </svg>
                      </div>
                      <h3 className="text-white font-semibold mb-1">Acessórios</h3>
                      <p className="text-purple-200 text-sm">Premium</p>
                    </div>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-pulse"></div>
                <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-green-400 rounded-full animate-pulse delay-1000"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Explorar Produtos</h2>
          <p className="text-gray-600">Encontre o que procura nas nossas lojas certificadas</p>
        </div>

        {/* Search Bar */}
        <div className="mb-8 flex justify-center">
          <div className="relative w-full max-w-2xl">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-12 pr-4 py-3 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-900 focus:border-transparent shadow-sm"
              placeholder="Pesquisar produtos..."
            />
          </div>
        </div>

        {/* Top Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </button>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-900 bg-white"
            >
              <option value="newest">Newest</option>
              <option value="price_low">Price: Low to High</option>
              <option value="price_high">Price: High to Low</option>
              <option value="rating">Best Rated</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'text-gray-900 bg-gray-100' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <List className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'text-gray-900 bg-gray-100' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Grid className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mb-6 p-4 bg-white rounded-lg border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-900"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name} ({category.productCount})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Brand
                </label>
                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-900"
                >
                  <option value="">All Brands</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name} ({brand.productCount})
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSelectedCategory('')
                    setSelectedBrand('')
                    setSearchTerm('')
                    setCurrentPage(1)
                    fetchData()
                  }}
                  className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Recommended Products */}
        {recommendedProducts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {session ? 'Recomendado para si' : 'Produtos em destaque'}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
              {recommendedProducts.slice(0, 4).map((product) => (
                <Link key={product.id} href={`/marketplace/products/${product.id}`}>
                  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow product-card">
                    <div className="aspect-square relative">
                      <img
                        src={product.images[0] || '/placeholder-product.jpg'}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                      {product.originalPrice && (
                        <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                          -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                        </div>
                      )}
                      {product.seller.isFeatured && (
                        <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                          ⭐
                        </div>
                      )}
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
                        {product.name}
                      </h3>
                      <p className="text-xs text-gray-500 mb-2">
                        {product.brand?.name} • {product.category?.name}
                      </p>
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-lg font-bold text-gray-900">€{product.price}</span>
                          {product.originalPrice && (
                            <span className="text-sm text-gray-500 line-through ml-1">
                              €{product.originalPrice}
                            </span>
                          )}
                        </div>
                        {product.seller.isVerified && (
                          <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Products Grid */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No products found</div>
          </div>
        ) : (
          <>
            <div className="mb-4 text-sm text-gray-600">
              Show: {products.length} {products.length === 1 ? 'product' : 'products'}
            </div>

            <div className={viewMode === 'grid' ?
              "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" :
              "space-y-4"
            }>
              {products.map((product) => {
                if (viewMode === 'grid') {
                  return (
                    <div key={product.id} className="product-card bg-white rounded-xl border border-gray-100 overflow-hidden hover:shadow-lg group">
                      <Link href={`/marketplace/products/${product.id}`}>
                        <div className="aspect-square bg-gray-50 relative overflow-hidden">
                          {product.images.length > 0 ? (
                            <img
                              src={product.images[0]}
                              alt={product.name}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400">
                              <div className="text-center">
                                <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-2"></div>
                                <span className="text-sm">No Image</span>
                              </div>
                            </div>
                          )}

                          <div className="absolute top-3 left-3">
                            <span className={`px-2 py-1 text-xs font-medium rounded-md ${
                              product.condition === 'NEW' ? 'bg-green-500 text-white' :
                              product.condition === 'LIKE_NEW' ? 'bg-blue-500 text-white' :
                              product.condition === 'GOOD' ? 'bg-yellow-500 text-white' :
                              'bg-gray-500 text-white'
                            }`}>
                              {product.condition === 'NEW' ? 'Novo' :
                               product.condition === 'LIKE_NEW' ? 'Como Novo' :
                               product.condition === 'GOOD' ? 'Bom Estado' : 'Usado'}
                            </span>
                          </div>

                          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <button className="p-2 bg-white rounded-lg shadow-sm hover:bg-gray-50 transition-colors mb-2">
                              <Heart className="w-4 h-4 text-gray-600" />
                            </button>
                          </div>
                        </div>
                      </Link>

                      <div className="p-4">
                        <div className="mb-2">
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            {product.category.name}
                          </span>
                        </div>

                        <Link href={`/marketplace/products/${product.id}`}>
                          <h3 className="font-semibold text-gray-900 mb-1 hover:text-gray-700 transition-colors line-clamp-2">
                            {product.name}
                          </h3>
                        </Link>

                        <p className="text-sm text-gray-600 mb-3">
                          {product.brand.name}
                        </p>

                        <div className="flex flex-col space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                              <span className="text-lg font-bold text-gray-900">
                                €{Number(product.price).toFixed(2)}
                              </span>
                              {product.originalPrice && Number(product.originalPrice) > Number(product.price) && (
                                <span className="text-sm text-gray-500 line-through">
                                  €{Number(product.originalPrice).toFixed(2)}
                                </span>
                              )}
                            </div>

                            <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                              <Heart className="w-4 h-4" />
                            </button>
                          </div>

                          {/* Stock Info */}
                          <div className="text-xs text-gray-500 mb-2">
                            {product.stock > 0 ? (
                              product.stock <= 5 ? (
                                <span className="text-orange-600">Apenas {product.stock} em stock</span>
                              ) : (
                                <span className="text-green-600">Em stock</span>
                              )
                            ) : (
                              <span className="text-red-600">Esgotado</span>
                            )}
                          </div>

                          <button
                            onClick={(e) => {
                              e.preventDefault()
                              addToCart(product.id)
                            }}
                            disabled={addingToCart === product.id || product.stock <= 0}
                            className={`w-full py-2 px-4 rounded-lg transition-colors font-medium disabled:opacity-50 flex items-center justify-center ${
                              product.stock <= 0
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-black text-white hover:bg-gray-800'
                            }`}
                          >
                            {addingToCart === product.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : product.stock <= 0 ? (
                              'Indisponível'
                            ) : (
                              <>
                                <ShoppingCart className="w-4 h-4 mr-2" />
                                {t('addToCart') || 'Adicionar'}
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                } else {
                  return (
                    <div key={product.id} className="product-card bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-lg group">
                      <div className="flex">
                        <Link href={`/marketplace/products/${product.id}`} className="flex-shrink-0">
                          <div className="w-32 h-32 bg-gray-50 relative overflow-hidden">
                            {product.images.length > 0 ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-gray-400">
                                <div className="text-center">
                                  <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-1"></div>
                                  <span className="text-xs">No Image</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </Link>

                        <div className="flex-1 p-4 flex justify-between">
                          <div className="flex-1">
                            <div className="mb-1">
                              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                {product.category.name}
                              </span>
                            </div>

                            <Link href={`/marketplace/products/${product.id}`}>
                              <h3 className="font-semibold text-lg text-gray-900 mb-1 hover:text-gray-700 transition-colors">
                                {product.name}
                              </h3>
                            </Link>

                            <p className="text-sm text-gray-600 mb-2">
                              {product.brand.name}
                            </p>

                            <div className="flex items-center space-x-4">
                              <span className={`px-2 py-1 text-xs font-medium rounded-md ${
                                product.condition === 'NEW' ? 'bg-green-500 text-white' :
                                product.condition === 'LIKE_NEW' ? 'bg-blue-500 text-white' :
                                product.condition === 'GOOD' ? 'bg-yellow-500 text-white' :
                                'bg-gray-500 text-white'
                              }`}>
                                {product.condition === 'NEW' ? 'Novo' :
                                 product.condition === 'LIKE_NEW' ? 'Como Novo' :
                                 product.condition === 'GOOD' ? 'Bom Estado' : 'Usado'}
                              </span>
                            </div>
                          </div>

                          <div className="flex flex-col justify-between items-end space-y-3">
                            <div className="text-right">
                              <span className="text-xl font-bold text-gray-900">
                                €{Number(product.price).toFixed(2)}
                              </span>
                              {product.originalPrice && Number(product.originalPrice) > Number(product.price) && (
                                <div className="text-sm text-gray-500 line-through">
                                  €{Number(product.originalPrice).toFixed(2)}
                                </div>
                              )}
                            </div>

                            <div className="flex flex-col space-y-2">
                              <div className="flex items-center space-x-2">
                                <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
                                  <Heart className="w-4 h-4" />
                                </button>
                              </div>

                              <div className="flex flex-col items-end space-y-2">
                                <div className="text-xs text-gray-500">
                                  {product.stock > 0 ? (
                                    product.stock <= 5 ? (
                                      <span className="text-orange-600">Apenas {product.stock} em stock</span>
                                    ) : (
                                      <span className="text-green-600">Em stock</span>
                                    )
                                  ) : (
                                    <span className="text-red-600">Esgotado</span>
                                  )}
                                </div>

                                <button
                                  onClick={(e) => {
                                    e.preventDefault()
                                    addToCart(product.id)
                                  }}
                                  disabled={addingToCart === product.id || product.stock <= 0}
                                  className={`py-2 px-4 rounded-lg transition-colors font-medium disabled:opacity-50 flex items-center justify-center text-sm ${
                                    product.stock <= 0
                                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                      : 'bg-black text-white hover:bg-gray-800'
                                  }`}
                                >
                                  {addingToCart === product.id ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                  ) : product.stock <= 0 ? (
                                    'Indisponível'
                                  ) : (
                                    <>
                                      <ShoppingCart className="w-4 h-4 mr-2" />
                                      {t('addToCart') || 'Adicionar'}
                                    </>
                                  )}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                }
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center mt-8">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`w-8 h-8 rounded-lg text-sm font-medium transition-colors ${
                            currentPage === pageNum
                              ? 'bg-gray-900 text-white'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="p-2 text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <AlertModal
        isOpen={modal.isOpen}
        onClose={() => setModal((prev) => ({ ...prev, isOpen: false }))}
        title={modal.title}
        message={modal.message}
        type={modal.type}
      />
    </ModernLayout>
  )
}
