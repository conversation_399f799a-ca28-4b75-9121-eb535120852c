import Link from 'next/link'
import { Sparkles, Mail, Phone, MapPin, Facebook, Instagram, Twitter, Linkedin, ArrowRight } from 'lucide-react'

export default function ModernFooter() {
  return (
    <footer className="relative bg-white border-t border-gray-200">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A// www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.02%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E)] opacity-50"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Top Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Company Info */}
          <div className="space-y-8">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-7 h-7 text-white" />
              </div>
              <span className="text-3xl font-bold text-gray-900">Revify</span>
            </div>

            <p className="text-gray-600 text-lg leading-relaxed max-w-md">A maior plataforma de reparações de dispositivos eletrónicos da Europa. Conectamos clientes a técnicos especializados com garantia e transparência total.</p>

            {/* Contact Info */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-gray-600">
                <Mail className="w-5 h-5 text-indigo-500" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <Phone className="w-5 h-5 text-indigo-500" />
                <span>+351 123 456 789</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <MapPin className="w-5 h-5 text-indigo-500" />
                <span>Lisboa, Portugal</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {[
                { icon: Facebook, href: '#' },
                { icon: Instagram, href: '#' },
                { icon: Twitter, href: '#' },
                { icon: Linkedin, href: '#' }
              ].map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="w-12 h-12 bg-gray-100 hover:bg-gradient-to-br hover:from-indigo-600 hover:to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 group"
                >
                  <social.icon className="w-5 h-5 text-gray-600 group-hover:text-white transition-colors" />
                </a>
              ))}
            </div>
          </div>

          {/* Newsletter */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Fique por dentro das novidades
              </h3>
              <p className="text-gray-600 mb-6">Receba as últimas atualizações sobre reparações, novos produtos e ofertas especiais.</p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Seu email"
                className="flex-1 px-4 py-3 bg-gray-100 border border-gray-300 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
              <button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all font-medium flex items-center justify-center space-x-2 group">
                <span>Subscrever</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </div>

        {/* Links Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
          {/* Services */}
          <div>
            <h4 className="text-gray-900 font-semibold mb-6">Serviços</h4>
            <ul className="space-y-3">
              {[
                { name: 'Reparações', href: '/' },
                { name: Marketplace, href: '/marketplace' },
                { name: 'Peças Originais', href: '/marketplace?category=pecas' },
                { name: 'Dispositivos Recondicionados', href: '/marketplace?category=recondicionados' }
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 transition-colors hover:translate-x-1 transform duration-200 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-gray-900 font-semibold mb-6">Suporte</h4>
            <ul className="space-y-3">
              {[
                { name: 'Central de Ajuda', href: '/ajuda' },
                { name: Contactos, href: '/contactos' },
                { name: FAQ, href: '/ajuda/faq' },
                { name: 'Estado do Sistema', href: '#' }
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 transition-colors hover:translate-x-1 transform duration-200 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="text-gray-900 font-semibold mb-6">Empresa</h4>
            <ul className="space-y-3">
              {[
                { name: 'Sobre Nós', href: '#' },
                { name: Carreiras, href: '#' },
                { name: Imprensa, href: '#' },
                { name: Parceiros, href: '#' }
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 transition-colors hover:translate-x-1 transform duration-200 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="text-gray-900 font-semibold mb-6">Legal</h4>
            <ul className="space-y-3">
              {[
                { name: 'Termos de Serviço', href: '/termos' },
                { name: 'Política de Privacidade', href: '/politica-privacidade' },
                { name: Cookies, href: '/cookies' },
                { name: GDPR, href: '#' }
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 transition-colors hover:translate-x-1 transform duration-200 inline-block"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-600 text-sm">© 2024 Revify. Todos os direitos reservados.</p>
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <span>Feito com ❤️ em Portugal</span>
              <span>•</span>
              <span>Versão 2.0</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
