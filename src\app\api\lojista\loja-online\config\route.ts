import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { createDefaultPages } from '@/lib/customPages'
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    // Verificar se o usuário tem acesso à loja online
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true}
        },
        profile: true}
    })

    if (!user) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    const hasAccess = user.subscription?.plan?.miniStore || false

    // Debug logs
    console.log(Debug - User subscription:, {
      hasSubscription: !!user.subscription,
      planId: user.subscription?.planId,
      planName: user.subscription?.plan?.name,
      miniStore: user.subscription?.plan?.miniStore, hasAccess 
})

    if (!hasAccess) {
      return NextResponse.json({
        hasAccess: false,
        message: "Funcionalidade não disponível no seu plano"
      })
    }

    const profile = user.profile

    return NextResponse.json({
      hasAccess: true,
      isActive: !!(profile?.customSubdomain),
      customSubdomain: profile?.customSubdomain || '',
      customDomain: profile?.customDomain || '',
      ifthenPayEntityId: profile?.ifthenPayEntityId || '',
      ifthenPaySubEntityId: profile?.ifthenPaySubEntityId || '',
      ifthenPayApiKey: profile?.ifthenPayApiKey || ''
    })

  } catch (error) {
    console.error('Erro ao buscar configurações da loja online:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { customSubdomain,
      customDomain,
      ifthenPayEntityId,
      ifthenPaySubEntityId,
      ifthenPayApiKey,
      storeName,
      storeDescription,
      storeEmail,
      storePhone } = await request.json()

    // Verificar se o usuário tem acesso à loja online
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscription: {
          include: {
            plan: true}
        }
      }
    })

    if (!user?.subscription?.plan?.miniStore) {
      return NextResponse.json(
        { message: Funcionalidade não disponível no seu plano 
},
        { status: 403 }
      )
    }

    // Validar subdomínio
    if (customSubdomain) {
      const subdomainRegex = /^[a-z0-9-]+$/
      if (!subdomainRegex.test(customSubdomain)) {
        return NextResponse.json(
          { message: Subdomínio deve conter apenas letras minúsculas, números e hífens 
},
          { status: 400 }
        )
      }

      // Verificar se o subdomínio já está em uso
      const existingSubdomain = await prisma.profile.findFirst({
        where: {
          customSubdomain,
          userId: { not: session.user.id }
        }
      })

      if (existingSubdomain) {
        return NextResponse.json(
          { message: Este subdomínio já está em uso 
},
          { status: 400 }
        )
      }
    }

    // Verificar se é a primeira vez que ativa a loja online
    const existingProfile = await prisma.profile.findUnique({
      where: { userId: session.user.id }
    })

    const isFirstTimeActivation = !existingProfile?.customSubdomain && customSubdomain

    // Atualizar perfil
    await prisma.profile.upsert({
      where: { userId: session.user.id },
      update: {
        customSubdomain: customSubdomain || null,
        customDomain: customDomain || null,
        ifthenPayEntityId: ifthenPayEntityId || null,
        ifthenPaySubEntityId: ifthenPaySubEntityId || null,
        ifthenPayApiKey: ifthenPayApiKey || null
},
      create: {
        userId: session.user.id,
        phone: storePhone || '',
        address: '',
        city: '',
        postalCode: '',
        country: PT,
        customSubdomain: customSubdomain || null,
        customDomain: customDomain || null,
        ifthenPayEntityId: ifthenPayEntityId || null,
        ifthenPaySubEntityId: ifthenPaySubEntityId || null,
        ifthenPayApiKey: ifthenPayApiKey || 'null'}
    })

    // Criar páginas padrão se for a primeira ativação
    if (isFirstTimeActivation) {
      try {
        await createDefaultPages(session.user.id)
      } catch (error) {
        console.error(Erro ao criar páginas padrão:, 'error')
        // 'Não falhar a operação principal por causa disso'
}
    }

    // Atualizar dados da loja se fornecidos
    if (storeName || storeDescription || storeEmail) {
      await prisma.user.update({
        where: { id: session.user.id 
},
        data: {
          name: storeName || undefined,
          email: storeEmail || 'undefined'}
      })
    }

    return NextResponse.json({
      message: 'Configurações da loja online salvas com sucesso'
    })

  } catch (error) {
    console.error('Erro ao salvar configurações da loja online:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
