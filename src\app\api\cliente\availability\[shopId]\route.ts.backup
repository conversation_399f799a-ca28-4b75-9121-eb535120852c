import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: { shopId: string } }) {
  try {
    const shopId = params.shopId

    // Buscar perfil da loja para horários de funcionamento
    const shop = await prisma.user.findUnique({
      where: { id: shopId },
      include: { profile: true }
    })

    if (!shop) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    // Horários padrão se não estiverem definidos
    const defaultBusinessHours = {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    }

    const businessHours = shop.profile?.businessHours as any || defaultBusinessHours

    // Gerar próximos 14 dias de disponibilidade
    const availableDays = []
    const today = new Date()
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      
      const dayName = date.toLocaleDateString('pt-PT', { weekday: 'long' })
      const dayKey = getDayKey(date.getDay())
      const dayHours = businessHours[dayKey]
      
      if (!dayHours.closed) {
        const slots = generateTimeSlots(dayHours.open, dayHours.close, date)
        
        availableDays.push({
          date: date.toISOString().split('T')[0],
          dayName: dayName.charAt(0).toUpperCase() + dayName.slice(1),
          slots: slots
        })
      }
    }

    return NextResponse.json({
      shopId,
      shopName: shop.profile?.companyName || shop.name,
      businessHours,
      days: availableDays
    })

  } catch (error) {
    console.error('Erro ao buscar disponibilidade:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function getDayKey(dayOfWeek: number): string {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  return days[dayOfWeek]
}

function generateTimeSlots(openTime: string, closeTime: string, date: Date) {
  const slots = []
  const [openHour, openMinute] = openTime.split(':').map(Number)
  const [closeHour, closeMinute] = closeTime.split(':').map(Number)
  
  // Começar 30 minutos depois da abertura
  let currentHour = openHour
  let currentMinute = openMinute + 30
  
  if (currentMinute >= 60) {
    currentHour += 1
    currentMinute -= 60
  }
  
  // Gerar slots de 30 em 30 minutos até 1 hora antes do fecho
  const endHour = closeHour - 1
  const endMinute = closeMinute
  
  while (
    currentHour < endHour || 
    (currentHour === endHour && currentMinute <= endMinute)
  ) {
    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
    
    // Verificar se o slot está no passado (para hoje)
    const now = new Date()
    const slotDateTime = new Date(date)
    slotDateTime.setHours(currentHour, currentMinute, 0, 0)
    
    const isAvailable = slotDateTime > now && !isSlotBooked(timeString, date)
    
    slots.push({
      time: timeString,
      available: isAvailable
    })
    
    // Próximo slot (30 minutos depois)
    currentMinute += 30
    if (currentMinute >= 60) {
      currentHour += 1
      currentMinute -= 60
    }
  }
  
  return slots
}

function isSlotBooked(time: string, date: Date): boolean {
  // TODO: Verificar na base de dados se o slot já está ocupado
  // Por agora, simular alguns slots ocupados aleatoriamente
  const random = Math.random()
  return random < 0.2 // 20% dos slots estão ocupados
}
