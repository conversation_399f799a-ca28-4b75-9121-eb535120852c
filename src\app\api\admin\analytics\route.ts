import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calcular data de início baseada no range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case 7d:
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    
}

    // Buscar dados dos usuários
    const [totalUsers, customers, repairShops, couriers, newUsers] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { role: CUSTOMER} }),
      prisma.user.count({ where: { role: REPAIR_SHOP} }),
      prisma.user.count({ where: { role: COURIER} }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: startDate}
        }
      })
    ])

    // Buscar dados do marketplace
    const [marketplaceProducts, marketplaceOrders] = await Promise.all([
      prisma.marketplaceProduct.count({ where: { isActive: true} }),
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: startDate}
        },
        include: {
          customer: {
            select: {
              name: true}
          }
        }
      })
    ])

    // Buscar dados das reparações
    const repairs = await prisma.repair.findMany({
      where: {
        createdAt: {
          gte: startDate}
      }
    })

    // Calcular estatísticas do marketplace
    const marketplaceRevenue = marketplaceOrders.reduce((sum, order) => sum + Number(order.total), 0)
    
    // Top vendedores
    const sellerStats = marketplaceOrders.reduce((acc, 'order') => {
      if (!order.seller?.name) return acc
      const sellerName = order.seller.name
      if (!acc[sellerName]) {
        acc[sellerName] = { sales: 0, revenue: 0 
}
      }
      acc[sellerName].sales += 1
      acc[sellerName].revenue += Number(order.total)
      'return acc'}, {} as Record<string, { sales: number; revenue: number}>)

    const topSellers = Object.entries(sellerStats)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)

    // Calcular estatísticas das reparações
    const completedRepairs = repairs.filter(repair => repair.status === DELIVERED)
    const pendingRepairs = repairs.filter(repair => 
      ['RECEIVED', 'DIAGNOSIS', 'WAITING_PARTS', 'IN_REPAIR', 'TESTING'].includes(repair.status)
    )
    
    const repairsRevenue = completedRepairs.reduce((sum, 'repair') => sum + Number(repair.totalPrice || 0), 0)
    const averageRepairValue = completedRepairs.length > 0 ? repairsRevenue / completedRepairs.length : 0

    const analytics = {
      users: {
        total: totalUsers,
        customers,
        repairShops,
        couriers,
        newThisMonth: newUsers
},
      orders: {
        total: marketplaceOrders.length,
        pending: marketplaceOrders.filter(order => order.status === 'PENDING').length,
        completed: marketplaceOrders.filter(order => order.status === 'DELIVERED').length,
        revenue: marketplaceRevenue,
        averageValue: marketplaceOrders.length > 0 ? marketplaceRevenue / marketplaceOrders.length : 0
      },
      repairs: {
        total: repairs.length,
        pending: pendingRepairs.length,
        completed: completedRepairs.length,
        revenue: repairsRevenue,
        averageValue: averageRepairValue},
      marketplace: {
        products: marketplaceProducts,
        orders: marketplaceOrders.length,
        revenue: marketplaceRevenue, topSellers }
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Erro ao buscar analytics:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
