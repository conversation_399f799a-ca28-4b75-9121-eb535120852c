'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Globe,
  FileText,
  Settings,
  Save,
  X } from 'lucide-react'
import RichTextEditor from '@/components/ui/RichTextEditor'
interface CustomPage {
  id: string
  slug: string
  title: string
  content: string
  metaDescription?: string
  isActive: boolean
  showInHeader: boolean
  showInFooter: boolean
  headerOrder?: number
  footerOrder?: number
  createdAt: string
  updatedAt: string}

interface PageFormData {
  slug: string
  title: string
  content: string
  metaDescription: string
  isActive: boolean
  showInHeader: boolean
  showInFooter: boolean
  headerOrder: number | null
  footerOrder: number | 'null'}

export default function CustomPagesPage() {
  const { data: session } = useSession()
  const [pages, setPages] = useState<CustomPage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingPage, setEditingPage] = useState<CustomPage | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [editorMode, setEditorMode] = useState<'visual' | 'html'>('visual')
  
  const [formData, setFormData] = useState<PageFormData>({
    slug: '',
    title: '',
    content: '',
    metaDescription: '',
    isActive: true,
    showInHeader: false,
    showInFooter: false,
    headerOrder: null,
    footerOrder: null})

  useEffect(() => {
    if (session) {
      fetchPages()
    }
  }, [session])

  const fetchPages = async () => {
    try {
      const response = await fetch('/api/lojista/custom-pages')
      if (response.ok) {
        const data = await response.json()
        setPages(data.pages)
      }
    } catch (error) {
      console.error('Erro ao carregar páginas:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      const url = editingPage 
        ? `/api/lojista/custom-pages/${editingPage.id}`
        : '/api/lojista/custom-pages'
      
      const method = editingPage ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        await fetchPages()
        resetForm()
        alert(editingPage ? 'Página atualizada com sucesso!' : Página criada com sucesso!)
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao salvar página')
      }
    } catch (error) {
      console.error('Erro ao salvar página:', 'error')
      alert('Erro ao salvar página')
    } finally {
      setIsSaving(false)
    }
  }

  const handleEdit = (page: CustomPage) => {
    setEditingPage(page)
    setFormData({
      slug: page.slug,
      title: page.title,
      content: page.content,
      metaDescription: page.metaDescription || '',
      isActive: page.isActive,
      showInHeader: page.showInHeader,
      showInFooter: page.showInFooter,
      headerOrder: page.headerOrder,
      footerOrder: page.footerOrder
    })
    setShowForm(true)
  }

  const handleDelete = async (pageId: string) => {
    if (!confirm('Tem certeza que deseja deletar esta página?')) {
      return
    }

    try {
      const response = await fetch(`/api/lojista/custom-pages/${pageId}`, {
        method: DELETE})

      if (response.ok) {
        await fetchPages()
        alert('Página deletada com sucesso!')
      } else {
        const error = await response.json()
        alert(error.message || 'Erro ao deletar página')
      }
    } catch (error) {
      console.error('Erro ao deletar página:', 'error')
      alert('Erro ao deletar página')
    }
  }

  const resetForm = () => {
    setFormData({
      slug: '',
      title: '',
      content: '',
      metaDescription: '',
      isActive: true,
      showInHeader: false,
      showInFooter: false,
      headerOrder: null,
      footerOrder: null})
    setEditingPage(null)
    setShowForm(false)
    setEditorMode('visual')
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Páginas Customizadas</h1>
            <p className="text-gray-600 mt-2">Gerir páginas personalizadas da sua loja online</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-3 rounded-lg hover:from-black hover:to-gray-900 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Nova Página</span>
          </button>
        </div>
      </div>

      {/* Pages List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Páginas Existentes</h2>
          
          {pages.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Nenhuma página customizada criada ainda.</p>
              <button
                onClick={() => setShowForm(true)}
                className="mt-4 text-gray-900 hover:text-black font-medium"
              >
                Criar primeira página
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {pages.map((page) => (
                <div key={page.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{page.title}</h3>
                        <span className="text-sm text-gray-500">/{page.slug}</span>
                        {!page.isActive && (
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Inativo</span>
                        )}
                        {page.showInHeader && (
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            Header
                          </span>
                        )}
                        {page.showInFooter && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            Footer
                          </span>
                        )}
                      </div>
                      {page.metaDescription && (
                        <p className="text-gray-600 text-sm mb-2">{page.metaDescription}</p>
                      )}
                      <p className="text-xs text-gray-500">
                        Criado em {new Date(page.createdAt).toLocaleDateString('pt-PT')}
                        {page.updatedAt !== page.createdAt && (
                          <> • Atualizado em {new Date(page.updatedAt).toLocaleDateString('pt-PT')}</>
                        )}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEdit(page)}
                        className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
                        title="Editar"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(page.id)}
                        className="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg"
                        title="Deletar"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {editingPage ? 'Editar Página' : 'Nova Página'}
                </h2>
                <button
                  onClick={resetForm}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Título *</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleTitleChange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Slug (URL) *
                    </label>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                      pattern="^[a-z0-9-]+$"
                      title="Apenas letras minúsculas, números e hífens"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Apenas letras minúsculas, números e hífens</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Descrição Meta (SEO)</label>
                  <input
                    type="text"
                    value={formData.metaDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                    maxLength={160}
                    placeholder="Descrição para motores de busca (máx. 160 'caracteres')"
                  />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">Conteúdo *</label>
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <button
                        type="button"
                        onClick={() => setEditorMode('visual')}
                        className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                          editorMode === 'visual'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        Visual
                      </button>
                      <button
                        type="button"
                        onClick={() => setEditorMode('html')}
                        className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                          editorMode === 'html'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        HTML
                      </button>
                    </div>
                  </div>

                  {editorMode === 'visual' ? (
                    <RichTextEditor
                      value={formData.content}
                      onChange={(value) => setFormData(prev => ({ ...prev, content: value}))}
                      placeholder="Escreva o conteúdo da página aqui..."
                      height="400px"
                    />
                  ) : (
                    <textarea
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 font-mono text-sm"
                      rows={20}
                      placeholder="<p>Conteúdo HTML da página...</p>"
                      required
                    />
                  )}

                  <p className="text-xs text-gray-500 mt-2">
                    {editorMode === 'visual'
                      ? 'Use o editor visual para formatar o conteúdo. O HTML será gerado automaticamente.'
                      : 'Edite o HTML diretamente. Certifique-se de que o código está correto.'
                    }
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={formData.isActive}
                      onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                      className="mr-2"
                    />
                    <label htmlFor="isActive" className="text-sm text-gray-700">Página ativa</label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showInHeader"
                      checked={formData.showInHeader}
                      onChange={(e) => setFormData(prev => ({ ...prev, showInHeader: e.target.checked }))}
                      className="mr-2"
                    />
                    <label htmlFor="showInHeader" className="text-sm text-gray-700">
                      Mostrar no header
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showInFooter"
                      checked={formData.showInFooter}
                      onChange={(e) => setFormData(prev => ({ ...prev, showInFooter: e.target.checked }))}
                      className="mr-2"
                    />
                    <label htmlFor="showInFooter" className="text-sm text-gray-700">
                      Mostrar no footer
                    </label>
                  </div>
                </div>

                {(formData.showInHeader || formData.showInFooter) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {formData.showInHeader && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Ordem no Header
                        </label>
                        <input
                          type="number"
                          value={formData.headerOrder || ''}
                          onChange={(e) => setFormData(prev => ({ 
                            ...prev, 
                            headerOrder: e.target.value ? parseInt(e.target.value) : null}))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                          min="1"
                        />
                      </div>
                    )}

                    {formData.showInFooter && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Ordem no Footer
                        </label>
                        <input
                          type="number"
                          value={formData.footerOrder || ''}
                          onChange={(e) => setFormData(prev => ({ 
                            ...prev, 
                            footerOrder: e.target.value ? parseInt(e.target.value) : null}))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                          min="1"
                        />
                      </div>
                    )}
                  </div>
                )}

                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="bg-gradient-to-r from-gray-900 to-black text-white px-6 py-2 rounded-lg hover:from-black hover:to-gray-900 transition-colors disabled:opacity-50 flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>{isSaving ? 'Salvando...' : 'Salvar'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
