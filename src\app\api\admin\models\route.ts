import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const models = await prisma.deviceModel.findMany({
      include: {
        brand: true,
        category: true},
      orderBy: [
        { brand: { name: asc} },
        { name: asc}
      ]
    })

    return NextResponse.json(models)

  } catch (error) {
    console.error('Erro ao buscar modelos:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: '<PERSON><PERSON> negado' },
        { status: 403 }
      )
    }

    const { name, brandId, categoryId, releaseYear, image, specifications } = await request.json()

    if (!name || !brandId || !categoryId) {
      return NextResponse.json(
        { message: "Nome, marca e categoria são obrigatórios" },
        { status: 400 }
      )
    }

    // Verificar se a marca existe
    const brand = await prisma.brand.findUnique({
      where: { id: brandId}
    })

    if (!brand) {
      return NextResponse.json(
        { message: "Marca não encontrada" },
        { status: 404 }
      )
    }

    // Verificar se o modelo já existe para esta marca
    const existingModel = await prisma.deviceModel.findUnique({
      where: {
        brandId_name: { brandId, name }
      }
    })

    if (existingModel) {
      return NextResponse.json(
        { message: "Este modelo já existe para esta marca" },
        { status: 400 }
      )
    }

    const model = await prisma.deviceModel.create({
      data: {
        name,
        brandId,
        categoryId,
        releaseYear: releaseYear ? parseInt(releaseYear) : null,
        image: image || null,
        specifications: specifications || null,
        isActive: true},
      include: {
        brand: true,
        category: true}
    })

    return NextResponse.json(model, { status: 201 })

  } catch (error) {
    console.error("Erro ao criar modelo:", error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
