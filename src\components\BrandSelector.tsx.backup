'use client'

import { useState, useEffect } from 'react'
import { ChevronDown } from 'lucide-react'

interface Brand {
  id: string
  name: string
  description?: string
}

interface BrandSelectorProps {
  value: string | null
  onChange: (brandId: string | null) => void
  className?: string
  placeholder?: string
}

export default function BrandSelector({ 
  value, 
  onChange, 
  className = '',
  placeholder = 'Selecione uma marca'
}: BrandSelectorProps) {
  const [brands, setBrands] = useState<Brand[]>([])
  const [loading, setLoading] = useState(true)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    fetchBrands()
  }, [])

  const fetchBrands = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/brands')
      if (response.ok) {
        const data = await response.json()
        setBrands(data.brands || [])
      }
    } catch (error) {
      console.error('Erro ao buscar marcas:', error)
    } finally {
      setLoading(false)
    }
  }

  const selectedBrand = brands.find(brand => brand.id === value)

  const handleSelect = (brandId: string | null) => {
    onChange(brandId)
    setIsOpen(false)
  }

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
          Carregando marcas...
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 text-left border border-gray-300 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 flex items-center justify-between"
      >
        <span className={selectedBrand ? 'text-gray-900' : 'text-gray-500'}>
          {selectedBrand ? selectedBrand.name : placeholder}
        </span>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          <button
            type="button"
            onClick={() => handleSelect(null)}
            className="w-full px-3 py-2 text-left hover:bg-gray-50 text-gray-500"
          >
            Nenhuma marca
          </button>
          
          {brands.length === 0 ? (
            <div className="px-3 py-2 text-gray-500 text-sm">
              Nenhuma marca disponível
            </div>
          ) : (
            brands.map((brand) => (
              <button
                key={brand.id}
                type="button"
                onClick={() => handleSelect(brand.id)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 ${
                  value === brand.id ? 'bg-indigo-50 text-indigo-700' : 'text-gray-900'
                }`}
              >
                <div>
                  <span>{brand.name}</span>
                </div>
                {brand.description && (
                  <div className="text-sm text-gray-500 mt-1">
                    {brand.description}
                  </div>
                )}
              </button>
            ))
          )}
        </div>
      )}
    </div>
  )
}
