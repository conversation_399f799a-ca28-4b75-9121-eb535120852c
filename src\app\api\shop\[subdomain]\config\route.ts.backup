import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ subdomain: string }> }
) {
  try {
    const { subdomain } = await params

    // Buscar usuário pelo subdomínio configurado no perfil
    const user = await prisma.user.findFirst({
      where: {
        profile: {
          customSubdomain: subdomain
        },
        role: 'REPAIR_SHOP'
      },
      include: {
        profile: {
          include: {
            shopConfig: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { message: 'Loja não encontrada' },
        { status: 404 }
      )
    }

    const profile = user.profile

    // Retornar configuração da loja
    const shopConfig = {
      id: user.id,
      companyName: profile?.companyName || user.name,
      description: profile?.description || 'Loja de reparações e dispositivos eletrónicos',
      logo: profile?.shopConfig?.logoUrl || profile?.logo || null,
      phone: profile?.phone || '',
      address: profile?.address || '',
      city: profile?.city || '',
      businessHours: profile?.businessHours || {},
      isActive: true, // Assumir que está ativo se encontrou o usuário
      subdomain: subdomain,
      customDomain: profile?.customDomain,
      theme: 'default', // Valor padrão
      bannerImage: null, // Valor padrão
      showProducts: true, // Valor padrão
      showServices: true, // Valor padrão
      contactEmail: user.email,
      socialLinks: {}, // Valor padrão
      // Configurações de envio
      freeShippingThreshold: profile?.freeShippingThreshold || 50,
      defaultShippingRate: profile?.defaultShippingRate || 5.99
    }

    return NextResponse.json(shopConfig)

  } catch (error) {
    console.error('Erro ao buscar configuração da loja:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
