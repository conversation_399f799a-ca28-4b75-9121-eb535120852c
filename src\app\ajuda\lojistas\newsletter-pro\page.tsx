'use client'

import Link from 'next/link'
import { ArrowLeft, Mail, Users, BarChart3, FileText, Upload, Send } from 'lucide-react'

export default function NewsletterProPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />Voltar à Central de Ajuda</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Hero Section */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 text-white">
            <div className="flex items-center mb-4">
              <Mail className="w-8 h-8 mr-3" />
              <h1 className="text-2xl font-bold">Newsletter Pro</h1>
            </div>
            <p className="text-blue-100 text-lg">Sistema avançado de email marketing com templates profissionais e automação completa.</p>
          </div>

          {/* Main Content */}
          <div className="p-6">
            {/* Overview */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Visão Geral</h2>
              <p className="text-gray-700 mb-6">O Newsletter Pro é uma aplicação completa de email marketing que permite criar, enviar e acompanhar campanhas profissionais.  Com templates personalizáveis, segmentação avançada e analytics detalhados.</p>

              <h3 className="text-lg font-semibold text-blue-900 mb-3">Funcionalidades principais</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <FileText className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Templates Profissionais</h4>
                    <p className="text-blue-800 text-sm">3 templates pré-definidos e editor personalizado</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Users className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Gestão de Contactos</h4>
                    <p className="text-blue-800 text-sm">Listas organizadas e segmentação automática</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Send className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Automação</h4>
                    <p className="text-blue-800 text-sm">Campanhas automáticas e follow-ups</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <BarChart3 className="w-5 h-5 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-900">Analytics</h4>
                    <p className="text-blue-800 text-sm">Relatórios detalhados de performance</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Getting Started */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Como Começar</h2>
              
              <div className="space-y-6">
                {/* Step 1 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">
                      1
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-blue-900 mb-2">Instalar a aplicação</h3>
                      <p className="text-blue-800 mb-3">Vá ao AppStore do lojista e instale o Newsletter Pro. A aplicação está incluída no plano REVY PRO.</p>
                      <div className="bg-blue-100 border border-blue-300 rounded p-3">
                        <p className="text-blue-900 text-sm">
                          <strong>Nota:</strong>O Newsletter Pro requer o plano REVY PRO. Inclui trial de 30 dias grátis.</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">
                      2
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-green-900 mb-2">Criar lista de contactos</h3>
                      <p className="text-green-800 mb-3">Importe os seus contactos ou adicione-os manualmente. Pode importar:</p>
                      <ul className="list-disc list-inside text-green-800 space-y-1">
                        <li>Ficheiro CSV</li>
                        <li>Clientes existentes da loja</li>
                        <li>Contactos manuais</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">
                      3
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-orange-900 mb-2">Escolher template</h3>
                      <p className="text-orange-800 mb-3">Templates disponíveis:</p>
                      <ul className="list-disc list-inside text-orange-800 space-y-1">
                        <li><strong>Modern Business:</strong> Design moderno e profissional</li>
                        <li><strong>Newsletter Clássica:</strong> Layout tradicional para newsletters</li>
                        <li><strong>Promocional:</strong> Design vibrante para ofertas especiais</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Step 4 */}
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1">
                      4
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-purple-900 mb-2">Criar e enviar campanha</h3>
                      <p className="text-purple-800 mb-3">Personalize o conteúdo, defina os destinatários e programe o envio.</p>
                      <div className="bg-purple-100 border border-purple-300 rounded p-3">
                        <p className="text-purple-900 text-sm">
                          <strong>Dica:</strong> Use o modo de teste para verificar como fica antes de enviar.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Preços e Planos</h2>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-green-900">Newsletter Pro</h3>
                    <p className="text-green-800">€14.99/mês</p>
                  </div>
                  <div className="text-right">
                    <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">Trial 30 dias grátis</div>
                  </div>
                </div>
                <p className="text-green-800 text-sm mt-2">Inclui todas as funcionalidades, templates ilimitados e suporte prioritário.</p>
              </div>
            </div>

            {/* Navigation */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex justify-between">
                <Link
                  href="/ajuda/lojistas"
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  ← Outros guias para lojistas
                </Link>
                <Link
                  href="/lojista/appstore"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Instalar Newsletter Pro
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
