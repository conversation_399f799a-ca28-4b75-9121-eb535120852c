'use client'

import Link from 'next/link'
import { ArrowLeft, UserCheck, Settings, Store, CreditCard, BarChart3, Package } from 'lucide-react'

export default function PrimeirosPassosLojistaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="mr-8">
                <Link href="/" className="font-bold text-black text-xl">
                  Revify
                </Link>
                <div className="text-xs text-gray-600 font-light">Central de Ajuda</div>
              </div>
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar à Central de Ajuda
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Link href="/ajuda" className="hover:text-gray-700">Central de Ajuda</Link>
              <span className="mx-2">›</span>
              <Link href="/ajuda?category=lojistas" className="hover:text-gray-700">Para Lojistas</Link>
              <span className="mx-2">›</span>
              <span>Primeiros passos</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Primeiros passos para lojistas</h1>
            <p className="text-lg text-gray-600">
              Guia completo para começar a usar a plataforma Revify como oficina de reparação.
            </p>
          </div>

          <div className="prose max-w-none">
            <h2>Configuração inicial</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <UserCheck className="w-6 h-6 text-blue-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Passo 1: Registo e verificação</h3>
                  <p className="text-blue-800 mb-3">Complete o processo de registo:</p>
                  <ul className="list-disc list-inside text-blue-800 space-y-1">
                    <li>Registe-se como lojista na plataforma</li>
                    <li>Forneça documentação da empresa (NIF, licenças)</li>
                    <li>Aguarde a verificação da nossa equipa (24-48h)</li>
                    <li>Receba confirmação por email</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Settings className="w-6 h-6 text-green-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Passo 2: Configurar perfil da oficina</h3>
                  <p className="text-green-800 mb-3">Configure as informações da sua oficina:</p>
                  <ul className="list-disc list-inside text-green-800 space-y-1">
                    <li>Nome e descrição da oficina</li>
                    <li>Morada e horários de funcionamento</li>
                    <li>Tipos de reparação que oferece</li>
                    <li>Fotos da oficina e equipamentos</li>
                    <li>Informações de contacto</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Store className="w-6 h-6 text-purple-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900 mb-2">Passo 3: Definir serviços e preços</h3>
                  <p className="text-purple-800 mb-3">Configure o seu catálogo de serviços:</p>
                  <ul className="list-disc list-inside text-purple-800 space-y-1">
                    <li>Adicione tipos de reparação disponíveis</li>
                    <li>Defina preços base para cada serviço</li>
                    <li>Configure tempos estimados de reparação</li>
                    <li>Adicione opções de garantia</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <CreditCard className="w-6 h-6 text-yellow-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-yellow-900 mb-2">Passo 4: Configurar pagamentos</h3>
                  <p className="text-yellow-800 mb-3">Configure os métodos de pagamento:</p>
                  <ul className="list-disc list-inside text-yellow-800 space-y-1">
                    <li>Adicione conta bancária para recebimentos</li>
                    <li>Configure integração com Moloni (opcional)</li>
                    <li>Defina políticas de pagamento</li>
                    <li>Configure emissão automática de faturas</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 my-6">
              <div className="flex items-start">
                <Package className="w-6 h-6 text-orange-600 mt-1 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-orange-900 mb-2">Passo 5: Explorar a App Store</h3>
                  <p className="text-orange-800 mb-3">Potencialize o seu negócio com apps:</p>
                  <ul className="list-disc list-inside text-orange-800 space-y-1">
                    <li>Newsletter Pro - Marketing por email</li>
                    <li>CRM Avançado - Gestão de clientes</li>
                    <li>Gestor de Stock - Controlo de inventário</li>
                    <li>WhatsApp Business - Comunicação automatizada</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2>Dashboard do lojista</h2>
            <p>Após a configuração inicial, terá acesso ao dashboard com:</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Gestão de reparações
                </h3>
                <ul className="text-sm space-y-1">
                  <li>• Pedidos pendentes</li>
                  <li>• Reparações em curso</li>
                  <li>• Histórico completo</li>
                  <li>• Comunicação com clientes</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2 flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Gestão financeira
                </h3>
                <ul className="text-sm space-y-1">
                  <li>• Faturação automática</li>
                  <li>• Relatórios de vendas</li>
                  <li>• Controlo de pagamentos</li>
                  <li>• Analytics de performance</li>
                </ul>
              </div>
            </div>

            <h2>Primeiras reparações</h2>
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Dicas para começar:</h3>
              <ul className="space-y-2">
                <li><strong>Responda rapidamente:</strong> Clientes valorizam respostas rápidas a pedidos</li>
                <li><strong>Seja transparente:</strong> Forneça orçamentos detalhados e prazos realistas</li>
                <li><strong>Comunique regularmente:</strong> Mantenha os clientes informados sobre o progresso</li>
                <li><strong>Qualidade primeiro:</strong> Foque na qualidade para obter boas avaliações</li>
              </ul>
            </div>

            <h2>Suporte e recursos</h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <p className="mb-4">Recursos disponíveis para lojistas:</p>
              <ul className="space-y-2">
                <li>• <Link href="/ajuda/lojistas/gestao-reparacoes" className="text-blue-600 hover:underline">Guia de gestão de reparações</Link></li>
                <li>• <Link href="/ajuda/lojistas/sistema-orcamentos" className="text-blue-600 hover:underline">Como criar orçamentos</Link></li>
                <li>• <Link href="/ajuda/lojistas/marketplace-vender" className="text-blue-600 hover:underline">Vender no marketplace</Link></li>
                <li>• <Link href="/contactos" className="text-blue-600 hover:underline">Suporte técnico 24/7</Link></li>
              </ul>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex justify-between">
              <Link
                href="/ajuda"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar à Central de Ajuda
              </Link>
              <Link
                href="/ajuda/lojistas/gestao-reparacoes"
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                Próximo: Gestão de reparações
                <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
