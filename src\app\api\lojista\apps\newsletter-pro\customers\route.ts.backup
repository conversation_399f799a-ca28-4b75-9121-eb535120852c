import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const listId = searchParams.get('listId')

    // Buscar clientes únicos que fizeram encomendas ou reparações
    let excludeEmails = []
    
    if (listId) {
      // Se listId fornecido, excluir emails já na lista
      const existingSubscribers = await prisma.$queryRaw`
        SELECT email FROM newsletter_subscribers
        WHERE "listId" = ${listId} AND "isActive" = true
      ` as any[]
      
      excludeEmails = existingSubscribers.map((s: any) => s.email)
    }

    // Buscar clientes que fizeram reparações
    const repairCustomers = await prisma.repair.findMany({
      where: {
        repairShopId: session.user.id
      },
      select: {
        customerId: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
            profile: {
              select: {
                phone: true
              }
            }
          }
        }
      },
      distinct: ['customerId']
    })

    // Buscar clientes que fizeram encomendas (se existir marketplace)
    let orderCustomers: any[] = []
    try {
      orderCustomers = await prisma.$queryRaw`
        SELECT DISTINCT u.id, u.name, u.email, u."createdAt", p.phone
        FROM users u
        LEFT JOIN profiles p ON u.id = p."userId"
        WHERE u.id IN (
          SELECT DISTINCT o."customerId"
          FROM orders o
          JOIN marketplace_order_items moi ON o.id = moi."orderId"
          JOIN marketplace_products mp ON moi."productId" = mp.id
          WHERE mp."sellerId" = ${session.user.id}
        )
        AND u.email IS NOT NULL
      ` as any[]
    } catch (error) {
      console.log('Marketplace tables not found, using only repair customers')
    }

    // Combinar e deduplificar clientes
    const allCustomers = new Map()

    // Adicionar clientes de reparações
    repairCustomers.forEach(repair => {
      if (repair.customer && repair.customer.email) {
        allCustomers.set(repair.customer.id, {
          id: repair.customer.id,
          name: repair.customer.name,
          email: repair.customer.email,
          phone: repair.customer.profile?.phone || '',
          createdAt: repair.customer.createdAt,
          order_count: 0,
          repair_count: 1
        })
      }
    })

    // Adicionar clientes de encomendas
    orderCustomers.forEach(customer => {
      if (allCustomers.has(customer.id)) {
        allCustomers.get(customer.id).order_count = 1
      } else {
        allCustomers.set(customer.id, {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          phone: customer.phone || '',
          createdAt: customer.createdAt,
          order_count: 1,
          repair_count: 0
        })
      }
    })

    // Filtrar emails excluídos
    const customers = Array.from(allCustomers.values()).filter(customer =>
      !excludeEmails.includes(customer.email)
    )

    const formattedCustomers = customers.map((customer: any) => ({
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      createdAt: customer.createdAt,
      stats: {
        orders: Number(customer.order_count || 0),
        repairs: Number(customer.repair_count || 0),
        total: Number(customer.order_count || 0) + Number(customer.repair_count || 0)
      }
    }))

    return NextResponse.json({
      customers: formattedCustomers,
      total: formattedCustomers.length
    })

  } catch (error) {
    console.error('Erro ao buscar clientes:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
