import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { currentPassword, newPassword } = await request.json()

    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { message: 'Password atual e nova password são obrigatórias' },
        { status: 400 }
      )
    }

    if (newPassword.length < 6) {
      return NextResponse.json(
        { message: 'A nova password deve ter pelo menos 6 caracteres' },
        { status: 400 }
      )
    }

    // Buscar usuário atual
    const user = await prisma.$queryRaw`
      SELECT * FROM users WHERE id = ${session.user.id}
    `

    if (!user || user.length === 0) {
      return NextResponse.json(
        { message: 'Usuário não encontrado' },
        { status: 404 }
      )
    }

    const userData = user[0]

    // Verificar password atual
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userData.password)

    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { message: 'Password atual incorreta' },
        { status: 400 }
      )
    }

    // Hash da nova password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12)

    // Atualizar password
    await prisma.$queryRaw`
      UPDATE users 
      SET password = ${hashedNewPassword}, "updatedAt" = NOW()
      WHERE id = ${session.user.id}
    `

    return NextResponse.json({
      message: 'Password alterada com sucesso'
    })

  } catch (error) {
    console.error('Erro ao alterar password:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
