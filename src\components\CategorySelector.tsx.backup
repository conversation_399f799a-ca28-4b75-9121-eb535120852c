'use client'

import { useState, useEffect } from 'react'
import { ChevronDown } from 'lucide-react'

interface Category {
  id: string
  name: string
  description?: string
  isDefault?: boolean
}

interface CategorySelectorProps {
  value: string | null
  onChange: (categoryId: string | null) => void
  subdomain?: string
  className?: string
  placeholder?: string
}

export default function CategorySelector({ 
  value, 
  onChange, 
  subdomain, 
  className = '',
  placeholder = 'Selecione uma categoria'
}: CategorySelectorProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    fetchCategories()
  }, [subdomain])

  const fetchCategories = async () => {
    try {
      setLoading(true)

      // Buscar apenas categorias da plataforma por enquanto
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      }
    } catch (error) {
      console.error('Erro ao buscar categorias:', error)
    } finally {
      setLoading(false)
    }
  }

  const selectedCategory = categories.find(cat => cat.id === value)

  const handleSelect = (categoryId: string | null) => {
    onChange(categoryId)
    setIsOpen(false)
  }

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
          Carregando categorias...
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-left flex items-center justify-between"
      >
        <span className={selectedCategory ? 'text-gray-900' : 'text-gray-500'}>
          {selectedCategory ? selectedCategory.name : placeholder}
        </span>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          <button
            type="button"
            onClick={() => handleSelect(null)}
            className="w-full px-3 py-2 text-left hover:bg-gray-50 text-gray-500"
          >
            Nenhuma categoria
          </button>
          
          {categories.length === 0 ? (
            <div className="px-3 py-2 text-gray-500 text-sm">
              Nenhuma categoria disponível
            </div>
          ) : (
            categories.map((category) => (
              <button
                key={category.id}
                type="button"
                onClick={() => handleSelect(category.id)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 ${
                  value === category.id ? 'bg-indigo-50 text-indigo-700' : 'text-gray-900'
                }`}
              >
                <div>
                  <span>{category.name}</span>
                </div>
                {category.description && (
                  <div className="text-sm text-gray-500 mt-1">
                    {category.description}
                  </div>
                )}
              </button>
            ))
          )}
        </div>
      )}
    </div>
  )
}
