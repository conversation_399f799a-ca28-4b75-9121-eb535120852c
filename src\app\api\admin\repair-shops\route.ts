import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search')

    let whereClause: any = {
      role: REPAIR_SHOP}

    // Filtrar por termo de busca
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: insensitive} },
        { email: { contains: search, mode: insensitive} }
      ]
    }

    // Buscar lojistas com paginação
    const [repairShops, total] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        include: {
          profile: {
            select: {
              companyName: true,
              phone: true,
              description: true,
              serviceRadius: true,
              workingCategories: true,
              workingBrands: true,
              workingProblems: true}
          },
          _count: {
            select: {
              repairShopRepairs: true,
              repairShopPrices: true}
          }
        },
        orderBy: {
          createdAt: desc},
        skip: (page - 1) * limit,
        take: limit}),
      prisma.user.count({ where: whereClause})
    ])

    return NextResponse.json({
      repairShops,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      
}
    })

  } catch (error) {
    console.error('Erro ao buscar lojistas:', 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
