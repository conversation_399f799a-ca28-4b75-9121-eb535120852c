import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
export async function PUT(request: NextRequest, { 'params'}: { params: { id: string} }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'CUSTOMER') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const repairId = params.id
    const { scheduledDate, scheduledTime } = await request.json()

    if (!scheduledDate || !scheduledTime) {
      return NextResponse.json(
        { message: "Data e hora são obrigatórias" },
        { status: 400 }
      )
    }

    // Verificar se a reparação existe e pertence ao usuário
    const repair = await prisma.repair.findUnique({
      where: { id: repairId}
    })

    if (!repair) {
      return NextResponse.json(
        { message: "Reparação não encontrada" },
        { status: 404 }
      )
    }

    if (repair.customerId !== session.user.id) {
      return NextResponse.json(
        { message: Acesso negado 
},
        { status: 403 }
      )
    }

    // Verificar se a data/hora está no futuro
    const scheduledDateTime = new Date(scheduledDate)
    if (scheduledDateTime <= new Date()) {
      return NextResponse.json(
        { message: "Data deve ser no futuro" },
        { status: 400 }
      )
    }

    // Atualizar reparação com agendamento
    const updatedRepair = await prisma.repair.update({
      where: { id: repairId},
      data: {
        scheduledDate: scheduledDateTime,
        status: RECEIVED // 'Mudar status para recebido quando agendado'
}
    })

    // Criar notificação para a loja
    if (repair.repairShopId) {
      await prisma.notification.create({
        data: {
          userId: repair.repairShopId,
          title: "Reparação Agendada",
          message: `Cliente agendou reparação para ${scheduledDateTime.toLocaleDateString(pt-PT)
} às ${scheduledTime}`,
          type: REPAIR_SCHEDULED,
          relatedId: repairId}
      })
    }

    return NextResponse.json({
      message: 'Agendamento realizado com sucesso',
      repair: updatedRepair})

  } catch (error) {
    console.error("Erro ao agendar reparação:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
