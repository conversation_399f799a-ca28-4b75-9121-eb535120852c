import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      // Buscar ou criar código de referral do usuário
      let user = await prisma.user.findUnique({
        where: { id: session.user.id 
},
        select: {
          id: true,
          referralCode: true,
          role: true,
          referrals: {
            include: {
              referred: {
                select: {
                  name: true,
                  email: true}
              }
            },
            orderBy: {
              createdAt: desc}
          }
        }
      })

      if (!user) {
        return NextResponse.json(
          { error: "Usu<PERSON>rio não encontrado" },
          { status: 404 }
        )
      }

      // Gerar código de referral se não existir
      if (!user.referralCode) {
        const referralCode = generateReferralCode(session.user.id)
        
        user = await prisma.user.update({
          where: { id: session.user.id },
          data: { referralCode
},
          select: {
            id: true,
            referralCode: true,
            role: true,
            referrals: {
              include: {
                referred: {
                  select: {
                    name: true,
                    email: true}
                }
              },
              orderBy: {
                createdAt: desc}
            }
          }
        })
      }

      // Calcular estatísticas
      const totalReferrals = user.referrals.length
      const completedReferrals = user.referrals.filter(r => r.status === COMPLETED)
      const pendingReferrals = user.referrals.filter(r => r.status === 'PENDING')
      
      const completedRewards = completedReferrals.reduce((sum, r) => sum + (r.reward || 0), 0)
      const pendingRewards = pendingReferrals.length * getRoleReward(user.role)

      // Gerar link de referral
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:4040'
      const referralLink = `${baseUrl
}/auth/signup?ref=${user.referralCode}`

      // Formatar referrals recentes
      const recentReferrals = user.referrals.slice(0, 10).map(referral => ({
        id: referral.id,
        referredUser: referral.referred.name || referral.referred.email,
        status: referral.status.toLowerCase(),
        reward: referral.reward || 0,
        createdAt: referral.createdAt.toISOString(),
        completedAt: referral.completedAt?.toISOString()
      }))

      const response = {
        referralCode: user.referralCode,
        userRole: user.role,
        totalReferrals,
        pendingReferrals: pendingReferrals.length,
        completedReferrals: completedReferrals.length,
        totalEarnings: completedRewards,
        pendingEarnings: pendingRewards,
        referralLink, recentReferrals }

      return NextResponse.json(response)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao buscar dados de referral:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" 
},
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      )
    }

    const { referredUserId, type } = await request.json()

    if (!referredUserId || !type) {
      return NextResponse.json(
        { error: "Dados obrigatórios em falta" },
        { status: 400 }
      )
    }

    // Import Prisma dynamically to avoid initialization issues
    const { PrismaClient } = await import(@prisma/client)
    const prisma = new PrismaClient()

    try {
      // Verificar se o referral já existe
      const existingReferral = await prisma.referral.findFirst({
        where: {
          referrerId: session.user.id,
          referredId: referredUserId
}
      })

      if (existingReferral) {
        return NextResponse.json(
          { error: "Referral já existe" },
          { status: 400 }
        )
      }

      // Buscar dados do referrer para calcular reward
      const referrer = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { role: true}
      })

      if (!referrer) {
        return NextResponse.json(
          { error: "Usuário não encontrado" },
          { status: 404 }
        )
      }

      const reward = getRoleReward(referrer.role)

      // Criar referral
      const referral = await prisma.referral.create({
        data: {
          referrerId: session.user.id,
          referredId: referredUserId,
          type: type as any,
          reward,
          status: PENDING}
      })

      return NextResponse.json(referral)

    } finally {
      await prisma.$disconnect()
    }

  } catch (error) {
    console.error("Erro ao criar referral:", error)
    return NextResponse.json(
      { error: "Erro interno do servidor" 
},
      { status: 500 }
    )
  }
}

// Funções auxiliares
function generateReferralCode(userId: string): string {
  const prefix = userId.slice(0, 4).toUpperCase()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `${prefix}${random}`
}

function getRoleReward(role: string): number {
  switch (role?.toLowerCase()) {
    case lojista:
    case 'shop_owner':
      return 5 // €5
    case 'estafeta':
    case 'courier':
      return 25 // €25
    case 'cliente':
    case 'customer':
    default:
      return 10 // 10% (representado 'como valor fixo para simplicidade')
  
}
}
