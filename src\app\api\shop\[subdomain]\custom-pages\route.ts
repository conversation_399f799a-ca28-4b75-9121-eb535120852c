import { NextRequest, NextResponse } from 'next/server'
import { getHeaderPages, getFooterPages } from '@/lib/customPages'
import { prisma } from '@/lib/prisma'
// GET - Obter páginas customizadas para header e footer da loja
export async function GET(
  request: NextRequest,
  { params
}: { params: { subdomain: string} }
) {
  try {
    const { subdomain } = params
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // header, 'footer', ou 'all'

    // Buscar o usuário pela subdomain
    const profile = await prisma.profile.findFirst({
      where: { customSubdomain: subdomain
},
      include: { user: true}
    })

    if (!profile) {
      return NextResponse.json(
        { message: "Loja não encontrada" },
        { status: 404 }
      )
    }

    let pages = []

    if (type === 'header') {
      pages = await getHeaderPages(profile.userId)
    } else if (type === 'footer') {
      pages = await getFooterPages(profile.userId)
    } else { // Retornar ambos
      const headerPages = await getHeaderPages(profile.userId)
      const footerPages = await getFooterPages(profile.userId)
      
      return NextResponse.json({
        headerPages, footerPages })
    }

    return NextResponse.json({
      pages
})

  } catch (error) {
    console.error("Erro ao buscar páginas customizadas da loja:", 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
