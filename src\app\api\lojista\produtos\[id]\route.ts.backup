import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { id } = await params
    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: id,
        sellerId: session.user.id
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      product: {
        id: product.id,
        name: product.name,
        description: product.description,
        price: Number(product.price),
        stock: product.stock,
        categoryId: product.categoryId,
        images: product.images || [],
        isActive: product.isActive,
        createdAt: product.createdAt.toISOString(),
        updatedAt: product.updatedAt.toISOString()
      }
    })

  } catch (error) {
    console.error('Erro ao buscar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { isActive } = await request.json()

    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: params.id,
        sellerId: session.user.id
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    const updatedProduct = await prisma.marketplaceProduct.update({
      where: { id: params.id },
      data: { isActive }
    })

    return NextResponse.json({
      message: 'Produto atualizado com sucesso',
      product: {
        id: updatedProduct.id,
        name: updatedProduct.name,
        description: updatedProduct.description,
        price: Number(updatedProduct.price),
        stock: updatedProduct.stock,
        category: updatedProduct.category,
        images: updatedProduct.images || [],
        isActive: updatedProduct.isActive,
        createdAt: updatedProduct.createdAt.toISOString(),
        updatedAt: updatedProduct.updatedAt.toISOString()
      }
    })

  } catch (error) {
    console.error('Erro ao atualizar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const { name, description, price, stock, categoryId, isActive } = await request.json()

    const { id } = await params
    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: id,
        sellerId: session.user.id
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    const updatedProduct = await prisma.marketplaceProduct.update({
      where: { id: id },
      data: {
        name,
        description,
        price,
        stock,
        categoryId: categoryId || null,
        isActive
      }
    })

    return NextResponse.json({
      message: 'Produto atualizado com sucesso',
      product: {
        id: updatedProduct.id,
        name: updatedProduct.name,
        description: updatedProduct.description,
        price: Number(updatedProduct.price),
        stock: updatedProduct.stock,
        category: updatedProduct.category,
        images: updatedProduct.images || [],
        isActive: updatedProduct.isActive,
        createdAt: updatedProduct.createdAt.toISOString(),
        updatedAt: updatedProduct.updatedAt.toISOString()
      }
    })

  } catch (error) {
    console.error('Erro ao atualizar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: 'Acesso negado' },
        { status: 403 }
      )
    }

    const product = await prisma.marketplaceProduct.findFirst({
      where: {
        id: params.id,
        sellerId: session.user.id
      }
    })

    if (!product) {
      return NextResponse.json(
        { message: 'Produto não encontrado' },
        { status: 404 }
      )
    }

    await prisma.marketplaceProduct.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: 'Produto eliminado com sucesso'
    })

  } catch (error) {
    console.error('Erro ao eliminar produto:', error)
    return NextResponse.json(
      { message: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
