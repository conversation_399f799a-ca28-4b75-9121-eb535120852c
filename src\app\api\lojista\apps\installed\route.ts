import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { PrismaClient } from '@prisma/client'
import { SYSTEM_APPS } from '@/lib/app-definitions'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'REPAIR_SHOP') {
      return NextResponse.json(
        { message: '<PERSON>sso negado' },
        { status: 403 }
      )
    }

    // Buscar apps instaladas do usuário
    const installedAppsRaw = await prisma.$queryRaw`
      SELECT * FROM installed_apps
      WHERE "userId" = ${session.user.id}
      AND "isActive" = true
      ORDER BY "installedAt" DESC
    ` as any[]

    // Buscar configurações personalizadas das apps (do admin)
    let appConfigs: Record<string, any> = {
}
    try {
      const configs = await prisma.appConfig.findMany()
      configs.forEach(config => {
        appConfigs[config.appId] = config.settings
      })
    } catch (error) {
      console.log('No custom app configs found')
    }

    // Enriquecer dados das apps instaladas
    const installedApps = installedAppsRaw.map((installedApp: any) => {
      const systemApp = SYSTEM_APPS.find(app => app.appId === installedApp.appId)
      const customConfig = appConfigs[installedApp.appId] || {}

      if (!systemApp) {
        return {
          ...installedApp,
          name: installedApp.appId,
          monthlyPrice: 0,
          isPaid: false}
      }

      return {
        ...installedApp,
        name: customConfig.name || systemApp.name,
        description: customConfig.description || systemApp.description,
        monthlyPrice: customConfig.monthlyPrice ?? systemApp.monthlyPrice,
        isPaid: (customConfig.monthlyPrice ?? systemApp.monthlyPrice) > 0,
        isTrial: installedApp.isTrial || false,
        isActive: installedApp.isActive
      }
    })

    return NextResponse.json({
      apps: installedApps})

  } catch (error) {
    console.error(Erro ao buscar apps instaladas:, 'error')
    return NextResponse.json(
      { message: 'Erro interno do servidor' 
},
      { status: 500 }
    )
  }
}
